from abc import ABC, abstractmethod
from typing import Annotated, Any, Generic, Literal

from pydantic import ConfigDict, Field, ValidationError

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import ErrorDetails, InvalidArgumentError
from salestech_be.common.type.metadata.field.field_type import FieldType
from salestech_be.common.type.metadata.field.field_type_property import (
    BooleanCheckboxFieldProperty,
    CurrencyFieldProperty,
    CustomFieldTypeProperty,
    CustomFieldTypePropertyT,
    EmailFieldProperty,
    GeoLocationFieldProperty,
    LocalDateFieldProperty,
    LocalTimeOfDayFieldProperty,
    LongTextAreaFieldProperty,
    MultiSelectFieldProperty,
    NumericFieldProperty,
    PercentFieldProperty,
    PhoneFieldProperty,
    RichTextAreaFieldProperty,
    SingleSelectFieldProperty,
    TextAreaFieldProperty,
    TextFieldProperty,
    TimeOfDayFieldProperty,
    TimestampFieldProperty,
    Url<PERSON>ieldProperty,
    UUI<PERSON>ieldProperty,
)
from salestech_be.common.type.patch_request import (
    UNSET,
    UnsetAware,
    specified,
)
from salestech_be.common.type.shape_constrained_model import ShapeConstrainedModel
from salestech_be.util.validation import cast_or_error


class BaseCustomFieldPropertyUpdate(
    Generic[CustomFieldTypePropertyT],
    ABC,
):
    def validate_update(
        self, current_property: CustomFieldTypeProperty
    ) -> CustomFieldTypePropertyT:
        try:
            return self._validate_update(current_property=current_property)
        except ValidationError as e:
            raise InvalidArgumentError(
                f"Failed to validate update for {current_property}",
                additional_error_details=ErrorDetails(
                    code=ErrorCode.CUSTOM_OBJECT_DATA_VALIDATION_ERROR,
                    details=str(e.json()),
                ),
            )
        except ValueError as e:
            raise InvalidArgumentError(
                f"Failed to validate update for {current_property}",
                additional_error_details=ErrorDetails(
                    code=ErrorCode.CUSTOM_OBJECT_DATA_VALIDATION_ERROR,
                    details=str(e),
                ),
            )

    @abstractmethod
    def _validate_update(
        self, current_property: CustomFieldTypeProperty
    ) -> CustomFieldTypePropertyT:
        raise NotImplementedError


class BaseShapeConstrainedCustomFieldPropertyUpdate(
    BaseCustomFieldPropertyUpdate[CustomFieldTypePropertyT],
    ShapeConstrainedModel[CustomFieldTypePropertyT],
    Generic[CustomFieldTypePropertyT],
    ABC,
):
    field_display_name: UnsetAware[str] = UNSET
    field_description: UnsetAware[str] = UNSET
    is_required: UnsetAware[bool] = UNSET
    is_pinned: UnsetAware[bool] = UNSET

    def _additional_validation(
        self, current_property: CustomFieldTypePropertyT
    ) -> None:
        pass

    def _validate_update(
        self, current_property: CustomFieldTypeProperty
    ) -> CustomFieldTypePropertyT:
        casted: CustomFieldTypePropertyT = cast_or_error(
            current_property, self.constraint()
        )
        self._additional_validation(current_property=casted)
        return self.constraint().model_validate(
            casted.model_copy(update=self.flatten_specified_values())
        )


class UUIDFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[UUIDFieldProperty]
):
    model_config = ConfigDict(title="UUIDFieldPropertyUpdate")

    field_type: Literal[FieldType.UUID] = FieldType.UUID


class NumericFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[NumericFieldProperty]
):
    model_config = ConfigDict(title="NumericFieldPropertyUpdate")

    field_type: Literal[FieldType.NUMERIC] = FieldType.NUMERIC
    total_precision: UnsetAware[int] = UNSET
    decimal_precision: UnsetAware[int] = UNSET


class CurrencyFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[CurrencyFieldProperty]
):
    model_config = ConfigDict(title="CurrencyFieldPropertyUpdate")

    field_type: Literal[FieldType.CURRENCY] = FieldType.CURRENCY
    total_precision: UnsetAware[int] = UNSET
    decimal_precision: UnsetAware[int] = UNSET


class PercentFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[PercentFieldProperty]
):
    model_config = ConfigDict(title="PercentFieldPropertyUpdate")

    field_type: Literal[FieldType.PERCENT] = FieldType.PERCENT
    total_precision: UnsetAware[int] = UNSET
    decimal_precision: UnsetAware[int] = UNSET


class TextFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[TextFieldProperty]
):
    model_config = ConfigDict(title="TextFieldPropertyUpdate")

    field_type: Literal[FieldType.TEXT] = FieldType.TEXT
    max_length: UnsetAware[int] = UNSET


class TextAreaFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[TextAreaFieldProperty]
):
    model_config = ConfigDict(title="TextAreaFieldPropertyUpdate")

    field_type: Literal[FieldType.TEXT_AREA] = FieldType.TEXT_AREA
    max_length: UnsetAware[int] = UNSET


class LongTextAreaFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[LongTextAreaFieldProperty]
):
    model_config = ConfigDict(title="LongTextAreaFieldPropertyUpdate")

    field_type: Literal[FieldType.LONG_TEXT_AREA] = FieldType.LONG_TEXT_AREA
    max_length: UnsetAware[int] = UNSET
    visible_lines: UnsetAware[int] = UNSET


class RichTextAreaFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[RichTextAreaFieldProperty]
):
    model_config = ConfigDict(title="RichTextAreaFieldPropertyUpdate")

    field_type: Literal[FieldType.RICH_TEXT_AREA] = FieldType.RICH_TEXT_AREA
    max_length: UnsetAware[int] = UNSET
    visible_lines: UnsetAware[int] = UNSET


class TimestampFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[TimestampFieldProperty]
):
    model_config = ConfigDict(title="TimestampFieldPropertyUpdate")

    field_type: Literal[FieldType.TIMESTAMP] = FieldType.TIMESTAMP


class LocalTimeOfDayFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[LocalTimeOfDayFieldProperty]
):
    model_config = ConfigDict(title="LocalTimeOfDayFieldPropertyUpdate")

    field_type: Literal[FieldType.LOCAL_TIME_OF_DAY] = FieldType.LOCAL_TIME_OF_DAY


class TimeOfDayFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[TimeOfDayFieldProperty]
):
    model_config = ConfigDict(title="TimeOfDayFieldPropertyUpdate")

    field_type: Literal[FieldType.TIME_OF_DAY] = FieldType.TIME_OF_DAY


class LocalDateFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[LocalDateFieldProperty]
):
    model_config = ConfigDict(title="LocalDateFieldPropertyUpdate")

    field_type: Literal[FieldType.LOCAL_DATE] = FieldType.LOCAL_DATE


class BooleanCheckboxFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[BooleanCheckboxFieldProperty]
):
    model_config = ConfigDict(title="BooleanCheckboxFieldPropertyUpdate")

    field_type: Literal[FieldType.BOOLEAN_CHECKBOX] = FieldType.BOOLEAN_CHECKBOX
    default_is_checked: UnsetAware[bool] = UNSET


class EmailFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[EmailFieldProperty]
):
    model_config = ConfigDict(title="EmailFieldPropertyUpdate")

    field_type: Literal[FieldType.EMAIL] = FieldType.EMAIL


class PhoneFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[PhoneFieldProperty]
):
    model_config = ConfigDict(title="PhoneFieldPropertyUpdate")

    field_type: Literal[FieldType.PHONE_NUMBER] = FieldType.PHONE_NUMBER


class UrlFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[UrlFieldProperty]
):
    model_config = ConfigDict(title="UrlFieldPropertyUpdate")
    field_type: Literal[FieldType.URL] = FieldType.URL


class GeoLocationFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[GeoLocationFieldProperty]
):
    model_config = ConfigDict(title="GeoLocationFieldPropertyUpdate")
    field_type: Literal[FieldType.GEO_LOCATION] = FieldType.GEO_LOCATION


class SingleSelectFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[SingleSelectFieldProperty],
):
    model_config = ConfigDict(title="SingleSelectFieldPropertyUpdate")
    field_type: Literal[FieldType.SINGLE_SELECT] = FieldType.SINGLE_SELECT

    def _to_updated_property(
        self, current_property: SingleSelectFieldProperty
    ) -> SingleSelectFieldProperty:
        updated: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if specified(self.field_display_name):
            updated["field_display_name"] = self.field_display_name
        if specified(self.is_required):
            updated["is_required"] = self.is_required

        return SingleSelectFieldProperty.model_validate(
            current_property.model_copy(update=updated)
        )


class MultiSelectFieldPropertyUpdate(
    BaseShapeConstrainedCustomFieldPropertyUpdate[MultiSelectFieldProperty],
):
    model_config = ConfigDict(title="MultiSelectFieldPropertyUpdate")
    field_type: Literal[FieldType.MULTI_SELECT] = FieldType.MULTI_SELECT

    def _to_updated_property(
        self, current_property: MultiSelectFieldProperty
    ) -> MultiSelectFieldProperty:
        updated: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if specified(self.field_display_name):
            updated["field_display_name"] = self.field_display_name
        if specified(self.is_required):
            updated["is_required"] = self.is_required

        return MultiSelectFieldProperty.model_validate(
            current_property.model_copy(update=updated)
        )


CustomFieldTypePropertyUpdate = Annotated[
    UUIDFieldPropertyUpdate
    | NumericFieldPropertyUpdate
    | CurrencyFieldPropertyUpdate
    | PercentFieldPropertyUpdate
    | TextFieldPropertyUpdate
    | TextAreaFieldPropertyUpdate
    | LongTextAreaFieldPropertyUpdate
    | RichTextAreaFieldPropertyUpdate
    | TimestampFieldPropertyUpdate
    | LocalTimeOfDayFieldPropertyUpdate
    | TimeOfDayFieldPropertyUpdate
    | LocalDateFieldPropertyUpdate
    | BooleanCheckboxFieldPropertyUpdate
    | EmailFieldPropertyUpdate
    | PhoneFieldPropertyUpdate
    | UrlFieldPropertyUpdate
    | GeoLocationFieldPropertyUpdate
    | SingleSelectFieldPropertyUpdate
    | MultiSelectFieldPropertyUpdate,
    Field(discriminator="field_type"),
]
