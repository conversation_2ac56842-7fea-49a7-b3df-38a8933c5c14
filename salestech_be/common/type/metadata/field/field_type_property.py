import typing
from abc import ABC, abstractmethod
from datetime import date, datetime, time
from decimal import Decimal
from enum import StrEnum
from functools import lru_cache
from typing import Annotated, Any, Literal, NamedTuple, Self, TypeVar
from uuid import UUID

from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    TypeAdapter,
    ValidationError,
    computed_field,
    model_validator,
)
from pydantic.json_schema import SkipJsonSchema
from pydantic_core import Url
from typing_extensions import TypeIs

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import ErrorDetails, InvalidArgumentError
from salestech_be.common.type.metadata.common import (
    ObjectIdentifier,
)
from salestech_be.common.type.metadata.field.field_indexable_config import (
    CaseAwareNonUniqueIndexableConfig,
    CaseAwareUniqueIndexableConfig,
    IndexableConfigMixin,
    NonUniqueIndexableConfig,
    UniqueIndexableConfig,
)
from salestech_be.common.type.metadata.field.field_type import (
    CustomFieldType,
    FieldDisplayMode,
    FieldType,
    FieldTypeDescription,
    common_constraint,
    currency_constraint,
    field_type_description,
    long_text_area_constraint,
    multi_select_constraint,
    numeric_constraint,
    percent_constraint,
    rich_text_area_constraint,
    single_select_constraint,
    text_area_constraint,
    text_constraint,
)
from salestech_be.common.type.metadata.field.field_value import (
    BooleanCheckboxFieldValue,
    CurrencyFieldValue,
    DefaultEnumFieldValue,
    DictFieldValue,
    EmailFieldValue,
    FieldValue,
    GeoLocationFieldValue,
    IndexableFieldValue,
    ListFieldValue,
    LocalDateFieldValue,
    LocalTimeOfDayFieldValue,
    LongTextAreaFieldValue,
    MultiSelectFieldValue,
    NestedObjectFieldValue,
    NumericFieldValue,
    PercentFieldValue,
    PhoneNumberFieldValue,
    RichTextAreaFieldValue,
    SingleSelectFieldValue,
    SingularFieldValue,
    TextAreaFieldValue,
    TextFieldValue,
    TimeOfDayFieldValue,
    TimestampFieldValue,
    UrlFieldValue,
    UUIDFieldValue,
    is_field_value,
)
from salestech_be.common.type.metadata.value import (
    MatchOperatorName,
    NativeValueType,
)
from salestech_be.db.models.organization_external_sync import (  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    CrmProvider,
    SyncMode,
)
from salestech_be.integrations.field_origin.field_origin_types import (
    HubspotObjEnum,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.enum_util import OrderedColoredStrEnum, get_external_name
from salestech_be.util.validation import cast_or_error, not_none


@lru_cache(maxsize=1000, typed=True)
def get_decimal_type_validator(
    max_total_digits: int, max_decimal_places: int
) -> TypeAdapter[Decimal]:
    return TypeAdapter(
        Annotated[
            Decimal,
            Field(max_digits=max_total_digits, decimal_places=max_decimal_places),
        ]
    )


class NonUniqueIndexableConfigMixin(IndexableConfigMixin[NonUniqueIndexableConfig]):
    index_config: NonUniqueIndexableConfig | None = None


class CaseAwareNonUniqueIndexableConfigMixin(
    IndexableConfigMixin[CaseAwareNonUniqueIndexableConfig]
):
    index_config: CaseAwareNonUniqueIndexableConfig | None = None


class UniqueIndexableConfigMixin(IndexableConfigMixin[UniqueIndexableConfig]):
    index_config: UniqueIndexableConfig | None = None


class CaseAwareUniqueIndexableConfigMixin(
    IndexableConfigMixin[CaseAwareUniqueIndexableConfig]
):
    index_config: CaseAwareUniqueIndexableConfig | None = None


class FieldExternalProvider(BaseModel):
    """
    There is an analogous aggregated version of ExternalProvider at the
    Object level called ObjectExternalProvider.
    """

    provider: CrmProvider
    provider_obj: HubspotObjEnum | None
    provider_obj_field: str | None
    sync_mode: SyncMode | None = None


class FieldValueTypeBinding(NamedTuple):
    field_value_type: type[FieldValue]
    element_value_type: type[SingularFieldValue] | None = None


class BaseFieldTypeProperty(BaseModel, ABC):
    model_config = ConfigDict(frozen=True)
    field_type: FieldType
    is_required: Annotated[
        bool,
        Field(description="Whether user or client need to specify the field value"),
    ] = False
    is_nullable: Annotated[
        bool,
        Field(description="Whether the value of the field can be set to null or blank"),
    ] = True
    field_display_name: Annotated[
        str, Field(max_length=common_constraint.max_field_display_name_length)
    ]
    field_display_mode: Annotated[
        FieldDisplayMode, Field(description="How the field should be displayed")
    ] = FieldDisplayMode.default
    field_description: str | None = None
    is_ui_displayable: bool = False
    is_ui_editable: bool = True
    is_pinned: bool = False
    is_import_field_mappable: bool = True
    is_sortable_system_override: Annotated[
        SkipJsonSchema[bool | None],
        Field(
            description="Override into the default is_sortable value as a special case by field. "
            "This shouldn't be exposed to the client, but only here for internal overriding purpose."
        ),
    ] = None
    is_filterable_system_override: Annotated[
        SkipJsonSchema[bool | None],
        Field(
            description="Override into the default is_filterable value as a special case by field. "
            "This shouldn't be exposed to the client, but only here for internal overriding purpose."
        ),
    ] = None
    external_provider: FieldExternalProvider | None = None

    @abstractmethod
    def field_value_type(self) -> FieldValueTypeBinding:
        raise NotImplementedError

    @model_validator(mode="after")
    def validate_field_display_mode(self) -> Self:
        if (
            self.field_display_mode == FieldDisplayMode.due_date
            and self.field_type != FieldType.TIMESTAMP
        ):
            raise ValueError(
                "FieldDisplayMode.due_date can only be specified for TimestampFieldProperty"
            )
        return self

    @property
    def field_type_description(self) -> FieldTypeDescription:
        return field_type_description(self.field_type)

    @computed_field  # type: ignore[prop-decorator]
    @property
    def native_value_type(self) -> NativeValueType | None:
        return self.field_type_description.native_value_type

    @computed_field  # type: ignore[prop-decorator]
    @property
    def is_sortable(self) -> bool:
        if self.is_sortable_system_override is not None:
            return self.is_sortable_system_override
        return self.field_type_description.is_sortable

    @computed_field  # type: ignore[prop-decorator]
    @property
    def is_filterable(self) -> bool:
        if self.is_filterable_system_override is not None:
            return self.is_filterable_system_override
        return self.field_type_description.is_filterable

    @computed_field  # type: ignore[prop-decorator]
    @property
    def supported_filter_match_operators(self) -> tuple[MatchOperatorName, ...] | None:
        return self.field_type_description.supported_filter_match_operators


class BaseStandardFieldTypeProperty(BaseFieldTypeProperty, ABC):
    @abstractmethod
    def validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        raise NotImplementedError


class BaseCustomFieldTypeProperty(BaseFieldTypeProperty, ABC):
    field_type: CustomFieldType
    field_display_name: Annotated[
        str, Field(max_length=common_constraint.max_field_display_name_length)
    ]
    field_description: str | None = None

    @abstractmethod
    def _validate_custom_field_value(self, value: FieldValue) -> None:
        raise NotImplementedError

    def validate_custom_field_value(
        self, value: FieldValue | None, custom_field_id: UUID
    ) -> None:
        field_error_info = (
            f"[custom field: {self.field_display_name} (id: {custom_field_id})]"
        )
        if value is None:
            if self.is_required:
                raise InvalidArgumentError(
                    f"{field_error_info} value is "
                    f"configured as required for custom field"
                )
            else:
                return

        if (not self.is_nullable) and (not value.is_present()):
            raise InvalidArgumentError(
                f"{field_error_info} value is configured as non-null for custom field"
            )
        if (
            isinstance(self, IndexableConfigMixin)
            and value
            and (not isinstance(value, IndexableFieldValue))
        ):
            raise InvalidArgumentError(
                f"{field_error_info} must have an indexable value"
            )
        if value:
            try:
                self._validate_custom_field_value(value)
            except ValidationError as e:
                raise InvalidArgumentError(
                    f"{field_error_info} invalid value",
                    additional_error_details=ErrorDetails(
                        code=ErrorCode.CUSTOM_OBJECT_DATA_VALIDATION_ERROR,
                        details=str(e.json()),
                    ),
                ) from e
            except ValueError as e:
                raise InvalidArgumentError(
                    f"{field_error_info} invalid value",
                    additional_error_details=ErrorDetails(
                        code=ErrorCode.CUSTOM_OBJECT_DATA_VALIDATION_ERROR,
                        details=str(e),
                    ),
                ) from e


class BaseCustomOrStandardFieldTypeProperty(
    BaseStandardFieldTypeProperty, BaseCustomFieldTypeProperty, ABC
):
    def validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if is_field_value(value):
            self._validate_custom_field_value(value=value)
        elif self.is_required or (value is not None):
            self._validate_standard_field_value(value=value)

    @abstractmethod
    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        raise NotImplementedError


class BaseNumberFieldProperty(BaseCustomOrStandardFieldTypeProperty, ABC):
    total_precision: int | None = None
    decimal_precision: int | None = None

    @model_validator(mode="after")
    def validate_base_number_field_property(self) -> Self:
        if (self.total_precision is None) is not (self.decimal_precision is None):
            raise ValueError(
                "Both total_precision and decimal_precision must be set or not set"
            )
        if (
            (self.total_precision is not None)
            and (self.decimal_precision is not None)
            and (self.total_precision < self.decimal_precision)
        ):
            raise ValueError(
                f"total_precision {self.total_precision} should be greater than or "
                f"equal to decimal_precision {self.decimal_precision}"
            )
        return self

    def validate_decimal(self, decimal: Decimal | None) -> None:
        if decimal is None:
            return
        if self.total_precision is None or self.decimal_precision is None:
            return
        get_decimal_type_validator(
            max_decimal_places=self.decimal_precision,
            max_total_digits=self.total_precision,
        ).validate_python(decimal)


class BasePicklistFieldProperty(BaseCustomOrStandardFieldTypeProperty, ABC):
    select_list_id: UUID


class UUIDFieldProperty(
    BaseCustomOrStandardFieldTypeProperty, UniqueIndexableConfigMixin
):
    field_type: Literal[FieldType.UUID] = FieldType.UUID

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        cast_or_error(value, UUIDFieldValue)

    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        cast_or_error(value, UUID)

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=UUIDFieldValue)


class NumericFieldProperty(BaseNumberFieldProperty, UniqueIndexableConfigMixin):
    field_type: Literal[FieldType.NUMERIC] = FieldType.NUMERIC
    total_precision: int | None = Field(
        default=None,
        ge=numeric_constraint.min_total_precision,
        le=numeric_constraint.max_total_precision,
    )
    decimal_precision: int | None = Field(
        default=None,
        ge=numeric_constraint.min_decimal_precision,
        le=numeric_constraint.max_decimal_precision,
    )

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        numeric_value = cast_or_error(value, NumericFieldValue)
        self.validate_decimal(numeric_value.number)

    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        decimal_value = cast_or_error(value, Decimal)
        self.validate_decimal(decimal_value)

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=NumericFieldValue)


class CurrencyFieldProperty(BaseNumberFieldProperty):
    field_type: Literal[FieldType.CURRENCY] = FieldType.CURRENCY
    total_precision: int | None = Field(
        default=None,
        ge=currency_constraint.min_total_precision,
        le=currency_constraint.max_total_precision,
    )
    decimal_precision: int | None = Field(
        default=None,
        ge=currency_constraint.min_decimal_precision,
        le=currency_constraint.max_decimal_precision,
    )

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        currency_value = cast_or_error(value, CurrencyFieldValue)
        self.validate_decimal(currency_value.currency)

    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        decimal_value = cast_or_error(value, Decimal)
        self.validate_decimal(decimal_value)

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=CurrencyFieldValue)


class PercentFieldProperty(BaseNumberFieldProperty):
    field_type: Literal[FieldType.PERCENT] = FieldType.PERCENT
    total_precision: int | None = Field(
        default=None,
        ge=percent_constraint.min_total_precision,
        le=percent_constraint.max_total_precision,
    )
    decimal_precision: int | None = Field(
        default=None,
        ge=percent_constraint.min_decimal_precision,
        le=percent_constraint.max_decimal_precision,
    )

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        percent_value = cast_or_error(value, PercentFieldValue)
        self.validate_decimal(percent_value.percent)

    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        decimal_value = cast_or_error(value, Decimal)
        self.validate_decimal(decimal_value)

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=PercentFieldValue)


class TextFieldProperty(
    BaseCustomOrStandardFieldTypeProperty, CaseAwareUniqueIndexableConfigMixin
):
    field_type: Literal[FieldType.TEXT] = FieldType.TEXT
    max_length: Annotated[
        int,
        Field(
            ge=1,
            le=float(text_constraint.max_str_length),
        ),
    ] = text_constraint.max_str_length

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        text_field_value = cast_or_error(value, TextFieldValue)
        if (length := len(text_field_value.text or "")) > self.max_length:
            raise InvalidArgumentError(
                f"Text length {length} exceeds max length {self.max_length}"
            )

    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        text_value = cast_or_error(value, str)
        if (length := len(text_value)) > self.max_length:
            raise InvalidArgumentError(
                f"Text length {length} exceeds max length {self.max_length}"
            )

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=TextFieldValue)


class TextAreaFieldProperty(BaseCustomOrStandardFieldTypeProperty):
    field_type: Literal[FieldType.TEXT_AREA] = FieldType.TEXT_AREA
    max_length: Annotated[
        int, Field(ge=1, le=float(text_area_constraint.max_str_length))
    ] = text_area_constraint.max_str_length

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        text_area_field_value = cast_or_error(value, TextAreaFieldValue)
        if (length := len(text_area_field_value.text_area or "")) > self.max_length:
            raise InvalidArgumentError(
                f"Text length {length} exceeds max length {self.max_length}"
            )

    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        text_value = cast_or_error(value, str)
        if (length := len(text_value)) > self.max_length:
            raise InvalidArgumentError(
                f"Text length {length} exceeds max length {self.max_length}"
            )

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=TextAreaFieldValue)


class LongTextAreaFieldProperty(BaseCustomOrStandardFieldTypeProperty):
    field_type: Literal[FieldType.LONG_TEXT_AREA] = FieldType.LONG_TEXT_AREA
    max_length: Annotated[
        int,
        Field(
            ge=1,
            le=float(long_text_area_constraint.max_str_length),
        ),
    ] = long_text_area_constraint.max_str_length
    visible_lines: Annotated[
        int,
        Field(
            ge=1,
            le=long_text_area_constraint.max_visible_lines,
        ),
    ] = long_text_area_constraint.max_visible_lines

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        long_text_area_field_value = cast_or_error(value, LongTextAreaFieldValue)
        if (
            length := len(long_text_area_field_value.long_text_area or "")
        ) > self.max_length:
            raise InvalidArgumentError(
                f"Text length {length} exceeds max length {self.max_length}"
            )

    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        text_value = cast_or_error(value, str)
        if (length := len(text_value)) > self.max_length:
            raise InvalidArgumentError(
                f"Text length {length} exceeds max length {self.max_length}"
            )

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=LongTextAreaFieldValue)


class RichTextAreaFieldProperty(BaseCustomOrStandardFieldTypeProperty):
    field_type: Literal[FieldType.RICH_TEXT_AREA] = FieldType.RICH_TEXT_AREA
    max_length: Annotated[
        int, Field(ge=1, le=float(rich_text_area_constraint.max_str_length))
    ] = rich_text_area_constraint.max_str_length
    visible_lines: Annotated[
        int, Field(ge=1, le=rich_text_area_constraint.max_visible_lines)
    ] = rich_text_area_constraint.max_visible_lines

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        rich_text_area_value = cast_or_error(value, RichTextAreaFieldValue)
        if (length := len(rich_text_area_value.rich_text_area or "")) > self.max_length:
            raise InvalidArgumentError(
                f"Text length {length} exceeds max length {self.max_length}"
            )

    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        text_value = cast_or_error(value, str)
        if (length := len(text_value)) > self.max_length:
            raise InvalidArgumentError(
                f"Text length {length} exceeds max length {self.max_length}"
            )

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=RichTextAreaFieldValue)


class TimestampFieldProperty(
    BaseCustomOrStandardFieldTypeProperty, NonUniqueIndexableConfigMixin
):
    field_type: Literal[FieldType.TIMESTAMP] = FieldType.TIMESTAMP

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        cast_or_error(value, TimestampFieldValue)

    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        datetime_value = cast_or_error(value, datetime)
        if not datetime_value.tzinfo:
            raise InvalidArgumentError("Timestamp must have a timezone")

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=TimestampFieldValue)


class LocalTimeOfDayFieldProperty(
    BaseCustomOrStandardFieldTypeProperty, NonUniqueIndexableConfigMixin
):
    field_type: Literal[FieldType.LOCAL_TIME_OF_DAY] = FieldType.LOCAL_TIME_OF_DAY

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        cast_or_error(value, LocalTimeOfDayFieldValue)

    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        time_value = cast_or_error(value, time)
        if time_value.tzinfo:
            raise InvalidArgumentError("LocalTimeOfDay must not have a timezone")

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=LocalTimeOfDayFieldValue)


class TimeOfDayFieldProperty(
    BaseCustomOrStandardFieldTypeProperty, NonUniqueIndexableConfigMixin
):
    field_type: Literal[FieldType.TIME_OF_DAY] = FieldType.TIME_OF_DAY

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        cast_or_error(value, TimeOfDayFieldValue)

    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        time_value = cast_or_error(value, time)
        if not time_value.tzinfo:
            raise InvalidArgumentError("TimeOfDay must have a timezone")

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=TimeOfDayFieldValue)


class LocalDateFieldProperty(
    BaseCustomOrStandardFieldTypeProperty, NonUniqueIndexableConfigMixin
):
    field_type: Literal[FieldType.LOCAL_DATE] = FieldType.LOCAL_DATE

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        cast_or_error(value, LocalDateFieldValue)

    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        cast_or_error(value, date)

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=LocalDateFieldValue)


class BooleanCheckboxFieldProperty(
    BaseCustomOrStandardFieldTypeProperty, NonUniqueIndexableConfigMixin
):
    field_type: Literal[FieldType.BOOLEAN_CHECKBOX] = FieldType.BOOLEAN_CHECKBOX
    default_is_checked: bool = False

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        cast_or_error(value, BooleanCheckboxFieldValue)

    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        cast_or_error(value, bool)

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=BooleanCheckboxFieldValue)


class SingleSelectFieldProperty(
    BasePicklistFieldProperty, NonUniqueIndexableConfigMixin
):
    field_type: Literal[FieldType.SINGLE_SELECT] = FieldType.SINGLE_SELECT

    def max_value_def_allowed(self) -> int:
        return single_select_constraint.max_number_of_enum_values

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        pass

    # todo(xw): add actual picklist validations on standard fields
    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        pass

    @property
    def is_sortable(self) -> bool:
        return True

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=SingleSelectFieldValue)


class MultiSelectFieldProperty(BasePicklistFieldProperty):
    field_type: Literal[FieldType.MULTI_SELECT] = FieldType.MULTI_SELECT

    def max_value_def_allowed(self) -> int:
        return multi_select_constraint.max_number_of_enum_values

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        pass

    # todo(xw): add actual picklist validations on standard fields
    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        pass

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=MultiSelectFieldValue)


class EmailFieldProperty(
    BaseCustomOrStandardFieldTypeProperty, UniqueIndexableConfigMixin
):
    field_type: Literal[FieldType.EMAIL] = FieldType.EMAIL

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        cast_or_error(value, EmailFieldValue)

    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        email_str = cast_or_error(value, str)
        EmailFieldValue(email=email_str)

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=EmailFieldValue)


class PhoneFieldProperty(
    BaseCustomOrStandardFieldTypeProperty, NonUniqueIndexableConfigMixin
):
    field_type: Literal[FieldType.PHONE_NUMBER] = FieldType.PHONE_NUMBER

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        cast_or_error(value, PhoneNumberFieldValue)

    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        phone_str = cast_or_error(value, str)
        PhoneNumberFieldValue(phone_number=phone_str)

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=PhoneNumberFieldValue)


class UrlFieldProperty(
    BaseCustomOrStandardFieldTypeProperty, NonUniqueIndexableConfigMixin
):
    field_type: Literal[FieldType.URL] = FieldType.URL

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        cast_or_error(value, UrlFieldValue)

    def _validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if isinstance(value, str | Url):
            UrlFieldValue(value=value)  # type: ignore[arg-type]
        else:
            raise InvalidArgumentError(f"Invalid URL value {value}")

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=UrlFieldValue)


class GeoLocationFieldProperty(BaseCustomFieldTypeProperty):
    field_type: Literal[FieldType.GEO_LOCATION] = FieldType.GEO_LOCATION

    def _validate_custom_field_value(self, value: FieldValue) -> None:
        cast_or_error(value, GeoLocationFieldValue)

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=GeoLocationFieldValue)


class DefaultEnumFieldProperty(BaseStandardFieldTypeProperty):
    field_type: Literal[FieldType.DEFAULT_ENUM] = FieldType.DEFAULT_ENUM
    enum_class: Annotated[SkipJsonSchema[type[StrEnum] | None], Field(exclude=True)] = (
        None
    )
    allowed_values: list[str] = Field(default_factory=list)
    value_to_display_name: dict[str, str] = Field(default_factory=dict)
    value_to_color: dict[str, str] = Field(default_factory=dict)

    @model_validator(mode="after")
    def validate_default_enum_field_property(self) -> Self:
        has_value_defined = bool(self.allowed_values) and bool(
            self.value_to_display_name
        )
        has_enum_class = bool(self.enum_class)
        if not (has_enum_class or has_value_defined):
            raise ValueError(
                "Either enum_class or allowed_values and value_to_display_name should be defined."
            )
        if has_enum_class:
            _enum_class = not_none(self.enum_class)
            if not isinstance(_enum_class, type) or not issubclass(
                _enum_class, StrEnum
            ):
                raise ValueError(
                    f"Enum class {self.enum_class} should be a subclass of StrEnum"
                )
            self.allowed_values.clear()
            self.allowed_values.extend(sorted(_enum_class))
            self.value_to_display_name.clear()
            self.value_to_display_name.update(
                {member: get_external_name(member.value) for member in _enum_class}
            )
            if issubclass(_enum_class, OrderedColoredStrEnum):
                self.value_to_color.clear()
                self.value_to_color.update(
                    {member: member.color for member in _enum_class}
                )
        elif diff := set(self.allowed_values).difference(
            self.value_to_display_name.keys()
        ):
            raise ValueError(
                f"allowed_values and value_to_display_name have different keys: {diff}"
            )
        return self

    def validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if value is None and self.is_nullable:
            return
        if value not in self.allowed_values:
            raise InvalidArgumentError(
                f"Value {value} is not in enum {self.allowed_values}"
            )

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=DefaultEnumFieldValue)


class NestedObjectFieldProperty(BaseStandardFieldTypeProperty):
    field_type: Literal[FieldType.NESTED_OBJECT] = FieldType.NESTED_OBJECT
    object_identifier: ObjectIdentifier

    def validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return

    def field_value_type(self) -> FieldValueTypeBinding:
        return FieldValueTypeBinding(field_value_type=NestedObjectFieldValue)


class ListFieldProperty(BaseStandardFieldTypeProperty):
    field_type: Literal[FieldType.LIST] = FieldType.LIST
    element_field_type_property: "StandardSingularFieldTypeProperty"

    def validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        pass

    def field_value_type(self) -> FieldValueTypeBinding:
        element_binding = self.element_field_type_property.field_value_type()
        return FieldValueTypeBinding(
            field_value_type=ListFieldValue,
            element_value_type=element_binding.element_value_type,
        )


class DictFieldProperty(BaseStandardFieldTypeProperty):
    field_type: Literal[FieldType.DICT] = FieldType.DICT
    key_field_type: Literal[
        NativeValueType.STRING, NativeValueType.UUID, NativeValueType.DECIMAL
    ] = NativeValueType.STRING
    value_field_type_property: "StandardSingularFieldTypeProperty"

    def validate_standard_field_value(self, value: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        pass

    def field_value_type(self) -> FieldValueTypeBinding:
        value_binding = self.value_field_type_property.field_value_type()
        return FieldValueTypeBinding(
            field_value_type=DictFieldValue,
            element_value_type=value_binding.element_value_type,
        )


FieldTypeProperty = Annotated[
    UUIDFieldProperty
    | NumericFieldProperty
    | CurrencyFieldProperty
    | PercentFieldProperty
    | TextFieldProperty
    | TextAreaFieldProperty
    | LongTextAreaFieldProperty
    | RichTextAreaFieldProperty
    | TimestampFieldProperty
    | LocalTimeOfDayFieldProperty
    | TimeOfDayFieldProperty
    | LocalDateFieldProperty
    | BooleanCheckboxFieldProperty
    | SingleSelectFieldProperty
    | MultiSelectFieldProperty
    | EmailFieldProperty
    | PhoneFieldProperty
    | UrlFieldProperty
    | GeoLocationFieldProperty
    | DefaultEnumFieldProperty
    | NestedObjectFieldProperty
    | ListFieldProperty
    | DictFieldProperty,
    Field(discriminator="field_type"),
]

field_property_types: tuple[type[FieldTypeProperty], ...] = tuple(
    typing.cast(type[FieldTypeProperty], ftp)
    for ftp in typing.get_args(typing.get_args(FieldTypeProperty)[0])
    if issubclass(ftp, BaseFieldTypeProperty)
)


def is_field_property(val: Any) -> TypeIs[FieldTypeProperty]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    return isinstance(val, field_property_types)


StandardFieldTypeProperty = Annotated[
    UUIDFieldProperty
    | NumericFieldProperty
    | CurrencyFieldProperty
    | PercentFieldProperty
    | TextFieldProperty
    | TextAreaFieldProperty
    | LongTextAreaFieldProperty
    | RichTextAreaFieldProperty
    | TimestampFieldProperty
    | LocalTimeOfDayFieldProperty
    | TimeOfDayFieldProperty
    | LocalDateFieldProperty
    | BooleanCheckboxFieldProperty
    | SingleSelectFieldProperty
    | MultiSelectFieldProperty
    | EmailFieldProperty
    | PhoneFieldProperty
    | UrlFieldProperty
    | DefaultEnumFieldProperty
    | NestedObjectFieldProperty
    | ListFieldProperty
    | DictFieldProperty,
    Field(discriminator="field_type"),
]

standard_field_property_types: tuple[type[StandardFieldTypeProperty], ...] = tuple(
    typing.cast(type[StandardFieldTypeProperty], ftp)
    for ftp in typing.get_args(typing.get_args(StandardFieldTypeProperty)[0])
    if issubclass(ftp, BaseStandardFieldTypeProperty)
)

StandardSingularFieldTypeProperty = Annotated[
    UUIDFieldProperty
    | NumericFieldProperty
    | CurrencyFieldProperty
    | PercentFieldProperty
    | TextFieldProperty
    | TextAreaFieldProperty
    | LongTextAreaFieldProperty
    | RichTextAreaFieldProperty
    | TimestampFieldProperty
    | LocalTimeOfDayFieldProperty
    | TimeOfDayFieldProperty
    | LocalDateFieldProperty
    | BooleanCheckboxFieldProperty
    | SingleSelectFieldProperty
    | MultiSelectFieldProperty
    | EmailFieldProperty
    | PhoneFieldProperty
    | UrlFieldProperty
    | DefaultEnumFieldProperty
    | NestedObjectFieldProperty,
    Field(discriminator="field_type"),
]


def is_standard_field_property(val: Any) -> TypeIs[StandardFieldTypeProperty]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    return isinstance(val, standard_field_property_types)


CustomFieldTypeProperty = Annotated[
    UUIDFieldProperty
    | NumericFieldProperty
    | CurrencyFieldProperty
    | PercentFieldProperty
    | TextFieldProperty
    | TextAreaFieldProperty
    | LongTextAreaFieldProperty
    | RichTextAreaFieldProperty
    | TimestampFieldProperty
    | LocalTimeOfDayFieldProperty
    | TimeOfDayFieldProperty
    | LocalDateFieldProperty
    | BooleanCheckboxFieldProperty
    | SingleSelectFieldProperty
    | MultiSelectFieldProperty
    | EmailFieldProperty
    | PhoneFieldProperty
    | UrlFieldProperty
    | GeoLocationFieldProperty,
    Field(discriminator="field_type"),
]

custom_field_property_types: tuple[type[CustomFieldTypeProperty], ...] = tuple(
    typing.cast(type[CustomFieldTypeProperty], ftp)
    for ftp in typing.get_args(typing.get_args(CustomFieldTypeProperty)[0])
    if issubclass(ftp, BaseCustomFieldTypeProperty)
)


def is_custom_field_property(val: Any) -> TypeIs[CustomFieldTypeProperty]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    return isinstance(val, custom_field_property_types)


CustomFieldTypePropertyT = TypeVar(
    "CustomFieldTypePropertyT",
    UUIDFieldProperty,
    NumericFieldProperty,
    CurrencyFieldProperty,
    PercentFieldProperty,
    TextFieldProperty,
    TextAreaFieldProperty,
    LongTextAreaFieldProperty,
    RichTextAreaFieldProperty,
    TimestampFieldProperty,
    LocalTimeOfDayFieldProperty,
    TimeOfDayFieldProperty,
    LocalDateFieldProperty,
    BooleanCheckboxFieldProperty,
    SingleSelectFieldProperty,
    MultiSelectFieldProperty,
    EmailFieldProperty,
    PhoneFieldProperty,
    UrlFieldProperty,
    GeoLocationFieldProperty,
)

ListFieldProperty.model_rebuild()
DictFieldProperty.model_rebuild()
