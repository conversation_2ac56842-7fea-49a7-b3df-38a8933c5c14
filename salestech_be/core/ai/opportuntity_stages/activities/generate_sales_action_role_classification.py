from dataclasses import dataclass
from uuid import UUID

from temporalio import activity

from salestech_be.common.core_crm.sales_action import (
    SalesActionActivityType,
    StandardSalesActionRequirement,
    StandardSalesActionType,
)
from salestech_be.core.ai.opportuntity_stages.llm_calls.contact_pipeline_role_classification_with_llm import (
    ContactPipelineRoleClassificationRequest,
    LLMContactPipelineRoleClassificationResult,
    contact_pipeline_role_classification_with_llm,
)
from salestech_be.core.ai.opportuntity_stages.llm_calls.sales_action_classification_with_llm import (
    LLMSalesActionClassificationResult,
    SalesActionClassificationRequest,
    sales_action_classification_with_llm,
)
from salestech_be.core.crm_ai_rec.crm_ai_rec_service import get_crm_ai_rec_service
from salestech_be.core.email.global_email.global_message_ai_rec_service import (
    GlobalMessageAIRecService,
    get_global_message_ai_rec_service,
)
from salestech_be.core.meeting.meeting_ai_rec_service import (
    MeetingAIRecService,
    get_meeting_ai_rec_service,
)
from salestech_be.core.meeting.service.meeting_query_service import (
    get_meeting_query_service,
)
from salestech_be.core.opportunity_stage_criteria.criteria_types import (
    CriteriaExtractionSourceObjectId,
    CriteriaExtractionSourceObjectType,
    SourceObject,
)
from salestech_be.core.pipeline.service.contact_pipeline_role_ai_rec_service import (
    get_contact_pipeline_role_ai_rec_service,
)
from salestech_be.core.pipeline.service.pipeline_service import get_pipeline_service
from salestech_be.core.stage_criteria.constants import (
    get_standard_sales_action_requirements_by_activity_type,
)
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.crm_ai_rec import ParentRecordIds
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore
)

logger = get_logger(__name__)


@dataclass
class SalesActionRoleClassificationContext:
    current_sales_action_types: list[StandardSalesActionType] | None
    sales_action_requirements: list[StandardSalesActionRequirement] | None

    record_id: UUID


async def fetch_meeting_context(
    db_engine: DatabaseEngine,
    organization_id: UUID,
    source_object: CriteriaExtractionSourceObjectId,
) -> SalesActionRoleClassificationContext:
    meeting_query_service = get_meeting_query_service(db_engine=db_engine)
    meeting = await meeting_query_service.get_meeting_v2(
        meeting_id=source_object.object_id,
        organization_id=organization_id,
    )
    if meeting is None:
        raise ValueError("Meeting not found")
    return SalesActionRoleClassificationContext(
        current_sales_action_types=meeting.sales_action_types,
        sales_action_requirements=get_standard_sales_action_requirements_by_activity_type(
            SalesActionActivityType.MEETING
        ),
        record_id=source_object.object_id,
    )


async def fetch_email_context(
    db_engine: DatabaseEngine,
    organization_id: UUID,
    source_object: CriteriaExtractionSourceObjectId,
) -> SalesActionRoleClassificationContext:
    thread_repository = ThreadRepository(engine=db_engine)
    global_messages = (
        await thread_repository.find_global_messages_by_thread_id_order_by_received_at(
            global_thread_id=source_object.object_id,
            organization_id=organization_id,
        )
    )
    if len(global_messages) == 0:
        raise ValueError("No global messages found")
    global_message = global_messages[0]
    return SalesActionRoleClassificationContext(
        current_sales_action_types=global_message.sales_action_types,
        sales_action_requirements=get_standard_sales_action_requirements_by_activity_type(
            SalesActionActivityType.EMAIL
        ),
        record_id=source_object.object_id,
    )


@activity.defn
async def generate_sales_action_classification(
    organization_id: UUID,
    pipeline_id: UUID,
    source_object: CriteriaExtractionSourceObjectId,
) -> LLMSalesActionClassificationResult | None:
    db_engine = await get_or_init_db_engine()
    # Fetch context
    ai_rec_service: MeetingAIRecService | GlobalMessageAIRecService
    if source_object.object_type == CriteriaExtractionSourceObjectType.MEETING:
        context = await fetch_meeting_context(db_engine, organization_id, source_object)
        ai_rec_service = get_meeting_ai_rec_service(db_engine=db_engine)
    elif source_object.object_type == CriteriaExtractionSourceObjectType.EMAIL:
        context = await fetch_email_context(db_engine, organization_id, source_object)
        ai_rec_service = get_global_message_ai_rec_service(db_engine=db_engine)
    else:
        raise ValueError("Invalid source object type")

    # Build the LLM request
    request = SalesActionClassificationRequest(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        source_object=source_object,
        current_sales_action_types=context.current_sales_action_types,
        sales_action_requirements=context.sales_action_requirements,
    )
    result = await sales_action_classification_with_llm(request)

    # Patch sales actions if needed
    sales_action_patch_request = result.patch_sales_actions_request
    if sales_action_patch_request:
        await ai_rec_service.create_property_ai_recs_from_patch_request(
            organization_id=organization_id,
            user_id=UUID(settings.intel_hardcoded_user_id),
            record_id=context.record_id,
            patch_request=sales_action_patch_request.request,  # type: ignore
            citations=sales_action_patch_request.citations,
            ai_rec_types=sales_action_patch_request.field_update_types,  # type: ignore
        )

    return result


@activity.defn
async def generate_contact_pipeline_role_classification(
    organization_id: UUID,
    pipeline_id: UUID,
    source_object: SourceObject,
) -> LLMContactPipelineRoleClassificationResult | None:
    db_engine = await get_or_init_db_engine()
    pipeline_service = get_pipeline_service(db_engine=db_engine)
    contact_pipeline_role_ai_rec_service = get_contact_pipeline_role_ai_rec_service(
        db_engine=db_engine
    )
    crm_ai_rec_service = get_crm_ai_rec_service(db_engine=db_engine)
    contact_pipeline_roles = (
        await pipeline_service.list_contact_pipeline_role_by_pipeline_id(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
        )
    )

    contact_pipeline_role_by_contact_id = {
        role.contact_id: role for role in contact_pipeline_roles
    }

    request = ContactPipelineRoleClassificationRequest(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        source_object=source_object,
        current_contact_pipeline_roles=contact_pipeline_roles,
    )
    result = await contact_pipeline_role_classification_with_llm(request)

    # Patch contact pipeline roles if needed
    for patch_request in result.patch_contact_pipeline_role_requests or []:
        contact_pipeline_role = contact_pipeline_role_by_contact_id.get(
            patch_request.request.contact_id, None
        )
        if not contact_pipeline_role:
            logger.warning(
                "Contact pipeline role not found for contact",
                contact_id=patch_request.request.contact_id,
            )
            continue
        await contact_pipeline_role_ai_rec_service.create_property_ai_recs_from_patch_request(
            organization_id=organization_id,
            user_id=UUID(settings.intel_hardcoded_user_id),
            record_id=contact_pipeline_role.id,
            patch_request=patch_request.request,
            citations=patch_request.citations,
            ai_rec_types=patch_request.field_update_types,  # type: ignore
        )

    # Create contact pipeline roles if needed
    for creation_request in result.create_contact_pipeline_role_requests or []:
        await contact_pipeline_role_ai_rec_service.create_object_ai_rec_from_create_request(
            organization_id=organization_id,
            user_id=UUID(settings.intel_hardcoded_user_id),
            create_request=creation_request.request,
            parent_record_ids=ParentRecordIds(pipeline_id=pipeline_id),
            citation_ids=[citation.id for citation in creation_request.citations]
            if creation_request.citations
            else None,
        )

    if result.delete_pending_recs:
        await crm_ai_rec_service.delete_crm_object_creation_recs(
            organization_id=organization_id,
            user_id=UUID(settings.intel_hardcoded_user_id),
            ai_rec_ids=set(result.delete_pending_recs),
        )

    return result
