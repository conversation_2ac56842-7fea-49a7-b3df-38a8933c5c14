import asyncio
import uuid
from datetime import datetime

import pytz

from salestech_be.core.ai.common.helpers.get_intel_context import (
    get_contacts_by_contact_ids,
)
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger()


async def run_test_get_contacts_by_contact_ids(
    global_thread_id: uuid.UUID,
    organization_id: uuid.UUID,
) -> list[ContactV2] | None:
    engine = await get_or_init_db_engine()
    thread_repository = ThreadRepository(engine=engine)
    messages = await thread_repository.list_messages_by_global_thread_id(
        global_thread_id=global_thread_id,
        organization_id=organization_id,
    )

    messages = sorted(
        messages,
        key=lambda x: x.send_at.replace(tzinfo=pytz.UTC)
        if x.send_at
        else datetime.min.replace(tzinfo=pytz.UTC),
        reverse=True,
    )

    # Get latest message from global thread
    latest_message = messages[0] if len(messages) > 0 else None

    if latest_message is None:
        return None

    contacts = await get_contacts_by_contact_ids(
        contact_ids=latest_message.unique_contact_ids,
        organization_id=organization_id,
    )

    logger.info(f"Contacts: {contacts}")

    return contacts


if __name__ == "__main__":
    asyncio.run(
        run_test_get_contacts_by_contact_ids(
            global_thread_id=uuid.UUID("f80bba58-a104-44d0-9abd-3837173c91f1"),
            organization_id=uuid.UUID("01718a4b-afb3-4f5a-bd5d-aefc43f1cdf0"),
        )
    )
