from typing import Annotated, override
from uuid import UUID

from fastapi import Depends, Request

from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.core.common.domain_service import DomainQueryService
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.prospecting.type.query import ProspectingSavedSearchQueryV2
from salestech_be.db.dao.search_query_repository import SearchQueryRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.search_query import ProspectingSavedSearchQueryPermission


class ProspectingSavedSearchQueryQueryService(
    DomainQueryService[ProspectingSavedSearchQueryV2]
):
    def __init__(
        self,
        prospecting_search_query_repository: Annotated[
            SearchQueryRepository, Depends()
        ],
    ) -> None:
        self.prospecting_search_query_repository = prospecting_search_query_repository

    async def list_prospecting_saved_queries(
        self,
        organization_id: UUID,
        only_include_prospecting_saved_query_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> list[ProspectingSavedSearchQueryV2]:
        db_search_queries = await self.prospecting_search_query_repository.list_by_organization_id(
            organization_id=organization_id,
            only_include_prospecting_saved_query_ids=only_include_prospecting_saved_query_ids,
        )
        return [
            ProspectingSavedSearchQueryV2.map_from_db(db_search_query)
            for db_search_query in db_search_queries
        ]

    @override
    async def is_entity_viewable_by_user(
        self,
        user_auth_context: UserAuthContext,
        domain_object: ProspectingSavedSearchQueryV2,
    ) -> bool:
        """
        Returns a bool indicating if the provided user can view the domain object.
        """
        if user_auth_context.is_admin:
            return True
        if user_auth_context.user_id == domain_object.user_id:
            return True
        if (
            domain_object.permission
            == ProspectingSavedSearchQueryPermission.ORGANIZATION_SHARED
        ):
            return True
        return False

    @override
    async def filter_viewable_records(
        self,
        records: list[ProspectingSavedSearchQueryV2],
        user_auth_context: UserAuthContext,
    ) -> list[ProspectingSavedSearchQueryV2]:
        filtered_records: list[ProspectingSavedSearchQueryV2] = []
        for record in records:
            if await self.is_entity_viewable_by_user(
                user_auth_context=user_auth_context,
                domain_object=record,
            ):
                filtered_records.append(record)
        return filtered_records


def get_prospecting_saved_search_query_query_service_by_db(
    db_engine: DatabaseEngine,
) -> ProspectingSavedSearchQueryQueryService:
    return ProspectingSavedSearchQueryQueryService(
        prospecting_search_query_repository=SearchQueryRepository(engine=db_engine),
    )


def get_prospecting_saved_search_query_query_service(
    request: Request,
) -> ProspectingSavedSearchQueryQueryService:
    db_engine = get_db_engine(request)
    return get_prospecting_saved_search_query_query_service_by_db(db_engine=db_engine)
