from typing import Annotated
from uuid import UUID

from salestech_be.common.query_util.filter_schema import FilterSpec
from salestech_be.common.schema_manager.std_object_field_identifier import (
    ProspectingSavedSearchQueryField,
    StdObjectIdentifiers,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    DefaultEnumFieldProperty,
    TextFieldProperty,
    TimestampFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.common.type.metadata.schema import SkipDescriptor
from salestech_be.core.common.types import DomainModel, FieldMetadata
from salestech_be.db.models.search_query import (
    ProspectingSavedSearchQueryPermission,
    ProspectingSearchQueryType,
    SearchQuery,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class ProspectingSavedSearchQueryV2(DomainModel):
    object_id = StdObjectIdentifiers.prospecting_saved_search_query.identifier
    field_name_provider = ProspectingSavedSearchQueryField
    object_display_name = "Prospecting Saved Search Query"

    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="ID",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    name: Annotated[
        str,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Name",
                is_ui_displayable=True,
                is_ui_editable=True,
            ),
        ),
    ]

    type: Annotated[
        ProspectingSearchQueryType,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=ProspectingSearchQueryType,
                field_display_name="Prospecting Search Query Type",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    filter_spec: Annotated[FilterSpec, SkipDescriptor()]

    permission: Annotated[
        ProspectingSavedSearchQueryPermission,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=ProspectingSavedSearchQueryPermission,
                field_display_name="Prospecting Search Query Permission",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    user_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    organization_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    created_at: Annotated[
        ZoneRequiredDateTime,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Created At",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    updated_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Updated At",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    deleted_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Deleted At",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    @classmethod
    def map_from_db(
        cls, db_search_query: SearchQuery
    ) -> "ProspectingSavedSearchQueryV2":
        return cls(
            id=db_search_query.id,
            name=db_search_query.name,
            type=db_search_query.type,
            filter_spec=db_search_query.filter_spec,
            permission=db_search_query.permission,
            user_id=db_search_query.user_id,
            organization_id=db_search_query.organization_id,
            created_at=db_search_query.created_at,
            updated_at=db_search_query.updated_at,
            deleted_at=db_search_query.deleted_at,
        )
