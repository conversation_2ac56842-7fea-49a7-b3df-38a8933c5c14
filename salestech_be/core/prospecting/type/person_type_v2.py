from typing import Annotated
from uuid import UUID

from pydantic import BaseModel, EmailStr

from salestech_be.common.schema_manager.std_object_field_identifier import (
    ContactField,
    ProspectingCompanyField,
    ProspectingPersonField,
    ProspectingPhoneNumberDetailField,
    StdObjectIdentifiers,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    ProspectingPersonRelationship,
)
from salestech_be.common.type.metadata.field.field_indexable_config import (
    UniqueIndexableConfig,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    BooleanCheckboxFieldProperty,
    DefaultEnumFieldProperty,
    ListFieldProperty,
    NestedObjectFieldProperty,
    TextFieldProperty,
    TimestampFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.common.type.metadata.schema import OutboundRelationship
from salestech_be.core.common.types import DomainModel, FieldMetadata
from salestech_be.db.dto.person_dto import PersonDto
from salestech_be.db.models.person import ProspectingEnrichStatus
from salestech_be.util.pydantic_types.str import PhoneNumberWithExtension
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class ProspectingPhoneNumberDetail(DomainModel):
    object_id = StdObjectIdentifiers.prospecting_phone_number_detail.identifier
    object_display_name = "Prospecting PhoneNumber Detail"
    field_name_provider = ProspectingPhoneNumberDetailField

    number: Annotated[
        PhoneNumberWithExtension,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Phone Number",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]
    type: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Phone Number Type",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    status: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Phone Number Status",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None


class ProspectingPerson(DomainModel):
    object_id = StdObjectIdentifiers.person.identifier
    object_display_name = "Prospecting Person"
    field_name_provider = ProspectingPersonField

    outbound_relationships = (
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=ProspectingPersonRelationship.prospecting_person__to__prospecting_company,
            relationship_name="Associated Prospecting Company",
            self_object_identifier=StdObjectIdentifiers.person.identifier,
            related_object_identifier=StdObjectIdentifiers.company.identifier,
            self_cardinality=OutboundRelationship.Cardinality.ONE,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                ProspectingPersonField.company_id.identifier,
            ),
            ordered_related_field_identifiers=(ProspectingCompanyField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=ProspectingPersonRelationship.prospecting_person__to__contact,
            relationship_name="Associated Prospecting Contact",
            self_object_identifier=StdObjectIdentifiers.person.identifier,
            related_object_identifier=StdObjectIdentifiers.contact.identifier,
            self_cardinality=OutboundRelationship.Cardinality.ONE,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                ProspectingPersonField.contact_id.identifier,
            ),
            ordered_related_field_identifiers=(ContactField.id.identifier,),
        ),
    )

    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="ID",
                is_required=True,
                is_ui_displayable=False,
                is_ui_editable=False,
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=True),
            )
        ),
    ]

    first_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="First Name",
                is_ui_displayable=True,
                is_ui_editable=False,
            )
        ),
    ] = None

    last_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Last Name",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    full_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Full Name",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    job_title: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Job Title",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    current_company: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Current Company",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    company_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Current Company ID",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    company_website_url: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Company Website URL",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    state: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="State",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    country: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Country",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    location: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Location",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    linkedin_url: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="LinkedIn Url",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    work_email: Annotated[
        EmailStr | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Work Email",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    phone_numbers: Annotated[
        list[ProspectingPhoneNumberDetail] | None,
        FieldMetadata(
            type_property=ListFieldProperty(
                field_display_name="Prospecting Phone Number Details",
                element_field_type_property=NestedObjectFieldProperty(
                    field_display_name="Prospecting Phone Number Detail",
                    object_identifier=ProspectingPhoneNumberDetail.object_id,
                ),
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    work_email_enrich_status: Annotated[
        ProspectingEnrichStatus,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=ProspectingEnrichStatus,
                field_display_name="Work Email Enrich Status",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    phone_number_enrich_status: Annotated[
        ProspectingEnrichStatus,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=ProspectingEnrichStatus,
                field_display_name="Phone Number Enrich Status",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    last_enriched_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Last Enriched At",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    has_email: Annotated[
        bool | None,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Has Email",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    has_phone_numbers: Annotated[
        bool | None,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Has Phone Numbers",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    contact_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Contact ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            )
        ),
    ] = None

    @classmethod
    def map_from_person_dto(
        cls,
        person_dto: PersonDto,
        contact_id: UUID | None = None,
    ) -> "ProspectingPerson":
        db_person = person_dto.db_person
        return ProspectingPerson(
            id=db_person.id,
            first_name=db_person.first_name,
            last_name=db_person.last_name,
            full_name=db_person.full_name,
            job_title=db_person.job_title,
            current_company=db_person.current_company,
            company_id=db_person.company_id,
            company_website_url=db_person.current_company_website_url,
            state=db_person.state,
            country=db_person.country,
            location=db_person.location,
            linkedin_url=db_person.linkedin_url,
            work_email=db_person.work_email,
            phone_numbers=[
                ProspectingPhoneNumberDetail(
                    number=phone_number.number,
                    type=phone_number.type,
                    status=phone_number.status,
                )
                for phone_number in db_person.phone_numbers
            ]
            if db_person.phone_number_enrich_status == ProspectingEnrichStatus.ENRICHED
            and db_person.phone_numbers
            else None,
            work_email_enrich_status=db_person.work_email_enrich_status
            if db_person.work_email_enrich_status
            else ProspectingEnrichStatus.NOT_REQUESTED,
            phone_number_enrich_status=db_person.phone_number_enrich_status
            if db_person.phone_number_enrich_status
            else ProspectingEnrichStatus.NOT_REQUESTED,
            last_enriched_at=db_person.last_enriched_at,
            has_email=db_person.has_email,
            has_phone_numbers=db_person.has_phone_numbers,
            contact_id=contact_id,
        )


class BulkEnrichPersonRequest(BaseModel):
    """Request model for bulk person enrichment."""

    contact_id: UUID
    first_name: str | None = None
    last_name: str | None = None
    display_name: str | None = None
    email: str | None = None
    linkedin_url: str | None = None
    company_name: str | None = None
    person_id: UUID | None = None
    has_email: bool = False
    has_phone_number: bool = False
