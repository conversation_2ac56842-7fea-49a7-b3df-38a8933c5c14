from uuid import UUID, uuid4

from fastapi import Request

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import ServiceError
from salestech_be.common.exception.exception import ErrorDetails, ResourceNotFoundError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.core.prospecting.prospecting_quota_service import (
    ProspectingQuotaService,
    get_prospecting_quota_service_by_db_engine,
)
from salestech_be.core.prospecting.type.person_type_v2 import (
    BulkEnrichPersonRequest,
)
from salestech_be.db.dao.company_repository import CompanyRepository
from salestech_be.db.dao.pdl_person_repository import PDLPersonRepository
from salestech_be.db.dao.person_repository import PersonRepository
from salestech_be.db.dao.prospecting_run_repository import ProspectingRunRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.person_dto import PersonDto
from salestech_be.db.dto.prospecting_dto import (
    EMAIL_ENRICH_CREDITS_PER_ENRICHMENT,
    MOBILE_ENRICH_CREDITS_PER_ENRICHMENT,
)
from salestech_be.db.models.person import ProspectingEnrichStatus
from salestech_be.db.models.prospecting_run import (
    ProspectingBulkEnrichPersonItem,
    ProspectingBulkEnrichPersonRunRequest,
    ProspectingRun,
    ProspectingRunResult,
    ProspectingRunStatus,
    ProspectingRunType,
    ProspectingRunUpdate,
    ResourceCredit,
)
from salestech_be.db.models.quota import (
    QuotaConsumingResource,
)
from salestech_be.db.models.search_query import ProspectingSearchQueryType
from salestech_be.integrations.pdl.model import (
    PDL_SEARCH_MAX_PAGE_SIZE,
    PeopleDataLabsBulkEnrichItem,
    PeopleDataLabsBulkEnrichMetadata,
    PeopleDataLabsBulkEnrichParams,
    PeopleDataLabsBulkEnrichPersonRequest,
    PeopleDataLabsPersonResponse,
    PeopleDataLabsPersonResponseStatus,
)
from salestech_be.integrations.pdl.pdl_client import PdlClient
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import TemporalTaskQueue
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.workflows.prospecting.type import (
    BulkEnrichContactWorkflowInput,  # tach-ignore
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none

logger = get_logger(__name__)


class ProspectingEnrichmentService:
    def __init__(
        self,
        pdl_client: PdlClient,
        person_repository: PersonRepository,
        company_repository: CompanyRepository,
        pdl_person_repository: PDLPersonRepository,
        prospecting_run_repository: ProspectingRunRepository,
        prospecting_quota_service: ProspectingQuotaService,
    ):
        self.pdl_client = pdl_client
        self.person_repository = person_repository
        self.company_repository = company_repository
        self.pdl_person_repository = pdl_person_repository
        self.prospecting_run_repository = prospecting_run_repository
        self.prospecting_quota_service = prospecting_quota_service

    async def _create_and_track_prospecting_run_result(
        self,
        *,
        contact_id: UUID,
        person_dto: PersonDto,
        prospecting_run_id: UUID,
        organization_id: UUID,
        enrich_requests: list[BulkEnrichPersonRequest],
        enrich_phone_numbers: bool,
        credit_tracker: dict[QuotaConsumingResource, int],
    ) -> None:
        """
        Helper method to create prospecting run result and update credit tracking.
        Extracts common logic used in both bulk_enrich_contact and _process_pdl_responses.
        """
        if not enrich_requests:
            return

        # Find the corresponding request for this contact
        contact_request: BulkEnrichPersonRequest = not_none(
            next(
                (req for req in enrich_requests if req.contact_id == contact_id),
                None,
            )
        )

        # Initialize enrichment status and credits
        email_enrichment_status = ProspectingEnrichStatus.NOT_REQUESTED
        phone_enrichment_status = ProspectingEnrichStatus.NOT_REQUESTED
        actual_email_credits = 0
        actual_phone_credits = 0

        # Check if email was enriched
        if not contact_request.has_email and person_dto.db_person.work_email:
            email_enrichment_status = ProspectingEnrichStatus.ENRICHED
            actual_email_credits = EMAIL_ENRICH_CREDITS_PER_ENRICHMENT
            credit_tracker[QuotaConsumingResource.PROSPECTING_EMAIL_ENRICHMENT] += (
                EMAIL_ENRICH_CREDITS_PER_ENRICHMENT
            )

        # Check if phone number was enriched
        if (
            enrich_phone_numbers
            and not contact_request.has_phone_number
            and person_dto.db_person.phone_numbers
        ):
            phone_enrichment_status = ProspectingEnrichStatus.ENRICHED
            actual_phone_credits = MOBILE_ENRICH_CREDITS_PER_ENRICHMENT
            credit_tracker[
                QuotaConsumingResource.PROSPECTING_PHONE_NUMBER_ENRICHMENT
            ] += MOBILE_ENRICH_CREDITS_PER_ENRICHMENT

        # Create or update prospecting run result
        await self.prospecting_run_repository.upsert_unique_target_columns(
            ProspectingRunResult(
                id=uuid4(),
                prospecting_run_id=prospecting_run_id,
                organization_id=organization_id,
                person_id=person_dto.db_person.id,
                contact_id=contact_id,
                email_enrichment_status=email_enrichment_status,
                phone_enrichment_status=phone_enrichment_status,
                actual_email_credits=actual_email_credits,
                actual_phone_credits=actual_phone_credits,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            ),
            on_conflict_target_columns=[
                "prospecting_run_id",
                "person_id",
                "organization_id",
            ],
            exclude_columns_from_update=[
                "created_at",
                "organization_id",
                "prospecting_run_id",
                "person_id",
            ],
        )

    async def _execute_enrichment_process(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        prospecting_run_id: UUID,
        enrich_requests: list[BulkEnrichPersonRequest],
        enrich_phone_numbers: bool,
    ) -> dict[UUID, PersonDto | None]:
        """
        Execute the core enrichment process for a given prospecting run.

        Args:
            organization_id: Organization identifier
            user_id: User making the request
            prospecting_run_id: ID of the prospecting run
            enrich_requests: List of contacts to enrich
            enrich_phone_numbers: Whether to enrich phone numbers

        Returns:
            Dictionary mapping contact_id to PersonDto (None if enrichment failed)
        """
        # Initialize results dictionary
        results: dict[UUID, PersonDto | None] = {}

        # Preprocess requests to separate already enriched contacts
        (
            processed_enrich_requests,
            existing_enriched_contacts,
        ) = await self._preprocess_enrich_requests(
            organization_id=organization_id,
            enrich_requests=enrich_requests,
        )
        results.update(existing_enriched_contacts)

        # Initialize credit tracker
        credit_tracker = {
            QuotaConsumingResource.PROSPECTING_EMAIL_ENRICHMENT: 0,
            QuotaConsumingResource.PROSPECTING_PHONE_NUMBER_ENRICHMENT: 0,
        }

        # Process existing enriched contacts
        for contact_id, person_dto in existing_enriched_contacts.items():
            await self._create_and_track_prospecting_run_result(
                contact_id=contact_id,
                person_dto=not_none(person_dto),
                prospecting_run_id=prospecting_run_id,
                organization_id=organization_id,
                enrich_requests=enrich_requests,
                enrich_phone_numbers=enrich_phone_numbers,
                credit_tracker=credit_tracker,
            )

        # Execute enrichment process
        try:
            # Get PDL responses for new enrichments
            pdl_response_with_contact_id_list = await self._process_enrichment_requests(
                organization_id=organization_id,
                enrich_requests=processed_enrich_requests,
            )

            # Process responses and update database
            await self._process_pdl_responses(
                organization_id=organization_id,
                user_id=user_id,
                pdl_response_with_contact_id_list=pdl_response_with_contact_id_list,
                prospecting_run_id=prospecting_run_id,
                enrich_phone_numbers=enrich_phone_numbers,
                credit_tracker=credit_tracker,
                results=results,
                enrich_requests=processed_enrich_requests,
            )

            # Finalize the run record
            await self._finalize_prospecting_run(
                user_id=user_id,
                organization_id=organization_id,
                prospecting_run_id=prospecting_run_id,
                credit_tracker=credit_tracker,
                status=ProspectingRunStatus.COMPLETED,
            )
        except Exception as e:
            logger.bind(
                organization_id=organization_id,
                prospecting_run_id=prospecting_run_id,
                exc_info=e,
            ).error("Error during bulk person enrichment")
            await self._finalize_prospecting_run(
                user_id=user_id,
                organization_id=organization_id,
                prospecting_run_id=prospecting_run_id,
                status=ProspectingRunStatus.FAILED,
                error_info=str(e),
            )
            raise ServiceError(
                f"Prospecting enrichment failed for run {prospecting_run_id}"
            )

        logger.bind(
            organization_id=organization_id,
            prospecting_run_id=prospecting_run_id,
        ).info("Completed bulk person enrichment")
        return results

    async def bulk_enrich_contact(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        enrich_requests: list[BulkEnrichPersonRequest],
        enrich_phone_numbers: bool = False,
    ) -> dict[UUID, PersonDto | None]:
        """
        Bulk enrich contact information for a list of contacts.
        """
        logger.bind(
            organization_id=organization_id,
            user_id=user_id,
            request_count=len(enrich_requests),
        ).info("Starting bulk person enrichment for contacts")

        # Calculate estimated credits and check quota
        estimated_credits = await self._calculate_estimated_credits(
            request_count=len(enrich_requests),
            enrich_phone_numbers=enrich_phone_numbers,
        )

        await self._check_quota(
            organization_id=organization_id,
            user_id=user_id,
            resource=QuotaConsumingResource.PROSPECTING_ENRICHMENT,
            estimated_usage=sum(credit.credit for credit in estimated_credits),
        )

        # Create enrichment run record
        prospecting_run = await self._create_prospecting_run(
            organization_id=organization_id,
            user_id=user_id,
            enrich_requests=enrich_requests,
            estimated_credits=estimated_credits,
        )

        # Execute the common enrichment process
        return await self._execute_enrichment_process(
            organization_id=organization_id,
            user_id=user_id,
            prospecting_run_id=prospecting_run.id,
            enrich_requests=enrich_requests,
            enrich_phone_numbers=enrich_phone_numbers,
        )

    async def bulk_enrich_contact_async(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        enrich_requests: list[BulkEnrichPersonRequest],
        enrich_phone_numbers: bool = False,
    ) -> UUID:
        """
        Start an asynchronous bulk enrichment process for contacts.

        Args:
            organization_id: Organization identifier
            user_id: User making the request
            enrich_requests: List of contacts to enrich
            enrich_phone_numbers: Whether to enrich phone numbers

        Returns:
            UUID of the prospecting run that was created
        """
        logger.bind(
            organization_id=organization_id,
            user_id=user_id,
            request_count=len(enrich_requests),
        ).info("Starting asynchronous bulk person enrichment for contacts")

        # Calculate estimated credits and check quota
        estimated_credits = await self._calculate_estimated_credits(
            request_count=len(enrich_requests),
            enrich_phone_numbers=enrich_phone_numbers,
        )

        await self._check_quota(
            organization_id=organization_id,
            user_id=user_id,
            resource=QuotaConsumingResource.PROSPECTING_ENRICHMENT,
            estimated_usage=sum(credit.credit for credit in estimated_credits),
        )

        # Create enrichment run record for async processing
        prospecting_run = await self._create_prospecting_run(
            organization_id=organization_id,
            user_id=user_id,
            enrich_requests=enrich_requests,
            estimated_credits=estimated_credits,
            run_type=ProspectingRunType.ASYNC,
        )

        # Trigger temporal workflow for asynchronous processing
        try:
            client = await get_temporal_client()
            await client.start_workflow(
                "BulkEnrichContactWorkflow",
                args=[
                    BulkEnrichContactWorkflowInput(
                        user_id=user_id,
                        organization_id=organization_id,
                        prospecting_run_id=prospecting_run.id,
                        enrich_requests=enrich_requests,
                        enrich_phone_numbers=enrich_phone_numbers,
                    )
                ],
                id=f"bulk_enrich_contact:{prospecting_run.id}",
                task_queue=TemporalTaskQueue.PROSPECTING_TASK_QUEUE,
            )
            logger.bind(
                organization_id=organization_id,
                user_id=user_id,
                prospecting_run_id=prospecting_run.id,
            ).info("Successfully triggered workflow for bulk enriching contacts")
        except Exception as e:
            logger.bind(
                organization_id=organization_id,
                user_id=user_id,
                prospecting_run_id=prospecting_run.id,
                exc_info=e,
            ).error("Error triggering bulk enrichment workflow")
            raise ServiceError(
                f"Prospecting enrichment failed for run {prospecting_run.id}"
            )
        return prospecting_run.id

    async def bulk_enrich_contact_by_run_id(
        self,
        prospecting_run_id: UUID,
        organization_id: UUID,
        user_id: UUID,
        enrich_requests: list[BulkEnrichPersonRequest],
        enrich_phone_numbers: bool = False,
    ) -> dict[UUID, PersonDto | None]:
        # fetch prospecting_run
        prospecting_run = (
            await self.prospecting_run_repository.find_by_tenanted_primary_key_or_fail(
                table_model=ProspectingRun,
                id=prospecting_run_id,
                organization_id=organization_id,
            )
        )

        # validate prospecting_run status
        if prospecting_run.status != ProspectingRunStatus.PENDING:
            logger.bind(
                prospecting_run_id=prospecting_run_id,
                organization_id=organization_id,
                user_id=user_id,
            ).error("Prospecting run is not pending")
            raise ValueError("Prospecting run is not pending")

        if prospecting_run.run_type != ProspectingRunType.ASYNC:
            logger.bind(
                prospecting_run_id=prospecting_run_id,
                organization_id=organization_id,
                user_id=user_id,
            ).error("Prospecting run is not async")
            raise ValueError("Prospecting run is not async")

        # Execute the common enrichment process
        return await self._execute_enrichment_process(
            organization_id=organization_id,
            user_id=user_id,
            prospecting_run_id=prospecting_run_id,
            enrich_requests=enrich_requests,
            enrich_phone_numbers=enrich_phone_numbers,
        )

    async def _create_prospecting_run(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        enrich_requests: list[BulkEnrichPersonRequest],
        estimated_credits: list[ResourceCredit],
        run_type: ProspectingRunType = ProspectingRunType.MANUAL,
    ) -> ProspectingRun:
        return not_none(
            await self.prospecting_run_repository.insert(
                ProspectingRun(
                    id=uuid4(),
                    organization_id=organization_id,
                    user_id=user_id,
                    run_type=run_type,
                    estimated_credits=estimated_credits,
                    run_request=ProspectingBulkEnrichPersonRunRequest(
                        enrich_requests=[
                            ProspectingBulkEnrichPersonItem(
                                contact_id=request.contact_id,
                                person_id=request.person_id,
                                first_name=request.first_name,
                                last_name=request.last_name,
                                display_name=request.display_name,
                                email=request.email,
                                linkedin_url=request.linkedin_url,
                                company_name=request.company_name,
                            )
                            for request in enrich_requests
                        ]
                    ),
                    status=ProspectingRunStatus.PENDING,
                    starts_at=zoned_utc_now(),
                    created_at=zoned_utc_now(),
                    search_query_type=ProspectingSearchQueryType.PEOPLE,
                )
            )
        )

    async def _find_person_dto_list_by_person_ids(
        self,
        organization_id: UUID,
        person_ids: list[UUID],
        raise_exception_if_missing: bool = False,
    ) -> list[PersonDto]:
        db_person_list = await self.person_repository.list_by_ids(
            ids=person_ids,
            organization_id=organization_id,
        )

        # Check for missing person IDs
        missing_person_ids: list[str] = [
            str(person_id)
            for person_id in person_ids
            if person_id not in [db_person.id for db_person in db_person_list]
        ]

        # Raise error if missing any and raise_exception_if_missing is True
        if missing_person_ids and raise_exception_if_missing:
            raise ResourceNotFoundError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.RESOURCE_NOT_FOUND,
                    details=f"People {', '.join(missing_person_ids)} not found.",
                )
            )

        # Get company information for each person
        db_company_list = await self.company_repository.list_by_ids(
            organization_id=organization_id,
            ids=[
                db_person.company_id
                for db_person in db_person_list
                if db_person.company_id
            ],
        )

        # Build and return person DTOs with company information
        return [
            PersonDto(
                db_person=db_person,
                db_company=next(
                    (
                        db_company
                        for db_company in db_company_list
                        if db_person.company_id
                        and db_company.id == db_person.company_id
                    ),
                    None,
                ),
            )
            for db_person in db_person_list
        ]

    async def _preprocess_enrich_requests(
        self,
        *,
        organization_id: UUID,
        enrich_requests: list[BulkEnrichPersonRequest],
    ) -> tuple[list[BulkEnrichPersonRequest], dict[UUID, PersonDto | None]]:
        """
        Preprocess enrichment requests to separate already enriched contacts from new ones.

        Args:
            organization_id: Organization identifier
            enrich_requests: List of enrichment requests to process

        Returns:
            Tuple of (requests_needing_enrichment, existing_enriched_contacts)
        """
        # Create lookup index for efficient access
        contact_id_to_request = {req.contact_id: req for req in enrich_requests}
        requests_with_person_id = []
        requests_without_person_id = []
        existing_enriched_contacts: dict[UUID, PersonDto | None] = {}

        # Classify requests based on whether they have person_id
        for request in enrich_requests:
            if request.person_id is not None:
                requests_with_person_id.append(request)
            else:
                requests_without_person_id.append(request)

        # All requests without person_id need enrichment
        requests_needing_enrichment = requests_without_person_id.copy()

        # Process requests that already have person_id
        if requests_with_person_id:
            # Create efficient mappings
            contact_id_to_person_id = {
                not_none(req.contact_id): not_none(req.person_id)
                for req in requests_with_person_id
            }
            person_ids = list(set(contact_id_to_person_id.values()))

            # Fetch person DTOs for existing persons
            person_dto_list = await self._find_person_dto_list_by_person_ids(
                organization_id=organization_id,
                person_ids=person_ids,
                raise_exception_if_missing=True,
            )

            person_id_to_dto = {dto.db_person.id: dto for dto in person_dto_list}

            # Determine which persons are already enriched vs need re-enrichment
            for contact_id, person_id in contact_id_to_person_id.items():
                person_dto = person_id_to_dto.get(person_id)
                if person_dto and person_dto.db_person.last_enriched_at:
                    # Already enriched, add to existing contacts
                    existing_enriched_contacts[contact_id] = person_dto
                    continue

                # Not enriched yet, add to enrichment queue
                if request := not_none(contact_id_to_request.get(contact_id)):
                    requests_needing_enrichment.append(request)

        logger.bind(
            organization_id=organization_id,
            requests_needing_enrichment=len(requests_needing_enrichment),
            existing_enriched_contacts=len(existing_enriched_contacts),
        ).info("Preprocessed enrichment requests")

        return requests_needing_enrichment, existing_enriched_contacts

    async def _process_enrichment_requests(
        self,
        *,
        organization_id: UUID,
        enrich_requests: list[BulkEnrichPersonRequest],
    ) -> list[tuple[UUID, PeopleDataLabsPersonResponse]]:
        logger.bind(
            organization_id=organization_id,
            total_requests=len(enrich_requests),
        ).info("Processing enrichment requests")

        # Separate requests
        enrich_requests_without_person_id = []
        enrich_requests_with_person_id = []

        for request in enrich_requests:
            if request.person_id is None:
                enrich_requests_without_person_id.append(request)
            else:
                enrich_requests_with_person_id.append(request)

        # Initialize response list
        pdl_response_with_contact_id_list: list[
            tuple[UUID, PeopleDataLabsPersonResponse]
        ] = []

        # Process requests without person_id
        pdl_response_with_contact_id_list.extend(
            await self._process_new_enrichment_requests(
                enrich_requests_without_person_id
            )
        )

        # Process requests with person_id (existing people)
        pdl_response_with_contact_id_list.extend(
            await self._process_existing_enrichment_requests(
                organization_id=organization_id,
                enrich_requests_with_person_id=enrich_requests_with_person_id,
            )
        )
        logger.bind(
            organization_id=organization_id,
            total_responses=len(pdl_response_with_contact_id_list),
        ).info("Completed processing all enrichment requests")

        return pdl_response_with_contact_id_list

    async def _process_new_enrichment_requests(
        self,
        enrich_requests: list[BulkEnrichPersonRequest],
    ) -> list[tuple[UUID, PeopleDataLabsPersonResponse]]:
        pdl_response_with_contact_id_list: list[
            tuple[UUID, PeopleDataLabsPersonResponse]
        ] = []

        # Process in batches to respect PDL API limits (max 100 per request)
        for i in range(0, len(enrich_requests), PDL_SEARCH_MAX_PAGE_SIZE):
            batch_enrich_requests = enrich_requests[i : i + PDL_SEARCH_MAX_PAGE_SIZE]

            # Convert our request format to PDL's format
            pdl_bulk_enrich_request = PeopleDataLabsBulkEnrichPersonRequest(
                requests=[
                    PeopleDataLabsBulkEnrichItem(
                        metadata=PeopleDataLabsBulkEnrichMetadata(
                            contact_id=str(req.contact_id),
                        ),
                        params=PeopleDataLabsBulkEnrichParams(
                            email=req.email,
                            profile=([req.linkedin_url] if req.linkedin_url else []),
                            name=req.display_name,
                            first_name=req.first_name,
                            last_name=req.last_name,
                            company=req.company_name,
                        ),
                    )
                    for req in batch_enrich_requests
                ],
            )

            # Call PDL API - response order matches request order
            pdl_response = await self.pdl_client.bulk_enrich_person(
                pdl_bulk_enrich_person_request=pdl_bulk_enrich_request
            )
            # validate response order matches request order
            if len(pdl_bulk_enrich_request.requests) != len(pdl_response.list_data):
                raise ServiceError(
                    f"Expected {len(pdl_bulk_enrich_request.requests)} responses, got {len(pdl_response.list_data)}"
                )

            pdl_response_with_contact_id_list.extend(
                [
                    (request_item.contact_id, pdl_response_item)
                    for request_item, pdl_response_item in zip(
                        batch_enrich_requests, pdl_response.list_data, strict=False
                    )
                ]
            )
            logger.bind(
                batch_size=len(batch_enrich_requests),
                list_data_size=len(pdl_response.list_data),
            ).info("Processed new enrichment requests")

        return pdl_response_with_contact_id_list

    async def _process_existing_enrichment_requests(
        self,
        *,
        organization_id: UUID,
        enrich_requests_with_person_id: list[BulkEnrichPersonRequest],
    ) -> list[tuple[UUID, PeopleDataLabsPersonResponse]]:
        if not enrich_requests_with_person_id:
            logger.debug("No requests with person_id to process")
            return []

        logger.bind(
            organization_id=organization_id,
            request_count=len(enrich_requests_with_person_id),
        ).info("Processing enrichment requests for existing people")

        # Extract unique person IDs from requests
        contact_to_person_map = {
            req.contact_id: not_none(req.person_id)
            for req in enrich_requests_with_person_id
        }

        # Get PDL IDs
        pdl_person_list = await self.pdl_person_repository.list_by_person_ids(
            person_ids=list(set(contact_to_person_map.values())),
            organization_id=organization_id,
        )

        # Create efficient lookup map
        person_to_pdl_id_map = {
            pdl_person.person_id: pdl_person.ext_id for pdl_person in pdl_person_list
        }

        pdl_response_with_contact_id_list: list[
            tuple[UUID, PeopleDataLabsPersonResponse]
        ] = []
        processed_person_ids = set()

        # Process in batches to respect PDL API limits
        batch_size = PDL_SEARCH_MAX_PAGE_SIZE
        for i in range(0, len(enrich_requests_with_person_id), batch_size):
            batch = enrich_requests_with_person_id[i : i + batch_size]
            enrich_requests = []

            # Build batch request items
            for req in batch:
                person_id = contact_to_person_map.get(req.contact_id)

                # Skip if already processed or no PDL ID found
                if not person_id or person_id in processed_person_ids:
                    continue

                pdl_id = person_to_pdl_id_map.get(person_id)
                if not pdl_id:
                    continue

                enrich_requests.append(
                    PeopleDataLabsBulkEnrichItem(
                        metadata=PeopleDataLabsBulkEnrichMetadata(
                            contact_id=str(req.contact_id),
                        ),
                        params=PeopleDataLabsBulkEnrichParams(
                            pdl_id=pdl_id,
                        ),
                    )
                )
                processed_person_ids.add(person_id)

            # Skip API call if no valid items in batch
            if not enrich_requests:
                continue

            # Make API request for batch
            pdl_response = await self.pdl_client.bulk_enrich_person(
                pdl_bulk_enrich_person_request=PeopleDataLabsBulkEnrichPersonRequest(
                    requests=enrich_requests,
                )
            )
            if len(enrich_requests) != len(pdl_response.list_data):
                raise ServiceError(
                    f"Expected {len(enrich_requests)} responses, got {len(pdl_response.list_data)}"
                )
            pdl_response_with_contact_id_list.extend(
                [
                    (request_item.contact_id, pdl_response_item)
                    for request_item, pdl_response_item in zip(
                        batch, pdl_response.list_data, strict=False
                    )
                ]
            )

            logger.bind(
                batch_size=len(batch),
                list_data_size=len(pdl_response.list_data),
            ).info("Processed existing enrichment requests")

        return pdl_response_with_contact_id_list

    async def _process_pdl_responses(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        pdl_response_with_contact_id_list: list[
            tuple[UUID, PeopleDataLabsPersonResponse]
        ],
        prospecting_run_id: UUID,
        enrich_phone_numbers: bool,
        credit_tracker: dict[QuotaConsumingResource, int],
        results: dict[UUID, PersonDto | None],
        enrich_requests: list[BulkEnrichPersonRequest],
    ) -> None:
        """
        Process PDL responses and update database with enriched person information.
        """
        # Track unique PDL IDs to avoid processing duplicates
        pdl_id_set = set()

        # Process each PDL response
        for contact_id, pdl_response in pdl_response_with_contact_id_list:
            person_dto = None
            # Process successful enrichment responses
            if pdl_response.status == PeopleDataLabsPersonResponseStatus.SUCCESS.value:
                pdl_person = not_none(pdl_response.data)

                # Skip duplicate persons (PDL sometimes returns the same person multiple times)
                if pdl_person.id in pdl_id_set:
                    continue
                pdl_id_set.add(pdl_person.id)

                # Create or update person record with enriched data
                person_dto = (
                    await self.person_repository.upsert_person_with_pdl_enriched_person(
                        people_data_labs_person=pdl_person,
                        enrich_phone_numbers=enrich_phone_numbers,
                        user_id=user_id,
                        organization_id=organization_id,
                    )
                )

                # Create prospecting run result and track credits
                await self._create_and_track_prospecting_run_result(
                    contact_id=contact_id,
                    person_dto=person_dto,
                    prospecting_run_id=prospecting_run_id,
                    organization_id=organization_id,
                    enrich_requests=enrich_requests,
                    enrich_phone_numbers=enrich_phone_numbers,
                    credit_tracker=credit_tracker,
                )

            # Store result (None if enrichment failed)
            results[contact_id] = person_dto

    async def _finalize_prospecting_run(
        self,
        *,
        user_id: UUID,
        organization_id: UUID,
        prospecting_run_id: UUID,
        status: ProspectingRunStatus = ProspectingRunStatus.COMPLETED,
        credit_tracker: dict[QuotaConsumingResource, int] | None = None,
        error_info: str | None = None,
    ) -> None:
        """
        Finalize a prospecting run by updating its status and recording credit usage.
        """
        # Prepare update request with basic run completion data
        update_prospecting_run_request = ProspectingRunUpdate(
            status=status,
            updated_at=zoned_utc_now(),
            ends_at=zoned_utc_now(),
        )

        # Record actual credit usage for completed runs
        if status == ProspectingRunStatus.COMPLETED and credit_tracker:
            update_prospecting_run_request.actual_credits = [
                ResourceCredit(
                    resource=QuotaConsumingResource.PROSPECTING_EMAIL_ENRICHMENT,
                    credit=credit_tracker.get(
                        QuotaConsumingResource.PROSPECTING_EMAIL_ENRICHMENT, 0
                    ),
                ),
                ResourceCredit(
                    resource=QuotaConsumingResource.PROSPECTING_PHONE_NUMBER_ENRICHMENT,
                    credit=credit_tracker.get(
                        QuotaConsumingResource.PROSPECTING_PHONE_NUMBER_ENRICHMENT, 0
                    ),
                ),
            ]

            # record quota usage
            quota_usage_list = [
                await self.prospecting_quota_service.insert_quota_usage(
                    organization_id=organization_id,
                    user_id=user_id,
                    resource=QuotaConsumingResource.PROSPECTING_ENRICHMENT,
                    usage=credit,
                    applied_sub_resource=resource,
                )
                for resource, credit in credit_tracker.items()
                if credit > 0
            ]
            if quota_usage_list:
                update_prospecting_run_request.quota_usage_ids = [
                    quota_usage.id for quota_usage in quota_usage_list
                ]

        # Add error information if run failed
        if error_info:
            update_prospecting_run_request.error_info = error_info

        # Update the run record in database
        await self.prospecting_run_repository.update_by_tenanted_primary_key(
            table_model=ProspectingRun,
            organization_id=organization_id,
            primary_key_to_value={
                "id": prospecting_run_id,
            },
            column_to_update=update_prospecting_run_request,
        )

    async def _calculate_estimated_credits(
        self,
        *,
        request_count: int,
        enrich_phone_numbers: bool = False,
    ) -> list[ResourceCredit]:
        estimated_credits: list[ResourceCredit] = []

        # Base credits for email enrichment
        email_credits = request_count * EMAIL_ENRICH_CREDITS_PER_ENRICHMENT
        if email_credits > 0:
            estimated_credits.append(
                ResourceCredit(
                    resource=QuotaConsumingResource.PROSPECTING_EMAIL_ENRICHMENT,
                    credit=email_credits,
                )
            )

        # Additional credits for phone enrichment if requested
        if enrich_phone_numbers:
            phone_credits = request_count * MOBILE_ENRICH_CREDITS_PER_ENRICHMENT
            if phone_credits > 0:
                estimated_credits.append(
                    ResourceCredit(
                        resource=QuotaConsumingResource.PROSPECTING_PHONE_NUMBER_ENRICHMENT,
                        credit=phone_credits,
                    )
                )

        return estimated_credits

    async def _check_quota(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        resource: QuotaConsumingResource,
        estimated_usage: int,
    ) -> None:
        await self.prospecting_quota_service.check_quota_limit_exceeded(
            organization_id=organization_id,
            user_id=user_id,
            resource=resource,
            estimated_usage=estimated_usage,
        )


class SingletonProspectingEnrichmentService(Singleton, ProspectingEnrichmentService):
    """Singleton implementation of ProspectingEnrichmentService for efficient reuse."""


def get_prospecting_enrichment_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> ProspectingEnrichmentService:
    return SingletonProspectingEnrichmentService(
        pdl_client=PdlClient(),
        person_repository=PersonRepository(engine=db_engine),
        company_repository=CompanyRepository(engine=db_engine),
        pdl_person_repository=PDLPersonRepository(engine=db_engine),
        prospecting_run_repository=ProspectingRunRepository(engine=db_engine),
        prospecting_quota_service=get_prospecting_quota_service_by_db_engine(
            db_engine=db_engine
        ),
    )


def get_prospecting_enrichment_service(
    request: Request,
) -> ProspectingEnrichmentService:
    db_engine = get_db_engine(request=request)
    return get_prospecting_enrichment_service_by_db_engine(db_engine=db_engine)
