from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, ConfigDict

from salestech_be.core.research_agent.models.company_activity import (
    ResearchLinkedinPost,
    ResearchSocialActivityHyperlinks,
)
from salestech_be.core.research_agent.utils import info_source_repr_from_url

type PersonResearchLinkedinPost = ResearchLinkedinPost

type PersonResearchSocialActivityHyperlinks = ResearchSocialActivityHyperlinks


class PersonActivityLinkedinPost(BaseModel):
    linkedin_post: PersonResearchLinkedinPost


class PersonResearchActivityBase(BaseModel):
    model_config = ConfigDict(extra="allow")

    # salestech_be.db.models.intel_provider.ProviderUuid
    provider_uuid: UUID | None = None


class PersonResearchSocialActivity(PersonResearchActivityBase):  # linkedin post
    model_config = ConfigDict(extra="allow")

    backend_urn: str | None = None
    date_posted: datetime | None = None
    share_url: str | None = None
    text: str | None = None
    actor_name: str | None = None
    hyperlinks: ResearchSocialActivityHyperlinks | None = None
    total_reactions: int | None = None

    @property
    def feed_url(self) -> str | None:
        return f"https://www.linkedin.com/feed/update/{self.backend_urn}"

    @property
    def info_source_repr(self) -> str | None:
        return info_source_repr_from_url(self.share_url)
