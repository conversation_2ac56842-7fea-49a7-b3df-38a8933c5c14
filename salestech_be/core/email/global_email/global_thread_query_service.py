import html
from asyncio import TaskGroup
from typing import Annotated
from uuid import UUID

from fastapi import Depends
from pydantic import EmailStr

from salestech_be.common.core_crm.sales_action import StandardSalesActionType
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import (
    UNSET,
    UnsetAware,
    specified,
    specified_or_default,
)
from salestech_be.core.contact.service.contact_resolve_service import (
    ContactResolveService,
    get_contact_resolve_service,
)
from salestech_be.core.email.attachment.email_attachment_service import (
    EmailAttachmentService,
    get_email_attachment_service,
    get_email_attachment_service_by_db_engine,
)
from salestech_be.core.email.global_email.attachment_details_type_v2 import (
    AttachmentDetailsV2,
)
from salestech_be.core.email.global_email.email_participant_type_v2 import (
    EmailParticipantV2,
)
from salestech_be.core.email.global_email.global_message_ai_rec_service import (
    GlobalMessageAIRecService,
    get_global_message_ai_rec_service,
)
from salestech_be.core.email.global_email.global_message_type import GlobalMessage
from salestech_be.core.email.global_email.global_thread_type import GlobalThread
from salestech_be.core.email.global_email.types_v2 import (
    GlobalMessageErrorInfoV2,
    GlobalMessageEventSummaryV2,
)
from salestech_be.core.email.type.email import EmailHydratedParticipant
from salestech_be.core.user.service.user_service import (
    UserService,
    get_user_service_general,
)
from salestech_be.db.dao.contact_repository import ContactRepository
from salestech_be.db.dao.email_account import EmailAccountRepository
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.email_attchment_dto import AttachmentDto
from salestech_be.db.dto.email_dto import GlobalMessageEventSummaryDTO
from salestech_be.db.models.global_thread import (
    GlobalMessage as GlobalMessageDB,
)
from salestech_be.db.models.global_thread import (
    GlobalMessageAssociation,
)
from salestech_be.db.models.global_thread import (
    GlobalThread as GlobalThreadDB,
)
from salestech_be.db.models.message import Message
from salestech_be.db.models.sequence import EmailEventType
from salestech_be.ree_logging import get_logger

logger = get_logger()


class GlobalThreadQueryService:
    def __init__(
        self,
        thread_repository: Annotated[ThreadRepository, Depends()],
        email_account_repository: Annotated[EmailAccountRepository, Depends()],
        email_attachment_service: Annotated[
            EmailAttachmentService, Depends(get_email_attachment_service)
        ],
        contact_resolve_service: Annotated[ContactResolveService, Depends()],
        user_service: Annotated[UserService, Depends()],
        contact_repository: Annotated[ContactRepository, Depends()],
        global_message_ai_rec_service: Annotated[GlobalMessageAIRecService, Depends()],
    ):
        super().__init__()
        self.thread_repository = thread_repository
        self.email_account_repository = email_account_repository
        self.email_attachment_service = email_attachment_service
        self.contact_resolve_service = contact_resolve_service
        self.user_service = user_service
        self.contact_repository = contact_repository
        self.global_message_ai_rec_service = global_message_ai_rec_service

    async def list_global_threads(  # noqa: C901, PLR0912
        self,
        user_id: UUID,
        organization_id: UUID,
        only_include_thread_ids: UnsetAware[set[UUID]] = UNSET,
        only_include_thread_for_contacts: UnsetAware[set[UUID]] = UNSET,
        only_include_thread_for_accounts: UnsetAware[set[UUID]] = UNSET,
        only_include_thread_for_pipeline: UnsetAware[UUID] = UNSET,
        only_include_thread_for_contacts_email: UnsetAware[set[str]] = UNSET,
        only_include_thread_for_sequence: UnsetAware[UUID] = UNSET,
        include_custom_object: bool = False,
        only_include_message_sent_from_user_to_contact: bool = False,
    ) -> list[GlobalThread]:
        # Early return if any of the specified filters is empty
        if (
            (specified(only_include_thread_ids) and not only_include_thread_ids)
            or (
                specified(only_include_thread_for_contacts)
                and not only_include_thread_for_contacts
            )
            or (
                specified(only_include_thread_for_accounts)
                and not only_include_thread_for_accounts
            )
            or (
                specified(only_include_thread_for_contacts_email)
                and not only_include_thread_for_contacts_email
            )
        ):
            return []

        # Get all email accounts owned by this user
        user_email_accounts = (
            await self.email_account_repository.find_accounts_by_owner_user_id(
                owner_user_id=user_id,
                organization_id=organization_id,
            )
        )
        user_email_account_ids = [acc.id for acc in user_email_accounts]
        # Get threads from repository
        threads = []
        if specified(only_include_thread_ids) and only_include_thread_ids:
            # If specific thread IDs are requested, fetch only those
            threads = await self.thread_repository.find_global_thread_by_ids(
                global_thread_ids=list(only_include_thread_ids),
                organization_id=organization_id,
            )
        elif any(
            [
                specified(only_include_thread_for_contacts),
                specified(only_include_thread_for_accounts),
                specified(only_include_thread_for_pipeline),
                specified(only_include_thread_for_sequence),
            ]
        ):
            # Support all_of style in list request with filter spec
            if (
                specified(only_include_thread_for_contacts)
                and only_include_thread_for_contacts
            ):
                threads += await self.list_global_threads_by_contact_ids_and_emails(
                    contact_ids=only_include_thread_for_contacts,
                    organization_id=organization_id,
                    contact_emails=list(only_include_thread_for_contacts_email)
                    if specified(only_include_thread_for_contacts_email)
                    else None,
                )

            if (
                specified(only_include_thread_for_accounts)
                and only_include_thread_for_accounts
            ):
                threads += (
                    await self.thread_repository.list_global_threads_by_account_ids(
                        account_ids=list(only_include_thread_for_accounts),
                        organization_id=organization_id,
                    )
                )

            if specified(only_include_thread_for_pipeline):
                threads += (
                    await self.thread_repository.list_global_threads_by_pipeline_id(
                        pipeline_id=only_include_thread_for_pipeline,
                        organization_id=organization_id,
                    )
                )

            if specified(only_include_thread_for_sequence):
                threads += (
                    await self.thread_repository.list_global_threads_by_sequence_id(
                        sequence_id=only_include_thread_for_sequence,
                        organization_id=organization_id,
                    )
                )
        else:
            # Otherwise fetch all threads for the organization
            threads = (
                await self.thread_repository.list_global_threads_by_organization_id(
                    organization_id=organization_id,
                )
            )

        # Convert DB models to domain models with populated messages
        result = []
        if specified(only_include_thread_ids) and only_include_thread_ids:
            for global_thread in threads:
                messages = await self._populate_messages_by_global_thread(
                    user_email_account_ids=user_email_account_ids,
                    organization_id=organization_id,
                    global_thread=global_thread,
                )
                messages_count = len(messages)
                result.append(
                    self.populate_global_thread(
                        db_global_thread=global_thread,
                        global_messages=messages,
                        messages_count=messages_count,
                    )
                )
            return result

        if only_include_message_sent_from_user_to_contact:
            for global_thread in threads:
                messages = await self._populate_messages_by_global_thread(
                    user_email_account_ids=user_email_account_ids,
                    organization_id=organization_id,
                    global_thread=global_thread,
                )
                messages_count = len(messages)
                result.append(
                    self.populate_global_thread(
                        db_global_thread=global_thread,
                        global_messages=messages,
                        messages_count=messages_count,
                    )
                )
            return self.filter_global_thread_by_only_sent_to_contact_messages(
                threads=result,
                for_account_ids=specified_or_default(
                    only_include_thread_for_accounts, set()
                ),
            )

        global_thread_id_message_count_map: dict[
            UUID, int
        ] = await self.thread_repository.count_global_messages_by_thread_ids(
            global_thread_ids=[global_thread.id for global_thread in threads],
            organization_id=organization_id,
        )
        for global_thread in threads:
            result.append(
                self.populate_global_thread(
                    db_global_thread=global_thread,
                    global_messages=[],
                    messages_count=global_thread_id_message_count_map.get(
                        global_thread.id, 0
                    ),
                )
            )

        return result

    @classmethod
    def filter_global_thread_by_only_sent_to_contact_messages(
        cls, threads: list[GlobalThread], for_account_ids: set[UUID]
    ) -> list[GlobalThread]:
        """
        Filter global threads to only include messages sent from user to contact.
        If for_account_ids is provided, only include messages sent to contacts associated with the account.
        """
        result: list[GlobalThread] = []
        for thread in threads:
            filtered_messages: list[GlobalMessage] = []
            for message in thread.messages:
                sent_from_user = any(
                    a.email_account_id is not None for a in message.send_from
                )
                sent_to_contact = any(
                    (a.contact_id is not None)
                    # and (a.account_id in for_account_ids or len(for_account_ids) == 0)
                    for a in (message.to or [])
                )
                cc_to_contact = any(
                    (a.contact_id is not None)
                    # and (a.account_id in for_account_ids or len(for_account_ids) == 0)
                    for a in (message.cc or [])
                )
                if sent_from_user and (sent_to_contact or cc_to_contact):
                    filtered_messages.append(message)
            if filtered_messages:
                filtered_message_count = len(filtered_messages)
                result.append(
                    thread.model_copy(
                        update={
                            "messages": filtered_messages,
                            "message_count": filtered_message_count,
                        }
                    )
                )
        logger.info(
            "filter_global_thread_by_only_sent_to_contact_messages",
            before_filter_count=threads,
            after_filter_count=result,
        )
        return result

    async def list_global_threads_for_pipeline_sales_action_tagging(
        self,
        user_id: UUID,
        organization_id: UUID,
        pipeline_id: UUID,
        account_id: UUID,
    ) -> list[GlobalThread]:
        """
        This is for FE being able to render a list of selectable global messages
        that are sent from seller to buyers for the seller user (reps) to tag specific
        sales action taken in the message.
        """
        threads = await self.list_global_threads(
            user_id=user_id,
            organization_id=organization_id,
            only_include_thread_for_pipeline=pipeline_id,
            only_include_thread_for_accounts={account_id},
            only_include_message_sent_from_user_to_contact=True,
        )
        # do one additional filter to dedupe and ensure if the thread only has account_id, it doesn't have pipeline id
        seen_thread_id: set[UUID] = set()
        result: list[GlobalThread] = []
        for thread in threads:
            if thread.id in seen_thread_id:
                continue
            seen_thread_id.add(thread.id)
            if (thread.account_ids and not thread.pipeline_id) or thread.pipeline_id:
                result.append(thread)
        return result

    def populate_global_thread(
        self,
        db_global_thread: GlobalThreadDB,
        global_messages: list[GlobalMessage],
        messages_count: int,
    ) -> GlobalThread:
        participant_emails = {
            participant.email
            for message in global_messages
            for participants in [message.bcc, message.cc, message.send_from, message.to]
            if participants
            for participant in participants
            if participant.email
        }

        return GlobalThread(
            id=db_global_thread.id,
            subject=db_global_thread.subject,
            snippet=html.unescape(db_global_thread.snippet),
            owner_user_ids=[],  # This needs to be populated from somewhere
            latest_message_received_date=db_global_thread.latest_message_received_date,
            latest_message_date=db_global_thread.latest_message_date,
            messages=global_messages,
            participant_contact_id_list=db_global_thread.contact_ids or [],
            account_id=db_global_thread.account_ids[0]
            if db_global_thread.account_ids
            else None,
            message_count=messages_count,
            account_ids=db_global_thread.account_ids or [],
            pipeline_id=db_global_thread.pipeline_id,
            sequence_id=db_global_thread.sequence_id,
            potential_pipeline_ids=[],
            participant_emails=list(participant_emails),
        )

    async def _populate_messages_by_global_thread(
        self,
        global_thread: GlobalThreadDB,
        user_email_account_ids: list[UUID],
        organization_id: UUID,
    ) -> list[GlobalMessage]:
        """Populate messages for a global thread."""
        # Get global messages ordered by received_at
        global_messages = await self.thread_repository.find_global_messages_by_thread_id_order_by_received_at(
            global_thread_id=global_thread.id, organization_id=organization_id
        )
        return await self._populate_messages(
            global_messages=global_messages,
            user_email_account_ids=user_email_account_ids,
            organization_id=organization_id,
        )

    async def _populate_messages(  # noqa: C901, PLR0912, PLR0915
        self,
        global_messages: list[GlobalMessageDB],
        user_email_account_ids: list[UUID],
        organization_id: UUID,
    ) -> list[GlobalMessage]:
        """Populate messages for a global thread."""
        if not global_messages:
            return []

        # Get message mappings for these global messages
        async with TaskGroup() as tg:
            mappings_task = tg.create_task(
                self.thread_repository.find_message_associations_by_global_message_ids(
                    global_message_ids=[gm.id for gm in global_messages],
                    organization_id=organization_id,
                )
            )
            global_message_property_metadata_task = tg.create_task(
                self.global_message_ai_rec_service.find_property_metadata_for_record(
                    sobject_name=StdObjectIdentifiers.global_message,
                    record_ids={gm.id for gm in global_messages},
                    organization_id=organization_id,
                )
            )
            email_events_data_by_global_message_id_task = tg.create_task(
                self.thread_repository.find_email_events_summaries_by_global_message_ids(
                    global_message_ids=[gm.id for gm in global_messages],
                    organization_id=organization_id,
                )
            )

        mappings = mappings_task.result()
        global_message_property_metadata = (
            global_message_property_metadata_task.result()
        )

        # Group mappings by global message ID
        mapping_by_global_message_id: dict[UUID, list[GlobalMessageAssociation]] = {}
        for mapping in mappings:
            if mapping.global_message_id not in mapping_by_global_message_id:
                mapping_by_global_message_id[mapping.global_message_id] = []
            mapping_by_global_message_id[mapping.global_message_id].append(mapping)

        # Get all referenced messages
        message_ids = [m.message_id for m in mappings]
        messages = await self.thread_repository.find_messages_by_ids(
            message_ids=message_ids, organization_id=organization_id
        )
        messages_by_id = {m.id: m for m in messages}

        email_events_data_by_global_message_id = (
            email_events_data_by_global_message_id_task.result()
        )

        # Collect all attachment IDs from all messages
        all_attachment_ids: list[UUID] = []
        for msg in messages_by_id.values():
            if msg.attachment_ids:
                all_attachment_ids.extend(msg.attachment_ids)

        # Fetch all attachments in a batch
        all_attachments = []
        if all_attachment_ids:
            all_attachments = (
                await self.email_attachment_service.find_attachment_by_ids(
                    organization_id=organization_id,
                    attachment_ids=all_attachment_ids,
                )
            )

        # Create mapping of attachment ID to attachment details
        attachment_by_id: dict[UUID, AttachmentDto] = {}
        for a in all_attachments:
            attachment_by_id[a.attachment.id] = a

        # Prepare global_message_to_message mapping
        global_message_to_message: dict[
            UUID, tuple[GlobalMessageDB, Message, list[GlobalMessageAssociation]]
        ] = {}

        # First, identify messages to process and collect all unique participants
        all_unique_participants = set()

        for global_message in global_messages:
            mappings = mapping_by_global_message_id.get(global_message.id, [])

            # Try to find a message owned by this user
            message = None
            for mapping in mappings:
                if mapping.message_id in messages_by_id:
                    message = messages_by_id[mapping.message_id]
                    break

            if not message and mappings:
                # If no message found for this user, use any available message
                for mapping in mappings:
                    if mapping.message_id in messages_by_id:
                        message = messages_by_id[mapping.message_id]
                        break

            if message:
                # Store the message mapping
                global_message_to_message[global_message.id] = (
                    global_message,
                    message,
                    mappings,
                )

                # Use the model's helper property to get unique participants
                all_unique_participants.update(message.unique_email_participants)

        # Process all unique participants in a single batch
        all_participants_list = list(all_unique_participants)
        hydrated_participants = await self._hydrate_participants(
            all_participants_list, organization_id
        )

        # Create lookup dictionaries for efficient hydrated participant retrieval
        # We'll use two methods for matching since participants may have different identifiers
        by_email: dict[str, EmailParticipantV2] = {}
        by_contact_id: dict[UUID, EmailParticipantV2] = {}
        by_email_account_id: dict[UUID, EmailParticipantV2] = {}

        # For participants that have multiple ways to identify them, we'll store them in all applicable dictionaries
        for participant in hydrated_participants:
            if participant.email:
                by_email[participant.email] = participant
            if participant.contact_id:
                by_contact_id[participant.contact_id] = participant
            if participant.email_account_id:
                by_email_account_id[participant.email_account_id] = participant

        # Convert to GlobalMessage objects
        result = []

        for (
            global_message,
            message,
            mappings,
        ) in global_message_to_message.values():
            # First create the email account map once
            message_owner_email_accounts = {
                messages_by_id[mapping.message_id].email_account_id: mapping.message_id
                for mapping in mappings
                if mapping.message_id in messages_by_id
            }

            # Get attachments for this message using the pre-fetched mapping
            message_db_attachments: list[AttachmentDto] = []
            if message.attachment_ids:
                message_db_attachments = [
                    attachment_by_id[attachment_id]
                    for attachment_id in message.attachment_ids
                    if attachment_id in attachment_by_id
                ]

            attachment_details = (
                [
                    AttachmentDetailsV2.from_attachment_dto(a)
                    for a in message_db_attachments
                ]
                if message_db_attachments
                else None
            )

            # Get email events for this global message
            email_events_data: list[GlobalMessageEventSummaryDTO] = (
                email_events_data_by_global_message_id.get(global_message.id, [])
            )
            # Convert to GlobalMessageEventSummaryV2 objects
            email_events = [
                GlobalMessageEventSummaryV2(
                    event_type=EmailEventType(email_event.event_type),
                    latest_event_time=email_event.latest_event_time,
                )
                for email_event in email_events_data
            ]

            # Hydrate participants by looking them up in our dictionaries
            # In order of priority: contact_id, email_account_id, email
            def get_hydrated_participant(
                p: EmailHydratedParticipant,
            ) -> EmailParticipantV2:
                if p.contact_id and p.contact_id in by_contact_id:
                    return by_contact_id[p.contact_id]
                if p.email_account_id and p.email_account_id in by_email_account_id:
                    return by_email_account_id[p.email_account_id]
                if p.email and p.email in by_email:
                    return by_email[p.email]
                # Fallback if no match found - should be very rare
                logger.warning(f"Could not find hydrated participant for {p}")
                return EmailParticipantV2.from_hydrated_participant(p)

            send_from_hydrated = (
                [get_hydrated_participant(p) for p in message.send_from]
                if message.send_from
                else []
            )

            to_hydrated = (
                [get_hydrated_participant(p) for p in message.send_to]
                if message.send_to
                else []
            )

            cc_hydrated = (
                [get_hydrated_participant(p) for p in message.cc] if message.cc else []
            )

            bcc_hydrated = (
                [get_hydrated_participant(p) for p in message.bcc]
                if message.bcc
                else []
            )

            reply_to_hydrated = (
                [get_hydrated_participant(p) for p in message.reply_to]
                if message.reply_to
                else []
            )

            # Create GlobalMessage object with hydrated participants
            result.append(
                GlobalMessage(
                    id=global_message.id,
                    global_thread_id=global_message.global_thread_id,
                    user_email_account_ids=user_email_account_ids,
                    message_owner_email_accounts_and_messages_map=message_owner_email_accounts,
                    can_reply=any(
                        email_account_id in message_owner_email_accounts
                        for email_account_id in user_email_account_ids
                    ),
                    subject=message.subject,
                    status=message.status,
                    send_from=send_from_hydrated,
                    to=to_hydrated,
                    cc=cc_hydrated,
                    bcc=bcc_hydrated,
                    reply_to=reply_to_hydrated,
                    snippet=message.snippet,
                    body_text=message.body_text,
                    body_html=message.body_html,
                    main_body_text=message.main_body_text,
                    main_body_html=message.main_body_html,
                    send_at=message.send_at,
                    folders=message.folders or [],
                    attachment_details=attachment_details,
                    received_date=message.received_at,
                    reply_to_global_message_id=global_message.parent_global_message_id,
                    error_info=GlobalMessageErrorInfoV2.from_message_error_info(
                        message.error_info
                    )
                    if message.error_info
                    else None,
                    email_events=email_events,
                    email_account_id=message.email_account_id,
                    property_metadata=global_message_property_metadata.get(
                        global_message.id, None
                    ),
                    sales_action_types=global_message.sales_action_types,
                )
            )

        return result

    async def _hydrate_participants(  # noqa: C901,PLR0912
        self,
        participants: list[EmailHydratedParticipant],
        organization_id: UUID,
    ) -> list[EmailParticipantV2]:
        if not participants:
            return []

        contact_ids = []
        email_account_ids = []

        for participant in participants:
            if participant.contact_id:
                contact_ids.append(participant.contact_id)
            elif participant.email_account_id:
                email_account_ids.append(participant.email_account_id)

        contacts = (
            await self.contact_repository.find_contacts_by_ids(
                organization_id=organization_id,
                contact_ids=contact_ids,
            )
            if contact_ids
            else []
        )
        contact_map = {c.id: c for c in contacts}

        email_accounts = (
            await self.email_account_repository.find_accounts_by_ids(
                organization_id=organization_id,
                email_account_ids=email_account_ids,
            )
            if email_account_ids
            else []
        )

        # Batch fetch all users for email accounts
        owner_user_ids = {
            email_account.owner_user_id
            for email_account in email_accounts
            if email_account.owner_user_id
        }

        # Create a mapping of user_id to user
        user_map = {}
        if owner_user_ids:
            try:
                users = await self.user_service.list_users_v2(
                    organization_id=organization_id,
                    only_include_user_ids=owner_user_ids,
                )
                user_map = {user.id: user for user in users}
            except Exception as e:
                logger.warning(
                    "Failed to get users info for email accounts",
                    user_ids=list(owner_user_ids),
                    error=str(e),
                )

        # Map email account IDs to user display names
        email_account_names = {}
        for email_account in email_accounts:
            if email_account.owner_user_id:
                user = user_map.get(email_account.owner_user_id)
                if user and user.display_name:
                    email_account_names[email_account.id] = user.display_name

        hydrated = []
        for participant in participants:
            # Keep original name as fallback
            original_name = participant.name

            if participant.contact_id:
                contact = contact_map.get(participant.contact_id)
                if contact and contact.display_name:
                    participant.name = contact.display_name
                elif original_name:
                    participant.name = original_name
            elif participant.email_account_id:
                if email_account_names.get(participant.email_account_id):
                    participant.name = email_account_names[participant.email_account_id]
                elif original_name:
                    participant.name = original_name

            hydrated.append(EmailParticipantV2.from_hydrated_participant(participant))

        return hydrated

    async def list_global_threads_by_contact_ids_and_emails(
        self,
        contact_ids: set[UUID],
        organization_id: UUID,
        contact_emails: list[EmailStr] | None = None,
    ) -> list[GlobalThreadDB]:
        global_thread_list = (
            await self.thread_repository.list_global_threads_by_contact_ids(
                contact_ids=list(contact_ids), organization_id=organization_id
            )
        )
        if not contact_emails:
            return global_thread_list
        results: list[GlobalThreadDB] = []
        thread_id_global_thread_map: dict[UUID, GlobalThreadDB] = {
            thread_id: global_thread
            for global_thread in global_thread_list
            for thread_id in global_thread.thread_ids
        }
        thread_list = await self.thread_repository.list_threads_by_ids(
            thread_ids=list(
                {
                    thread_id
                    for global_thread in global_thread_list
                    for thread_id in global_thread.thread_ids
                }
            ),
            organization_id=organization_id,
        )
        for thread in thread_list:
            if (
                bool(set(thread.participant_emails) & set(contact_emails))
                and thread_id_global_thread_map[thread.id] not in results
            ):
                results.append(thread_id_global_thread_map[thread.id])

        return results

    async def list_global_threads_by_account_ids(
        self,
        account_ids: set[UUID],
        organization_id: UUID,
    ) -> list[GlobalThreadDB]:
        return await self.thread_repository.list_global_threads_by_account_ids(
            account_ids=list(account_ids), organization_id=organization_id
        )

    async def list_global_messages(
        self,
        organization_id: UUID,
        user_id: UUID,
        only_include_global_message_ids: set[UUID],
    ) -> list[GlobalMessage]:
        # Get user email accounts for the specific user
        user_email_accounts = (
            await self.email_account_repository.find_accounts_by_owner_user_id(
                owner_user_id=user_id,
                organization_id=organization_id,
            )
        )
        user_email_account_ids = [acc.id for acc in user_email_accounts]

        # Fetch global messages based on provided IDs or all for the organization
        global_messages = await self.thread_repository.get_global_message_by_ids(
            global_message_ids=list(only_include_global_message_ids),
            organization_id=organization_id,
        )
        return await self._populate_messages(
            global_messages=global_messages,
            user_email_account_ids=user_email_account_ids,
            organization_id=organization_id,
        )

    async def list_global_messages_by_sales_action_types_for_pipeline_criteria(
        self,
        user_id: UUID,
        organization_id: UUID,
        sales_action_type_filters: list[list[StandardSalesActionType]],
        pipeline_id: UUID,
    ) -> list[GlobalMessage]:
        """
        List global messages that have any of the specified sales action types.

        Args:
            user_id: User ID for permission checks
            organization_id: Organization ID to filter by
            sales_action_types_filters: List of sales action types to filter by
                Each list contains a set of sales action types to filter by
                e.g. [[StandardSalesActionType.INTRO], [StandardSalesActionType.FOLLOWUP]]
                will return messages with either intro or followup sales action types
            pipeline_id: Optional pipeline ID to further filter results

        Returns:
            List of GlobalMessage objects matching the criteria
        """
        # Get user email accounts for the specific user
        user_email_accounts = (
            await self.email_account_repository.find_accounts_by_owner_user_id(
                owner_user_id=user_id,
                organization_id=organization_id,
            )
        )

        # Fetch global messages with sales action types filter
        global_messages = await self.thread_repository.list_global_messages_by_sales_action_types_for_pipeline(
            organization_id=organization_id,
            sales_action_types_filters=sales_action_type_filters,
            pipeline_id=pipeline_id,
        )

        logger.info(
            "[pre enrichment] list_global_messages_by_sales_action_types_for_pipeline_criteria",
            global_messages=[
                {
                    "id": msg.id,
                    "sales_action_types": msg.sales_action_types,
                }
                for msg in global_messages
            ],
            sales_action_type_filters=sales_action_type_filters,
            pipeline_id=pipeline_id,
        )

        if not global_messages:
            return []

        # Populate the global messages with additional data
        post_enrichment = await self._populate_messages(
            user_email_account_ids=[acc.id for acc in user_email_accounts],
            organization_id=organization_id,
            global_messages=global_messages,
        )
        logger.info(
            "[post enrichment] list_global_messages_by_sales_action_types_for_pipeline_criteria",
            post_enrichment=[
                {
                    "id": msg.id,
                    "sales_action_types": msg.sales_action_types,
                }
                for msg in post_enrichment
            ],
        )
        return post_enrichment


class SingletonGlobalThreadQueryService(Singleton, GlobalThreadQueryService):
    pass


def get_global_thread_query_service(
    db_engine: DatabaseEngine,
) -> GlobalThreadQueryService:
    if SingletonGlobalThreadQueryService.has_instance():
        return SingletonGlobalThreadQueryService.get_singleton_instance()
    return GlobalThreadQueryService(
        thread_repository=ThreadRepository(engine=db_engine),
        email_account_repository=EmailAccountRepository(engine=db_engine),
        email_attachment_service=get_email_attachment_service_by_db_engine(
            db_engine=db_engine
        ),
        contact_resolve_service=get_contact_resolve_service(db_engine=db_engine),
        user_service=get_user_service_general(db_engine=db_engine),
        contact_repository=ContactRepository(engine=db_engine),
        global_message_ai_rec_service=get_global_message_ai_rec_service(
            db_engine=db_engine
        ),
    )
