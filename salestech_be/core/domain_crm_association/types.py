from typing import Literal
from uuid import UUID

from pydantic import BaseModel, EmailStr

from salestech_be.common.type.patch_request import UNSET, BasePatchRequest, UnsetAware
from salestech_be.db.models.domain_crm_association import (
    AttributionInfo,
    AttributionState,
    DomainCRMAssociation,
    DomainCRMAssociationRole,
    DomainType,
)


class BaseCreateDomainCrmAssociation(BaseModel):
    organization_id: UUID
    domain_type: DomainType
    association_role: DomainCRMAssociationRole | None = None

    contact_id: UUID | None = None
    account_id: UUID | None = None
    pipeline_id: UUID | None = None

    user_id: UUID | None = None
    email_account_id: UUID | None = None

    created_by_user_id: UUID


class CreateEmailCrmAssociation(BaseCreateDomainCrmAssociation):
    domain_type: Literal[DomainType.EMAIL] = DomainType.EMAIL  # Add type annotation

    email: EmailStr
    message_id: UUID
    thread_id: UUID

    contact_id: UUID


class CreateMeetingCrmAssociation(BaseCreateDomainCrmAssociation):
    domain_type: Literal[DomainType.MEETING] = DomainType.MEETING

    email: EmailStr | None = None
    meeting_id: UUID
    user_calendar_event_id: UUID | None = None
    association_role: DomainCRMAssociationRole = DomainCRMAssociationRole.PARTICIPANT

    contact_id: UUID | None = (
        None  # todo: remove this field, leave contact_id to be filled in during attibution flow
    )


class CreateSequenceCrmAssociation(BaseCreateDomainCrmAssociation):
    domain_type: Literal[DomainType.SEQUENCE] = DomainType.SEQUENCE

    sequence_enrollment_id: UUID
    sequence_id: UUID


class CreateNoteCrmAssociation(BaseCreateDomainCrmAssociation):
    domain_type: Literal[DomainType.NOTE] = DomainType.NOTE

    note_id: UUID


class CreateTaskCrmAssociation(BaseCreateDomainCrmAssociation):
    domain_type: Literal[DomainType.TASK] = DomainType.TASK

    task_id: UUID

    sequence_id: UUID | None = None
    sequence_enrollment_id: UUID | None = None
    sequence_step_id: UUID | None = None
    sequence_step_variant_id: UUID | None = None
    sequence_step_execution_id: UUID | None = None
    global_thread_id: UUID | None = None
    meeting_id: UUID | None = None
    call_id: UUID | None = None


class CreateVoiceCallCrmAssociation(BaseCreateDomainCrmAssociation):
    domain_type: Literal[DomainType.VOICE_CALL] = DomainType.VOICE_CALL

    call_id: UUID
    phone_number: str | None = None
    meeting_id: UUID | None = None


# Update types using BasePatchRequest with UnsetAware fields
class UpdateDomainCrmAssociation(BasePatchRequest):
    # Base fields
    organization_id: UnsetAware[UUID] = UNSET
    domain_type: UnsetAware[DomainType] = UNSET

    contact_id: UnsetAware[UUID | None] = UNSET
    account_id: UnsetAware[UUID | None] = UNSET
    pipeline_id: UnsetAware[UUID | None] = UNSET

    email_account_id: UnsetAware[UUID | None] = UNSET

    # Attribution state and info fields
    contact_attribution_state: UnsetAware[AttributionState | None] = UNSET
    account_attribution_state: UnsetAware[AttributionState | None] = UNSET
    pipeline_attribution_state: UnsetAware[AttributionState | None] = UNSET

    contact_attribution_info: UnsetAware[AttributionInfo | None] = UNSET
    account_attribution_info: UnsetAware[AttributionInfo | None] = UNSET
    pipeline_attribution_info: UnsetAware[AttributionInfo | None] = UNSET

    # Email specific fields
    email: UnsetAware[EmailStr | None] = UNSET
    message_id: UnsetAware[UUID | None] = UNSET
    thread_id: UnsetAware[UUID | None] = UNSET

    # Meeting specific fields
    meeting_id: UnsetAware[UUID | None] = UNSET
    user_calendar_event_id: UnsetAware[UUID | None] = UNSET

    # Sequence specific fields
    sequence_id: UnsetAware[UUID | None] = UNSET
    sequence_enrollment_id: UnsetAware[UUID | None] = UNSET
    sequence_step_id: UnsetAware[UUID | None] = UNSET

    # Note specific field
    note_id: UnsetAware[UUID | None] = UNSET

    # Task specific field
    task_id: UnsetAware[UUID | None] = UNSET

    # Voice call specific fields
    call_id: UnsetAware[UUID | None] = UNSET
    phone_number: UnsetAware[str | None] = UNSET

    updated_by_user_id: UnsetAware[UUID | None] = UNSET


class DeleteDomainCrmAssociation(BasePatchRequest):
    organization_id: UUID
    domain_type: DomainType

    user_id: UnsetAware[UUID | None] = UNSET

    contact_id: UnsetAware[UUID | None] = UNSET
    account_id: UnsetAware[UUID | None] = UNSET
    pipeline_id: UnsetAware[UUID | None] = UNSET

    # Task specific field
    task_id: UnsetAware[UUID | None] = UNSET

    deleted_by_user_id: UUID

    # Email specific field
    email: UnsetAware[EmailStr | None] = UNSET
    message_id: UnsetAware[UUID | None] = UNSET
    thread_id: UnsetAware[UUID | None] = UNSET

    # Meeting specific field
    meeting_id: UnsetAware[UUID | None] = UNSET

    # Sequence specific field
    sequence_id: UnsetAware[UUID | None] = UNSET
    sequence_enrollment_id: UnsetAware[UUID | None] = UNSET
    sequence_step_id: UnsetAware[UUID | None] = UNSET
    sequence_step_variant_id: UnsetAware[UUID | None] = UNSET
    sequence_step_execution_id: UnsetAware[UUID | None] = UNSET
    global_thread_id: UnsetAware[UUID | None] = UNSET

    # Note specific field
    note_id: UnsetAware[UUID | None] = UNSET

    # Task specific field

    # Voice call specific field
    call_id: UnsetAware[UUID | None] = UNSET
    phone_number: UnsetAware[str | None] = UNSET


class DeleteMeetingCrmAssociation(
    BaseModel
):  # refactor later to replace with DeleteDomainCrmAssociation
    organization_id: UUID
    domain_type: Literal[DomainType.MEETING] = DomainType.MEETING
    user_id: UUID | None = None
    email: EmailStr | None = None
    # contact_id  - not needed for meeting deletion
    association_role: DomainCRMAssociationRole = DomainCRMAssociationRole.PARTICIPANT

    meeting_id: UUID
    deleted_by_user_id: UUID


class BasicDomainCrmActivityInput(BaseModel):
    organization_id: UUID
    associations: list[DomainCRMAssociation]
    user_id: UUID


class DomainActivityCaptureAttributionResult(BaseModel):
    success: bool
    associations: list[DomainCRMAssociation]
    error_message: str | None = None
