import uuid
from collections.abc import Mapping, Sequence
from datetime import datetime
from typing import Annotated
from uuid import UUID

from fastapi import Depends
from temporalio.client import Client
from temporalio.common import WorkflowIDReusePolicy

from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.core.domain_crm_association.types import (
    BasicDomainCrmActivityInput,
    CreateEmailCrmAssociation,
    CreateMeetingCrmAssociation,
    CreateNoteCrmAssociation,
    CreateSequenceCrmAssociation,
    CreateTaskCrmAssociation,
    CreateVoiceCallCrmAssociation,
    DeleteDomainCrmAssociation,
    DeleteMeetingCrmAssociation,
    UpdateDomainCrmAssociation,
)
from salestech_be.db.dao.domain_crm_association_repository import (
    DomainCRMAssociationRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.domain_crm_association import (
    DomainCRMAssociation,
    DomainType,
)
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import DOMAIN_CRM_ASSOCIATION_TASK_QUEUE
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.workflows.domain_crm_association.activity_attribution import (
    DomainCrmAssociationAttributionWorkflow,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.temporal.workflows.domain_crm_association.bulk_activity_attribution import (
    BulkActivityAttributionWorkflow,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none

# Define type for domain CRM association field values
DomainCRMAssociationValue = str | UUID | datetime | DomainType | None

logger = get_logger(__name__)


class DomainCRMAssociationService:
    def __init__(
        self,
        domain_crm_association_repository: Annotated[
            DomainCRMAssociationRepository, Depends()
        ],
        temporal_client: Annotated[Client, Depends(get_temporal_client)] | None = None,
    ) -> None:
        super().__init__()
        self.domain_crm_association_repository = domain_crm_association_repository
        self._temporal_client = temporal_client

    async def _get_client(self) -> Client:
        if self._temporal_client is not None:
            return self._temporal_client
        return await get_temporal_client()

    async def create_domain_crm_association(
        self,
        domain_crm_association: CreateEmailCrmAssociation
        | CreateMeetingCrmAssociation
        | CreateSequenceCrmAssociation
        | CreateNoteCrmAssociation
        | CreateTaskCrmAssociation
        | CreateVoiceCallCrmAssociation,
    ) -> DomainCRMAssociation:
        db_domain_crm_association = DomainCRMAssociation(
            **domain_crm_association.model_dump(),
            id=uuid.uuid4(),
            created_at=zoned_utc_now(),
        )

        result = await self.domain_crm_association_repository.insert(
            db_domain_crm_association
        )

        # Start activity capture workflow
        await self.run_activity_capture_workflow(
            organization_id=domain_crm_association.organization_id,
            user_id=domain_crm_association.created_by_user_id,
            associations=[result],
            contact_id=getattr(domain_crm_association, "contact_id", None),
            pipeline_id=getattr(domain_crm_association, "pipeline_id", None),
            account_id=getattr(domain_crm_association, "account_id", None),
        )

        return result

    async def bulk_create_domain_crm_associations(
        self,
        domain_crm_associations: Sequence[
            CreateEmailCrmAssociation
            | CreateMeetingCrmAssociation
            | CreateSequenceCrmAssociation
            | CreateNoteCrmAssociation
            | CreateTaskCrmAssociation
            | CreateVoiceCallCrmAssociation
        ],
    ) -> list[DomainCRMAssociation]:
        """Create multiple domain CRM associations in a single operation.

        Args:
            domain_crm_associations: A sequence of CRM association objects to create.

        Returns:
            A list of created DomainCRMAssociation objects.
        """
        now = zoned_utc_now()
        db_associations = []

        for assoc in domain_crm_associations:
            db_assoc = DomainCRMAssociation(
                **assoc.model_dump(),
                id=uuid.uuid4(),
                created_at=now,
            )

            db_associations.append(db_assoc)
        async with self.domain_crm_association_repository.engine.begin():
            results = await self.domain_crm_association_repository.bulk_insert(
                DomainCRMAssociation, db_associations
            )

        # Start bulk activity attribution workflow if we have associations
        if results and len(results) > 0:
            await self.run_bulk_domain_crm_attribution_workflow(
                organization_id=results[0].organization_id,
                user_id=not_none(results[0].created_by_user_id),
                associations=results,
                domain_assocs=domain_crm_associations,
            )

        return results

    async def get_domain_crm_association_by_id(
        self, association_id: UUID
    ) -> DomainCRMAssociation:
        """Get a domain CRM association by its ID.

        Args:
            association_id: The UUID of the association to retrieve

        Returns:
            The domain CRM association

        Raises:
            ResourceNotFoundError: If the association does not exist
        """
        association = await self.domain_crm_association_repository.find_by_primary_key(
            DomainCRMAssociation, id=association_id
        )

        if not association:
            raise ResourceNotFoundError(
                f"Domain CRM association with ID {association_id} not found"
            )

        return association

    async def get_domain_crm_associations_by_related_id(
        self,
        domain_id: UUID,
        domain_type: DomainType,
    ) -> Sequence[DomainCRMAssociation]:
        """Get domain CRM associations by related IDs.

        Args:
            domain_id: The ID of the related entity
            domain_type: The type of domain entity

        Returns:
            A list of matching domain CRM associations
        """
        return await self.domain_crm_association_repository.find_by_related_id_and_domain_type(
            domain_id=domain_id,
            domain_type=domain_type,
        )

    async def update_domain_crm_association(
        self,
        association_id: UUID,
        domain_crm_association: UpdateDomainCrmAssociation,
    ) -> DomainCRMAssociation:
        """Update a domain CRM association.

        Args:
            association_id: The ID of the association to update
            domain_crm_association: The data to update with

        Returns:
            The updated domain CRM association

        Raises:
            ResourceNotFoundError: If the association does not exist
        """
        # Ensure the association exists
        db_domain_crm_association = await self.get_domain_crm_association_by_id(
            association_id
        )

        # Update fields from the input model
        update_data = domain_crm_association.model_dump(exclude_unset=True)
        update_data["updated_at"] = zoned_utc_now()

        # Set updated_by_user_id if created_by_user_id is provided
        if domain_crm_association.updated_by_user_id:
            update_data["updated_by_user_id"] = (
                domain_crm_association.updated_by_user_id
            )

        # Create updated instance using model_copy
        db_domain_crm_association = db_domain_crm_association.model_copy(
            update=update_data
        )

        # Perform the update and ensure a non-None return value
        result = await self.domain_crm_association_repository.update_instance(
            db_domain_crm_association
        )
        if result is None:
            raise ResourceNotFoundError(
                f"Failed to update domain CRM association with ID {association_id}"
            )
        return result

    async def bulk_delete_domain_crm_associations(
        self,
        domain_crm_associations: Sequence[DeleteDomainCrmAssociation],
        deleted_by_user_id: UUID,
    ) -> None:
        """Delete domain CRM associations by request using soft delete.

        This method is domain agnostic - it can handle any type of domain association
        (task, meeting, email, etc.) by delegating to the repository layer.
        """
        if not domain_crm_associations:
            return

        logger.bind(
            associations_count=len(domain_crm_associations),
            deleted_by_user_id=deleted_by_user_id,
            domain_types=[assoc.domain_type.value for assoc in domain_crm_associations],
        ).info("Starting bulk soft delete of domain CRM associations")

        # Delegate to repository for the actual bulk delete operation
        await (
            self.domain_crm_association_repository.bulk_delete_domain_crm_associations(
                domain_crm_associations=domain_crm_associations,
                deleted_by_user_id=deleted_by_user_id,
            )
        )

        logger.bind(
            associations_count=len(domain_crm_associations),
        ).info("Completed bulk soft delete of domain CRM associations")

    async def bulk_delete_meeting_domain_crm_associations(
        self,
        domain_crm_associations: Sequence[DeleteMeetingCrmAssociation],
    ) -> None:
        """Delete domain CRM associations by request."""
        async with self.domain_crm_association_repository.engine.begin():
            # todo: revisit bulk deletion
            for domain_crm_association in domain_crm_associations:
                await self.domain_crm_association_repository.delete_meeting_domain_crm_association(
                    domain_crm_association
                )

    async def clone_domain_crm_association_with_updates(
        self,
        association_id: UUID,
        updates: Mapping[str, DomainCRMAssociationValue],
    ) -> DomainCRMAssociation:
        """Clone a domain CRM association with updates.

        This method creates a new domain CRM association record based on an existing one,
        but with specified fields updated.

        Args:
            association_id: The ID of the association to clone
            updates: Mapping of field names to their new values
            user_id: Optional ID of the user performing the operation

        Returns:
            The newly created domain CRM association

        Raises:
            ResourceNotFoundError: If the source association does not exist
        """
        # Ensure the source association exists
        source_association = await self.get_domain_crm_association_by_id(association_id)

        # Create a new instance with a new ID and timestamp
        now = zoned_utc_now()

        # Apply updates and set new required fields
        update_data = {
            "id": uuid.uuid4(),
            "created_at": source_association.created_at,
            "updated_at": now,
            **updates,
        }

        if updates.get("updated_by_user_id"):
            update_data["updated_by_user_id"] = updates["updated_by_user_id"]

        # Create the new instance using model_copy
        new_association = source_association.model_copy(update=update_data)

        # Insert the new association
        return await self.domain_crm_association_repository.insert(new_association)

    async def run_activity_capture_workflow(
        self,
        organization_id: UUID,
        user_id: UUID,
        associations: list[DomainCRMAssociation],
        contact_id: UUID | None = None,
        pipeline_id: UUID | None = None,
        account_id: UUID | None = None,
    ) -> None:
        """
        Start a workflow to capture and attribute activities for a domain entity.

        This method starts the Temporal DomainCrmAssociationAttributionWorkflow to process
        and create appropriate domain-CRM associations for the given entity.

        Args:
            organization_id: The organization ID
            user_id: The user ID
            associations: The list of domain entities (e.g. calls, meetings)
            contact_id: Optional contact ID to associate
            pipeline_id: Optional pipeline ID to associate
            account_id: Optional account ID to associate
        """
        try:
            if not associations:
                logger.bind(
                    association_ids=[association.id for association in associations],
                ).warning("No associations provided for activity capture workflow")
                return

            logger.bind(
                association_ids=[association.id for association in associations],
                contact_id=str(contact_id) if contact_id else None,
                pipeline_id=str(pipeline_id) if pipeline_id else None,
                account_id=str(account_id) if account_id else None,
                associations=associations,
            ).info("Starting domain activity capture workflow")

            client: Client = await self._get_client()
            workflow_id = f"domain_crm_attribution_{associations[0].organization_id}_{associations[0].domain_type}_{associations[0].id}"

            input_data = BasicDomainCrmActivityInput(
                organization_id=organization_id,
                user_id=user_id,
                associations=associations,
            )
            await client.start_workflow(
                DomainCrmAssociationAttributionWorkflow.run,
                args=[input_data],
                id=workflow_id,
                task_queue=DOMAIN_CRM_ASSOCIATION_TASK_QUEUE,
                id_reuse_policy=WorkflowIDReusePolicy.ALLOW_DUPLICATE,
            )
        except Exception as e:
            logger.error(
                f"Failed to prepare domain activity attribution workflow: {e}",
                exc_info=e,
            )

    async def run_bulk_domain_crm_attribution_workflow(
        self,
        organization_id: UUID,
        user_id: UUID,
        associations: list[DomainCRMAssociation],
        domain_assocs: Sequence[
            CreateEmailCrmAssociation
            | CreateMeetingCrmAssociation
            | CreateSequenceCrmAssociation
            | CreateNoteCrmAssociation
            | CreateTaskCrmAssociation
            | CreateVoiceCallCrmAssociation
        ],
    ) -> None:
        """
        Start a bulk workflow to capture and attribute activities for multiple domain entities.

        This method starts the Temporal BulkActivityAttributionWorkflow to process
        and attribute appropriate domain-CRM associations for the given entities in bulk.

        Args:
            organization_id: The organization ID
            user_id: The user ID
            associations: The list of domain entities (e.g. calls, meetings)
            domain_assocs: The original domain association create objects
        """
        try:
            if not associations:
                logger.bind(
                    association_ids=[association.id for association in associations],
                ).warning(
                    "No associations provided for bulk activity attribution workflow"
                )
                return

            logger.bind(
                association_ids=[association.id for association in associations],
                associations_count=len(associations),
            ).info("Starting bulk domain activity attribution workflow")

            client: Client = await self._get_client()
            workflow_id = (
                f"bulk_domain_crm_attribution_{organization_id}_{uuid.uuid4()}"
            )

            # Create input data for each association
            bulk_input_data = []
            for i, assoc in enumerate(associations):
                domain_assocs[i]
                input_data = BasicDomainCrmActivityInput(
                    organization_id=organization_id,
                    user_id=user_id,
                    associations=[assoc],
                )
                bulk_input_data.append(input_data)

            await client.start_workflow(
                BulkActivityAttributionWorkflow.run,
                args=[bulk_input_data],
                id=workflow_id,
                task_queue=DOMAIN_CRM_ASSOCIATION_TASK_QUEUE,
                id_reuse_policy=WorkflowIDReusePolicy.ALLOW_DUPLICATE,
            )
        except Exception as e:
            logger.error(
                f"Failed to prepare bulk domain activity attribution workflow: {e}",
                exc_info=e,
            )

    async def bulk_update_domain_crm_associations(
        self,
        updates: list[tuple[UUID, UpdateDomainCrmAssociation]],
    ) -> list[DomainCRMAssociation]:
        """
        Update multiple domain CRM associations in bulk for better performance.

        Args:
            updates: List of tuples containing (association_id, update_data)

        Returns:
            List of updated domain CRM associations

        Raises:
            ResourceNotFoundError: If any association is not found
        """
        if not updates:
            return []

        updated_associations = []

        # Fetch all associations first to verify they exist
        association_ids = [association_id for association_id, _ in updates]

        # Use the repository to fetch associations
        associations_by_id = {}
        for association_id in association_ids:
            association = (
                await self.domain_crm_association_repository.find_by_primary_key(
                    DomainCRMAssociation,
                    id=association_id,
                    exclude_deleted_or_archived=True,
                )
            )
            if association:
                associations_by_id[str(association_id)] = association

        # Check if all associations exist
        missing_ids = [
            str(association_id)
            for association_id, _ in updates
            if str(association_id) not in associations_by_id
        ]

        if missing_ids:
            raise ResourceNotFoundError(
                f"Domain CRM associations not found: {', '.join(missing_ids)}"
            )

        # Process all updates
        for association_id, update_data in updates:
            association = associations_by_id[str(association_id)]

            # Create updated association
            update_fields = update_data.model_dump(exclude_unset=True)
            update_fields["updated_at"] = zoned_utc_now()
            updated_association = association.model_copy(update=update_fields)

            # Update in database
            updated_instance = (
                await self.domain_crm_association_repository.update_instance(
                    updated_association
                )
            )

            if updated_instance:
                updated_associations.append(updated_instance)

        return updated_associations


def get_domain_crm_association_service(
    db_engine: DatabaseEngine,
) -> DomainCRMAssociationService:
    return DomainCRMAssociationService(
        domain_crm_association_repository=DomainCRMAssociationRepository(
            engine=db_engine
        ),
    )
