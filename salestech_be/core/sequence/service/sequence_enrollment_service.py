from asyncio import TaskGroup
from collections import defaultdict
from typing import Annotated
from uuid import UUID, uuid4

from fastapi import Depends, Request
from temporalio.client import WorkflowExecutionStatus

from salestech_be.common.exception.exception import (
    <PERSON>rror<PERSON>ode,
    ErrorDetails,
    ForbiddenError,
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.core.account.service.account_query_service import (
    AccountQueryService,
    get_account_query_service,
)
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.contact.service.contact_query_service import (
    ContactQueryService,
    get_contact_query_service,
)
from salestech_be.core.contact.service.contact_resolve_service import (
    ContactResolveService,
    ResolveContactInfos,
    get_contact_resolve_service,
)
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.domain_crm_association.domain_crm_association_service import (
    DomainCRMAssociationService,
    get_domain_crm_association_service,
)
from salestech_be.core.domain_crm_association.types import (
    CreateSequenceCrmAssociation,
    DeleteDomainCrmAssociation,
    DomainType,
)
from salestech_be.core.domain_object_list.service.domain_object_list_query_service import (
    DomainObjectListQueryService,
    get_domain_object_list_query_service_by_db_engine,
)
from salestech_be.core.email.account.service_v2 import EmailAccountServiceV2
from salestech_be.core.email.pool.service import EmailAccountPoolService
from salestech_be.core.email.service.message_service import MessageService
from salestech_be.core.email.unsubscription_group.unsubscription_group_ext import (
    UnsuscriptionGroupServiceExt,
    get_unsubscription_ext_service_by_db_engine,
)
from salestech_be.core.notification.service.notification_service import (
    NotificationService,
    get_notification_service_by_db_engine,
)
from salestech_be.core.notification.types import SendNotificationRequest
from salestech_be.core.sequence.service.sequence_enrollment_query_service import (
    SequenceEnrollmentQueryService,
    get_sequence_enrollment_query_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_permission_service import (
    SequencePermissionService,
    get_sequence_permission_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_query_service import (
    SequenceQueryService,
    get_sequence_query_service_by_db,
)
from salestech_be.core.sequence.type.sequence_enrollment_contact_type import (
    SequenceEnrollmentContact,
)
from salestech_be.core.sequence.type.sequence_enrollment_types import (
    EnrollmentTerminationMetadataType,
    SequenceEnrollmentEligibility,
    SequenceEnrollmentExitReason,
    SequenceEnrollmentTerminationNotificationMetadata,
)
from salestech_be.core.sequence.type.sequence_signal import SequenceSignal
from salestech_be.core.task.service.task_query_service import (
    TaskQueryService,
    get_task_query_service_from_engine,
)
from salestech_be.core.task.service.task_v2_service import (
    TaskV2Service,
    get_task_v2_service_general,
)
from salestech_be.db.dao.sequence_enrollment_repo import (
    SequenceEnrollmentRepository,
)
from salestech_be.db.dao.sequence_repository import (
    SequenceExecutionRepository,
    SequenceStepRepository,
)
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.email_dto import EmailAccountPoolDto
from salestech_be.db.models.notification import (
    NotificationReferenceIdType,
    NotificationSequenceEnrollmentTerminationData,
)
from salestech_be.db.models.sequence import (
    SequenceEnrollment as DbSequenceEnrollment,
)
from salestech_be.db.models.sequence import (
    SequenceEnrollmentContact as DBSequenceEnrollmentContact,
)
from salestech_be.db.models.sequence import (
    SequenceEnrollmentContactStatus,
    SequenceEnrollmentExitedByReferenceIdType,
    SequenceEnrollmentExitReasonCode,
    SequenceEnrollmentRunMode,
    SequenceEnrollmentRunStatus,
    SequenceEnrollmentStatus,
    SequenceFailureReason,
    SequenceStepExecutionStatus,
)
from salestech_be.db.models.sequence import (
    SequenceEnrollmentRun as DBSequenceEnrollmentRun,
)
from salestech_be.db.models.task import TaskReferenceIdType, TaskStatus
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import TemporalTaskQueue
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.workflows.sequence.enrollment_run_workflow import (
    SequenceEnrollmentRunWorkflow,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    SequenceEnrollmentRunWorkflowInput,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.temporal.workflows.sequence.sequence_enrollment_workflow import (
    SequenceEnrollmentWorkflow,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.sequence.enrollment.schema import (
    BulkChangeSequenceEnrollmentRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    BulkChangeSequenceEnrollmentResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    ContactForSequenceEnrollment,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    CreateSequenceEnrollmentRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    CreateSequenceEnrollmentResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    CreateSequenceEnrollmentResponseV2,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    DeleteSequenceEnrollmentResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    EnrolledContact,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    EnrollmentIneligibilityReason,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    EnrollmentPreviewItem,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    EnrollmentWarningReason,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    FailedEnrollment,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PatchEnrollmentEmailResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PatchSequenceEnrollmentRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PatchSequenceEnrollmentResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PreviewSequenceEnrollmentRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PreviewSequenceEnrollmentResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    ProceedEnrollment,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    SequenceEnrollmentRun,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    UpdatedEnrollment,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.task.schema import (
    PatchTaskRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


MAX_SUPPORTED_CONTACTS_SIZE = 8000


class SequenceEnrollmentService:
    def __init__(
        self,
        sequence_permission_service: Annotated[SequencePermissionService, Depends()],
        sequence_enrollment_repository: Annotated[
            SequenceEnrollmentRepository, Depends()
        ],
        sequence_step_repository: Annotated[SequenceStepRepository, Depends()],
        sequence_execution_repository: Annotated[
            SequenceExecutionRepository, Depends()
        ],
        domain_object_list_query_service: Annotated[
            DomainObjectListQueryService, Depends()
        ],
        contact_query_service: Annotated[ContactQueryService, Depends()],
        contact_resolve_service: Annotated[ContactResolveService, Depends()],
        account_query_service: Annotated[AccountQueryService, Depends()],
        sequence_query_service: Annotated[SequenceQueryService, Depends()],
        email_account_pool_service: Annotated[EmailAccountPoolService, Depends()],
        email_account_service_v2: Annotated[EmailAccountServiceV2, Depends()],
        message_service: Annotated[MessageService, Depends()],
        sequence_enrollment_query_service: Annotated[
            SequenceEnrollmentQueryService, Depends()
        ],
        task_query_service: Annotated[TaskQueryService, Depends()],
        task_v2_service: Annotated[TaskV2Service, Depends()],
        thread_repository: Annotated[ThreadRepository, Depends()],
        unsubscription_group_service_ext: Annotated[
            UnsuscriptionGroupServiceExt, Depends()
        ],
        domain_crm_association_service: Annotated[
            DomainCRMAssociationService, Depends()
        ],
        notification_service: Annotated[NotificationService, Depends()],
    ) -> None:
        self.sequence_permission_service = sequence_permission_service
        self.sequence_enrollment_repository = sequence_enrollment_repository
        self.sequence_execution_repository = sequence_execution_repository
        self.domain_object_list_query_service = domain_object_list_query_service
        self.contact_query_service = contact_query_service
        self.contact_resolve_service = contact_resolve_service
        self.account_query_service = account_query_service
        self.sequence_query_service = sequence_query_service
        self.email_account_pool_service = email_account_pool_service
        self.email_account_service_v2 = email_account_service_v2
        self.message_service = message_service
        self.sequence_enrollment_query_service = sequence_enrollment_query_service
        self.sequence_step_repository = sequence_step_repository
        self.task_query_service = task_query_service
        self.task_v2_service = task_v2_service
        self.thread_repository = thread_repository
        self.unsubscription_group_service_ext = unsubscription_group_service_ext
        self.domain_crm_association_service = domain_crm_association_service
        self.notification_service = notification_service

    async def can_access_sequence_by_id_for_update(
        self,
        user_auth_context: UserAuthContext,
        sequence_id: UUID,
    ) -> bool:
        return await self.sequence_permission_service.can_access_sequence_by_ids_for_update(
            user_auth_context=user_auth_context, sequence_ids=[sequence_id]
        )

    async def create_sequence_enrollment(
        self,
        request: CreateSequenceEnrollmentRequest,
        user_auth_context: UserAuthContext,
    ) -> CreateSequenceEnrollmentResponse:
        logger.bind(
            request=request,
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
        ).info("Creating sequence enrollment")

        if not (
            await self.sequence_permission_service.can_access_sequence_by_ids_for_update(
                user_auth_context=user_auth_context,
                sequence_ids=[request.sequence_id],
            )
        ):
            raise ForbiddenError(
                "You do not have permission to access this Sequence enrollment"
            )

        enrolled_contacts: list[EnrolledContact] = []
        failed_enrollments: list[FailedEnrollment] = []

        if request.contacts:
            (
                contacts_enrolled,
                contacts_failed,
            ) = await self.create_sequence_enrollment_from_contacts(
                contacts=request.contacts,
                sequence_id=request.sequence_id,
                organization_id=user_auth_context.organization_id,
                user_id=user_auth_context.user_id,
            )

            enrolled_contacts.extend(contacts_enrolled)
            failed_enrollments.extend(contacts_failed)

        elif request.domain_object_list_id:
            (
                list_enrolled,
                list_failed,
            ) = await self.create_sequence_enrollment_from_domain_object_list(
                domain_object_list_id=request.domain_object_list_id,
                sequence_id=request.sequence_id,
                organization_id=user_auth_context.organization_id,
                user_id=user_auth_context.user_id,
            )
            enrolled_contacts.extend(list_enrolled)
            failed_enrollments.extend(list_failed)

        return CreateSequenceEnrollmentResponse(
            message=f"{len(enrolled_contacts)} contacts enrolled successfully, {len(failed_enrollments)} contacts failed to enroll",
            enrolled_contacts=enrolled_contacts,
            failed_enrollments=failed_enrollments,
        )

    async def _validate_contacts_existence(
        self,
        existing_contact_ids: set[UUID],
        existing_account_ids: set[UUID],
        contacts: list[ContactForSequenceEnrollment],
        organization_id: UUID,
    ) -> dict[UUID, list[SequenceFailureReason]]:
        """Validate contacts and return mapping of valid contacts and failure reasons"""

        failed_enrollments: dict[UUID, list[SequenceFailureReason]] = {}

        # Process missing contacts and accounts
        for contact in contacts:
            failure_reasons: list[SequenceFailureReason] = []

            # Check if contact exists
            if contact.contact_id not in existing_contact_ids:
                failure_reasons.append(SequenceFailureReason.CONTACT_DOES_NOT_EXIST)

            # Check if account exists
            if contact.account_id and contact.account_id not in existing_account_ids:
                failure_reasons.append(SequenceFailureReason.ACCOUNT_DOES_NOT_EXIST)

            if failure_reasons:
                failed_enrollments[contact.contact_id] = failure_reasons

        return failed_enrollments

    async def _pick_account_and_email_to_use(
        self,
        contact: ContactForSequenceEnrollment,
        contact_email_account_pair: tuple[str | None, UUID | None] | None,
        organization_id: UUID,
    ) -> tuple[UUID | None, str | None, list[SequenceFailureReason]]:
        """
        Pick the most appropriate account and email to use for sequence enrollment.

        Args:
            contact: The contact information for sequence enrollment
            contact_email_dto: The DTO containing relevant contact email information

        Returns:
            A tuple of (account_id, email, failure_reasons) where failure_reasons is a list of
            any failure reasons encountered during the selection process
        """
        account_id_to_use = contact.account_id
        email_to_use = contact.email
        failure_reasons: list[SequenceFailureReason] = []

        if account_id_to_use and email_to_use:
            return account_id_to_use, email_to_use, []

        if not contact_email_account_pair:
            failure_reasons.append(SequenceFailureReason.NO_EMAIL_FOUND_FOR_CONTACT)
            return account_id_to_use, None, failure_reasons

        email, account_id = (
            contact_email_account_pair if contact_email_account_pair else (None, None)
        )
        if not email_to_use:
            email_to_use = email

        # If no account or email explicitly provided, use the most relevant one
        if not account_id_to_use:
            # Try to resolve account from contact and email
            account_id_to_use_map = await self.contact_resolve_service.resolve_relevant_account_by_contact_and_email_pairs(
                organization_id=organization_id,
                contact_email_pairs=[(contact.contact_id, email_to_use)],
            )
            account_id_to_use = account_id_to_use_map[contact.contact_id]

        if not account_id_to_use:
            failure_reasons.append(
                SequenceFailureReason.COULD_NOT_RESOLVE_ACCOUNT_FOR_CONTACT
            )

        return account_id_to_use, email_to_use, failure_reasons

    async def create_sequence_enrollment_from_contacts(  # noqa: C901, PLR0915
        self,
        contacts: list[ContactForSequenceEnrollment],
        sequence_id: UUID,
        organization_id: UUID,
        user_id: UUID,
    ) -> tuple[list[EnrolledContact], list[FailedEnrollment]]:
        logger.bind(
            contact_count=len(contacts),
            sequence_id=sequence_id,
            organization_id=organization_id,
            user_id=user_id,
        ).info("Creating sequence enrollment from contacts")

        # Check if we exceed max - either from this specific request, or if we would add too many new ones
        if len(contacts) > MAX_SUPPORTED_CONTACTS_SIZE:
            raise InvalidArgumentError(
                f"Too many contacts added at once, please try again with {MAX_SUPPORTED_CONTACTS_SIZE} or less",
            )

        existing_enrollments_count = await self.sequence_enrollment_repository.find_count_of_sequence_enrollments_by_sequence_id_and_status(
            sequence_id=sequence_id,
            organization_id=organization_id,
            status=SequenceEnrollmentStatus.ACTIVE,
        )
        if existing_enrollments_count + len(contacts) > MAX_SUPPORTED_CONTACTS_SIZE:
            raise InvalidArgumentError(
                f"Too many contacts added to this sequence, either add less or create a new sequence (max {MAX_SUPPORTED_CONTACTS_SIZE} contacts allowed)",
            )

        await self.resolve_and_fill_sequence_enrollment_v2(
            organization_id=organization_id,
            contacts=contacts,
        )
        enrolled_contacts: list[EnrolledContact] = []
        failed_enrollments: list[FailedEnrollment] = []

        contact_ids = {contact.contact_id for contact in contacts}
        account_ids = {contact.account_id for contact in contacts if contact.account_id}

        # Get all contacts and accounts in parallel
        async with TaskGroup() as tg:
            existing_contacts_task = tg.create_task(
                self.contact_query_service.list_contacts_v2(
                    organization_id=organization_id,
                    only_include_contact_ids=contact_ids,
                )
            )
            existing_accounts_task = tg.create_task(
                self.account_query_service.list_accounts_v2(
                    organization_id=organization_id,
                    only_include_account_ids=account_ids,
                )
            )
            enrollment_status_failed_map_task = tg.create_task(
                self.check_contact_enrollment_status_for_particular_sequence(
                    contact_ids=contact_ids,
                    sequence_id=sequence_id,
                    organization_id=organization_id,
                )
            )
            relevant_contact_email_task = tg.create_task(
                self.contact_resolve_service.map_the_most_relevant_contact_emails(
                    organization_id=organization_id,
                    contact_account_pairs=[
                        (contact.contact_id, contact.account_id) for contact in contacts
                    ],
                )
            )

        existing_contacts: list[ContactV2] = existing_contacts_task.result()
        existing_accounts: list[AccountV2] = existing_accounts_task.result()
        # Check if contacts are already enrolled in this sequence
        enrollment_status_failed_map = enrollment_status_failed_map_task.result()
        # Map the most relevant contact emails
        relevant_contact_email_map = relevant_contact_email_task.result()

        # Check if contacts and accounts exist
        existing_contact_ids = {
            existing_contact.id for existing_contact in existing_contacts
        }
        existing_account_ids = {
            existing_account.id for existing_account in existing_accounts
        }

        failed_enrollments_map = await self._validate_contacts_existence(
            existing_contact_ids=existing_contact_ids,
            existing_account_ids=existing_account_ids,
            contacts=contacts,
            organization_id=organization_id,
        )

        for contact_id, failure_reasons in enrollment_status_failed_map.items():
            if contact_id not in failed_enrollments_map:
                failed_enrollments_map[contact_id] = []
            failed_enrollments_map[contact_id].extend(failure_reasons)

        for contact_id, failure_reasons in failed_enrollments_map.items():
            failed_enrollments.append(
                FailedEnrollment(
                    contact_id=contact_id,
                    failure_reasons=failure_reasons,
                )
            )

        existing_contact_map = {
            existing_contact.id: existing_contact
            for existing_contact in existing_contacts
        }

        # Gather all user_ids that need email account pools
        user_ids_set = set()
        contact_to_owner_map: dict[UUID, UUID] = {}

        for contact_id, contact_v2 in existing_contact_map.items():
            # Skip if contact already failed validation
            if contact_id in failed_enrollments_map:
                continue

            owner_user_id = contact_v2.owner_user_id or user_id
            user_ids_set.add(owner_user_id)
            contact_to_owner_map[contact_id] = owner_user_id

        # Batch fetch all email account pools
        default_pools_by_owner_ids = (
            await self.email_account_pool_service.map_default_pools_by_owner_ids(
                organization_id=organization_id,
                owner_user_ids=list(user_ids_set),
            )
        )
        # Prepare enrollments for bulk insert
        enrollments_to_insert: list[DbSequenceEnrollment] = []
        now = zoned_utc_now()

        for contact in contacts:
            # Skip if contact doesn't exist (already added to failed_enrollments)
            if contact.contact_id in failed_enrollments_map:
                continue

            contact_v2 = existing_contact_map[contact.contact_id]

            # Get the email account pool for the contact's owner
            owner_user_id_from_map: UUID | None = contact_to_owner_map.get(
                contact.contact_id
            )
            email_account_pool_response = (
                default_pools_by_owner_ids.get(owner_user_id_from_map)
                if owner_user_id_from_map is not None
                else None
            )

            # Check if email account pool is available
            if not email_account_pool_response:
                failed_enrollments.append(
                    FailedEnrollment(
                        contact_id=contact.contact_id,
                        failure_reasons=[
                            SequenceFailureReason.EMAIL_ACCOUNT_NOT_AVAILABLE
                        ],
                    )
                )
                continue

            (
                account_id_to_use,
                email_to_use,
                failure_reasons,
            ) = await self._pick_account_and_email_to_use(
                contact=contact,
                contact_email_account_pair=relevant_contact_email_map.get(
                    contact.contact_id
                ),
                organization_id=organization_id,
            )

            if failure_reasons:
                failed_enrollments.append(
                    FailedEnrollment(
                        contact_id=contact.contact_id, failure_reasons=failure_reasons
                    )
                )
                continue

            enrollments_to_insert.append(
                DbSequenceEnrollment(
                    id=uuid4(),
                    organization_id=organization_id,
                    sequence_id=sequence_id,
                    email_account_pool_id=email_account_pool_response.id,
                    contact_id=contact.contact_id,
                    account_id=account_id_to_use,
                    email=email_to_use,
                    status=SequenceEnrollmentStatus.ACTIVE,
                    enrolled_at=now,
                    enrolled_by_user_id=user_id,
                    updated_at=now,
                    updated_by_user_id=user_id,
                )
            )

        # Perform bulk insert
        if enrollments_to_insert:
            db_sequence_enrollments = (
                await self.sequence_enrollment_repository.bulk_insert(
                    table_model=DbSequenceEnrollment, instances=enrollments_to_insert
                )
            )
            enrolled_contacts = [
                EnrolledContact(
                    id=enrollment.id,
                    contact_id=enrollment.contact_id,
                    status=enrollment.status,
                    enrolled_at=enrollment.enrolled_at,
                )
                for enrollment in db_sequence_enrollments
            ]

        return enrolled_contacts, failed_enrollments

    async def _pick_account_and_email_to_use_v2(
        self,
        contact: ContactForSequenceEnrollment,
        organization_id: UUID,
    ) -> tuple[UUID | None, str | None, list[SequenceFailureReason]]:
        account_id_to_use = contact.account_id
        email_to_use = contact.email
        failure_reasons: list[SequenceFailureReason] = []

        if account_id_to_use and email_to_use:
            return account_id_to_use, email_to_use, []

        if not email_to_use:
            failure_reasons.append(SequenceFailureReason.NO_EMAIL_FOUND_FOR_CONTACT)

        if not account_id_to_use:
            failure_reasons.append(
                SequenceFailureReason.COULD_NOT_RESOLVE_ACCOUNT_FOR_CONTACT
            )

        return account_id_to_use, email_to_use, failure_reasons

    async def _pick_account_and_email_to_use_v3(
        self,
        contact: ContactForSequenceEnrollment,
        organization_id: UUID,
    ) -> tuple[UUID | None, str | None, list[EnrollmentIneligibilityReason]]:
        account_id_to_use = contact.account_id
        email_to_use = contact.email
        failure_reasons: list[EnrollmentIneligibilityReason] = []

        if account_id_to_use:
            return account_id_to_use, email_to_use, []
        else:
            failure_reasons.append(
                EnrollmentIneligibilityReason.CONTACT_DOES_NOT_HAVE_ACCOUNT
            )

        return account_id_to_use, email_to_use, failure_reasons

    async def resolve_and_fill_sequence_enrollment(
        self, organization_id: UUID, contacts: list[ContactForSequenceEnrollment]
    ) -> None:
        contact_email_map_for_account_resolution: dict[UUID, UUID | None] = (
            defaultdict()
        )
        contact_account_map_for_email_resolution: dict[UUID, str | None] = defaultdict()
        to_resolve_contacts: list[ContactForSequenceEnrollment] = []
        for contact in contacts:
            if contact.email and contact.account_id:
                continue
            to_resolve_contacts.append(contact)
            if contact.email:
                contact_account_map_for_email_resolution[contact.contact_id] = (
                    contact.email
                )
            else:
                contact_email_map_for_account_resolution[contact.contact_id] = (
                    contact.account_id
                )

        contact_email_res_map = (
            await self.contact_resolve_service.batch_resolve_relevant_email_account_by_contact_info(
                organization_id=organization_id,
                resolve_contact_infos=ResolveContactInfos(
                    contact_account_map=contact_email_map_for_account_resolution
                ),
            )
            if contact_email_map_for_account_resolution
            else defaultdict()
        )
        logger.info(f"contact_email_res_map: {contact_email_res_map}")
        contact_account_res_map = (
            await self.contact_resolve_service.batch_resolve_relevant_email_account_by_contact_info(
                organization_id=organization_id,
                resolve_contact_infos=ResolveContactInfos(
                    contact_email_map=contact_account_map_for_email_resolution
                ),
            )
            if contact_account_map_for_email_resolution
            else defaultdict()
        )
        logger.info(f"contact_account_res_map: {contact_account_res_map}")

        for contact in to_resolve_contacts:
            if contact.email and contact_account_res_map.get(contact.contact_id, None):
                email, account_id = contact_account_res_map[contact.contact_id]
                contact.account_id = account_id
            elif contact_email_res_map.get(contact.contact_id, None):
                email, account_id = contact_email_res_map[contact.contact_id]
                contact.email = email
                if not contact.account_id:
                    contact.account_id = account_id

    async def resolve_and_fill_sequence_enrollment_v2(  # noqa: C901, PLR0912
        self, organization_id: UUID, contacts: list[ContactForSequenceEnrollment]
    ) -> None:
        contact_email_map_for_account_resolution: dict[UUID, UUID | None] = (
            defaultdict()
        )
        contact_account_map_for_email_resolution: dict[UUID, str | None] = defaultdict()
        to_resolve_contacts: list[ContactForSequenceEnrollment] = []
        for contact in contacts:
            if contact.email and contact.account_id:
                continue
            to_resolve_contacts.append(contact)
            if contact.email:
                contact_account_map_for_email_resolution[contact.contact_id] = (
                    contact.email
                )
            else:
                contact_email_map_for_account_resolution[contact.contact_id] = (
                    contact.account_id
                )

        # Step 1: First resolve emails for contacts with no email
        contact_email_res_map = (
            await self.contact_resolve_service.batch_resolve_relevant_email_account_by_contact_info(
                organization_id=organization_id,
                resolve_contact_infos=ResolveContactInfos(
                    contact_account_map=contact_email_map_for_account_resolution
                ),
            )
            if contact_email_map_for_account_resolution
            else defaultdict()
        )

        # Apply the emails to the contacts
        for contact in to_resolve_contacts:
            if contact.contact_id in contact_email_res_map:
                email, account_id = contact_email_res_map[contact.contact_id]
                if email:
                    contact.email = email
                if not contact.account_id and account_id:
                    contact.account_id = account_id

        # Step 2: Now resolve accounts for contacts who have email (original + newly resolved)
        updated_contact_account_map_for_email_resolution: dict[UUID, str | None] = (
            defaultdict()
        )
        for contact in to_resolve_contacts:
            if not contact.account_id:
                updated_contact_account_map_for_email_resolution[contact.contact_id] = (
                    contact.email
                )

        contact_account_res_map: dict[UUID, tuple[str | None, UUID | None]] = (
            defaultdict()
        )
        if updated_contact_account_map_for_email_resolution:
            contact_account_res_map = await self.contact_resolve_service.batch_resolve_relevant_email_account_by_contact_info(
                organization_id=organization_id,
                resolve_contact_infos=ResolveContactInfos(
                    contact_email_map=updated_contact_account_map_for_email_resolution
                ),
            )

        # Apply the accounts to the contacts
        for contact in to_resolve_contacts:
            if contact.email and contact_account_res_map.get(contact.contact_id, None):
                email, account_id = contact_account_res_map[contact.contact_id]
                contact.account_id = account_id
            elif contact_email_res_map.get(contact.contact_id, None):
                email, account_id = contact_email_res_map[contact.contact_id]
                contact.email = email
                if not contact.account_id:
                    contact.account_id = account_id
            elif contact_account_res_map.get(contact.contact_id, None):
                email, account_id = contact_account_res_map[contact.contact_id]
                if not contact.email:
                    contact.email = email
                contact.account_id = account_id

    async def create_sequence_enrollment_from_domain_object_list(
        self,
        domain_object_list_id: UUID,
        sequence_id: UUID,
        organization_id: UUID,
        user_id: UUID,
    ) -> tuple[list[EnrolledContact], list[FailedEnrollment]]:
        logger.bind(
            domain_object_list_id=domain_object_list_id,
            sequence_id=sequence_id,
            organization_id=organization_id,
            user_id=user_id,
        ).info("Creating sequence enrollment from domain object list")

        list_items = (
            await self.domain_object_list_query_service.list_domain_object_list_items(
                organization_id=organization_id,
                list_id=domain_object_list_id,
            )
        )

        contact_ids = {item.data.reference_id for item in list_items}
        contact_for_sequence_enrollment_list = [
            ContactForSequenceEnrollment(
                contact_id=contact_id,
            )
            for contact_id in contact_ids
        ]

        return await self.create_sequence_enrollment_from_contacts(
            contacts=contact_for_sequence_enrollment_list,
            sequence_id=sequence_id,
            organization_id=organization_id,
            user_id=user_id,
        )

    async def patch_sequence_enrollment_by_id(
        self,
        request: PatchSequenceEnrollmentRequest,
        sequence_enrollment_id: UUID,
        user_auth_context: UserAuthContext,
    ) -> PatchSequenceEnrollmentResponse:
        logger.bind(
            sequence_enrollment_id=sequence_enrollment_id,
            organization_id=user_auth_context.organization_id,
            request=request,
        ).info("Patching sequence enrollment by id")

        if not (
            await self.sequence_permission_service.can_access_sequence_enrollment_by_ids_for_update(
                user_auth_context=user_auth_context,
                enrollment_ids=[sequence_enrollment_id],
            )
        ):
            raise ForbiddenError(
                "You do not have permission to access this Sequence enrollment"
            )

        enrollment = (
            await self.sequence_enrollment_repository.find_by_tenanted_primary_key(
                table_model=DbSequenceEnrollment,
                organization_id=user_auth_context.organization_id,
                id=sequence_enrollment_id,
            )
        )
        if not enrollment:
            raise ResourceNotFoundError(
                f"Sequence enrollment with id {sequence_enrollment_id} not found"
            )

        # Upon resume, check if email is still associated with contact
        if (
            enrollment.status == SequenceEnrollmentStatus.FAILED
            and request.status == SequenceEnrollmentStatus.ACTIVE
        ):
            contact_emails = (
                await self.contact_query_service.list_contact_emails_by_contact_id(
                    organization_id=user_auth_context.organization_id,
                    contact_id=enrollment.contact_id,
                )
            )

            if not any(
                contact_email.email == enrollment.email
                for contact_email in contact_emails
            ):
                raise ResourceNotFoundError(
                    additional_error_details=ErrorDetails(
                        error_code=ErrorCode.CONTACT_NOT_ASSOCIATED_WITH_EMAIL,
                        details=f"The email {enrollment.email} is not associated with contact {enrollment.contact_id}",
                    ),
                )

        fields_to_update = request.model_dump(exclude_unset=True)

        now = zoned_utc_now()

        fields_to_update["updated_at"] = now
        if user_auth_context.user_id:
            fields_to_update["updated_by_user_id"] = user_auth_context.user_id

        if request.status == SequenceEnrollmentStatus.REMOVED:
            fields_to_update["deleted_at"] = now
            fields_to_update["deleted_by_user_id"] = user_auth_context.user_id

        updated_db_record = (
            await self.sequence_enrollment_repository.update_by_tenanted_primary_key(
                table_model=DbSequenceEnrollment,
                primary_key_to_value={"id": sequence_enrollment_id},
                organization_id=user_auth_context.organization_id,
                column_to_update=fields_to_update,
            )
        )

        if not updated_db_record:
            raise ResourceNotFoundError(
                f"Sequence enrollment with id {sequence_enrollment_id} not found"
            )

        if (
            request.status == SequenceEnrollmentStatus.FAILED
            and updated_db_record.current_step_id
        ):
            # terminate step execution if applicable
            db_sequence_step_execution = await self.sequence_execution_repository.terminate_step_execution_by_step_id_and_enrollment_id(
                sequence_step_id=updated_db_record.current_step_id,
                sequence_enrollment_id=sequence_enrollment_id,
                organization_id=user_auth_context.organization_id,
            )

            if (
                db_sequence_step_execution
                and db_sequence_step_execution.global_message_id
            ):
                # cancel scheduled message if applicable
                await (
                    self.message_service.cancel_scheduled_message_by_global_message_id(
                        organization_id=user_auth_context.organization_id,
                        global_message_id=db_sequence_step_execution.global_message_id,
                    )
                )

        return PatchSequenceEnrollmentResponse(
            id=sequence_enrollment_id,
            status=updated_db_record.status,
            updated_at=now,
        )

    async def delete_sequence_enrollment_by_id(
        self,
        sequence_enrollment_id: UUID,
        user_auth_context: UserAuthContext,
    ) -> DeleteSequenceEnrollmentResponse:
        logger.bind(
            sequence_enrollment_id=sequence_enrollment_id,
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
        ).info("Deleting sequence enrollment by id")

        if not (
            await self.sequence_permission_service.can_access_sequence_enrollment_by_ids_for_update(
                user_auth_context=user_auth_context,
                enrollment_ids=[sequence_enrollment_id],
            )
        ):
            raise ForbiddenError(
                "You do not have permission to access this Sequence enrollment"
            )

        enrollment = (
            await self.sequence_enrollment_repository.find_by_tenanted_primary_key(
                table_model=DbSequenceEnrollment,
                organization_id=user_auth_context.organization_id,
                id=sequence_enrollment_id,
                exclude_deleted=False,
            )
        )
        if not enrollment:
            raise ResourceNotFoundError(
                f"Sequence enrollment with id {sequence_enrollment_id} not found"
            )

        if not enrollment.deleted_at:
            terminated_enrollment = await self.terminate_sequence_enrollment(
                enrollment=enrollment,
                terminate_reason=SequenceEnrollmentExitReason.USER_TERMINATED,
                organization_id=user_auth_context.organization_id,
                end_enrollment_status=SequenceEnrollmentStatus.REMOVED,
                exited_by_reference_id=None,
                exited_by_reference_id_type=None,
                mark_as_deleted=True,
                deleted_by_user_id=user_auth_context.user_id,
            )
        else:
            terminated_enrollment = enrollment
        # Delete associated domain CRM associations
        try:
            # Find associations by sequence_enrollment_id
            # Delete each association individually
            delete_request = DeleteDomainCrmAssociation(
                organization_id=user_auth_context.organization_id,
                deleted_by_user_id=user_auth_context.user_id,
                sequence_enrollment_id=sequence_enrollment_id,
                domain_type=DomainType.SEQUENCE,
            )
            await (
                self.domain_crm_association_service.bulk_delete_domain_crm_associations(
                    domain_crm_associations=[delete_request],
                    deleted_by_user_id=user_auth_context.user_id,
                )
            )
            logger.bind(
                enrollment_id=sequence_enrollment_id,
                organization_id=user_auth_context.organization_id,
            ).info("Deleted domain CRM associations for sequence enrollment")
        except Exception as e:
            logger.bind(
                enrollment_id=sequence_enrollment_id,
                organization_id=user_auth_context.organization_id,
            ).error("Failed to delete domain CRM association", exc_info=e)

        return DeleteSequenceEnrollmentResponse(
            message="Sequence enrollment deleted successfully",
            id=sequence_enrollment_id,
            exited_at=not_none(terminated_enrollment.exited_at),
            deleted_at=not_none(terminated_enrollment.deleted_at),
            deleted_by_user_id=not_none(terminated_enrollment.deleted_by_user_id),
        )

    async def bulk_change_sequence_enrollments(
        self,
        request: BulkChangeSequenceEnrollmentRequest,
        user_auth_context: UserAuthContext,
    ) -> BulkChangeSequenceEnrollmentResponse:
        if not (
            await self.sequence_permission_service.can_access_sequence_enrollment_by_ids_for_update(
                user_auth_context=user_auth_context,
                enrollment_ids=request.enrollment_ids,
            )
        ):
            raise ForbiddenError(
                "You do not have permission to access one or more specified Sequence enrollment"
            )

        # Get current enrollments to check their status
        current_enrollments = await self.sequence_enrollment_repository.find_all_sequence_enrollments_by_ids(
            organization_id=user_auth_context.organization_id,
            enrollment_ids=request.enrollment_ids,
        )

        # Filter enrollment IDs based on valid status transitions
        valid_enrollment_ids = []

        # Define valid previous statuses for each target status
        valid_previous_statuses = {
            SequenceEnrollmentStatus.ACTIVE: [
                SequenceEnrollmentStatus.INACTIVE,
                SequenceEnrollmentStatus.FAILED,
                SequenceEnrollmentStatus.PENDING,
            ],
            SequenceEnrollmentStatus.INACTIVE: [
                SequenceEnrollmentStatus.ACTIVE,
                SequenceEnrollmentStatus.PENDING,
            ],
            SequenceEnrollmentStatus.FAILED: [],  # No status can change to FAILED through bulk change
            SequenceEnrollmentStatus.EXITED: [
                SequenceEnrollmentStatus.ACTIVE,
                SequenceEnrollmentStatus.INACTIVE,
                SequenceEnrollmentStatus.FAILED,
                SequenceEnrollmentStatus.PENDING,
            ],
            SequenceEnrollmentStatus.REMOVED: [
                SequenceEnrollmentStatus.ACTIVE,
                SequenceEnrollmentStatus.INACTIVE,
                SequenceEnrollmentStatus.FAILED,
                SequenceEnrollmentStatus.EXITED,
                SequenceEnrollmentStatus.PENDING,
            ],
            SequenceEnrollmentStatus.PENDING: [],  # No status can change to PENDING through bulk change
        }

        # Apply validation for each enrollment
        for enrollment in current_enrollments:
            # Skip if current status is the same as target status
            if enrollment.status == request.status:
                continue

            # Check if the transition is valid
            if enrollment.status in valid_previous_statuses[request.status]:
                valid_enrollment_ids.append(enrollment.id)
            else:
                logger.bind(
                    enrollment_id=enrollment.id,
                    from_status=enrollment.status.value,
                    to_status=request.status.value,
                    organization_id=user_auth_context.organization_id,
                ).debug("Invalid status transition skipped")

        if not valid_enrollment_ids:
            return BulkChangeSequenceEnrollmentResponse(
                message="No valid status transitions found",
                updated_enrollments=[],
            )

        exited_reason = None
        if request.status is SequenceEnrollmentStatus.EXITED:
            exited_reason = SequenceEnrollmentExitReasonCode.USER_EXITED
        updated_db_enrollments = await self.sequence_enrollment_repository.bulk_update_sequence_enrollment_status(
            sequence_enrollment_ids=valid_enrollment_ids,
            organization_id=user_auth_context.organization_id,
            status=request.status,
            exited_reason=exited_reason,
        )

        # Delete domain CRM associations if status is changed to REMOVED
        if request.status == SequenceEnrollmentStatus.REMOVED:
            for enrollment_id in valid_enrollment_ids:
                try:
                    delete_request = DeleteDomainCrmAssociation(
                        organization_id=user_auth_context.organization_id,
                        domain_type=DomainType.SEQUENCE,
                        deleted_by_user_id=user_auth_context.user_id,
                        sequence_enrollment_id=enrollment_id,
                    )
                    await self.domain_crm_association_service.bulk_delete_domain_crm_associations(
                        domain_crm_associations=[delete_request],
                        deleted_by_user_id=user_auth_context.user_id,
                    )
                    logger.bind(
                        enrollment_id=enrollment_id,
                        organization_id=user_auth_context.organization_id,
                    ).info("Deleted domain CRM associations for sequence enrollment")
                except Exception as e:
                    logger.bind(
                        enrollment_id=enrollment_id,
                        organization_id=user_auth_context.organization_id,
                    ).error("Failed to delete domain CRM associations", exc_info=e)

        updated_enrollments = [
            UpdatedEnrollment(
                id=enrollment.id,
                status=enrollment.status,
            )
            for enrollment in updated_db_enrollments
        ]

        return BulkChangeSequenceEnrollmentResponse(
            message="Sequence enrollment status updated successfully",
            updated_enrollments=updated_enrollments,
        )

    async def preview_sequence_enrollments_from_contacts(  # noqa: C901, PLR0912, PLR0915
        self,
        contacts: list[ContactForSequenceEnrollment],
        sequence_id: UUID,
        organization_id: UUID,
        user_id: UUID | None = None,
    ) -> list[EnrollmentPreviewItem]:
        logger.bind(
            contact_count=len(contacts),
            sequence_id=sequence_id,
            organization_id=organization_id,
            user_id=user_id,
        ).info("Previewing sequence enrollments from contacts")

        contact_ids = {contact.contact_id for contact in contacts}

        async with TaskGroup() as tg:
            resolve_task = tg.create_task(
                self.resolve_and_fill_sequence_enrollment_v2(
                    organization_id=organization_id,
                    contacts=contacts,
                )
            )
            contacts_task = tg.create_task(
                self.contact_query_service.list_contacts_v2(
                    organization_id=organization_id,
                    only_include_contact_ids=contact_ids,
                )
            )
            contacts_enrollments_task = tg.create_task(
                self.sequence_enrollment_query_service.get_contacts_enrollments(
                    contact_ids=contact_ids,
                    organization_id=organization_id,
                )
            )
            enrollment_status_failed_map_task = tg.create_task(
                self.check_contact_enrollment_status_for_particular_sequence(
                    contact_ids=contact_ids,
                    sequence_id=sequence_id,
                    organization_id=organization_id,
                )
            )
            default_unsub_group_task = tg.create_task(
                self.unsubscription_group_service_ext.get_default_unsub_group_for_org(
                    organization_id=organization_id,
                )
            )

        _ = resolve_task.result()
        contacts_v2 = contacts_task.result()
        contacts_enrollments = contacts_enrollments_task.result()
        enrollment_status_failed_map = enrollment_status_failed_map_task.result()
        default_unsub_group = default_unsub_group_task.result()

        contacts_to_display_name_map = {
            contact.id: contact.display_name for contact in contacts_v2
        }
        contacts_map = {contact.id: contact for contact in contacts_v2}

        # Gather all user_ids that need email account pools
        user_ids_set = set()
        contact_to_owner_map: dict[UUID, UUID] = {}

        for contact_id, contact_v2 in contacts_map.items():
            owner_user_id = contact_v2.owner_user_id or user_id
            if owner_user_id:
                user_ids_set.add(owner_user_id)
                contact_to_owner_map[contact_id] = owner_user_id

        # Gather all other sequence ids that contacts are enrolled in
        other_sequences_that_contacts_are_enrolled_in = set()
        for contact in contacts:
            enrollments_for_this_contact = contacts_enrollments.get(
                contact.contact_id, []
            )
            other_sequences_that_contacts_are_enrolled_in.update(
                {
                    enrollment.sequence_id
                    for enrollment in enrollments_for_this_contact
                    if enrollment.sequence_id != sequence_id
                }
            )

        async with TaskGroup() as tg:
            unsubscribed_email_task = tg.create_task(
                self.unsubscription_group_service_ext.list_unsubscribed_emails(
                    emails=[contact.email for contact in contacts if contact.email],
                    unsubscription_group_id=default_unsub_group.id,
                    organization_id=organization_id,
                )
            )
            default_pools_by_owner_ids_task = tg.create_task(
                self.email_account_pool_service.map_default_pools_by_owner_ids(
                    organization_id=organization_id,
                    owner_user_ids=list(user_ids_set),
                )
            )
            other_sequences_task = tg.create_task(
                self.sequence_query_service.map_sequences_by_id(
                    organization_id=organization_id,
                    only_include_sequence_ids=other_sequences_that_contacts_are_enrolled_in,
                )
            )

        unsubscribed_emails = unsubscribed_email_task.result()
        unsubscribed_emails_set = set(unsubscribed_emails)
        default_pools_by_owner_ids = default_pools_by_owner_ids_task.result()
        other_sequences = other_sequences_task.result()
        # Batch fetch email account pools associated with each respective user.
        user_id_to_email_account_pool_dto_map: dict[
            UUID, EmailAccountPoolDto | None
        ] = {}

        for owner_user_id in user_ids_set:
            default_pool_response = default_pools_by_owner_ids.get(owner_user_id, None)

            if not default_pool_response:
                continue

            pool_dto = await self.email_account_pool_service.get_email_account_pool_dto(
                organization_id=organization_id,
                email_account_pool_id=default_pool_response.id,
            )
            user_id_to_email_account_pool_dto_map[owner_user_id] = pool_dto

        preview_items: list[EnrollmentPreviewItem] = []

        eap_dtos = [
            user_id_to_email_account_pool_dto_map.get(owner_user_id)
            for owner_user_id in user_ids_set
        ]
        email_account_pool_dtos = [
            pool_dto for pool_dto in eap_dtos if pool_dto is not None
        ]
        mailbox_capacities_by_pool_id = (
            await self.email_account_pool_service.check_mailbox_capacities_for_pools(
                email_account_pool_dtos=email_account_pool_dtos,
                organization_id=organization_id,
            )
        )

        # Create preview items
        for contact in contacts:
            (
                account_id_to_use,
                email_to_use,
                failure_reasons,
            ) = await self._pick_account_and_email_to_use_v2(
                contact=contact,
                organization_id=organization_id,
            )

            # If there are failure reasons, add them to the preview
            if failure_reasons:
                reason = ", ".join(
                    [f"{failure_reason.value}" for failure_reason in failure_reasons]
                )
                preview_items.append(
                    EnrollmentPreviewItem(
                        contact_id=contact.contact_id,
                        email=email_to_use,
                        eligibility=SequenceEnrollmentEligibility.INELIGIBLE,
                        can_enroll=False,
                        display_name=contacts_to_display_name_map[contact.contact_id],
                        reason=reason,
                    )
                )
                continue

            enrollments_for_this_contact = contacts_enrollments.get(
                contact.contact_id, []
            )
            # Check if contact is enrolled in this sequence
            if contact.contact_id in enrollment_status_failed_map:
                reason = ", ".join(
                    [
                        f"{failure_reason.value}"
                        for failure_reason in enrollment_status_failed_map[
                            contact.contact_id
                        ]
                    ]
                )
                preview_items.append(
                    EnrollmentPreviewItem(
                        contact_id=contact.contact_id,
                        email=email_to_use,
                        eligibility=SequenceEnrollmentEligibility.INELIGIBLE,
                        can_enroll=False,
                        display_name=contacts_to_display_name_map[contact.contact_id],
                        reason=reason,
                    )
                )
                continue

            # Check if email account pool is available
            contact_v2_from_map: ContactV2 | None = contacts_map.get(contact.contact_id)
            if contact_v2_from_map is not None:
                owner_user_id_from_map: UUID | None = contact_to_owner_map.get(
                    contact.contact_id
                )
                if owner_user_id_from_map is not None:
                    email_account_pool_dto = user_id_to_email_account_pool_dto_map.get(
                        owner_user_id_from_map
                    )

                    if not email_account_pool_dto:
                        preview_items.append(
                            EnrollmentPreviewItem(
                                contact_id=contact.contact_id,
                                email=email_to_use,
                                eligibility=SequenceEnrollmentEligibility.INELIGIBLE,
                                can_enroll=False,
                                display_name=contacts_to_display_name_map[
                                    contact.contact_id
                                ],
                                reason="No email account pool available for contact's owner",
                            )
                        )
                        continue

                    mailbox_check_result = mailbox_capacities_by_pool_id[
                        email_account_pool_dto.email_account_pool.id
                    ]
                    if (
                        mailbox_check_result.over_capacity_email_accounts
                        and not mailbox_check_result.under_capacity_email_accounts
                    ):
                        reason = "All of your mailboxes are over capacity. Please configure or buy more mailboxes."
                        preview_items.append(
                            EnrollmentPreviewItem(
                                contact_id=contact.contact_id,
                                email=email_to_use,
                                eligibility=SequenceEnrollmentEligibility.INELIGIBLE,
                                can_enroll=False,
                                display_name=contacts_to_display_name_map[
                                    contact.contact_id
                                ],
                                reason=reason,
                            )
                        )
                        continue

                    warmed_up_email_account_ids = (
                        email_account_pool_dto.get_email_account_ids_fully_warm()
                    )
                    override_email_account_ids = (
                        email_account_pool_dto.get_email_account_ids_with_use_override()
                    )

                    has_usable_email_account = bool(
                        warmed_up_email_account_ids or override_email_account_ids
                    )

                    if not has_usable_email_account:
                        reason = "Your mailboxes are still warming up or have not been configured to override warmup! Once warmed, we will launch your sequence on your behalf."
                        preview_items.append(
                            EnrollmentPreviewItem(
                                contact_id=contact.contact_id,
                                email=email_to_use,
                                eligibility=SequenceEnrollmentEligibility.ELIGIBLE_WITH_WARNINGS,
                                can_enroll=True,
                                display_name=contacts_to_display_name_map[
                                    contact.contact_id
                                ],
                                reason=reason,
                            )
                        )
                        continue

            # Check if contact is unsubscribed
            if email_to_use in unsubscribed_emails_set:
                preview_items.append(
                    EnrollmentPreviewItem(
                        contact_id=contact.contact_id,
                        email=email_to_use,
                        eligibility=SequenceEnrollmentEligibility.ELIGIBLE_WITH_WARNINGS,
                        can_enroll=True,
                        display_name=contacts_to_display_name_map[contact.contact_id],
                        reason="Contact is unsubscribed",
                    )
                )
                continue

            # Check if contact is enrolled in other sequences
            if any(
                enrollment.sequence_id != sequence_id
                for enrollment in enrollments_for_this_contact
            ):
                other_seqs = [
                    other_sequences[enrollment.sequence_id]
                    for enrollment in enrollments_for_this_contact
                    if enrollment.sequence_id != sequence_id
                ]

                reason = f"Contact is already enrolled in other sequences: {', '.join([f'{seq.name}' for seq in other_seqs])}"
                preview_items.append(
                    EnrollmentPreviewItem(
                        contact_id=contact.contact_id,
                        email=email_to_use,
                        eligibility=SequenceEnrollmentEligibility.ELIGIBLE_WITH_WARNINGS,
                        can_enroll=True,
                        display_name=contacts_to_display_name_map[contact.contact_id],
                        reason=reason,
                    )
                )
                continue

            # contact is fully eligible
            preview_items.append(
                EnrollmentPreviewItem(
                    contact_id=contact.contact_id,
                    email=email_to_use,
                    eligibility=SequenceEnrollmentEligibility.ELIGIBLE,
                    can_enroll=True,
                    display_name=contacts_to_display_name_map[contact.contact_id],
                    reason="",
                )
            )

        return preview_items

    async def preview_sequence_enrollments_from_domain_object_list(
        self,
        domain_object_list_id: UUID,
        sequence_id: UUID,
        organization_id: UUID,
        user_id: UUID,
    ) -> list[EnrollmentPreviewItem]:
        list_items = (
            await self.domain_object_list_query_service.list_domain_object_list_items(
                organization_id=organization_id,
                list_id=domain_object_list_id,
            )
        )

        contact_ids = {item.data.reference_id for item in list_items}
        contact_for_sequence_enrollment_list = [
            ContactForSequenceEnrollment(
                contact_id=contact_id,
            )
            for contact_id in contact_ids
        ]

        return await self.preview_sequence_enrollments_from_contacts(
            contacts=contact_for_sequence_enrollment_list,
            sequence_id=sequence_id,
            organization_id=organization_id,
            user_id=user_id,
        )

    async def preview_sequence_enrollments(
        self,
        request: PreviewSequenceEnrollmentRequest,
        user_auth_context: UserAuthContext,
    ) -> PreviewSequenceEnrollmentResponse:
        logger.bind(
            request=request,
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
        ).info("Previewing sequence enrollments")

        sequence_access_dict = (
            await self.sequence_permission_service.can_access_sequence_by_ids_for_read(
                user_auth_context=user_auth_context,
                sequence_ids=[request.sequence_id],
            )
        )
        if not sequence_access_dict.get(request.sequence_id, False):
            raise ForbiddenError(
                "You do not have permission to access this Sequence enrollment"
            )

        preview_items: list[EnrollmentPreviewItem] = []

        if request.contacts:
            preview_items = await self.preview_sequence_enrollments_from_contacts(
                contacts=request.contacts,
                sequence_id=request.sequence_id,
                organization_id=user_auth_context.organization_id,
                user_id=user_auth_context.user_id,
            )
        elif request.domain_object_list_id:
            preview_items = (
                await self.preview_sequence_enrollments_from_domain_object_list(
                    domain_object_list_id=request.domain_object_list_id,
                    sequence_id=request.sequence_id,
                    organization_id=user_auth_context.organization_id,
                    user_id=user_auth_context.user_id,
                )
            )

        return PreviewSequenceEnrollmentResponse(
            sequence_id=request.sequence_id,
            preview=preview_items,
        )

    async def check_contact_enrollment_status_for_particular_sequence(
        self,
        contact_ids: set[UUID],
        sequence_id: UUID,
        organization_id: UUID,
    ) -> dict[UUID, list[SequenceFailureReason]]:
        """
        Check if contacts are already enrolled in the sequence and determine failure reasons.

        Args:
            contact_ids: Set of contact IDs to check
            sequence_id: The sequence ID to check enrollment against
            organization_id: The organization ID

        Returns:
            Dictionary mapping contact IDs to their failure reasons (if any)
        """
        # Check if contacts are already enrolled in this sequence
        enrolled_contacts_map = await self.sequence_enrollment_query_service.find_enrollments_by_contacts_for_sequence(
            contact_ids=contact_ids,
            sequence_id=sequence_id,
            organization_id=organization_id,
        )

        # Map contact IDs to failure reasons
        failure_reasons: dict[UUID, list[SequenceFailureReason]] = defaultdict(list)

        for contact_id, enrollment in enrolled_contacts_map.items():
            if enrollment.status == SequenceEnrollmentStatus.ACTIVE:
                failure_reasons[contact_id].append(
                    SequenceFailureReason.CONTACT_ALREADY_ENROLLED
                )
            elif enrollment.status == SequenceEnrollmentStatus.FAILED:
                failure_reasons[contact_id].append(
                    SequenceFailureReason.CONTACT_ALREADY_FAILED
                )
            elif enrollment.status == SequenceEnrollmentStatus.EXITED:
                failure_reasons[contact_id].append(
                    SequenceFailureReason.CONTACT_ALREADY_EXITED
                )
            # enrollment.status == SequenceEnrollmentStatus.REMOVED - new enrollment should proceed
            # enrollment.status == SequenceEnrollmentStatus.INACTIVE is still valid for enrollment

        return failure_reasons

    async def check_contact_enrollment_status_for_particular_sequence_v2(
        self,
        contact_ids: set[UUID],
        sequence_id: UUID,
        organization_id: UUID,
    ) -> dict[UUID, list[EnrollmentIneligibilityReason]]:
        """
        Check if contacts are already enrolled in the sequence and determine failure reasons.

        Args:
            contact_ids: Set of contact IDs to check
            sequence_id: The sequence ID to check enrollment against
            organization_id: The organization ID

        Returns:
            Dictionary mapping contact IDs to their failure reasons (if any)
        """
        # Check if contacts are already enrolled in this sequence
        enrolled_contacts_map = await self.sequence_enrollment_query_service.find_enrollments_by_contacts_for_sequence(
            contact_ids=contact_ids,
            sequence_id=sequence_id,
            organization_id=organization_id,
        )

        # Map contact IDs to failure reasons
        failure_reasons: dict[UUID, list[EnrollmentIneligibilityReason]] = defaultdict(
            list
        )

        for contact_id, enrollment in enrolled_contacts_map.items():
            if enrollment.status == SequenceEnrollmentStatus.ACTIVE:
                failure_reasons[contact_id].append(
                    EnrollmentIneligibilityReason.CONTACT_ALREADY_ENROLLED_IN_THIS_SEQUENCE
                )
            elif enrollment.status == SequenceEnrollmentStatus.FAILED:
                failure_reasons[contact_id].append(
                    EnrollmentIneligibilityReason.CONTACT_ALREADY_FAILED_THIS_SEQUENCE_PREVIOUSLY
                )
            elif enrollment.status == SequenceEnrollmentStatus.EXITED:
                failure_reasons[contact_id].append(
                    EnrollmentIneligibilityReason.CONTACT_ALREADY_EXITED_THIS_SEQUENCE_PREVIOUSLY
                )
            # enrollment.status == SequenceEnrollmentStatus.REMOVED - new enrollment should proceed
            # enrollment.status == SequenceEnrollmentStatus.INACTIVE is still valid for enrollment

        return failure_reasons

    async def proceed_sequence_enrollment(
        self, enrollment_id: UUID, user_auth_context: UserAuthContext
    ) -> ProceedEnrollment:
        if not (
            await self.sequence_permission_service.can_access_sequence_enrollment_by_ids_for_update(
                user_auth_context=user_auth_context,
                enrollment_ids=[enrollment_id],
            )
        ):
            raise ForbiddenError(
                "You do not have permission to access this Sequence enrollment"
            )

        # Get the enrollment to check workflow ID and current step
        enrollment = (
            await self.sequence_enrollment_repository.find_by_tenanted_primary_key(
                table_model=DbSequenceEnrollment,
                organization_id=user_auth_context.organization_id,
                id=enrollment_id,
            )
        )

        if (
            not enrollment
            or not enrollment.current_step_id
            or not enrollment.workflow_id
        ):
            raise ResourceNotFoundError(
                f"Sequence enrollment {enrollment_id} not found or missing required data"
            )

        # Check if the current step execution is in TASK_COMPLETED_WITH_PAUSED status
        step_execution = await self.sequence_step_repository.find_execution_by_step_id_and_enrollment_id(
            sequence_step_id=enrollment.current_step_id,
            sequence_enrollment_id=enrollment_id,
            organization_id=user_auth_context.organization_id,
        )

        if (
            not step_execution
            or step_execution.status
            != SequenceStepExecutionStatus.TASK_COMPLETED_WITH_PAUSED
        ):
            raise InvalidArgumentError(
                "Cannot proceed enrollment - current step is not in TASK_COMPLETED_WITH_PAUSED status"
            )

        # Get temporal client and resume the workflow
        client = await get_temporal_client()
        handle = client.get_workflow_handle(enrollment.workflow_id)
        await handle.signal(SequenceEnrollmentWorkflow.resume)

        return ProceedEnrollment(
            id=enrollment_id,
            status=enrollment.status,
        )

    async def signal_all_eligible_enrollments_for_sequence(
        self,
        sequence_id: UUID,
        organization_id: UUID,
        signal: SequenceSignal,
    ) -> None:
        if signal == SequenceSignal.RESUME:
            signal_function = SequenceEnrollmentWorkflow.resume
        elif signal == SequenceSignal.PAUSE:
            signal_function = SequenceEnrollmentWorkflow.pause
        else:
            # Cancel is not handled here, that is done through bulk termination
            logger.bind(
                signal=signal, sequence_id=sequence_id, organization_id=organization_id
            ).warning("Signal not supported")
            return

        # Find any active or pending enrollment
        # Enrollments we do not signal:
        # - Inactive enrollments were previously paused so don't include those (those must be resumed by user)
        # - Failed enrollments need resolution before user triggered retry
        # - Exited/removed: these are terminal states
        sequence_enrollments = await self.sequence_enrollment_repository.find_all_sequence_enrollments_by_sequence_id_and_status_list(
            organization_id=organization_id,
            sequence_id=sequence_id,
            status_list=[
                SequenceEnrollmentStatus.ACTIVE,
                SequenceEnrollmentStatus.PENDING,
            ],
        )
        if not sequence_enrollments:
            logger.bind(
                signal=signal, sequence_id=sequence_id, organization_id=organization_id
            ).info("No sequence enrollments found")
            return

        temporal_client = await get_temporal_client()
        for enrollment in sequence_enrollments:
            if not enrollment.workflow_id:
                logger.bind(
                    organization_id=organization_id,
                    sequence_id=sequence_id,
                    enrollment_id=enrollment.id,
                ).warning("Missing workflow ID, skipping")
                continue
            try:
                logger.bind(
                    organization_id=organization_id,
                    sequence_id=sequence_id,
                    enrollment_id=enrollment.id,
                    workflow_id=enrollment.workflow_id,
                    signal_value=signal,
                ).info("Signaling enrollment")
                handle = temporal_client.get_workflow_handle(
                    not_none(enrollment.workflow_id)
                )
                description = await handle.describe()
                if description.status == WorkflowExecutionStatus.RUNNING:
                    await handle.signal(signal_function)
                else:
                    logger.bind(
                        organization_id=organization_id,
                        sequence_id=sequence_id,
                        enrollment_id=enrollment.id,
                    ).info("Workflow is not running, skipping")
            except Exception as e:
                logger.bind(
                    organization_id=organization_id,
                    sequence_id=sequence_id,
                    enrollment_id=enrollment.id,
                ).error("Error signaling enrollment", exc_info=e)

    async def terminate_sequence_enrollment(
        self,
        *,
        enrollment: DbSequenceEnrollment,
        terminate_reason: SequenceEnrollmentExitReason,
        organization_id: UUID,
        end_enrollment_status: SequenceEnrollmentStatus,
        exited_by_reference_id: UUID | None,
        exited_by_reference_id_type: SequenceEnrollmentExitedByReferenceIdType | None,
        mark_as_deleted: bool = False,
        deleted_by_user_id: UUID | None = None,
        metadata: SequenceEnrollmentTerminationNotificationMetadata | None = None,
    ) -> DbSequenceEnrollment:
        is_already_terminated = (
            enrollment.status in SequenceEnrollmentStatus.get_terminated_statuses()
        )
        if is_already_terminated and not exited_by_reference_id:
            # Already over and nothing useful to record
            return enrollment

        return await self.execute_sequence_enrollment_termination(
            enrollment=enrollment,
            terminate_reason=terminate_reason,
            organization_id=organization_id,
            end_enrollment_status=end_enrollment_status,
            exited_by_reference_id=exited_by_reference_id,
            exited_by_reference_id_type=exited_by_reference_id_type,
            mark_as_deleted=mark_as_deleted,
            deleted_by_user_id=deleted_by_user_id,
            metadata=metadata,
        )

    async def execute_sequence_enrollment_termination(
        self,
        *,
        enrollment: DbSequenceEnrollment,
        terminate_reason: SequenceEnrollmentExitReason,
        organization_id: UUID,
        end_enrollment_status: SequenceEnrollmentStatus,
        exited_by_reference_id: UUID | None,
        exited_by_reference_id_type: SequenceEnrollmentExitedByReferenceIdType | None,
        mark_as_deleted: bool = False,
        deleted_by_user_id: UUID | None = None,
        metadata: SequenceEnrollmentTerminationNotificationMetadata | None = None,
    ) -> DbSequenceEnrollment:
        is_already_terminated = (
            enrollment.status in SequenceEnrollmentStatus.get_terminated_statuses()
        )

        now = zoned_utc_now()
        enrollment_fields_to_update = {
            "status": end_enrollment_status,
            "exited_by_reference_id": exited_by_reference_id,
            "exited_by_reference_id_type": exited_by_reference_id_type,
            "exited_reason": terminate_reason.map_to_sequence_enrollment_exit_reason_code(),
            "exited_at": now,
            "updated_at": now,
        }
        if mark_as_deleted:
            enrollment_fields_to_update["deleted_at"] = now
            enrollment_fields_to_update["deleted_by_user_id"] = deleted_by_user_id

        terminated_enrollment = not_none(
            await self.sequence_enrollment_repository.update_by_tenanted_primary_key(
                table_model=DbSequenceEnrollment,
                organization_id=organization_id,
                column_to_update=enrollment_fields_to_update,
                primary_key_to_value={"id": enrollment.id},
            )
        )

        if not is_already_terminated:
            # If transitioning to a terminal state for the first time - clean up
            if terminated_enrollment.current_step_id:
                terminated_execution = await self.sequence_execution_repository.terminate_step_execution_by_step_id_and_enrollment_id(
                    sequence_step_id=terminated_enrollment.current_step_id,
                    sequence_enrollment_id=terminated_enrollment.id,
                    organization_id=organization_id,
                )
                if terminated_execution and terminated_execution.global_message_id:
                    try:
                        await self.message_service.cancel_scheduled_message_by_global_message_id(
                            organization_id=organization_id,
                            global_message_id=terminated_execution.global_message_id,
                        )
                        logger.bind(
                            enrollment_id=enrollment.id,
                            step_id=enrollment.current_step_id,
                            organization_id=organization_id,
                            global_message_id=terminated_execution.global_message_id,
                        ).info("Cancelled scheduled message for sequence enrollment")
                    except Exception as e:
                        logger.bind(
                            enrollment_id=enrollment.id,
                            step_id=enrollment.current_step_id,
                            organization_id=organization_id,
                            global_message_id=terminated_execution.global_message_id,
                        ).error("Failed to cancel scheduled message", exc_info=e)

            if terminated_enrollment.workflow_id:
                await self._terminate_sequence_enrollment_workflow(
                    enrollment=enrollment,
                    terminate_reason=terminate_reason,
                )

            # Do this after workflow termination, so that we don't have workflow stuff happening due to task updates
            await self._terminate_open_tasks(enrollment=terminated_enrollment)

        if terminate_reason != SequenceEnrollmentExitReason.USER_TERMINATED:
            try:
                await self._notify_user_for_sequence_enrollment_conversion(
                    enrollment=terminated_enrollment,
                    terminate_reason=terminate_reason,
                    metadata=metadata,
                )
            except Exception as e:
                logger.bind(
                    enrollment_id=enrollment.id,
                    organization_id=organization_id,
                ).error(
                    "Failed to notify user for sequence enrollment termination",
                    exc_info=e,
                )

        return terminated_enrollment

    async def _terminate_open_tasks(self, enrollment: DbSequenceEnrollment) -> None:
        ref_id_to_tasks = (
            await self.task_query_service.get_tasks_by_reference_ids_and_type(
                reference_ids=[enrollment.id],
                reference_id_type=TaskReferenceIdType.SEQUENCE_ENROLLMENT_ID,
                organization_id=enrollment.organization_id,
                status_in=[TaskStatus.OPEN, TaskStatus.IN_PROGRESS, TaskStatus.BLOCKED],
            )
        )
        for tasks in ref_id_to_tasks.values():
            for task in tasks:
                await self.task_v2_service.update_entity(
                    organization_id=enrollment.organization_id,
                    user_id=enrollment.enrolled_by_user_id,
                    entity=task,
                    request=PatchTaskRequest(
                        status=TaskStatus.COMPLETED,
                    ),
                )

    def _get_user_facing_exited_with(
        self,
        enrollment: DbSequenceEnrollment,
        terminate_reason: SequenceEnrollmentExitReason,
    ) -> str | None:
        """
        Get summary that goes here:
        "A contact (<display name>) has exited the "<name>" sequence by <summary>."

        If the reason cannot be determined, then None is returned.
        """
        if (
            enrollment.exited_by_reference_id_type
            == SequenceEnrollmentExitedByReferenceIdType.MEETING
        ):
            return "booking a meeting"
        elif (
            enrollment.exited_by_reference_id_type
            == SequenceEnrollmentExitedByReferenceIdType.GLOBAL_MESSAGE
            and terminate_reason == SequenceEnrollmentExitReason.EMAIL_REPLIED
        ):
            return "replying to an email"
        else:
            return None

    def _populate_notification_metadata(
        self,
        notification_data: NotificationSequenceEnrollmentTerminationData,
        metadata: SequenceEnrollmentTerminationNotificationMetadata,
    ) -> None:
        if metadata.type == EnrollmentTerminationMetadataType.MEETING:
            notification_data.meeting_id = str(metadata.meeting_id)
        elif metadata.type == EnrollmentTerminationMetadataType.GLOBAL_MESSAGE:
            notification_data.global_message_id = str(metadata.global_message_id)
            notification_data.global_thread_id = (
                str(metadata.global_thread_id) if metadata.global_thread_id else None
            )

    async def _notify_user_for_sequence_enrollment_conversion(
        self,
        enrollment: DbSequenceEnrollment,
        terminate_reason: SequenceEnrollmentExitReason,
        metadata: SequenceEnrollmentTerminationNotificationMetadata | None = None,
    ) -> None:
        exited_by_summary = self._get_user_facing_exited_with(
            enrollment=enrollment, terminate_reason=terminate_reason
        )
        if not exited_by_summary:
            return

        sequence = await self.sequence_query_service.get_sequence_by_id(
            sequence_id=enrollment.sequence_id,
            organization_id=enrollment.organization_id,
        )

        contact = not_none(
            await self.contact_query_service.get_by_id(
                contact_id=enrollment.contact_id,
                organization_id=enrollment.organization_id,
            )
        )

        notification_data = NotificationSequenceEnrollmentTerminationData(
            contact_id=str(enrollment.contact_id),
            sequence_id=str(enrollment.sequence_id),
            contact_display_name=contact.display_name,
            sequence_name=sequence.name,
            exited_by_summary=exited_by_summary,
            account_id=str(enrollment.account_id) if enrollment.account_id else None,
        )
        if metadata:
            self._populate_notification_metadata(
                notification_data=notification_data, metadata=metadata
            )

        await self.notification_service.send_notification(
            send_notification_request=SendNotificationRequest(
                data=notification_data,
                reference_id=str(enrollment.sequence_id),
                reference_id_type=NotificationReferenceIdType.SEQUENCE,
                activity_id=None,
                actor_user_id=enrollment.enrolled_by_user_id,
                recipient_user_ids=[enrollment.enrolled_by_user_id],
                idempotency_key=str(enrollment.id),
            ),
            organization_id=enrollment.organization_id,
        )

    async def _terminate_sequence_enrollment_workflow(
        self,
        enrollment: DbSequenceEnrollment,
        terminate_reason: SequenceEnrollmentExitReason,
    ) -> None:
        # Return early if no workflow_id is provided
        if not enrollment.workflow_id:
            return

        # Get the Temporal client and workflow handle
        temporal_client = await get_temporal_client()
        wf_handle = temporal_client.get_workflow_handle(enrollment.workflow_id)

        try:
            # Get the workflow's current status
            description = await wf_handle.describe()
            if description.status == WorkflowExecutionStatus.RUNNING:
                await wf_handle.terminate(reason=terminate_reason)
            else:
                logger.bind(
                    workflow_id=enrollment.workflow_id,
                    status=description.status,
                ).info("Workflow is not running, skipping termination")
        except Exception as e:
            # Log any other errors during the describe operation
            logger.error(
                f"Failed to describe workflow {enrollment.workflow_id}", exc_info=e
            )

    async def list_sequence_enrollment_runs(
        self, sequence_id: UUID, organization_id: UUID
    ) -> list[SequenceEnrollmentRun]:
        runs = await self.sequence_enrollment_repository.list_sequence_enrollment_runs_by_sequence_id(
            organization_id=organization_id,
            sequence_id=sequence_id,
        )

        return [SequenceEnrollmentRun.from_db(run) for run in runs]

    async def create_sequence_enrollment_run_and_enrollments(
        self,
        user_id: UUID,
        organization_id: UUID,
        sequence_id: UUID,
        contacts: list[ContactForSequenceEnrollment] | None = None,
        domain_object_list_id: UUID | None = None,
        bypass_warnings: bool = False,
        enrollment_run_id: UUID | None = None,
    ) -> CreateSequenceEnrollmentResponseV2:
        if contacts:
            return (
                await self.create_sequence_enrollment_run_and_enrollments_from_contacts(
                    user_id=user_id,
                    organization_id=organization_id,
                    contacts=contacts,
                    sequence_id=sequence_id,
                    bypass_warnings=bypass_warnings,
                    enrollment_run_id=enrollment_run_id,
                )
            )

        elif domain_object_list_id:
            return await self.create_sequence_enrollment_run_and_enrollments_from_domain_object_list(
                user_id=user_id,
                organization_id=organization_id,
                domain_object_list_id=domain_object_list_id,
                sequence_id=sequence_id,
                bypass_warnings=bypass_warnings,
                enrollment_run_id=enrollment_run_id,
            )
        else:
            raise ValueError("No contacts or domain object list id provided")

    async def create_enrollments_and_run_async(
        self,
        user_auth_context: UserAuthContext,
        request: CreateSequenceEnrollmentRequest,
    ) -> CreateSequenceEnrollmentResponseV2:
        can_access_sequence = await self.can_access_sequence_by_id_for_update(
            user_auth_context=user_auth_context,
            sequence_id=request.sequence_id,
        )
        if not can_access_sequence:
            raise ForbiddenError("You do not have permission to access this sequence")

        if request.contacts:
            num_contacts_to_add = len(request.contacts)
        elif request.domain_object_list_id:
            num_contacts_to_add = len(
                await self.domain_object_list_query_service.list_domain_object_list_items(
                    organization_id=user_auth_context.organization_id,
                    list_id=request.domain_object_list_id,
                )
            )
        else:
            raise InvalidArgumentError("No contacts or domain object list id provided")

        if num_contacts_to_add > MAX_SUPPORTED_CONTACTS_SIZE:
            raise InvalidArgumentError(
                f"Too many contacts added at once, please try again with {MAX_SUPPORTED_CONTACTS_SIZE} or less",
            )

        existing_enrollments_count = await self.sequence_enrollment_repository.find_count_of_sequence_enrollments_by_sequence_id_and_status(
            sequence_id=request.sequence_id,
            organization_id=user_auth_context.organization_id,
            status=SequenceEnrollmentStatus.ACTIVE,
        )
        if (
            existing_enrollments_count + num_contacts_to_add
            > MAX_SUPPORTED_CONTACTS_SIZE
        ):
            raise InvalidArgumentError(
                f"Too many contacts added to this sequence, either add less or create a new sequence (max {MAX_SUPPORTED_CONTACTS_SIZE} contacts allowed)",
            )

        enrollment_run_id = uuid4()
        wf_id = f"sequence-enrollment-run-{enrollment_run_id}"

        client = await get_temporal_client()
        await client.start_workflow(
            SequenceEnrollmentRunWorkflow.run,
            id=wf_id,
            args=[
                SequenceEnrollmentRunWorkflowInput(
                    contacts=request.contacts,
                    domain_object_list_id=request.domain_object_list_id,
                    sequence_id=request.sequence_id,
                    bypass_warnings=request.bypass_warnings,
                    enrollment_run_id=enrollment_run_id,
                    user_id=user_auth_context.user_id,
                    organization_id=user_auth_context.organization_id,
                )
            ],
            task_queue=TemporalTaskQueue.SEQUENCE_TASK_QUEUE,
        )
        return CreateSequenceEnrollmentResponseV2(
            message="Async enrollment run started",
            enrollment_run_id=enrollment_run_id,
            enrollment_contacts=[],
        )

    async def create_sequence_enrollment_run_and_enrollments_from_domain_object_list(
        self,
        user_id: UUID,
        organization_id: UUID,
        domain_object_list_id: UUID,
        sequence_id: UUID,
        bypass_warnings: bool = False,
        enrollment_run_id: UUID | None = None,
    ) -> CreateSequenceEnrollmentResponseV2:
        logger.bind(
            domain_object_list_id=domain_object_list_id,
            sequence_id=sequence_id,
            organization_id=organization_id,
            user_id=user_id,
            enrollment_run_id=enrollment_run_id,
        ).info("Creating sequence enrollment V2 from domain object list")

        list_items = (
            await self.domain_object_list_query_service.list_domain_object_list_items(
                organization_id=organization_id,
                list_id=domain_object_list_id,
            )
        )

        if len(list_items) > MAX_SUPPORTED_CONTACTS_SIZE:
            raise InvalidArgumentError(
                f"Too many contacts added at once, please try again with {MAX_SUPPORTED_CONTACTS_SIZE} or less",
            )

        existing_enrollments_count = await self.sequence_enrollment_repository.find_count_of_sequence_enrollments_by_sequence_id_and_status(
            sequence_id=sequence_id,
            organization_id=organization_id,
            status=SequenceEnrollmentStatus.ACTIVE,
        )
        if existing_enrollments_count + len(list_items) > MAX_SUPPORTED_CONTACTS_SIZE:
            raise InvalidArgumentError(
                f"Too many contacts added to this sequence, either add less or create a new sequence (max {MAX_SUPPORTED_CONTACTS_SIZE} contacts allowed)",
            )

        contact_ids = {item.data.reference_id for item in list_items}
        contact_for_sequence_enrollment_list = [
            ContactForSequenceEnrollment(
                contact_id=contact_id,
            )
            for contact_id in contact_ids
        ]

        return await self.create_sequence_enrollment_run_and_enrollments_from_contacts(
            user_id=user_id,
            organization_id=organization_id,
            contacts=contact_for_sequence_enrollment_list,
            sequence_id=sequence_id,
            bypass_warnings=bypass_warnings,
            enrollment_run_id=enrollment_run_id,
        )

    async def create_sequence_enrollment_run_and_enrollments_from_contacts(  # noqa: C901, PLR0912, PLR0915
        self,
        user_id: UUID,
        organization_id: UUID,
        contacts: list[ContactForSequenceEnrollment],
        sequence_id: UUID,
        bypass_warnings: bool = False,
        enrollment_run_id: UUID | None = None,
    ) -> CreateSequenceEnrollmentResponseV2:
        logger.bind(
            contact_count=len(contacts),
            sequence_id=sequence_id,
            organization_id=organization_id,
            user_id=user_id,
            enrollment_run_id=enrollment_run_id,
        ).info("Creating sequence enrollment V2 from contacts")

        if len(contacts) > MAX_SUPPORTED_CONTACTS_SIZE:
            raise InvalidArgumentError(
                f"Too many contacts added at once, please try again with {MAX_SUPPORTED_CONTACTS_SIZE} or less",
            )

        existing_enrollments_count = await self.sequence_enrollment_repository.find_count_of_sequence_enrollments_by_sequence_id_and_status(
            sequence_id=sequence_id,
            organization_id=organization_id,
            status=SequenceEnrollmentStatus.ACTIVE,
        )
        if existing_enrollments_count + len(contacts) > MAX_SUPPORTED_CONTACTS_SIZE:
            raise InvalidArgumentError(
                f"Too many contacts added to this sequence, either add less or create a new sequence (max {MAX_SUPPORTED_CONTACTS_SIZE} contacts allowed)",
            )

        enrollment_contacts: list[DBSequenceEnrollmentContact] = []
        enrollments: list[DbSequenceEnrollment] = []

        contact_ids = {contact.contact_id for contact in contacts}

        if enrollment_run_id:
            db_enrollment_run = await self.sequence_enrollment_repository.insert(
                instance=DBSequenceEnrollmentRun(
                    id=enrollment_run_id,
                    organization_id=organization_id,
                    sequence_id=sequence_id,
                    mode=SequenceEnrollmentRunMode.ASYNC,
                    workflow_id=f"sequence-enrollment-run-{enrollment_run_id}",
                    status=SequenceEnrollmentRunStatus.IN_PROGRESS,
                    created_by_user_id=user_id,
                    created_at=zoned_utc_now(),
                ),
            )
        else:
            db_enrollment_run = await self.sequence_enrollment_repository.insert(
                instance=DBSequenceEnrollmentRun(
                    id=uuid4(),
                    organization_id=organization_id,
                    sequence_id=sequence_id,
                    mode=SequenceEnrollmentRunMode.SYNC,
                    workflow_id=None,
                    status=SequenceEnrollmentRunStatus.IN_PROGRESS,
                    created_by_user_id=user_id,
                    created_at=zoned_utc_now(),
                ),
            )

        async with TaskGroup() as tg:
            resolve_task = tg.create_task(
                self.resolve_and_fill_sequence_enrollment_v2(
                    organization_id=organization_id,
                    contacts=contacts,
                )
            )
            contacts_task = tg.create_task(
                self.contact_query_service.list_contacts_v2(
                    organization_id=organization_id,
                    only_include_contact_ids=contact_ids,
                )
            )
            contacts_enrollments_task = tg.create_task(
                self.sequence_enrollment_query_service.get_contacts_enrollments(
                    contact_ids=contact_ids,
                    organization_id=organization_id,
                )
            )
            enrollments_status_map_task = tg.create_task(
                self.check_contact_enrollment_status_for_particular_sequence_v2(
                    contact_ids=contact_ids,
                    sequence_id=sequence_id,
                    organization_id=organization_id,
                )
            )
            default_unsub_group_task = tg.create_task(
                self.unsubscription_group_service_ext.get_default_unsub_group_for_org(
                    organization_id=organization_id,
                )
            )

        _ = resolve_task.result()
        contacts_v2 = contacts_task.result()
        contacts_enrollments = contacts_enrollments_task.result()
        enrollments_status_map = enrollments_status_map_task.result()
        default_unsub_group = default_unsub_group_task.result()

        contacts_map = {contact.id: contact for contact in contacts_v2}
        contacts_display_name_map = {
            contact.id: contact.display_name for contact in contacts_v2
        }

        # Gather all user_ids that need email account pools
        user_ids_set = set()
        contact_to_owner_map: dict[UUID, UUID] = {}

        for contact_id, contact_v2 in contacts_map.items():
            owner_user_id = contact_v2.owner_user_id or user_id
            if owner_user_id:
                user_ids_set.add(owner_user_id)
                contact_to_owner_map[contact_id] = owner_user_id

        # Gather all other sequence ids that contacts are enrolled in
        other_sequences_that_contacts_are_enrolled_in = set()
        for contact in contacts:
            enrollments_for_this_contact = contacts_enrollments.get(
                contact.contact_id, []
            )
            other_sequences_that_contacts_are_enrolled_in.update(
                {
                    enrollment.sequence_id
                    for enrollment in enrollments_for_this_contact
                    if enrollment.sequence_id != sequence_id
                }
            )

        async with TaskGroup() as tg:
            unsubscribed_email_task = tg.create_task(
                self.unsubscription_group_service_ext.list_unsubscribed_emails(
                    emails=[contact.email for contact in contacts if contact.email],
                    unsubscription_group_id=default_unsub_group.id,
                    organization_id=organization_id,
                )
            )
            default_pools_by_owner_ids_task = tg.create_task(
                self.email_account_pool_service.map_default_pools_by_owner_ids(
                    organization_id=organization_id,
                    owner_user_ids=list(user_ids_set),
                )
            )

        unsubscribed_emails = unsubscribed_email_task.result()
        unsubscribed_emails_set = set(unsubscribed_emails)
        default_pools_by_owner_ids = default_pools_by_owner_ids_task.result()

        # Batch fetch email account pools associated with each respective user.
        user_id_to_email_account_pool_dto_map: dict[
            UUID, EmailAccountPoolDto | None
        ] = {}

        for owner_user_id in user_ids_set:
            default_pool_response = default_pools_by_owner_ids.get(owner_user_id, None)

            if not default_pool_response:
                continue

            pool_dto = await self.email_account_pool_service.get_email_account_pool_dto(
                organization_id=organization_id,
                email_account_pool_id=default_pool_response.id,
            )
            user_id_to_email_account_pool_dto_map[owner_user_id] = pool_dto

        eap_dtos = [
            user_id_to_email_account_pool_dto_map.get(owner_user_id)
            for owner_user_id in user_ids_set
        ]
        email_account_pool_dtos = [
            pool_dto for pool_dto in eap_dtos if pool_dto is not None
        ]
        mailbox_capacities_by_pool_id = (
            await self.email_account_pool_service.check_mailbox_capacities_for_pools(
                email_account_pool_dtos=email_account_pool_dtos,
                organization_id=organization_id,
            )
        )

        # Process each contact to determine eligibility
        for contact in contacts:
            warning_reasons: list[EnrollmentWarningReason] = []
            ineligibility_reasons: list[EnrollmentIneligibilityReason] = []

            (
                account_id_to_use,
                email_to_use,
                failure_reasons,
            ) = await self._pick_account_and_email_to_use_v3(
                contact=contact,
                organization_id=organization_id,
            )

            # If there are failure reasons, add them to ineligible contacts
            ineligibility_reasons.extend(failure_reasons)

            # Check if contact is enrolled/failed/exited in this sequence
            ineligibility_reasons.extend(
                enrollments_status_map.get(contact.contact_id, [])
            )

            # Check if email account pool is available
            contact_v2_from_map = contacts_map.get(contact.contact_id)
            if contact_v2_from_map is not None:
                owner_user_id_from_map = contact_to_owner_map.get(contact.contact_id)
                if owner_user_id_from_map is not None:
                    email_account_pool_dto = user_id_to_email_account_pool_dto_map.get(
                        owner_user_id_from_map
                    )

                    if not email_account_pool_dto:
                        ineligibility_reasons.append(
                            EnrollmentIneligibilityReason.CONTACT_DOES_NOT_HAVE_A_EMAIL_POOL_AVAILABLE
                        )

                    else:
                        mailbox_check_result = mailbox_capacities_by_pool_id[
                            email_account_pool_dto.email_account_pool.id
                        ]
                        if (
                            mailbox_check_result.over_capacity_email_accounts
                            and not mailbox_check_result.under_capacity_email_accounts
                        ):
                            ineligibility_reasons.append(
                                EnrollmentIneligibilityReason.ALL_MAILBOXES_IN_POOL_ARE_AT_OVER_CAPACITY
                            )

                        warmed_up_email_account_ids = (
                            email_account_pool_dto.get_email_account_ids_fully_warm()
                        )
                        override_email_account_ids = email_account_pool_dto.get_email_account_ids_with_use_override()

                        has_usable_email_account = bool(
                            warmed_up_email_account_ids or override_email_account_ids
                        )

                        if not has_usable_email_account and not bypass_warnings:
                            warning_reasons.append(
                                EnrollmentWarningReason.MAILBOXES_ARE_NOT_WARMED_UP
                            )

            # Check if contact is unsubscribed
            if email_to_use in unsubscribed_emails_set and not bypass_warnings:
                warning_reasons.append(EnrollmentWarningReason.CONTACT_IS_UNSUBSCRIBED)

            # Check if contact is enrolled in other sequences
            enrollments_for_this_contact = contacts_enrollments.get(
                contact.contact_id, []
            )
            if (
                any(
                    enrollment.sequence_id != sequence_id
                    for enrollment in enrollments_for_this_contact
                )
                and not bypass_warnings
            ):
                warning_reasons.append(
                    EnrollmentWarningReason.CONTACT_IS_ENROLLED_IN_OTHER_SEQUENCES
                )

            # Add to persistence lists for the contact
            if ineligibility_reasons:
                status = SequenceEnrollmentContactStatus.FAILED
            elif warning_reasons:
                status = SequenceEnrollmentContactStatus.WARNING
            else:
                status = SequenceEnrollmentContactStatus.ENROLLED

            enrollment_contacts.append(
                DBSequenceEnrollmentContact(
                    id=uuid4(),
                    organization_id=organization_id,
                    enrollment_run_id=db_enrollment_run.id,
                    contact_id=contact.contact_id,
                    account_id=account_id_to_use,
                    email=email_to_use,
                    status=status,
                    warning_reasons=[wr.value for wr in warning_reasons],
                    fail_reasons=[ir.value for ir in ineligibility_reasons],
                    description=None,
                )
            )

            if not ineligibility_reasons and (bypass_warnings or not warning_reasons):
                enrollments.append(
                    DbSequenceEnrollment(
                        id=uuid4(),
                        organization_id=organization_id,
                        sequence_id=sequence_id,
                        email_account_pool_id=email_account_pool_dto.email_account_pool.id
                        if email_account_pool_dto
                        else None,
                        contact_id=contact.contact_id,
                        account_id=account_id_to_use,
                        email=email_to_use,
                        status=SequenceEnrollmentStatus.ACTIVE,
                        enrolled_at=zoned_utc_now(),
                        enrolled_by_user_id=user_id,
                        updated_at=zoned_utc_now(),
                        updated_by_user_id=user_id,
                    )
                )

        # Persist enrollments and enrollment contacts
        if enrollments:
            db_sequence_enrollments = (
                await self.sequence_enrollment_repository.bulk_insert(
                    table_model=DbSequenceEnrollment,
                    instances=enrollments,
                )
            )

            # Create domain_crm_association records for each enrollment
            if db_sequence_enrollments:
                # Use bulk creation method from service
                sequence_enrollment_ids: list[UUID] = [
                    enrollment.id for enrollment in db_sequence_enrollments
                ]
                enrolled_contact_ids = [
                    enrollment.contact_id for enrollment in db_sequence_enrollments
                ]

                domain_crm_associations = [
                    CreateSequenceCrmAssociation(
                        sequence_enrollment_id=sequence_enrollment_id,
                        sequence_id=sequence_id,
                        contact_id=enrolled_contact_id,
                        organization_id=organization_id,
                        created_by_user_id=user_id,
                    )
                    for sequence_enrollment_id, enrolled_contact_id in zip(
                        sequence_enrollment_ids, enrolled_contact_ids, strict=False
                    )
                ]

                await self.domain_crm_association_service.bulk_create_domain_crm_associations(
                    domain_crm_associations=domain_crm_associations
                )

        if enrollment_contacts:
            await self.sequence_enrollment_repository.bulk_insert(
                table_model=DBSequenceEnrollmentContact,
                instances=enrollment_contacts,
            )

        # update enrollment run status to completed
        await self.sequence_enrollment_repository.update_by_tenanted_primary_key(
            table_model=DBSequenceEnrollmentRun,
            primary_key_to_value={"id": db_enrollment_run.id},
            organization_id=organization_id,
            column_to_update={"status": SequenceEnrollmentRunStatus.COMPLETED},
        )

        return CreateSequenceEnrollmentResponseV2(
            message="Sync enrollment run completed",
            enrollment_run_id=db_enrollment_run.id,
            enrollment_contacts=[
                SequenceEnrollmentContact.from_db_model(
                    ec, contacts_display_name_map.get(ec.contact_id)
                )
                for ec in enrollment_contacts
            ],
        )

    async def bulk_reenroll_contacts_by_run_id(
        self,
        user_auth_context: UserAuthContext,
        sequence_id: UUID,
        enrollment_run_id: UUID,
        include_prev_warnings: bool = False,
        include_prev_failures: bool = False,
        bypass_warnings: bool = False,
        mode: SequenceEnrollmentRunMode = SequenceEnrollmentRunMode.SYNC,
    ) -> CreateSequenceEnrollmentResponseV2:
        contacts_to_reenroll: list[ContactForSequenceEnrollment] = []

        if include_prev_warnings:
            warning_contacts = await self.sequence_enrollment_repository.find_enrollment_contacts_by_enrollment_run_id_and_status(
                organization_id=user_auth_context.organization_id,
                enrollment_run_id=enrollment_run_id,
                status=SequenceEnrollmentContactStatus.WARNING,
            )
            contacts_to_reenroll.extend(
                ContactForSequenceEnrollment(contact_id=wc.contact_id)
                for wc in warning_contacts
            )

        if include_prev_failures:
            failure_contacts = await self.sequence_enrollment_repository.find_enrollment_contacts_by_enrollment_run_id_and_status(
                organization_id=user_auth_context.organization_id,
                enrollment_run_id=enrollment_run_id,
                status=SequenceEnrollmentContactStatus.FAILED,
            )
            contacts_to_reenroll.extend(
                ContactForSequenceEnrollment(contact_id=fc.contact_id)
                for fc in failure_contacts
            )

        if not contacts_to_reenroll:
            return CreateSequenceEnrollmentResponseV2(
                message="No contacts to reenroll",
                enrollment_run_id=enrollment_run_id,
                enrollment_contacts=[],
            )

        if mode == SequenceEnrollmentRunMode.SYNC:
            response = (
                await self.create_sequence_enrollment_run_and_enrollments_from_contacts(
                    user_id=user_auth_context.user_id,
                    organization_id=user_auth_context.organization_id,
                    sequence_id=sequence_id,
                    contacts=contacts_to_reenroll,
                    bypass_warnings=bypass_warnings,
                )
            )
        elif mode == SequenceEnrollmentRunMode.ASYNC:
            response = await self.create_enrollments_and_run_async(
                user_auth_context=user_auth_context,
                request=CreateSequenceEnrollmentRequest(
                    sequence_id=sequence_id,
                    contacts=contacts_to_reenroll,
                    bypass_warnings=bypass_warnings,
                ),
            )

        return response

    async def bulk_reenroll_enrollments(
        self,
        user_auth_context: UserAuthContext,
        enrollment_ids: list[UUID],
        include_non_completed: bool,
    ) -> CreateSequenceEnrollmentResponseV2:
        """
        Bulk reenroll contacts from a list of enrollment IDs.

        Args:
            user_auth_context: The user auth context
            enrollment_ids: List of enrollment IDs to reenroll
            include_non_completed: Whether to include non-completed enrollments (ACTIVE, INACTIVE, FAILED)

        Returns:
            Response with enrollment details
        """
        if not enrollment_ids:
            raise InvalidArgumentError("No enrollment IDs provided")

        if not (
            await self.sequence_permission_service.can_access_sequence_enrollment_by_ids_for_update(
                user_auth_context=user_auth_context,
                enrollment_ids=enrollment_ids,
            )
        ):
            raise ForbiddenError(
                "You do not have permission to access these sequence enrollments"
            )

        # Fetch all enrollments first to determine if they're all from the same sequence
        enrollments = await self.sequence_enrollment_repository.find_all_sequence_enrollments_by_ids(
            organization_id=user_auth_context.organization_id,
            enrollment_ids=enrollment_ids,
        )

        if not enrollments:
            raise InvalidArgumentError("No enrollments found for the provided IDs")

        # Check that all enrollments belong to the same sequence
        sequence_ids = {enrollment.sequence_id for enrollment in enrollments}
        if len(sequence_ids) > 1:
            raise InvalidArgumentError(
                "All enrollments must belong to the same sequence"
            )

        sequence_id = sequence_ids.pop()

        enrollments_to_process = []
        for enrollment in enrollments:
            if (
                enrollment.status != SequenceEnrollmentStatus.EXITED
                and not include_non_completed
            ):
                # Check if the enrollment is already exited
                continue
            try:
                if not enrollment.deleted_at:
                    terminated_enrollment = await self.execute_sequence_enrollment_termination(
                        enrollment=enrollment,
                        terminate_reason=SequenceEnrollmentExitReason.USER_TERMINATED,
                        organization_id=enrollment.organization_id,
                        end_enrollment_status=SequenceEnrollmentStatus.REMOVED,
                        exited_by_reference_id=None,
                        exited_by_reference_id_type=None,
                    )
                    enrollments_to_process.append(terminated_enrollment)
            except Exception as e:
                logger.error("Error deleting enrollment", exc_info=e)
                continue

        if not enrollments_to_process:
            return CreateSequenceEnrollmentResponseV2(
                message="No eligible enrollments to reenroll",
                enrollment_run_id=None,
                enrollment_contacts=[],
            )

        # Create contacts for enrollments to be reenrolled
        contacts_to_reenroll = [
            ContactForSequenceEnrollment(
                contact_id=enrollment.contact_id,
                account_id=enrollment.account_id,
                email=enrollment.email,
            )
            for enrollment in enrollments_to_process
        ]

        # Create new enrollments
        return await self.create_sequence_enrollment_run_and_enrollments_from_contacts(
            user_id=user_auth_context.user_id,
            organization_id=user_auth_context.organization_id,
            sequence_id=sequence_id,
            contacts=contacts_to_reenroll,
            bypass_warnings=True,  # We're explicitly re-enrolling, so bypass warnings
        )

    async def patch_enrollment_email(
        self, organization_id: UUID, user_id: UUID, enrollment_id: UUID, email: str
    ) -> PatchEnrollmentEmailResponse:
        # Get the current enrollment to access its current email
        enrollment = (
            await self.sequence_enrollment_repository.find_by_tenanted_primary_key(
                table_model=DbSequenceEnrollment,
                organization_id=organization_id,
                id=enrollment_id,
            )
        )

        if not enrollment:
            raise ResourceNotFoundError(
                f"Sequence enrollment with id {enrollment_id} not found"
            )

        # Check if any emails have already been sent for this enrollment
        sent_executions = await self.sequence_execution_repository.find_completed_email_step_executions_by_enrollment_id(
            organization_id=organization_id,
            enrollment_id=enrollment_id,
        )

        if sent_executions:
            raise InvalidArgumentError(
                "Cannot change email because the sequence has already used the current email to engage contact(s)"
            )

        # Find any scheduled email step executions
        scheduled_executions = await self.sequence_execution_repository.find_active_email_step_executions_by_enrollment_id(
            organization_id=organization_id,
            enrollment_id=enrollment_id,
        )

        # Get all global_message_ids from the scheduled executions
        global_message_ids = [
            execution.global_message_id
            for execution in scheduled_executions
            if execution.global_message_id
        ]
        if not global_message_ids:
            await self.sequence_enrollment_repository.update_by_tenanted_primary_key(
                DbSequenceEnrollment,
                organization_id=organization_id,
                primary_key_to_value={"id": enrollment_id},
                column_to_update={"email": email},
            )
            return PatchEnrollmentEmailResponse(
                message="Email updated successfully",
                enrollment_id=enrollment_id,
                new_email=email,
            )

        # Get the message associations for all global_message_ids
        message_associations = (
            await self.thread_repository.map_message_associations_by_global_message_ids(
                global_message_ids=global_message_ids,
                organization_id=organization_id,
            )
        )

        # Get the messages for all message_ids
        message_ids = [
            assoc.message_id
            for assocs in message_associations.values()
            for assoc in assocs
        ]
        if not message_ids:
            await self.sequence_enrollment_repository.update_by_tenanted_primary_key(
                DbSequenceEnrollment,
                organization_id=organization_id,
                primary_key_to_value={"id": enrollment_id},
                column_to_update={"email": email},
            )
            return PatchEnrollmentEmailResponse(
                message="Email updated successfully",
                enrollment_id=enrollment_id,
                new_email=email,
            )

        messages = await self.thread_repository.map_messages_by_id(
            message_ids=message_ids,
            organization_id=organization_id,
        )

        await self.sequence_enrollment_repository.patch_enrollment_email(
            organization_id=organization_id,
            enrollment_id=enrollment_id,
            email=email,
            scheduled_executions=scheduled_executions,
            messages=messages,
            message_associations=message_associations,
        )

        return PatchEnrollmentEmailResponse(
            message="Email updated successfully",
            enrollment_id=enrollment_id,
            new_email=email,
        )


class SingletonSequenceEnrollmentService(SequenceEnrollmentService, Singleton):
    pass


def get_sequence_enrollment_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> SequenceEnrollmentService:
    if SingletonSequenceEnrollmentService.has_instance():
        return SingletonSequenceEnrollmentService.get_singleton_instance()
    return SingletonSequenceEnrollmentService(
        sequence_enrollment_repository=SequenceEnrollmentRepository(engine=db_engine),
        sequence_permission_service=get_sequence_permission_service_by_db_engine(
            engine=db_engine
        ),
        sequence_execution_repository=SequenceExecutionRepository(engine=db_engine),
        contact_query_service=get_contact_query_service(db_engine=db_engine),
        contact_resolve_service=get_contact_resolve_service(db_engine=db_engine),
        account_query_service=get_account_query_service(
            db_engine=db_engine,
        ),
        sequence_query_service=get_sequence_query_service_by_db(db_engine=db_engine),
        email_account_pool_service=EmailAccountPoolService(engine=db_engine),
        email_account_service_v2=EmailAccountServiceV2(engine=db_engine),
        message_service=MessageService(db_engine=db_engine),
        sequence_enrollment_query_service=get_sequence_enrollment_query_service_by_db_engine(
            db_engine=db_engine
        ),
        domain_object_list_query_service=get_domain_object_list_query_service_by_db_engine(
            db_engine=db_engine
        ),
        sequence_step_repository=SequenceStepRepository(engine=db_engine),
        task_query_service=get_task_query_service_from_engine(db_engine=db_engine),
        task_v2_service=get_task_v2_service_general(db_engine=db_engine),
        thread_repository=ThreadRepository(engine=db_engine),
        unsubscription_group_service_ext=get_unsubscription_ext_service_by_db_engine(
            engine=db_engine
        ),
        domain_crm_association_service=get_domain_crm_association_service(
            db_engine=db_engine
        ),
        notification_service=get_notification_service_by_db_engine(db_engine=db_engine),
    )


def get_sequence_enrollment_service_by_request(
    request: Request,
) -> SequenceEnrollmentService:
    return get_sequence_enrollment_service_by_db_engine(
        db_engine=get_db_engine(request)
    )
