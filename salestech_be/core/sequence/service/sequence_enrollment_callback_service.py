from uuid import UUID, uuid4

from temporalio.client import WorkflowExecutionStatus

from salestech_be.common.exception.exception import InvalidArgumentError
from salestech_be.common.singleton import Singleton
from salestech_be.core.email.service.message_service import (
    MessageService,
)
from salestech_be.core.sequence.service.sequence_enrollment_service import (
    SequenceEnrollmentService,
    get_sequence_enrollment_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_execution_service import (
    SequenceExecutionService,
    get_sequence_execution_service_by_db_engine,
)
from salestech_be.core.sequence.type.sequence_enrollment_types import (
    GlobalMessageEnrollmentTerminationNotificationMetadata,
    MeetingEnrollmentTerminationNotificationMetadata,
    SequenceEnrollmentExitReason,
)
from salestech_be.db.dao.sequence_enrollment_repo import SequenceEnrollmentRepository
from salestech_be.db.dao.sequence_repository import (
    SequenceExecutionRepository,
    SequenceRepository,
    SequenceStepRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.meeting import MeetingReferenceIdType
from salestech_be.db.models.message import MessageStatus
from salestech_be.db.models.sequence import (
    CallTaskFlowControlConfig,
    # TODO: (hao) why is this email event type defined in sequence??
    EmailEventType,
    FlowControlAction,
    FlowControlConfig,
    LinkedInConnectionRequestTaskFlowControlConfig,
    LinkedInInMailTaskFlowControlConfig,
    LinkedInMessageTaskFlowControlConfig,
    SequenceEnrollment,
    SequenceEnrollmentExitedByReferenceIdType,
    SequenceEnrollmentStatus,
    SequenceStepExecution,
    SequenceStepExecutionStatus,
    SequenceStepManualFlowControl,
    SequenceStepType,
    SequenceStepV2,
    SequenceV2,
)
from salestech_be.db.models.voice_v2 import CallDisposition
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.workflows.sequence.sequence_enrollment_workflow_v2 import (
    SequenceEnrollmentWorkflowV2,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none

logger = get_logger()


class SequenceEnrollmentCallbackService:
    """
    Service to process email events and update the status of the sequence step execution.
    """

    def __init__(
        self,
        sequence_repository: SequenceRepository,
        sequence_enrollment_repository: SequenceEnrollmentRepository,
        sequence_execution_repository: SequenceExecutionRepository,
        sequence_step_repository: SequenceStepRepository,
        message_service: MessageService,
        sequence_execution_service: SequenceExecutionService,
        sequence_enrollment_service: SequenceEnrollmentService,
    ) -> None:
        self.sequence_repository = sequence_repository
        self.sequence_enrollment_repository = sequence_enrollment_repository
        self.sequence_execution_repository = sequence_execution_repository
        self.sequence_step_repository = sequence_step_repository
        self.message_service = message_service
        self.sequence_execution_service = sequence_execution_service
        self.sequence_enrollment_service = sequence_enrollment_service

    async def stop_enrollments_for_email_account_id(
        self,
        *,
        email_account_id: UUID,
        organization_id: UUID,
    ) -> list[SequenceEnrollment]:
        """
        Terminates all sequence enrollments for the given email account id (across all sequences)
        """
        logger.bind(
            email_account_id=email_account_id, organization_id=organization_id
        ).info("Terminating enrollments by email account id")
        enrollments = await self.sequence_enrollment_repository.find_sequence_enrollments_by_email_account_id_and_status(
            organization_id=organization_id,
            email_account_id=email_account_id,
            status_list=[
                status
                for status in SequenceEnrollmentStatus
                if not status.is_terminated()
            ],
        )

        updated_enrollments = []
        for enrollment in enrollments:
            updated_enrollment = await self.sequence_enrollment_service.terminate_sequence_enrollment(
                enrollment=enrollment,
                organization_id=organization_id,
                terminate_reason=SequenceEnrollmentExitReason.EMAIL_ACCOUNT_DISABLED,
                end_enrollment_status=SequenceEnrollmentStatus.REMOVED,
                exited_by_reference_id=email_account_id,
                exited_by_reference_id_type=SequenceEnrollmentExitedByReferenceIdType.EMAIL_ACCOUNT,
                metadata=None,
            )
            if updated_enrollment:
                updated_enrollments.append(updated_enrollment)
        return updated_enrollments

    async def restart_enrollments_for_email_account(
        self,
        *,
        email_account_id: UUID,
        organization_id: UUID,
    ) -> None:
        """
        Restarts all sequence enrollments for the given email account id, picking a different mailbox from the same pool.
        """
        logger.bind(
            email_account_id=email_account_id, organization_id=organization_id
        ).info("Restarting enrollments by email account id")
        enrollments = await self.sequence_enrollment_repository.find_sequence_enrollments_by_email_account_id_and_status(
            organization_id=organization_id,
            email_account_id=email_account_id,
            status_list=[
                status
                for status in SequenceEnrollmentStatus
                if not status.is_terminated()
            ],
        )

        now = zoned_utc_now()
        for enrollment in enrollments:
            await self.sequence_enrollment_repository.update_sequence_enrollment_with_replacement(
                sequence_enrollment_to_update=enrollment.model_copy(
                    update={
                        "status": SequenceEnrollmentStatus.REMOVED,
                        "updated_at": now,
                        "exited_by_reference_id": email_account_id,
                        "exited_by_reference_id_type": SequenceEnrollmentExitedByReferenceIdType.EMAIL_ACCOUNT,
                        "exited_reason": SequenceEnrollmentExitReason.EMAIL_ACCOUNT_DISABLED.map_to_sequence_enrollment_exit_reason_code(),
                    }
                ),
                replacement_sequence_enrollment=SequenceEnrollment(
                    id=uuid4(),
                    organization_id=enrollment.organization_id,
                    sequence_id=enrollment.sequence_id,
                    contact_id=enrollment.contact_id,
                    email_account_id=None,  # Keep same pool but clear out this, it will be picked again during execution
                    email_account_pool_id=enrollment.email_account_pool_id,
                    account_id=enrollment.account_id,
                    domain_object_list_id=enrollment.domain_object_list_id,
                    email=enrollment.email,
                    status=SequenceEnrollmentStatus.ACTIVE,
                    current_step_id=None,  # Reset to start over
                    workflow_id=None,  # Reset to start over
                    enrolled_at=now,
                    enrolled_by_user_id=enrollment.enrolled_by_user_id,
                    updated_at=now,
                    updated_by_user_id=enrollment.enrolled_by_user_id,
                ),
            )

    async def switch_enrollments_email_account(  # noqa: C901
        self,
        *,
        email_account_id: UUID,
        organization_id: UUID,
    ) -> None:
        """
        Switches all sequence enrollments for the given email account id to a different mailbox from the same pool, keeping the same sequence step.
        """
        logger.bind(
            email_account_id=email_account_id, organization_id=organization_id
        ).info("Switching sequence step execution email account id")

        enrollments: list[
            SequenceEnrollment
        ] = await self.sequence_enrollment_repository.find_sequence_enrollments_by_email_account_id_and_status(
            organization_id=organization_id,
            email_account_id=email_account_id,
            status_list=[
                status
                for status in SequenceEnrollmentStatus
                if not status.is_terminated()
            ],
        )
        sequence_ids = {enrollment.sequence_id for enrollment in enrollments}
        sequences_by_id = {}
        steps_by_id = {}
        variants_by_id = {}
        for sequence_id in sequence_ids:
            sequence = not_none(
                await self.sequence_repository.find_by_tenanted_primary_key(
                    SequenceV2, id=sequence_id, organization_id=organization_id
                )
            )
            sequences_by_id[sequence_id] = sequence

            steps = await self.sequence_step_repository.list_active_sequence_steps_v2(
                sequence_id=sequence_id,
                organization_id=organization_id,
            )
            for step in steps:
                steps_by_id[step.id] = step

            variants = await self.sequence_step_repository.list_sequence_step_variants_by_step_ids(
                sequence_step_ids=[step.id for step in steps],
                organization_id=organization_id,
            )
            for variant in variants:
                variants_by_id[variant.id] = variant

        pools_with_no_available_email_account = set()
        for enrollment in enrollments:
            new_email_account_id = None
            try:
                if (
                    enrollment.email_account_id
                    not in pools_with_no_available_email_account
                ):
                    # Try to get new account to use
                    (
                        new_email_account_id,
                        _,
                    ) = await self.sequence_execution_service.get_auto_email_sending_info(
                        send_after=zoned_utc_now(),
                        email_account_pool_id=not_none(
                            enrollment.email_account_pool_id
                        ),
                        email_account_id=None,
                        sequence_schedule=sequences_by_id[
                            enrollment.sequence_id
                        ].schedule,
                        organization_id=organization_id,
                    )
            except InvalidArgumentError:
                # Cannot find a new email account to use
                pools_with_no_available_email_account.add(
                    enrollment.email_account_pool_id
                )

            # Check if we have any email to switch based on current step
            step = steps_by_id[not_none(enrollment.current_step_id)]
            if step.type == SequenceStepType.AUTO_EMAIL:
                message_id = await self.sequence_execution_service.get_previous_step_execution_message_id(
                    organization_id=organization_id,
                    enrollment_id=enrollment.id,
                    sequence_id=enrollment.sequence_id,
                )
                if message_id:
                    # Try to clean up scheduled message, which would have been drafted based on old email account
                    message = await self.message_service.get_message_by_id(
                        organization_id=organization_id,
                        message_id=message_id,
                    )
                    if message and message.status == MessageStatus.SCHEDULED:
                        await self.message_service.cancel_scheduled_message_by_global_message_id(
                            organization_id=organization_id,
                            global_message_id=message_id,
                        )

                if not new_email_account_id:
                    # If step type requires mailbox but we can't find one, we need to terminate the enrollment.
                    # Otherwise we can continue on.
                    await self.sequence_enrollment_service.terminate_sequence_enrollment(
                        enrollment=enrollment,
                        organization_id=organization_id,
                        terminate_reason=SequenceEnrollmentExitReason.EMAIL_ACCOUNT_UNAVAILABLE,
                        end_enrollment_status=SequenceEnrollmentStatus.FAILED,
                        exited_by_reference_id=email_account_id,
                        exited_by_reference_id_type=SequenceEnrollmentExitedByReferenceIdType.EMAIL_ACCOUNT,
                    )
                    continue

                # Swap to new email account
                updated_enrollment = not_none(
                    await self.sequence_enrollment_repository.update_by_tenanted_primary_key(
                        table_model=SequenceEnrollment,
                        organization_id=organization_id,
                        column_to_update={
                            "email_account_id": email_account_id,
                            "updated_at": zoned_utc_now(),
                        },
                        primary_key_to_value={"id": enrollment.id},
                    )
                )

                # Schedule new/replacement message, which will use latest email account
                step_executions = await self.sequence_execution_repository.find_sequence_step_executions_by_enrollment_id(
                    organization_id=organization_id,
                    enrollment_id=updated_enrollment.id,
                    sequence_id=updated_enrollment.sequence_id,
                )
                step_executions.sort(
                    key=lambda x: x.updated_at or x.created_at, reverse=True
                )
                step_execution = step_executions[0]
                await self.sequence_execution_service.create_scheduled_message(
                    step_execution_id=step_execution.id,
                    organization_id=organization_id,
                    enrollment=updated_enrollment,
                    variant=variants_by_id[step_execution.sequence_step_variant_id],
                    scheduled_at=zoned_utc_now(),
                )
            else:
                # Other step types: just update (even if it is None)
                updated_enrollment = not_none(
                    await self.sequence_enrollment_repository.update_by_tenanted_primary_key(
                        table_model=SequenceEnrollment,
                        organization_id=organization_id,
                        column_to_update={
                            "email_account_id": email_account_id,
                            "updated_at": zoned_utc_now(),
                        },
                        primary_key_to_value={"id": enrollment.id},
                    )
                )

    def _get_task_flow_action_from_manual_flow_control(
        self, sequence_step: SequenceStepV2
    ) -> FlowControlAction:
        """
        Return a FlowControlAction instance based on step configuration.
        """
        match sequence_step.manual_flow_control:
            case SequenceStepManualFlowControl.COMPLETE_AND_PAUSE:
                return FlowControlAction.PAUSE
            case SequenceStepManualFlowControl.COMPLETE_AND_STOP:
                return FlowControlAction.TERMINATE
            case _:
                return FlowControlAction.CONTINUE

    def _get_task_flow_action_from_call_task_config(  # noqa: PLR0911
        self, flow_control_config: CallTaskFlowControlConfig, disposition: str
    ) -> FlowControlAction:
        match disposition:
            case CallDisposition.INTERESTED:
                return flow_control_config.call_answered_interested.flow_control_action
            case CallDisposition.NOT_INTERESTED:
                return (
                    flow_control_config.call_answered_not_interested.flow_control_action
                )
            case CallDisposition.CALL_BACK_LATER:
                return flow_control_config.call_answered_call_back_later.flow_control_action
            case CallDisposition.WRONG_NUMBER:
                return (
                    flow_control_config.call_answered_wrong_number.flow_control_action
                )
            case CallDisposition.MEETING_SCHEDULED:
                return flow_control_config.call_answered_meeting_scheduled.flow_control_action
            case CallDisposition.NO_ANSWER:
                return flow_control_config.call_answered_no_answer.flow_control_action
            case CallDisposition.LEFT_VOICEMAIL:
                return (
                    flow_control_config.call_answered_left_voicemail.flow_control_action
                )
            case _:
                return flow_control_config.call_answered_no_answer.flow_control_action

    def _get_task_flow_action_from_config_for_disposition(
        self, flow_control_config: FlowControlConfig, disposition: str
    ) -> FlowControlAction | None:
        """
        Return a FlowControlAction instance based on step flow control config and disposition.
        """
        if isinstance(flow_control_config, CallTaskFlowControlConfig):
            return self._get_task_flow_action_from_call_task_config(
                flow_control_config=flow_control_config,
                disposition=disposition,
            )
        elif isinstance(
            flow_control_config,
            (
                LinkedInMessageTaskFlowControlConfig,
                LinkedInConnectionRequestTaskFlowControlConfig,
                LinkedInInMailTaskFlowControlConfig,
            ),
        ):
            return flow_control_config.task_completed.flow_control_action
        return None

    def _get_task_flow_action(
        self, sequence_step: SequenceStepV2, disposition: str | None
    ) -> FlowControlAction:
        """
        Return a FlowControlAction instance based on task disposition and step configuration.
        """
        if not disposition or not sequence_step.flow_control_config:
            # No disposition or no flow control config: fall back to old field
            return self._get_task_flow_action_from_manual_flow_control(
                sequence_step=sequence_step
            )
        else:
            mapped_disposition = self._get_task_flow_action_from_config_for_disposition(
                flow_control_config=sequence_step.flow_control_config,
                disposition=disposition,
            )
            return mapped_disposition or FlowControlAction.CONTINUE

    async def handle_sequence_step_execution_flow_control_for_completed_task(  # noqa: PLR0911
        self,
        *,
        organization_id: UUID,
        sequence_step_execution_id: UUID,
        disposition: str | None,
    ) -> None:
        # Step 1: Find the execution and step
        step_execution = (
            await self.sequence_execution_repository.find_by_tenanted_primary_key(
                SequenceStepExecution,
                id=sequence_step_execution_id,
                organization_id=organization_id,
            )
        )

        if not step_execution:
            logger.bind(
                organization_id=organization_id,
                step_execution_id=sequence_step_execution_id,
            ).error("Sequence step execution not found")
            return
        if step_execution.status != SequenceStepExecutionStatus.TASK_CREATED:
            return

        enrollment = not_none(
            await self.sequence_execution_repository.find_by_tenanted_primary_key(
                SequenceEnrollment,
                id=step_execution.sequence_enrollment_id,
                organization_id=organization_id,
            )
        )
        if enrollment.status.is_terminated():
            logger.bind(
                organization_id=organization_id,
                step_execution_id=sequence_step_execution_id,
                enrollment_id=enrollment.id,
            ).info("Enrollment is terminated, skipping sequence task handling")
            return

        # Find the sequence step to check manual flow control
        sequence_step = (
            await self.sequence_execution_repository.find_by_tenanted_primary_key(
                SequenceStepV2,
                id=step_execution.sequence_step_id,
                organization_id=organization_id,
            )
        )

        if not sequence_step:
            logger.bind(
                organization_id=organization_id,
                step_execution_id=sequence_step_execution_id,
                step_id=step_execution.sequence_step_id,
            ).error("Sequence step not found")
            return

        # Get the flow control action
        flow_control_action = self._get_task_flow_action(
            sequence_step=sequence_step, disposition=disposition
        )

        # Step 2: Check if manual flow control is COMPLETE_AND_CONTINUE
        if flow_control_action == FlowControlAction.PAUSE:
            logger.bind(
                organization_id=organization_id,
                step_execution_id=sequence_step_execution_id,
                step_id=sequence_step.id,
                manual_flow_control=sequence_step.manual_flow_control,
            ).info("Sequence step have COMPLETE_AND_PAUSE flow control")
            await self.sequence_execution_repository.update_by_tenanted_primary_key(
                SequenceEnrollment,
                organization_id=organization_id,
                primary_key_to_value={"id": step_execution.sequence_enrollment_id},
                column_to_update={"status": SequenceEnrollmentStatus.INACTIVE},
            )

            await self.sequence_execution_repository.update_by_tenanted_primary_key(
                SequenceStepExecution,
                organization_id=organization_id,
                primary_key_to_value={"id": sequence_step_execution_id},
                column_to_update={
                    "status": SequenceStepExecutionStatus.TASK_COMPLETED_WITH_PAUSED,
                    "updated_at": zoned_utc_now(),
                },
            )
            return
        elif flow_control_action == FlowControlAction.TERMINATE:
            logger.bind(
                organization_id=organization_id,
                step_execution_id=sequence_step_execution_id,
                step_id=sequence_step.id,
                manual_flow_control=sequence_step.manual_flow_control,
            ).info("Sequence step have COMPLETE_AND_STOP flow control")
            await self.sequence_execution_repository.update_by_tenanted_primary_key(
                SequenceEnrollment,
                organization_id=organization_id,
                primary_key_to_value={"id": step_execution.sequence_enrollment_id},
                column_to_update={"status": SequenceEnrollmentStatus.EXITED},
            )
            return

        # Step 3: resume the sequence enrollment workflow for the next step
        if not enrollment or not enrollment.workflow_id:
            logger.bind(
                organization_id=organization_id,
                step_execution_id=sequence_step_execution_id,
                enrollment_id=step_execution.sequence_enrollment_id,
            ).error("Sequence enrollment not found or missing workflow ID")
            return

        logger.bind(
            organization_id=organization_id,
            step_execution_id=sequence_step_execution_id,
            enrollment_id=enrollment.id,
            workflow_id=enrollment.workflow_id,
        ).info(
            "Sending RESUME signal to sequence enrollment workflow to continue the sequence"
        )

        client = await get_temporal_client()
        handle = client.get_workflow_handle(not_none(enrollment.workflow_id))
        try:
            description = await handle.describe()
            if description.status == WorkflowExecutionStatus.RUNNING:
                await handle.signal(SequenceEnrollmentWorkflowV2.complete_task)
            else:
                logger.bind(
                    workflow_id=enrollment.workflow_id,
                    status=description.status,
                ).info("Workflow is not running, skipping signal")
        except Exception as e:
            # Log any other errors during the describe operation
            logger.error(
                f"Failed to signal workflow {enrollment.workflow_id}", exc_info=e
            )

        logger.bind(
            organization_id=organization_id,
            step_execution_id=sequence_step_execution_id,
            enrollment_id=enrollment.id,
            workflow_id=enrollment.workflow_id,
        ).info("Successfully sent RESUME signal to sequence enrollment workflow")

        # Step 4: Update the sequence step execution status to TASK_COMPLETED
        await self.sequence_execution_repository.update_by_tenanted_primary_key(
            SequenceStepExecution,
            organization_id=organization_id,
            primary_key_to_value={"id": sequence_step_execution_id},
            column_to_update={"status": SequenceStepExecutionStatus.TASK_COMPLETED},
        )

    async def terminate_sequence_step_execution_by_meeting_for_contacts(
        self,
        *,
        organization_id: UUID,
        meeting_id: UUID,
        meeting_reference_id_type: MeetingReferenceIdType,
        contact_ids: list[UUID],
    ) -> list[SequenceEnrollment]:
        if meeting_reference_id_type != MeetingReferenceIdType.USER_CALENDAR_EVENT:
            # Voice: we reply spcifically only on call disposition
            # External recording: not relevant for sequences
            return []

        # find the sequence enrollment for the contacts (go through service layer instead but not available yet)
        db_enrollments_by_contact = await self.sequence_enrollment_repository.find_sequence_enrollments_by_contacts_and_status(
            contact_ids=set(contact_ids),
            organization_id=organization_id,
            status_list=[
                SequenceEnrollmentStatus.ACTIVE,
                SequenceEnrollmentStatus.PENDING,
                SequenceEnrollmentStatus.FAILED,
            ],
        )
        ended_enrollments = []
        for contact_enrollments in db_enrollments_by_contact.values():
            for enrollment in contact_enrollments:
                if not enrollment.workflow_id:
                    continue

                if (
                    enrollment.status
                    in SequenceEnrollmentStatus.get_terminated_statuses()
                ):
                    continue

                terminate_reason = SequenceEnrollmentExitReason.MEETING_BOOKED
                end_enrollment_status = SequenceEnrollmentStatus.EXITED
                updated_enrollment = await self.sequence_enrollment_service.terminate_sequence_enrollment(
                    enrollment=enrollment,
                    organization_id=organization_id,
                    terminate_reason=terminate_reason,
                    end_enrollment_status=end_enrollment_status,
                    exited_by_reference_id=meeting_id,
                    exited_by_reference_id_type=SequenceEnrollmentExitedByReferenceIdType.MEETING,
                    metadata=MeetingEnrollmentTerminationNotificationMetadata(
                        meeting_id=meeting_id,
                    ),
                )
                if updated_enrollment:
                    ended_enrollments.append(updated_enrollment)
        return ended_enrollments

    async def terminate_sequence_step_execution_by_global_message_and_event_type(
        self,
        *,
        global_message_id: UUID,
        global_thread_id: UUID | None,
        email_event_type: str,
        organization_id: UUID,
    ) -> SequenceEnrollment | None:
        """
        Terminate the sequence step execution for the given global message id and email
        event type. For now, email replied event will trigger sequence termination.

        Args:
            global_message_id: The global message id of the email reply
            email_event_type: The type of the email event
            organization_id: The organization id

        Returns:
            None
        """

        execution = await self.sequence_repository.get_sequence_step_execution_by_global_message_id(
            global_message_id=global_message_id, organization_id=organization_id
        )
        if not execution:
            logger.bind(global_message_id=global_message_id).debug(
                "[process_email_event] Unknown sequence execution for email"
            )
            return None

        sequence_enrollment = (
            await self.sequence_enrollment_repository.find_by_tenanted_primary_key(
                table_model=SequenceEnrollment,
                id=execution.sequence_enrollment_id,
                organization_id=organization_id,
            )
        )
        if not sequence_enrollment:
            logger.bind(global_message_id=global_message_id).debug(
                "[process_email_event] Unknown sequence enrollment for email"
            )
            return None

        terminate_reason: SequenceEnrollmentExitReason
        end_enrollment_status: SequenceEnrollmentStatus
        match email_event_type:
            case EmailEventType.REPLIED:
                terminate_reason = SequenceEnrollmentExitReason.EMAIL_REPLIED
                end_enrollment_status = SequenceEnrollmentStatus.EXITED
            case EmailEventType.UNSUBSCRIBED:
                terminate_reason = SequenceEnrollmentExitReason.EMAIL_UNSUBSCRIBED
                end_enrollment_status = SequenceEnrollmentStatus.EXITED
            case EmailEventType.SEND_ATTEMPTED_FAILED:
                terminate_reason = SequenceEnrollmentExitReason.EMAIL_SEND_FAILED
                end_enrollment_status = SequenceEnrollmentStatus.FAILED
            case EmailEventType.BOUNCE_DETECTED:
                terminate_reason = SequenceEnrollmentExitReason.EMAIL_BOUNCE_DETECTED
                end_enrollment_status = SequenceEnrollmentStatus.FAILED
            case _:
                logger.info(
                    f"[terminate_sequence_step_execution_by_global_message] No matching email event type for termination {email_event_type} - {global_message_id}"
                )
                return None

        return await self.sequence_enrollment_service.terminate_sequence_enrollment(
            enrollment=sequence_enrollment,
            organization_id=organization_id,
            terminate_reason=terminate_reason,
            end_enrollment_status=end_enrollment_status,
            exited_by_reference_id=global_message_id,
            exited_by_reference_id_type=SequenceEnrollmentExitedByReferenceIdType.GLOBAL_MESSAGE,
            metadata=GlobalMessageEnrollmentTerminationNotificationMetadata(
                global_message_id=global_message_id,
                global_thread_id=global_thread_id,
            ),
        )


class SingletonSequenceEnrollmentCallbackService(
    Singleton, SequenceEnrollmentCallbackService
):
    pass


def get_sequence_enrollment_callback_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> SequenceEnrollmentCallbackService:
    if SingletonSequenceEnrollmentCallbackService.has_instance():
        return SingletonSequenceEnrollmentCallbackService.get_singleton_instance()
    return SingletonSequenceEnrollmentCallbackService(
        sequence_repository=SequenceRepository(engine=db_engine),
        sequence_enrollment_repository=SequenceEnrollmentRepository(engine=db_engine),
        sequence_execution_repository=SequenceExecutionRepository(engine=db_engine),
        sequence_step_repository=SequenceStepRepository(engine=db_engine),
        message_service=MessageService(db_engine=db_engine),
        sequence_execution_service=get_sequence_execution_service_by_db_engine(
            db_engine=db_engine
        ),
        sequence_enrollment_service=get_sequence_enrollment_service_by_db_engine(
            db_engine=db_engine
        ),
    )
