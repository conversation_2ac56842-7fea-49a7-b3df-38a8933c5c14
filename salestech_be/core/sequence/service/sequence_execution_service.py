from collections import defaultdict
from datetime import datetime, timedelta
from uuid import UUID, uuid4

from fastapi import Request

from salestech_be.common.exception import InvalidArgumentError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.core.contact.service.contact_service import get_contact_service
from salestech_be.core.email.account.email_account_ext import EmailAccountServiceExt
from salestech_be.core.email.deliver_window.email_deliver_window_service import (
    get_email_deliver_window_service,
)
from salestech_be.core.email.render.email_rendering_service import EmailRenderingService
from salestech_be.core.email.service.message_service import (
    get_message_service_by_db_engine,
)
from salestech_be.core.email.template.email_template_service import (
    get_email_template_service_from_engine,
)
from salestech_be.core.email.thread.thread_service_ext import ThreadServiceExt
from salestech_be.core.email.type.email import EmailHydratedParticipant
from salestech_be.core.email.utils import (
    extract_text_snippet,
    get_next_occurrence_time_window,
)
from salestech_be.core.sequence.type.sequence_execution import (
    EnrollmentValidationResult,
)
from salestech_be.core.user.service.user_service import get_user_service_general
from salestech_be.db.dao.sequence_repository import (
    SequenceExecutionRepository,
    SequenceRepository,
    SequenceStepRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.message import MessageSource
from salestech_be.db.models.sequence import (
    SequenceEnrollment,
    SequenceEnrollmentStatus,
    SequenceEnrollmentStepVariantAssociation,
    SequenceErrorCode,
    SequenceStatus,
    SequenceStepExecution,
    SequenceStepExecutionResultEntityType,
    SequenceStepExecutionStatus,
    SequenceStepV2,
    SequenceStepVariant,
    SequenceStepVariantStatus,
    SequenceV2,
    SequenceV2Schedule,
)
from salestech_be.db.models.sequence import (
    SequenceStepExecution as DbSequenceStepExecution,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.email.message.schema import (
    SendMessageRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)


class SequenceExecutionService:
    def __init__(self, db_engine: DatabaseEngine) -> None:
        self._sequence_repository = SequenceRepository(engine=db_engine)
        self._sequence_step_repository = SequenceStepRepository(engine=db_engine)
        self._execution_repository = SequenceExecutionRepository(engine=db_engine)
        self._thread_service_ext = ThreadServiceExt(db_engine=db_engine)
        self._email_account_service = EmailAccountServiceExt(engine=db_engine)
        self._email_template_service = get_email_template_service_from_engine(
            db_engine=db_engine
        )
        self._email_rendering_service = EmailRenderingService(db_engine=db_engine)
        self._message_service = get_message_service_by_db_engine(db_engine=db_engine)
        self._contact_service = get_contact_service(db_engine=db_engine)
        self._user_service = get_user_service_general(db_engine=db_engine)
        self._email_deliver_window_service = get_email_deliver_window_service(
            db_engine=db_engine
        )
        self._logger = get_logger()

    ###########################
    # Sequence V2
    ###########################
    async def validate_current_enrollment_and_get_variant(
        self, organization_id: UUID, sequence_enrollment_id: UUID
    ) -> EnrollmentValidationResult:
        """
        Validates a sequence enrollment and retrieves related step and variant information.

        This method performs the following validations:
        1. Checks if the enrollment and sequence exists and is active
        2. Verifies that the current step exists and is not deleted
        3. Retrieves the step variant associated with the enrollment and step
        4. Validates that the variant is active


        Args:
            organization_id: The ID of the organization
            sequence_enrollment_id: The ID of the sequence enrollment to validate

        Returns:
            EnrollmentValidationResult: A DTO containing the enrollment, step, and variant
            with convenience methods to check their validity
        """
        result = EnrollmentValidationResult()

        enrollment = await self._sequence_step_repository.find_by_tenanted_primary_key(
            SequenceEnrollment,
            organization_id=organization_id,
            id=sequence_enrollment_id,
        )
        if not enrollment:
            self._logger.bind(sequence_enrollment_id=sequence_enrollment_id).info(
                "Contact is not enrolled in the sequence"
            )
            return result

        result.enrollment = enrollment

        if enrollment.status is not SequenceEnrollmentStatus.ACTIVE:
            self._logger.bind(sequence_enrollment_id=sequence_enrollment_id).info(
                "Sequence enrollment is not active"
            )
            return result

        sequence = await self._sequence_step_repository.find_by_tenanted_primary_key(
            SequenceV2,
            organization_id=organization_id,
            id=enrollment.sequence_id,
        )
        if not sequence:
            self._logger.bind(
                sequence_enrollment_id=sequence_enrollment_id,
                sequence_id=enrollment.sequence_id,
            ).info("Sequence is not exist")
            return result

        result.sequence = sequence

        if sequence.status != SequenceStatus.ACTIVE:
            self._logger.bind(
                sequence_enrollment_id=sequence_enrollment_id, sequence_id=sequence.id
            ).info("Sequence is not active")
            return result

        step = await self._sequence_step_repository.find_by_tenanted_primary_key(
            SequenceStepV2,
            organization_id=organization_id,
            id=enrollment.current_step_id,
            exclude_deleted_or_archived=False,
        )
        if not step or step.deleted_at is not None:
            self._logger.bind(
                sequence_enrollment_id=sequence_enrollment_id,
                sequence_step_id=enrollment.current_step_id,
            ).info("Sequence step not found")
            result.step = step
            raise ValueError(f"Current step not found: {enrollment.current_step_id}")

        result.step = step

        variant = (
            await self._sequence_step_repository.find_variant_by_enrollment_and_step(
                organization_id=organization_id,
                sequence_enrollment_id=sequence_enrollment_id,
                sequence_step_id=step.id,
            )
        )
        if not variant:
            self._logger.bind(
                sequence_enrollment_id=sequence_enrollment_id,
                sequence_step_id=enrollment.current_step_id,
            ).info("Sequence step variant not found")
            return result

        result.variant = variant

        if variant.status != SequenceStepVariantStatus.ACTIVE:
            self._logger.bind(
                sequence_enrollment_id=sequence_enrollment_id,
                sequence_step_id=enrollment.current_step_id,
            ).info("Sequence step variant is not active")

        return result

    async def assign_step_to_active_variant(
        self,
        organization_id: UUID,
        sequence_enrollment_id: UUID,
        sequence_step_id: UUID,
        is_reassign: bool,
    ) -> SequenceStepVariant | None:
        if is_reassign:
            await self._sequence_step_repository.delete_variant_and_enrollment_association_by_enrollment_and_step(
                organization_id=organization_id,
                sequence_enrollment_id=sequence_enrollment_id,
                sequence_step_id=sequence_step_id,
            )

        active_variants = (
            await self._sequence_step_repository.list_active_sequence_step_variants(
                organization_id=organization_id,
                sequence_step_id=sequence_step_id,
            )
        )
        if not active_variants:
            self._logger.bind(
                sequence_enrollment_id=sequence_enrollment_id,
                sequence_step_id=sequence_step_id,
            ).info("No active variants found")
            return None

        # Get the count of existing associations for each variant
        variant_associations = await self._sequence_step_repository.find_enrollment_step_variant_associations_by_step_id(
            sequence_step_id=sequence_step_id,
            organization_id=organization_id,
        )

        # Create a counter for each variant
        variant_counts = {variant.id: 0 for variant in active_variants}

        # Count existing associations
        for association in variant_associations:
            if association.sequence_step_variant_id in variant_counts:
                variant_counts[association.sequence_step_variant_id] += 1

        # Find the variant with the lowest count
        min_count = float("inf")
        min_variants = []

        for variant in active_variants:
            count = variant_counts[variant.id]
            if count < min_count:
                min_count = count
                min_variants = [variant]
            elif count == min_count:
                min_variants.append(variant)

        # If there are multiple variants with the same minimum count,
        # select one deterministically based on enrollment ID to ensure
        # consistency in repeated calls
        variant_index = hash(str(sequence_enrollment_id)) % len(min_variants)
        new_variant = min_variants[variant_index]

        await self._sequence_step_repository.insert(
            SequenceEnrollmentStepVariantAssociation(
                id=uuid4(),
                sequence_enrollment_id=sequence_enrollment_id,
                sequence_step_id=sequence_step_id,
                sequence_step_variant_id=new_variant.id,
                organization_id=organization_id,
                created_at=zoned_utc_now(),
            )
        )
        return new_variant

    async def move_enrollment_to_next_step(
        self,
        enrollment_id: UUID,
        next_step_id: UUID | None,
        organization_id: UUID,
    ) -> SequenceEnrollment:
        nxt_id = next_step_id

        while nxt_id is not None:
            # Check if the step exists and is not deleted
            step = await self._sequence_step_repository.find_by_tenanted_primary_key(
                SequenceStepV2,
                organization_id=organization_id,
                id=nxt_id,
                exclude_deleted_or_archived=False,
            )
            if not step:
                raise ValueError(f"Step not found: {nxt_id}")

            if not step.deleted_at:
                # Found a valid step (not deleted), so use it
                break

            # If step is deleted, move to next step
            self._logger.bind(
                enrollment_id=enrollment_id,
                step_id=nxt_id,
                deleted_at=step.deleted_at,
                deleted_by_user_id=step.deleted_by_user_id,
            ).info("Step is deleted, moving to next step")
            nxt_id = step.next_step_id

        # Update the enrollment with the final step_id (could be None if we reached the end)
        updated_enrollment = (
            await self._sequence_step_repository.update_by_tenanted_primary_key(
                SequenceEnrollment,
                primary_key_to_value={"id": enrollment_id},
                organization_id=organization_id,
                column_to_update={
                    "current_step_id": nxt_id,
                },
            )
        )
        if not updated_enrollment:
            raise ValueError(f"Enrollment not found: {enrollment_id}")
        return updated_enrollment

    async def terminate_enrollment_step(
        self,
        step_execution_id: UUID,
        enrollment_id: UUID,
        next_step_id: UUID | None,
        organization_id: UUID,
    ) -> SequenceEnrollment:
        await self._execution_repository.update_by_tenanted_primary_key(
            DbSequenceStepExecution,
            organization_id=organization_id,
            primary_key_to_value={"id": step_execution_id},
            column_to_update={
                "status": SequenceStepExecutionStatus.TERMINATED,
            },
        )
        return not_none(
            await self.move_enrollment_to_next_step(
                enrollment_id=enrollment_id,
                next_step_id=next_step_id,
                organization_id=organization_id,
            )
        )

    async def execute_auto_email_step(
        self,
        organization_id: UUID,
        step_execution_id: UUID,
        sequence_enrollment_id: UUID,
        embed_tracking_url: bool = True,
    ) -> SequenceEnrollment | None:
        # 1. Validation and fetch enrollment and step
        validation_result = await self.validate_current_enrollment_and_get_variant(
            organization_id=organization_id,
            sequence_enrollment_id=sequence_enrollment_id,
        )
        enrollment = validation_result.enrollment

        if enrollment is None or enrollment.status != SequenceEnrollmentStatus.ACTIVE:
            return enrollment

        step = validation_result.step
        if not step:
            raise ValueError("Current step not found")

        # No active step found (deleted), finishing this step
        if step.deleted_at is not None:
            return await self.terminate_enrollment_step(
                step_execution_id=step_execution_id,
                enrollment_id=enrollment.id,
                next_step_id=step.next_step_id,
                organization_id=organization_id,
            )

        variant = validation_result.variant
        if variant is None or variant.status != SequenceStepVariantStatus.ACTIVE:
            variant = await self.assign_step_to_active_variant(
                organization_id=organization_id,
                sequence_enrollment_id=sequence_enrollment_id,
                sequence_step_id=step.id,
                is_reassign=True,
            )
            if variant is None:
                # No active variant found in this step, finishing this step
                return await self.terminate_enrollment_step(
                    step_execution_id=step_execution_id,
                    enrollment_id=enrollment.id,
                    next_step_id=step.next_step_id,
                    organization_id=organization_id,
                )
            else:
                await self._execution_repository.update_by_tenanted_primary_key(
                    DbSequenceStepExecution,
                    organization_id=organization_id,
                    primary_key_to_value={"id": step_execution_id},
                    column_to_update={
                        "sequence_step_variant_id": variant.id,
                    },
                )

        # Get the step execution to check if it has a global_message_id
        step_execution = await self._execution_repository.find_by_tenanted_primary_key(
            DbSequenceStepExecution,
            organization_id=organization_id,
            id=step_execution_id,
        )

        if step_execution is None:
            raise ValueError(f"Step execution {step_execution_id} not found")

        if step_execution.global_message_id is None:
            # no message scheduled - move to next step
            self._logger.bind(step_id=step.id, enrollment_id=enrollment.id).error(
                "No message scheduled for step"
            )
            return await self.move_enrollment_to_next_step(
                enrollment_id=enrollment.id,
                next_step_id=step.next_step_id,
                organization_id=organization_id,
            )
        try:
            # Send the scheduled message by global_message_id
            await self._message_service.send_scheduled_message_by_global_message_id(
                organization_id=organization_id,
                global_message_id=step_execution.global_message_id,
                embed_tracking_url=embed_tracking_url,
            )
        except Exception as e:
            self._logger.bind(
                step_id=step.id, enrollment_id=enrollment.id, exc_info=e
            ).error(f"Failed to send scheduled message: {e}")
            _, enrollment = await self.update_failed_step_execution(
                organization_id=organization_id,
                step_execution_id=step_execution_id,
                enrollment_id=enrollment.id,
                error_code=SequenceErrorCode.MESSAGE_SEND_FAILURE,
                error_detail="Failed to send scheduled message.",
            )
            return enrollment

        await self._execution_repository.update_by_tenanted_primary_key(
            DbSequenceStepExecution,
            organization_id=organization_id,
            primary_key_to_value={"id": step_execution_id},
            column_to_update={
                "status": SequenceStepExecutionStatus.SENT,
                "executed_at": zoned_utc_now(),
            },
        )
        updated_enrollment = await self.move_enrollment_to_next_step(
            enrollment_id=enrollment.id,
            next_step_id=step.next_step_id,
            organization_id=organization_id,
        )

        self._logger.bind(step_id=step.id, enrollment_id=enrollment.id).info(
            "Successfully sent scheduled email message."
        )
        return updated_enrollment

    async def update_failed_step_execution(
        self,
        organization_id: UUID,
        step_execution_id: UUID,
        enrollment_id: UUID,
        error_code: SequenceErrorCode,
        error_detail: str,
        execution_status: SequenceStepExecutionStatus
        | None = SequenceStepExecutionStatus.FAILED,
    ) -> tuple[SequenceStepExecution, SequenceEnrollment]:
        (
            execution,
            enrollment,
        ) = await self._execution_repository.update_failed_step_execution(
            organization_id=organization_id,
            step_execution_id=step_execution_id,
            enrollment_id=enrollment_id,
            error_code=error_code,
            error_detail=error_detail,
            execution_status=execution_status,
        )
        return execution, enrollment

    async def get_auto_email_sending_info(
        self,
        send_after: datetime,
        email_account_pool_id: UUID,
        email_account_id: UUID | None,
        sequence_schedule: SequenceV2Schedule,
        organization_id: UUID,
    ) -> tuple[UUID, datetime]:
        assigned_email_account_id: UUID
        send_at: datetime
        if not email_account_id:
            (
                assigned_email_account_id,
                send_at,
            ) = await self._email_deliver_window_service.find_first_deliverable_slot_of_email_account_pool_v2(
                send_after=send_after or zoned_utc_now(),
                email_account_pool_id=email_account_pool_id,
                sequence_schedule=sequence_schedule,
                organization_id=organization_id,
            )
        else:
            if not self._email_deliver_window_service.check_if_email_account_available(
                organization_id=organization_id,
                email_account_pool_id=email_account_pool_id,
                email_account_id=email_account_id,
            ):
                raise InvalidArgumentError(
                    "Email account not found in the pool with warmup status or use override email accounts"
                )
            assigned_email_account_id = email_account_id
            # Find the deliverable slot for the recipient
            email_account = not_none(
                await self._email_account_service.get_email_account_by_id(
                    organization_id=organization_id, email_account_id=email_account_id
                )
            )
            send_at = (
                await self._email_deliver_window_service.find_first_deliverable_slot_v2(
                    send_after=send_after or zoned_utc_now(),
                    sender_email_account=email_account,
                    sequence_schedule=sequence_schedule,
                    organization_id=organization_id,
                )
            )
        # Persist the allocated time
        await self._email_account_service.record_email_account_slot_allocation(
            email_account_id=assigned_email_account_id,
            organization_id=organization_id,
            allocated_time=send_at,
        )
        return assigned_email_account_id, send_at

    async def get_previous_step_execution_message_id(
        self,
        organization_id: UUID,
        enrollment_id: UUID,
        sequence_id: UUID,
    ) -> UUID | None:
        # find the previous step execution by enrollment_id
        sequence_step_executions = await self._execution_repository.find_sequence_step_executions_by_enrollment_id(
            organization_id=organization_id,
            enrollment_id=enrollment_id,
            sequence_id=sequence_id,
        )
        self._logger.bind(
            organization_id=organization_id,
            enrollment_id=enrollment_id,
            sequence_step_executions=sequence_step_executions,
        ).info("Found previous step executions")
        if not sequence_step_executions:
            return None
        # get the most recent step execution global_message_id

        global_message_id = next(
            step_execution.global_message_id
            for step_execution in sequence_step_executions
            if step_execution.global_message_id
        )
        return await self._thread_service_ext.get_message_id_by_global_message_id(
            global_message_id=global_message_id,
            organization_id=organization_id,
        )

    def _get_unresolved_variables_message(self, unresolved_variables: set[str]) -> str:
        # Group variables by entity type
        variables_by_entity: defaultdict[str, list[str]] = defaultdict(list)
        for var in unresolved_variables:
            entity, field = var.strip("{}").split(".")
            # Convert snake_case to Title Case
            formatted_field = " ".join(word.title() for word in field.split("_"))
            variables_by_entity[entity].append(formatted_field)

        # Build the error message
        message_parts = []
        for entity, fields in variables_by_entity.items():
            # Convert entity from snake_case to Title Case if needed
            formatted_entity = " ".join(word.title() for word in entity.split("_"))
            fields_str = "', '".join(sorted(fields))
            verb = "is" if len(fields) == 1 else "are"
            message_parts.append(
                f"'{fields_str}' {verb} missing for this {formatted_entity.lower()}"
            )

        return "Email generation failed: " + "; ".join(message_parts)

    async def _create_sequence_message_internal(  # noqa: C901, PLR0911
        self,
        organization_id: UUID,
        step_execution_id: UUID,
        enrollment: SequenceEnrollment,
        variant: SequenceStepVariant,
        scheduled_at: datetime | None,
        use_draft: bool,
    ) -> tuple[DbSequenceStepExecution, SequenceEnrollment]:
        # Step 1: Get required data
        contact = await self._contact_service.get_contact_v2(
            organization_id=organization_id,
            contact_id=enrollment.contact_id,
        )
        if not contact:
            self._logger.bind(
                step_execution_id=step_execution_id,
                enrollment_id=enrollment.id,
                contact_id=enrollment.contact_id,
            ).error("Contact not found")
            execution, updated_enrollment = await self.update_failed_step_execution(
                organization_id=organization_id,
                step_execution_id=step_execution_id,
                enrollment_id=enrollment.id,
                error_code=SequenceErrorCode.CONTACT_NOT_EXIST,
                error_detail=str(f"Contact {enrollment.contact_id} not found"),
            )
            return execution, updated_enrollment

        if not enrollment.email:
            self._logger.bind(
                step_execution_id=step_execution_id,
                enrollment_id=enrollment.id,
                contact_id=enrollment.contact_id,
            ).error("No contact email provided")
            execution, updated_enrollment = await self.update_failed_step_execution(
                organization_id=organization_id,
                step_execution_id=step_execution_id,
                enrollment_id=enrollment.id,
                error_code=SequenceErrorCode.CONTACT_EMAIL_MISSING,
                error_detail="Contact has no email",
            )
            return execution, updated_enrollment

        email_account = await self._email_account_service.get_email_account_by_id(
            organization_id=organization_id,
            email_account_id=not_none(enrollment.email_account_id),
        )
        if not email_account:
            self._logger.bind(
                step_execution_id=step_execution_id,
                enrollment_id=enrollment.id,
                email_account_id=enrollment.email_account_id,
            ).error("Email account not found")
            execution, updated_enrollment = await self.update_failed_step_execution(
                organization_id=organization_id,
                step_execution_id=step_execution_id,
                enrollment_id=enrollment.id,
                error_code=SequenceErrorCode.EMAIL_ACCOUNT_NOT_EXIST,
                error_detail=str(
                    f"Email account {enrollment.email_account_id} not found"
                ),
            )
            return execution, updated_enrollment

        email_account_owner_user = await self._user_service.get_user_v2(
            organization_id=organization_id,
            user_id=email_account.owner_user_id,
        )
        if not email_account_owner_user:
            self._logger.bind(
                step_execution_id=step_execution_id,
                enrollment_id=enrollment.id,
                owner_user_id=email_account.owner_user_id,
            ).error("Owner user not found")
            execution, updated_enrollment = await self.update_failed_step_execution(
                organization_id=organization_id,
                step_execution_id=step_execution_id,
                enrollment_id=enrollment.id,
                error_code=SequenceErrorCode.OWNER_USER_NOT_EXIST,
                error_detail=str(f"User {email_account.owner_user_id} not found"),
            )
            return execution, updated_enrollment

        # Step 2: Get the template and render message
        template = await self._email_template_service.get_email_template_by_id(
            organization_id=organization_id,
            template_id=not_none(variant.template_id),
        )
        if not template:
            self._logger.bind(
                step_execution_id=step_execution_id,
                enrollment_id=enrollment.id,
                template_id=variant.template_id,
            ).error("Template not found")
            execution, updated_enrollment = await self.update_failed_step_execution(
                organization_id=organization_id,
                step_execution_id=step_execution_id,
                enrollment_id=enrollment.id,
                error_code=SequenceErrorCode.TEMPLATE_RENDER_ERROR,
                error_detail=str(f"Template {variant.template_id} not found"),
            )
            return execution, updated_enrollment

        try:
            (
                merged_subject,
                merged_body_html,
                unresolved_variables,
            ) = await self._email_rendering_service.render_email_with_domain_models(
                sender_email_account_id=not_none(email_account.id),
                recipient_contact_id=contact.id,
                pre_render_subject=template.subject,
                pre_render_body_html=template.body_html,
                include_email_signature=template.include_email_signature,
                organization_id=organization_id,
                user_id=enrollment.enrolled_by_user_id,
            )
            if unresolved_variables:
                message = self._get_unresolved_variables_message(unresolved_variables)
                self._logger.bind(
                    step_execution_id=step_execution_id,
                    enrollment_id=enrollment.id,
                    template_id=variant.template_id,
                ).error(message)
                execution, updated_enrollment = await self.update_failed_step_execution(
                    organization_id=organization_id,
                    step_execution_id=step_execution_id,
                    enrollment_id=enrollment.id,
                    error_code=SequenceErrorCode.TEMPLATE_RENDER_ERROR,
                    error_detail=message,
                )
                return execution, updated_enrollment
        except Exception as e:
            self._logger.bind(
                step_execution_id=step_execution_id,
                enrollment_id=enrollment.id,
                template_id=variant.template_id,
                exc_info=e,
            ).error(f"Failed to render template: {e}")
            execution, updated_enrollment = await self.update_failed_step_execution(
                organization_id=organization_id,
                step_execution_id=step_execution_id,
                enrollment_id=enrollment.id,
                error_code=SequenceErrorCode.TEMPLATE_RENDER_ERROR,
                error_detail="Failed to render template",
            )
            return execution, updated_enrollment

        # Step 3: Create the sequence message
        try:
            reply_to_message_id = None
            if variant.reply_to_previous_thread:
                reply_to_message_id = await self.get_previous_step_execution_message_id(
                    organization_id=organization_id,
                    enrollment_id=enrollment.id,
                    sequence_id=enrollment.sequence_id,
                )
                self._logger.bind(
                    step_execution_id=step_execution_id,
                    enrollment_id=enrollment.id,
                    reply_to_message_id=reply_to_message_id,
                    variant_id=variant.id,
                ).info("Found reply to message id")
            send_message_request = SendMessageRequest(
                subject=merged_subject,
                send_from=[
                    EmailHydratedParticipant(
                        email_account_id=email_account.id,
                        email=email_account.email,
                        name=email_account.display_name
                        or email_account_owner_user.display_name,
                    )
                ],
                to=[
                    EmailHydratedParticipant(
                        account_id=enrollment.account_id,
                        contact_id=enrollment.contact_id,
                        email=not_none(enrollment.email),
                        name=contact.display_name,
                    )
                ],
                cc=None,
                bcc=None,
                reply_to=None,
                account_ids=[enrollment.account_id] if enrollment.account_id else None,
                snippet=extract_text_snippet(merged_body_html),
                body_text=merged_body_html,
                body_html=merged_body_html,
                reply_to_message_id=reply_to_message_id,
                use_draft=use_draft,
                attachment_ids=template.attachment_ids,
                send_at=scheduled_at,
                source=MessageSource.SEQUENCE,
                sequence_id=enrollment.sequence_id,
            )

            email_dto, _ = await self._message_service.create_message_and_thread(
                organization_id=organization_id,
                send_message_request=send_message_request,
            )

            # Step 4: Update the step execution with the global message id
            if (
                email_dto.global_message_mapping
                and len(email_dto.global_message_mapping) > 0
            ):
                global_message_id = next(iter(email_dto.global_message_mapping))
                return not_none(
                    await self._execution_repository.update_by_tenanted_primary_key(
                        DbSequenceStepExecution,
                        organization_id=organization_id,
                        primary_key_to_value={"id": step_execution_id},
                        column_to_update={
                            "global_thread_id": email_dto.global_thread_id,
                            "global_message_id": global_message_id,
                            "result_entity_type": SequenceStepExecutionResultEntityType.GLOBAL_MESSAGE,
                            "result_entity_id": global_message_id,
                        },
                    )
                ), enrollment

            execution, updated_enrollment = await self.update_failed_step_execution(
                organization_id=organization_id,
                step_execution_id=step_execution_id,
                enrollment_id=enrollment.id,
                error_code=SequenceErrorCode.MESSAGE_SCHEDULED_FAILURE,
                error_detail="No message is scheduled",
            )
            return execution, updated_enrollment
        except Exception as e:
            self._logger.bind(
                step_execution_id=step_execution_id,
                enrollment_id=enrollment.id,
                exc_info=e,
            ).error(f"Failed to create scheduled message: {e}")
            execution, updated_enrollment = await self.update_failed_step_execution(
                organization_id=organization_id,
                step_execution_id=step_execution_id,
                enrollment_id=enrollment.id,
                error_code=SequenceErrorCode.MESSAGE_SCHEDULED_FAILURE,
                error_detail="Failed to create scheduled message",
            )
            return execution, updated_enrollment

    async def create_scheduled_message(
        self,
        organization_id: UUID,
        step_execution_id: UUID,
        enrollment: SequenceEnrollment,
        variant: SequenceStepVariant,
        scheduled_at: datetime,
    ) -> tuple[DbSequenceStepExecution, SequenceEnrollment]:
        return await self._create_sequence_message_internal(
            organization_id=organization_id,
            step_execution_id=step_execution_id,
            enrollment=enrollment,
            variant=variant,
            scheduled_at=scheduled_at,
            use_draft=False,
        )

    async def create_draft_message(
        self,
        organization_id: UUID,
        step_execution_id: UUID,
        enrollment: SequenceEnrollment,
        variant: SequenceStepVariant,
    ) -> tuple[DbSequenceStepExecution, SequenceEnrollment]:
        return await self._create_sequence_message_internal(
            organization_id=organization_id,
            step_execution_id=step_execution_id,
            enrollment=enrollment,
            variant=variant,
            scheduled_at=None,
            use_draft=True,
        )

    async def get_scheduled_to_execute_time(
        self,
        sequence: SequenceV2 | None,
        sequence_step: SequenceStepV2,
    ) -> ZoneRequiredDateTime:
        base_delay_time = zoned_utc_now() + timedelta(
            minutes=sequence_step.delay_minutes
        )
        if sequence and sequence.schedule and sequence.schedule.schedule_times:
            try:
                earliest_window = None
                for schedule_time in sequence.schedule.schedule_times:
                    next_start, next_end = get_next_occurrence_time_window(
                        start_time=base_delay_time,
                        day_of_the_week=schedule_time.day_of_the_week,
                        window_start_time=schedule_time.start_time,
                        window_end_time=schedule_time.end_time,
                        timezone=sequence.schedule.timezone,
                    )

                    if next_start <= base_delay_time:
                        earliest_window = base_delay_time
                    elif next_start >= base_delay_time and (
                        earliest_window is None or next_start < earliest_window
                    ):
                        earliest_window = next_start

                # If we found a valid window, use it; otherwise fall back to base delay
                scheduled_to_execute_at = (
                    earliest_window if earliest_window else base_delay_time
                )
                self._logger.bind(
                    scheduled_to_execute_at=scheduled_to_execute_at,
                    earliest_window=earliest_window,
                    base_delay_time=base_delay_time,
                ).info("get scheduled_to_execute time")
            except Exception as e:
                self._logger.bind(
                    sequence_id=sequence.id,
                    step_id=sequence_step.id,
                    exc_info=e,
                ).error(
                    "Failed to calculate next occurrence window, falling back to delay minutes."
                )
                scheduled_to_execute_at = base_delay_time
        else:
            # No schedule available, use the original delay calculation
            scheduled_to_execute_at = base_delay_time
        return scheduled_to_execute_at

    async def get_sequence_step_execution_by_global_message_id(
        self,
        global_message_id: UUID,
    ) -> SequenceStepExecution | None:
        return await self._execution_repository.get_sequence_step_execution_by_global_message_id(
            global_message_id=global_message_id,
        )

    async def get_sequence_step_executions_by_sequence_enrollment_id(
        self,
        sequence_enrollment_id: UUID,
        organization_id: UUID,
    ) -> list[SequenceStepExecution]:
        return await self._execution_repository.get_sequence_step_executions_by_sequence_enrollment_id(
            sequence_enrollment_id=sequence_enrollment_id,
            organization_id=organization_id,
        )


class SingletonSequenceExecutionService(Singleton, SequenceExecutionService):
    pass


def get_sequence_execution_service(request: Request) -> SequenceExecutionService:
    return get_sequence_execution_service_by_db_engine(db_engine=get_db_engine(request))


def get_sequence_execution_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> SingletonSequenceExecutionService:
    if SingletonSequenceExecutionService.has_instance():
        return SingletonSequenceExecutionService.get_singleton_instance()
    return SingletonSequenceExecutionService(db_engine=db_engine)
