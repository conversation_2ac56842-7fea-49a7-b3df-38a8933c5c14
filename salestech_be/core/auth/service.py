"""Auth Service."""

import secrets
from http import HTTPStatus
from uuid import UUID, uuid4

from auth0.rest import Auth0Error
from fastapi.requests import Request

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import (
    ConflictResourceError,
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.common.exception.exception import ErrorDetails, PaymentError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.quota.service.quota_policy_service import (
    QuotaPolicyService,
    get_quota_policy_service_from_engine,
)
from salestech_be.core.quota.service.quota_service import (
    QuotaService,
    get_quota_service_by_db_engine,
)
from salestech_be.core.quota.type.quota_policy_type import QuotaSummaryItem
from salestech_be.core.user.service.permission_service import (
    PermissionService,
    get_permission_service,
    get_permission_service_by_db_engine,
)
from salestech_be.core.user.service.user_service import (
    UserService,
    get_user_service,
    get_user_service_general,
)
from salestech_be.db.dao.auth0_user_repository import (
    Auth0UserRepository,
    UpsertAuth0UserRequest,
)
from salestech_be.db.dao.organization_repository import OrganizationRepository
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.auth0_user import Auth0User
from salestech_be.db.models.organization import Organization
from salestech_be.db.models.quota import (
    QuotaConsumerEntityType,
    QuotaConsumingResource,
    QuotaPeriod,
)
from salestech_be.db.models.user import User, UserInviteMode, UserRole
from salestech_be.db.models.user_organization_association import (
    UserOrganizationAssociation,
    UserOrganizationAssociationStatus,
)
from salestech_be.integrations.auth0.client import (
    AUTH0_DB_CONNECTION_NAME,
    Auth0ConnectionCache,
    get_auth0_database_authentication_api_client,
    get_auth0_management_api_client,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.asyncio_util.adapter import run_in_pool
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.user_invite.utils import (
    UserInviteUtils,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger("auth.service")

ONE_WEEK_IN_SECONDS = 60 * 60 * 24 * 7


class UserAuthService:
    def __init__(
        self,
        auth0_user_repository: Auth0UserRepository,
        user_repository: UserRepository,
        organization_repository: OrganizationRepository,
        user_service: UserService,
        permission_service: PermissionService,
        quota_policy_service: QuotaPolicyService,
        quota_service: QuotaService,
    ):
        self.auth0_user_repository = auth0_user_repository
        self.user_repository = user_repository
        self.organization_repository = organization_repository
        self.user_service = user_service
        self.permission_service = permission_service
        self.quota_service = quota_service
        self.quota_policy_service = quota_policy_service

    async def get_or_create_user(self, email: str) -> User:
        return await self.user_repository.get_or_insert_user(email=email)

    async def get_or_create_active_users_quota_with_backfill(
        self, organization_id: UUID, user_id: UUID
    ) -> QuotaSummaryItem:
        """
        An organization should have a quota policy against the number of active users.

        The quota policy is created if it does not exist. Since the quota policy was introduced after we onboarded active users,
        we will need to backfill the quota usage for pre-existing organizations.
        """
        # Get or create the quota policy for the organization.
        logger.info("get or create the quota policy for the organization")
        await self._create_user_level_voice_quota_policies(organization_id, user_id)
        await self.quota_policy_service.upsert_quota_policy(
            # TODO (colin): we need to be able to specify the initial limit depending on plan.
            quota_limit=settings.quota_policy_core_plan_max_active_users,
            period=QuotaPeriod.LIFETIME,
            resource=QuotaConsumingResource.USERS,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            entity_id=organization_id,
            organization_id=organization_id,
            user_id=user_id,
        )

        # Get the quota summary for the organization.
        logger.info("get the quota summary for the organization")
        quota_summary = await self.quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.USERS,
            period=QuotaPeriod.LIFETIME,
        )
        logger.bind(quota_summary=quota_summary, organization_id=organization_id).info(
            "quota summary for organization"
        )

        # Get the number of active users in the organization.
        num_active_users = (
            await self.user_repository.get_num_active_users_by_organization_id(
                organization_id=organization_id,
            )
        )
        logger.bind(num_active_users=num_active_users).info(
            "number of active users in the organization"
        )

        delta = num_active_users - quota_summary.total_used
        logger.bind(delta=delta).info(
            "delta between number of active users and quota usage"
        )

        if delta == 0:
            # No need to query the quota summary again.
            logger.info(
                "number of active users and quota usage are in sync, no need to query the quota summary again"
            )
            return quota_summary

        # Backfill quota usage if the number of active users and the quota usage are out of sync.
        logger.bind(organization_id=organization_id).info("backfilling quota usage")
        if delta > 0:
            logger.bind(organization_id=organization_id, delta=delta).info(
                "increasing quota usage"
            )
            await self.quota_service.increase_usage(
                organization_id=organization_id,
                entity_id=organization_id,
                entity_type=QuotaConsumerEntityType.ORGANIZATION,
                resource=QuotaConsumingResource.USERS,
                usage=delta,
                timestamp=zoned_utc_now(),
            )
        elif delta < 0:
            logger.bind(organization_id=organization_id, delta=abs(delta)).info(
                "decreasing quota usage"
            )
            await self.quota_service.decrease_usage(
                organization_id=organization_id,
                entity_id=organization_id,
                entity_type=QuotaConsumerEntityType.ORGANIZATION,
                resource=QuotaConsumingResource.USERS,
                usage=abs(delta),
                timestamp=zoned_utc_now(),
            )

        # Get the updated quota summary.
        updated_quota_summary = await self.quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.USERS,
            period=QuotaPeriod.LIFETIME,
        )
        logger.bind(
            organization_id=organization_id, updated_quota_summary=updated_quota_summary
        ).info("updated quota summary")
        return updated_quota_summary

    async def get_user_with_profile(self, email: str) -> User | None:
        return await self.user_repository.get_user_with_profile(email=email)

    async def create_user_with_profile(self, email: str) -> User:
        return await self.user_repository.get_or_insert_user_with_profile(
            email=email,
            first_name=None,
            last_name=None,
            linkedin_url=None,
        )

    async def get_or_create_user_with_profile(
        self,
        email: str,
        first_name: str | None = None,
        last_name: str | None = None,
        linkedin_url: str | None = None,
    ) -> User:
        return await self.user_repository.get_or_insert_user_with_profile(
            email=email,
            first_name=first_name,
            last_name=last_name,
            linkedin_url=linkedin_url,
        )

    async def get_user_by_email(self, email: str) -> User | None:
        return await self.user_repository.find_user_by_email(email=email)

    async def reinvite_user(self, email: str, organization_id: UUID) -> None:
        """Reinvite a user by email.

        This will only trigger an invitation/pw reset email to eligible user again.
        """
        # check user exists
        user_with_the_email = await self.get_user_by_email(email=email)
        if not user_with_the_email:
            logger.info("user with this email does not exist")
            raise ResourceNotFoundError("user with this email does not exist")

        # check user is part of the org
        existing_association = (
            await self.user_repository.find_association_by_user_id_and_organization_id(
                user_id=user_with_the_email.id,
                organization_id=organization_id,
            )
        )
        if not existing_association:
            logger.info("user org association does not exist")
            raise ResourceNotFoundError("user is not part of the org")

        # check user is using auth0 signup with email/pwd
        auth0_management_client = await get_auth0_management_api_client()
        existing_auth0_users = await run_in_pool(
            auth0_management_client.users_by_email.search_users_by_email,
            email=email,
        )

        # another option to search a user, but it seems slower than the above
        # auth0_management_client.users.list(
        #     search_engine="v3",
        #     q="identities.connection: Username-Password-Authentication AND email:<EMAIL>",
        # )

        # Now, check user exists and is using the database connection (username/password)
        # for other connection types (e.g. google workspace, okta, etc), raise err
        if not existing_auth0_users:
            logger.info("user with this email does not exist")
            raise ResourceNotFoundError("user with this email does not exist")

        found_eligible_account = False
        for existing_auth0_user in existing_auth0_users:
            for existing_auth0_user_identity in existing_auth0_user.get(
                "identities", []
            ):
                if (
                    existing_auth0_user_identity.get("connection", "")
                    == AUTH0_DB_CONNECTION_NAME
                ):
                    found_eligible_account = True
                    break

        if not found_eligible_account:
            logger.info("user with this email is not using username/password auth")
            raise InvalidArgumentError(
                "user with this email is not using username/password auth"
            )

        try:
            # trigger user pwd reset
            auth0_authentication_client = get_auth0_database_authentication_api_client()
            await run_in_pool(
                auth0_authentication_client.change_password,
                email=email,
                connection=AUTH0_DB_CONNECTION_NAME,
            )
        except Auth0Error as e:
            if e.status_code == HTTPStatus.BAD_REQUEST:
                logger.info(
                    {
                        "status_code": e.status_code,
                        "error_code": e.error_code,
                        "message": e.message,
                    }
                )
                raise InvalidArgumentError("Bad request")

    async def invite_new_user(
        self,
        user_auth_context: UserAuthContext,
        email: str,
        roles: list[UserRole],
        organization_id: UUID | None = None,
        first_name: str | None = None,
        last_name: str | None = None,
        mode: UserInviteMode = UserInviteMode.EMAIL,
        code: str | None = None,
        linkedin_url: str | None = None,
    ) -> tuple[UserOrganizationAssociation | None, str | None]:
        """Invite a new user to an organization.

        Args:
            email (str): the email of the user to invite
            organization_id (UUID): the id of the organization to invite the user to
            roles (list[UserRole]): the roles to assign to the user
            mode (UserInviteMode, optional): the mode to use to invite the user.
                - EMAIL: send a password reset email to the user
                - LINK: return a link to the user to reset their password
            code (str | None, optional): the code to use to invite the user.
            linkedin_url (str | None, optional): the linkedin url of the user.

        Returns:
            tuple[UserOrganizationAssociation, str | None]: the user organization association and the link to
            auth0 reset-password page.
        """

        # Defensively get or create the quota usage. For pre-existing organizations,
        # and users thereof, we will need to backfill the quota usage.
        #
        # On invite, we will need to increment the quota usage. To prevent exceeding the quota
        # when there are concurrent requests, we will need to implement a locking mechanism.
        #
        # e.g.
        # 1. acquire_lock()
        # 2. one of:
        #    a. get_or_create_user_organization_association()
        #    b. active_user_and_organization_association()
        # 3. increment_quota_usage()
        # 4. release_lock()

        # Attempt to get the user with the profile. Do not create a new user if not found.
        # We need to check the quota before proceeding.
        user = await self.get_user_with_profile(email=email)

        # If no organization_id is provided, create a placeholder organization for the user.
        if not organization_id:
            # test_user_invite_without_an_org_id() covers this path.
            user = await self.create_user_with_profile(email=email)

            # The user_auth_context.user_id is the person that is inviting the new user.
            organization = await self.create_organization(
                created_by_user_id=user_auth_context.user_id
            )
            organization_id = organization.id

        # This is expected to be None for a brand new user.
        user_org_association = None
        if user:
            # Check if user is already in the org. This should only happen if the user already exists,
            # but is not ACTIVE in the organization.
            user_org_association = await self.user_repository.find_association_by_user_id_and_organization_id(
                user_id=user.id,
                organization_id=organization_id,
            )

        active_users_quota = None
        if not (
            settings.enable_core_plan_quota_policy
            or str(organization_id) in settings.enable_core_plan_quota_policy_org_ids
        ):
            logger.bind(
                organization_id=organization_id,
                enable_core_plan_quota_policy=settings.enable_core_plan_quota_policy,
                enable_core_plan_quota_policy_org_ids=settings.enable_core_plan_quota_policy_org_ids,
            ).info(
                "core plan quota policy is not enabled, skipping quota creation and backfill"
            )
        else:
            # Defensively get or create the quota policy, and backfill the quota usage.
            active_users_quota = (
                await self.get_or_create_active_users_quota_with_backfill(
                    organization_id=organization_id, user_id=user_auth_context.user_id
                )
            )

        # If the user is already in the org, no need to do anything.
        if user_org_association and user_org_association.status not in (
            UserOrganizationAssociationStatus.INACTIVE,
            UserOrganizationAssociationStatus.HIDDEN,
        ):
            # test_existing_user_invite_into_an_existing_org() covers this path.
            logger.warning("user org association exists")
            return user_org_association, None

        # TODO (colin): ACQUIRE LOCK HERE.

        # This is effectively no-op if the enable_core_plan_quota_policy FF is not enabled.
        if active_users_quota and active_users_quota.total_remaining <= 0:
            raise PaymentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.QUOTA_LIMIT_EXCEEDED,
                    details="Your organization has reached its user limit.",
                )
            )

        # By now, an organization with quota policy is guaranteed to exist, as well as not exceeded.
        # If a user profile does not exist by now, create one.
        if not user:
            user = await self.create_user_with_profile(email=email)

        # Associate user with the organization.
        if not user_org_association:
            # test_user_invite_into_an_existing_org() covers this path.
            logger.bind(organization_id=organization_id, user_id=user.id).info(
                "creating user organization association"
            )
            user_org_association = (
                await self.get_or_create_user_organization_association(
                    user_id=user.id,
                    organization_id=organization_id,
                    roles=roles,
                )
            )
        else:
            # this re-associates the existing user with the organization (sets the status to active).
            # test_existing_inactive_user_invite_into_an_existing_org() covers this path.
            logger.bind(organization_id=organization_id, user_id=user.id).info(
                "setting user org association status to active"
            )
            user_org_association = not_none(
                await self.user_service.active_user_and_organization_association(
                    user_id=user.id,
                    organization_id=organization_id,
                )
            )

        # increment the quota usage
        if (
            settings.enable_core_plan_quota_policy
            or str(organization_id) in settings.enable_core_plan_quota_policy_org_ids
        ):
            await self.quota_service.increase_usage(
                organization_id=organization_id,
                entity_id=organization_id,
                entity_type=QuotaConsumerEntityType.ORGANIZATION,
                resource=QuotaConsumingResource.USERS,
                usage=1,
                timestamp=zoned_utc_now(),
            )

        # TODO (colin): RELEASE LOCK HERE.

        await self._assign_user_permission_set_groups(user_org_association)

        await self.setup_auth_db_user(email)
        link = None
        if mode == UserInviteMode.LINK:
            # return a reset link to user
            link = await self.get_reset_password_link(email, code)
        else:
            # send email to user to reset password
            auth0_authentication_client = get_auth0_database_authentication_api_client()
            await run_in_pool(
                auth0_authentication_client.change_password,
                email=email,
                connection=AUTH0_DB_CONNECTION_NAME,
            )

        return user_org_association, link

    async def _create_user_level_voice_quota_policies(
        self,
        organization_id: UUID,
        user_id: UUID,
    ) -> None:
        # 1. Create quota policy for voice seconds (60,000 seconds of dialer time per user per month)
        await self.quota_policy_service.upsert_quota_policy(
            quota_limit=settings.voice_quota_limit.get("voice_call_second", 60000),
            period=QuotaPeriod.MONTHLY,
            resource=QuotaConsumingResource.VOICE_SECONDS,
            entity_type=QuotaConsumerEntityType.USER,
            entity_id=user_id,
            applied_sub_entity_types=None,
            user_id=user_id,
            organization_id=organization_id,
        )

        # 2. Create quota policy for phone number updates (1 update per user per month)
        await self.quota_policy_service.upsert_quota_policy(
            quota_limit=settings.voice_quota_limit.get("phone_number_update", 1),
            period=QuotaPeriod.MONTHLY,
            resource=QuotaConsumingResource.VOICE_PHONE_NUMBER_UPDATE,
            entity_type=QuotaConsumerEntityType.USER,
            entity_id=user_id,
            applied_sub_entity_types=None,
            user_id=user_id,
            organization_id=organization_id,
        )

        # 3. Create quota policy for total phone lines (1 line per month when dialer is enabled)
        await self.quota_policy_service.upsert_quota_policy(
            quota_limit=settings.voice_quota_limit.get("total_phone_line", 1),
            period=QuotaPeriod.MONTHLY,
            resource=QuotaConsumingResource.TOTAL_PHONE_LINE,
            entity_type=QuotaConsumerEntityType.USER,
            entity_id=user_id,
            applied_sub_entity_types=None,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def check_existence_of_auth0_user(self, email: str) -> bool:
        # check if the user has been invited to auth0
        auth0_management_client = await get_auth0_management_api_client()
        existing_auth0_users = await run_in_pool(
            auth0_management_client.users_by_email.search_users_by_email,
            email=email,
        )
        return len(existing_auth0_users) > 0

    async def _assign_user_permission_set_groups(
        self, user_org_association: UserOrganizationAssociation
    ) -> None:
        user_id = user_org_association.user_id
        org_id = user_org_association.organization_id
        membership_roles = user_org_association.roles_or_empty_list

        for role in membership_roles:
            perm_set_group_name = (
                self.user_service.get_permission_set_group_name_by_role(
                    role=role,
                )
            )
            if not perm_set_group_name:
                continue

            perm_set_group = (
                await self.permission_service.get_permission_set_group_by_name(
                    name=perm_set_group_name,
                    organization_id=org_id,
                )
            )
            if not perm_set_group:
                continue

            await self.permission_service.assign_permission_set_group_to_users(
                permission_set_group_id=perm_set_group.id,
                to_user_ids=[user_id],
                organization_id=org_id,
                created_by_user_id=user_id,
            )

    async def setup_auth_db_user(self, email: str) -> None:
        # NOTE: auth0 authentication api does not provide user existence check
        # you will need to use the management api instead
        # TODO: (hao) - revisit this for multi-org auth0 setup
        if await self.check_existence_of_auth0_user(email):
            logger.info("auth0 user exists already, db out of sync?")
            return

        # if user does not exist yet, create the user on auth0 side
        auth0_authentication_client = get_auth0_database_authentication_api_client()

        try:
            # create an auth0 db user, with random pwd
            await run_in_pool(
                auth0_authentication_client.signup,
                email=email,
                password=secrets.token_urlsafe(16),
                connection=AUTH0_DB_CONNECTION_NAME,
            )
        except Auth0Error as e:
            if e.status_code == HTTPStatus.CONFLICT:
                raise ConflictResourceError(f"User {email} exists")

            if e.status_code == HTTPStatus.BAD_REQUEST:
                logger.info(
                    {
                        "status_code": e.status_code,
                        "error_code": e.error_code,
                        "message": e.message,
                    }
                )
                raise InvalidArgumentError("Failed to create auth0 user")

    async def get_reset_password_link(self, email: str, code: str | None) -> str | None:
        if not code:
            raise InvalidArgumentError(
                "code is required for generating reset password link"
            )

        auth0_management_client = await get_auth0_management_api_client()
        try:
            connection_id = await Auth0ConnectionCache.get_auth0_database_id()
            resp = await run_in_pool(
                auth0_management_client.tickets.create_pswd_change,
                body={
                    "result_url": UserInviteUtils.generate_invite_done_link(code),
                    "email": email,
                    "ttl_sec": ONE_WEEK_IN_SECONDS,
                    "connection_id": connection_id,
                },
            )
            return str(resp.get("ticket"))
        except Auth0Error as e:
            if e.status_code == HTTPStatus.BAD_REQUEST:
                logger.info(
                    {
                        "status_code": e.status_code,
                        "error_code": e.error_code,
                        "message": e.message,
                    }
                )
                raise InvalidArgumentError(
                    f"Failed to create auth0 reset password ticket for {email}"
                )
        return None

    async def get_or_create_auth0_user(self, req: UpsertAuth0UserRequest) -> Auth0User:
        return await self.auth0_user_repository.upsert_auth0_user(req)

    async def get_or_create_placeholder_organization(
        self, created_by_user_id: UUID
    ) -> Organization:
        return (
            await self.organization_repository.get_or_create_placeholder_organization(
                created_by_user_id=created_by_user_id
            )
        )

    async def create_organization(self, created_by_user_id: UUID) -> Organization:
        return not_none(
            await self.organization_repository.insert(
                Organization(
                    id=uuid4(),
                    display_name="",
                    created_at=zoned_utc_now(),
                    created_by_user_id=created_by_user_id,
                ),
            ),
        )

    async def get_or_create_user_organization_association(
        self,
        user_id: UUID,
        organization_id: UUID,
        roles: list[UserRole],
    ) -> UserOrganizationAssociation:
        return await self.user_repository.associate_user_to_an_organization(
            user_id=user_id,
            organization_id=organization_id,
            roles=roles,
        )


def get_user_auth_service(request: Request) -> UserAuthService:
    db_engine = get_db_engine(request=request)
    auth0_user_repository = Auth0UserRepository(engine=db_engine)
    user_repository = UserRepository(engine=db_engine)
    organization_repository = OrganizationRepository(engine=db_engine)
    user_service = get_user_service(request=request)
    permission_service = get_permission_service(request=request)
    quota_service = get_quota_service_by_db_engine(db_engine=db_engine)
    quota_policy_service = get_quota_policy_service_from_engine(engine=db_engine)

    return UserAuthService(
        auth0_user_repository=auth0_user_repository,
        user_repository=user_repository,
        organization_repository=organization_repository,
        user_service=user_service,
        permission_service=permission_service,
        quota_service=quota_service,
        quota_policy_service=quota_policy_service,
    )


def get_user_auth_service_with_engine(db_engine: DatabaseEngine) -> UserAuthService:
    auth0_user_repository = Auth0UserRepository(engine=db_engine)
    user_repository = UserRepository(engine=db_engine)
    organization_repository = OrganizationRepository(engine=db_engine)
    user_service = get_user_service_general(db_engine=db_engine)
    permission_service = get_permission_service_by_db_engine(db_engine=db_engine)
    quota_service = get_quota_service_by_db_engine(db_engine=db_engine)
    quota_policy_service = get_quota_policy_service_from_engine(engine=db_engine)

    return UserAuthService(
        auth0_user_repository=auth0_user_repository,
        user_repository=user_repository,
        organization_repository=organization_repository,
        user_service=user_service,
        permission_service=permission_service,
        quota_service=quota_service,
        quota_policy_service=quota_policy_service,
    )
