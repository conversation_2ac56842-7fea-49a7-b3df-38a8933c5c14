import base64
from uuid import UUID

from icalendar import Calendar, vCalAddress, vText
from icalendar import Event as IcalEvent
from sendgrid import Attachment, Email, FileContent, Mail, Subject

from salestech_be.common.exception import ResourceNotFoundError
from salestech_be.core.variable.variable_service import (
    VariableService,
    get_variable_service,
)
from salestech_be.db.dao.calendar_account_repository import CalendarAccountRepository
from salestech_be.db.dao.event_schedule_repository import EventScheduleRepository
from salestech_be.db.dao.user_integration_repository import UserIntegrationRepository
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.calendar_account import CalendarAccount
from salestech_be.db.models.event_schedule import (
    EventSchedule,
    EventScheduleBooking,
    EventScheduleBookingHost,
)
from salestech_be.db.models.user import User
from salestech_be.db.models.user_calendar_event import UserCalendarEvent
from salestech_be.db.models.user_integration import IntegrationProvider
from salestech_be.integrations.sendgrid.common.mail_client import SendGridMailClient
from salestech_be.settings import settings
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import convert_utc_to_local, zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.schedule.schema import (
    SchedulingType,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)


class EventScheduleNotificationService:
    def __init__(
        self,
        event_schedule_repo: EventScheduleRepository,
        email_client: SendGridMailClient,
        user_repo: UserRepository,
        calendar_account_repo: CalendarAccountRepository,
        user_integration_repo: UserIntegrationRepository,
        variable_service: VariableService,
    ):
        self.email_client = email_client
        self.event_schedule_repo = event_schedule_repo
        self.user_repo = user_repo
        self.calendar_account_repo = calendar_account_repo
        self.user_integration_repo = user_integration_repo
        self.variable_service = variable_service

    async def send_notification_emails(
        self,
        scheduling_type: SchedulingType,
        booking_id: UUID,
        organization_id: UUID,
    ) -> None:
        # 1. Get the booking details
        booking = await self.event_schedule_repo.find_by_tenanted_primary_key(
            EventScheduleBooking,
            id=booking_id,
            organization_id=organization_id,
            exclude_deleted_or_archived=False,
        )
        if booking is None:
            raise ResourceNotFoundError(f"Booking with id {booking_id} not found")
        event_schedule = await self.event_schedule_repo.find_by_tenanted_primary_key(
            EventSchedule,
            id=booking.event_schedule_id,
            organization_id=organization_id,
            exclude_deleted_or_archived=False,
        )
        if event_schedule is None:
            raise ResourceNotFoundError(
                f"Event Schedule with id {booking.event_schedule_id} not found"
            )
        cal_event = await self.event_schedule_repo.find_by_tenanted_primary_key(
            UserCalendarEvent,
            id=booking.user_calendar_event_id,
            organization_id=organization_id,
            exclude_deleted_or_archived=False,
        )
        if cal_event is None:
            raise ResourceNotFoundError(
                f"Calendar Event with id {booking.user_calendar_event_id} not found"
            )
        calendar_account = (
            await self.calendar_account_repo.find_by_tenanted_primary_key(
                CalendarAccount,
                id=event_schedule.calendar_account_id,
                organization_id=organization_id,
            )
        )
        if calendar_account is None:
            raise ResourceNotFoundError(
                f"Calendar Account with id {event_schedule.calendar_account_id} not found"
            )
        user_integration_dto = await self.user_integration_repo.find_user_integration_by_calendar_account_or_error(
            calendar_account_id=not_none(event_schedule.calendar_account_id),
            organization_id=organization_id,
        )
        user = await self.user_repo.find_by_primary_key(User, id=event_schedule.user_id)
        if user is None:
            raise ResourceNotFoundError(
                f"User with id {event_schedule.user_id} not found"
            )
        organizer_name = user.display_name
        organizer_email = calendar_account.email
        ical_uid = cal_event.ical_uid
        guest_name = booking.guest_name
        guest_email = booking.guest_email
        guest_notes = booking.guest_notes
        starts_at = cal_event.starts_at
        ends_at = cal_event.ends_at
        additional_guest_emails = booking.additional_guest_emails

        # 2. generate ics file for the event
        if (
            scheduling_type == SchedulingType.BOOK
            and user_integration_dto.integration_provider == IntegrationProvider.GOOGLE
        ):
            generated_ics_content = self._generate_ical_content(
                organizer_name=organizer_name,
                organizer_email=organizer_email,
                ical_uid=not_none(ical_uid),
                guest_name=booking.guest_name,
                guest_email=booking.guest_email,
                starts_at=starts_at,
                ends_at=ends_at,
                additional_attendees=additional_guest_emails,
                event_schedule=event_schedule,
            )
            ics_content = base64.b64encode(generated_ics_content.encode()).decode()
        else:
            ics_content = None

        # 3. Send notification emails
        await self._send_notification_email_to_hosts(
            scheduling_type=scheduling_type,
            hosts_info=booking.hosts_info
            or [EventScheduleBookingHost(email=organizer_email, name=organizer_name)],
            guest_name=guest_name,
            guest_email=guest_email,
            guest_notes=guest_notes,
            additional_attendees=additional_guest_emails,
            starts_at=starts_at,
            event_schedule=event_schedule,
        )
        await self._send_notification_email_to_guest(
            scheduling_type=scheduling_type,
            booking_id=str(booking.id),
            meeting_url=booking.meeting_url,
            organizer_name=organizer_name,
            hosts_info=booking.hosts_info
            or [EventScheduleBookingHost(email=organizer_email, name=organizer_name)],
            guest_name=booking.guest_name,
            guest_email=booking.guest_email,
            guest_notes=guest_notes,
            additional_attendees=additional_guest_emails,
            starts_at=starts_at,
            event_schedule=event_schedule,
            ics_content=ics_content,
        )
        await self._send_notification_email_to_additional_guests(
            scheduling_type=scheduling_type,
            meeting_url=booking.meeting_url,
            organizer_name=organizer_name,
            hosts_info=booking.hosts_info
            or [EventScheduleBookingHost(email=organizer_email, name=organizer_name)],
            guest_name=guest_name,
            guest_email=guest_email,
            guest_notes=guest_notes,
            additional_attendees=additional_guest_emails,
            starts_at=starts_at,
            event_schedule=event_schedule,
            ics_content=ics_content,
        )

    async def _send_notification_email_to_hosts(
        self,
        scheduling_type: SchedulingType,
        hosts_info: list[EventScheduleBookingHost],
        guest_name: str,
        guest_email: str,
        guest_notes: str | None,
        starts_at: ZoneRequiredDateTime,
        event_schedule: EventSchedule,
        additional_attendees: list[str] | None = None,
    ) -> None:
        if scheduling_type == SchedulingType.BOOK:
            subject_header = "New Event"
            details = "A new event has been scheduled."
        elif scheduling_type == SchedulingType.RESCHEDULE:
            subject_header = "Update"
            details = "The event has been rescheduled"
        else:
            subject_header = "Cancelled"
            details = "The event has been canceled"

        email_client = SendGridMailClient()
        local_starts_at = convert_utc_to_local(
            utc_datetime=starts_at,
            local_timezone=event_schedule.timezone
            if event_schedule.timezone
            else "UTC",
        )
        formatted_starts_at = local_starts_at.strftime(
            f"%I:%M%p %a, %b %d, %Y {local_starts_at.tzname()}"
        )
        rendered_event_title = self.variable_service.render_event_title(
            event_title_template=event_schedule.event_title_template,
            meeting_duration=event_schedule.duration_minutes,
            scheduler_full_name=guest_name,
        )
        event_title = rendered_event_title or event_schedule.scheduler_title
        for host in hosts_info:
            email_content = self._generate_organizer_notification_email(
                details=details,
                organizer_name=host.name,
                hosts_info=hosts_info,
                guest_name=guest_name,
                guest_email=guest_email,
                guest_notes=guest_notes,
                starts_at=formatted_starts_at,
                additional_attendees=additional_attendees,
                event_title=event_title,
            )
            mail_message = Mail(
                to_emails=host.email,
                from_email=Email(
                    name="Reevo Notification", email="<EMAIL>"
                ),
                subject=Subject(
                    f"{subject_header}: {guest_name} - {formatted_starts_at} - {event_title}"
                ),
                html_content=email_content,
            )

            await email_client.send_mail(mail_message=mail_message)

    async def _send_notification_email_to_guest(
        self,
        scheduling_type: SchedulingType,
        booking_id: str,
        meeting_url: str | None,
        organizer_name: str,
        hosts_info: list[EventScheduleBookingHost],
        guest_name: str,
        guest_email: str,
        guest_notes: str | None,
        starts_at: ZoneRequiredDateTime,
        event_schedule: EventSchedule,
        additional_attendees: list[str] | None = None,
        ics_content: str | None = None,
    ) -> None:
        if scheduling_type == SchedulingType.BOOK:
            subject_header = "Invitation"
            details = f"This is a confirmation of the meeting with {organizer_name}."
        elif scheduling_type == SchedulingType.RESCHEDULE:
            subject_header = "Rescheduled"
            details = f"The meeting with {organizer_name} is rescheduled"
        else:
            subject_header = "Cancelled"
            details = f"The meeting with {organizer_name} is canceled"

        email_client = SendGridMailClient()
        local_starts_at = convert_utc_to_local(
            utc_datetime=starts_at,
            local_timezone=event_schedule.timezone
            if event_schedule.timezone
            else "UTC",
        )
        formatted_starts_at = local_starts_at.strftime(
            f"%I:%M%p %a, %b %d, %Y {local_starts_at.tzname()}"
        )
        duration = event_schedule.duration_minutes

        additional_attendees_content = None
        if additional_attendees:
            formatted_additional_attendees = [
                f"<a href='mailto:{attendee}'>{attendee}</a>"
                for attendee in additional_attendees
            ]
            additional_attendees_content = (
                f"<p>{', '.join(formatted_additional_attendees)}</p>"
                if additional_attendees
                else None
            )
        reschedule_url = (
            f"{settings.public_app_base_url}/public/meeting/reschedule/{booking_id}"
        )
        if subject_header == "Cancelled" or not meeting_url:
            join_meeting_content = ""
        else:
            join_meeting_content = f"<p>Click <strong><a href='{meeting_url}'>here</a></strong> to join the meeting.<p>"
        hosts = [f"<a href='mailto:{h.email}'>{h.name}</a>" for h in hosts_info]
        rendered_event_title = self.variable_service.render_event_title(
            event_title_template=event_schedule.event_title_template,
            meeting_duration=event_schedule.duration_minutes,
            scheduler_full_name=guest_name,
        )
        event_title = rendered_event_title or event_schedule.scheduler_title
        content = f"""
                    <p>Hi {guest_name},</p>
                    <br>
                    <p>{details}</p>
                    <br>
                    <p><strong>Event Title:</strong> {event_title}</p>
                    <p><strong>Host(s):</strong> {", ".join(hosts)}</p>
                    <p><strong>Additional Guests:</strong><br>{additional_attendees_content}</p>
                    <p><strong>Guest Notes:</strong> {guest_notes}</p>
                    <p><strong>Event Date/Time:</strong> {formatted_starts_at}</p>
                    <p><strong>Duration:</strong> {duration} minutes</p>
                    <p><strong>Timezone:</strong> {event_schedule.timezone}</p>
                    <br>
                    {join_meeting_content}
                    <p>Click <strong><a href='{reschedule_url}'>here</a></strong> to modify or cancel your appointment<p>
                """
        html_content = self._get_content_with_template(content)
        if hosts_info and len(hosts_info) == 1:
            host_substr = f" <> {hosts_info[0].name}"
        elif organizer_name:
            host_substr = f" <> {organizer_name}"
        else:
            host_substr = ""

        mail_message = Mail(
            to_emails=guest_email,
            from_email=Email(
                name="Reevo Notification", email="<EMAIL>"
            ),
            subject=Subject(
                f"{subject_header}: {event_title} - {guest_name}{host_substr} @ {formatted_starts_at}"
            ),
            html_content=html_content,
        )
        if ics_content:
            mail_message.attachment = [
                Attachment(
                    file_content=FileContent(ics_content),
                    file_name="invite.ics",
                    file_type="text/calendar; method=REQUEST",
                    disposition="attachment",
                )
            ]

        await email_client.send_mail(mail_message=mail_message)

    async def _send_notification_email_to_additional_guests(
        self,
        scheduling_type: SchedulingType,
        meeting_url: str | None,
        organizer_name: str,
        hosts_info: list[EventScheduleBookingHost],
        guest_name: str,
        guest_email: str,
        guest_notes: str | None,
        starts_at: ZoneRequiredDateTime,
        event_schedule: EventSchedule,
        additional_attendees: list[str] | None,
        ics_content: str | None = None,
    ) -> None:
        if not additional_attendees:
            return

        if scheduling_type == SchedulingType.BOOK:
            subject_header = "Invitation"
            details = f"You have been invited to a meeting with {organizer_name} scheduled by {guest_name}."
        elif scheduling_type == SchedulingType.RESCHEDULE:
            subject_header = "Rescheduled"
            details = f"This is a rescheduled notification of the meeting with {organizer_name} scheduled by {guest_name}"
        else:
            subject_header = "Cancelled"
            details = f"The meeting with {organizer_name} scheduled by {guest_name} is cancelled."

        email_client = SendGridMailClient()
        local_starts_at = convert_utc_to_local(
            utc_datetime=starts_at,
            local_timezone=event_schedule.timezone
            if event_schedule.timezone
            else "UTC",
        )
        formatted_starts_at = local_starts_at.strftime(
            f"%I:%M%p %a, %b %d, %Y {local_starts_at.tzname()}"
        )
        duration = event_schedule.duration_minutes
        formatted_additional_attendees = [
            f"<a href='mailto:{attendee}'>{attendee}</a>"
            for attendee in additional_attendees
        ]
        formatted_additional_attendees.append(
            f"<a href='mailto:{guest_email}'>{guest_email}</a>"
        )
        additional_attendees_content = (
            f"<p>{', '.join(formatted_additional_attendees)}</p>"
        )
        if subject_header == "Cancelled" or not meeting_url:
            join_meeting_content = ""
        else:
            join_meeting_content = f"<p>Click <strong><a href='{meeting_url}'>here</a></strong> to join the meeting.<p>"
        hosts = [f"<a href='mailto:{h.email}'>{h.name}</a>" for h in hosts_info]
        rendered_event_title = self.variable_service.render_event_title(
            event_title_template=event_schedule.event_title_template,
            meeting_duration=event_schedule.duration_minutes,
            scheduler_full_name=guest_name,
        )
        event_title = rendered_event_title or event_schedule.scheduler_title
        content = f"""
                        <p>Hi,</p>
                        <br>
                        <p>{details}</p>
                        <br>
                        <p><strong>Event Title:</strong> {event_title}</p>
                        <p><strong>Host(s):</strong> {", ".join(hosts)}</p>
                        <p><strong>Guests:</strong><br>{additional_attendees_content}</p>
                        <p><strong>Guest Notes:</strong> {guest_notes}</p>
                        <p><strong>Event Date/Time:</strong> {formatted_starts_at}</p>
                        <p><strong>Duration:</strong> {duration} minutes</p>
                        <p><strong>Timezone:</strong> {event_schedule.timezone}</p>
                        <br>
                        {join_meeting_content}
                    """
        html_content = self._get_content_with_template(content)
        mail_message = Mail(
            to_emails=additional_attendees,
            from_email=Email(
                name="Reevo Notification", email="<EMAIL>"
            ),
            subject=Subject(
                f"{subject_header}: {event_title} - {guest_name} <> {organizer_name} @ {formatted_starts_at}"
            ),
            html_content=html_content,
        )
        if ics_content:
            mail_message.attachment = [
                Attachment(
                    file_content=FileContent(ics_content),
                    file_name="invite.ics",
                    file_type="text/calendar; method=REQUEST",
                    disposition="attachment",
                )
            ]
        await email_client.send_mail(mail_message=mail_message)

    @classmethod
    def _generate_organizer_notification_email(
        cls,
        details: str,
        organizer_name: str,
        hosts_info: list[EventScheduleBookingHost],
        guest_name: str,
        guest_email: str,
        guest_notes: str | None,
        starts_at: str,
        additional_attendees: list[str] | None,
        event_title: str,
    ) -> str:
        additional_attendees_content = None
        if additional_attendees:
            formatted_additional_attendees = [
                f"<a href='mailto:{attendee}'>{attendee}</a>"
                for attendee in additional_attendees
            ]
            additional_attendees_content = (
                f"<p>{'.'.join(formatted_additional_attendees)}</p>"
                if additional_attendees
                else None
            )
        hosts = [f"<a href='mailto:{h.email}'>{h.name}</a>" for h in hosts_info]
        content = f"""
            <p>Hi {organizer_name},</p>
            <p>{details}</p>
            <br>
            <p><strong>Event Title:</strong> {event_title}</p>
            <p><strong>Host(s):</strong> {", ".join(hosts)}</p>
            <p><strong>Invitee:</strong> {guest_name}</p>
            <p><strong>Invitee Email:</strong> <a href='mailto:{guest_email}'>{guest_email}</a></p>
            <p><strong>Additional Guests:</strong><br>{additional_attendees_content}</p>
            <p><strong>Guest Notes:</strong> {guest_notes}</p>
            <p><strong>Event Date/Time:</strong> {starts_at}</p>
        """
        return cls._get_content_with_template(content)

    def _generate_ical_content(
        self,
        organizer_name: str,
        organizer_email: str,
        ical_uid: str,
        guest_name: str,
        guest_email: str,
        starts_at: ZoneRequiredDateTime,
        ends_at: ZoneRequiredDateTime,
        additional_attendees: list[str] | None,
        event_schedule: EventSchedule,
    ) -> str:
        rendered_event_title = self.variable_service.render_event_title(
            event_title_template=event_schedule.event_title_template,
            meeting_duration=event_schedule.duration_minutes,
            scheduler_full_name=guest_name,
        )
        event_title = rendered_event_title or event_schedule.scheduler_title

        cal = Calendar()
        cal.add("prodid", "-//Reevo.ai//Reevo AI Scheduler//EN")
        cal.add("version", "2.0")
        cal.add("calscale", "GREGORIAN")
        cal.add("method", "REQUEST")
        event = IcalEvent()
        event.add("name", event_title)
        event.add("dtstart", starts_at)
        event.add("dtend", ends_at)
        event.add("dtstamp", zoned_utc_now())
        event.add("created", zoned_utc_now())
        event.add("summary", event_title)
        event.add("location", vText("Virtual Meeting Room"))

        organizer = vCalAddress(f"MAILTO:{organizer_email}")
        organizer.params["name"] = vText(organizer_name)
        event["organizer"] = organizer
        event["uid"] = ical_uid
        event.add("priority", 5)

        attendee = vCalAddress(f"MAILTO:{guest_email}")
        attendee.params["cutype"] = vText("INDIVIDUAL")
        attendee.params["role"] = vText("REQ-PARTICIPANT")
        attendee.params["cn"] = vText(guest_name)
        attendee.params["partstat"] = vText("NEEDS-ACTION")
        attendee.params["rsvp"] = vText("TRUE")
        event.add("attendee", attendee, encode=0)
        if additional_attendees:
            for additional_attendee in additional_attendees:
                attendee = vCalAddress(f"MAILTO:{additional_attendee}")
                attendee.params["cutype"] = vText("INDIVIDUAL")
                attendee.params["role"] = vText("REQ-PARTICIPANT")
                attendee.params["partstat"] = vText("NEEDS-ACTION")
                attendee.params["rsvp"] = vText("TRUE")
                event.add("attendee", attendee, encode=0)

        cal.add_component(event)
        return cal.to_ical().decode("utf-8")  # type: ignore[no-any-return]

    @classmethod
    def _get_content_with_template(cls, content: str) -> str:
        return f"""
                <!DOCTYPE html>
                <html lang="en">
                <head>
                    <meta charset="UTF-8">
                    <style>
                        body {{
                            font-family: Arial, sans-serif;
                            margin: 0;
                            padding: 0;
                            background-color: #f4f4f4;
                        }}
                        .email-container {{
                            max-width: 600px;
                            margin: 20px auto;
                            padding: 20px;
                            background-color: #ffffff;
                            border: 1px solid #dddddd;
                            box-shadow: 0 2px 3px rgba(0,0,0,0.1);
                        }}
                        .header {{
                            background-color: #4CAF50;
                            color: #ffffff;
                            padding: 10px 20px;
                            text-align: center;
                        }}
                        .content p {{
                            font-size: 16px;
                            color: #333333;
                            line-height: 1.5;
                            margin: 10px 0;
                        }}
                        .content a {{
                            color: #1a73e8;
                        }}
                        .footer {{
                            text-align: center;
                            font-size: 12px;
                            color: #777777;
                            padding: 10px 20px;
                            border-top: 1px solid #eeeeee;
                        }}
                    </style>
                </head>
                <body>
                    <div class="email-container">
                        <div class="content">
                            {content}
                        </div>
                    </div>
                </body>
                </html>
                """


def get_event_schedule_notification_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> EventScheduleNotificationService:
    email_client = SendGridMailClient()
    event_schedule_repo = EventScheduleRepository(engine=db_engine)
    user_repo = UserRepository(engine=db_engine)
    calendar_account_repo = CalendarAccountRepository(engine=db_engine)
    user_integration_repo = UserIntegrationRepository(engine=db_engine)
    return EventScheduleNotificationService(
        email_client=email_client,
        event_schedule_repo=event_schedule_repo,
        user_repo=user_repo,
        calendar_account_repo=calendar_account_repo,
        user_integration_repo=user_integration_repo,
        variable_service=get_variable_service(db_engine=db_engine),
    )
