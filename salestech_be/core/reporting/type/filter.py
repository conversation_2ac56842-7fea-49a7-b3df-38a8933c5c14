import datetime
from typing import Any
from uuid import UUID

from pydantic import BaseModel

# Define allowed value types for filters
FilterValue = Any  # type: ignore[explicit-any]
FILTER_BETWEEN_VALUES_COUNT = 2


class Filter(BaseModel):
    col: str
    op: str
    val: FilterValue
    col_alias: str | None = None

    def __str__(self) -> str:
        return self.to_sql()

    def __repr__(self) -> str:
        return self.__str__()

    def to_sql(self, col_alias: str | None = None) -> str:  # noqa: C901, PLR0911, PLR0912
        """Convert the filter to a SQL WHERE clause.
        Returns:
            str: A SQL WHERE clause fragment representing this filter.
        """
        col_name = col_alias or self.col
        # Handle different operators and value types
        if self.op in ("=", "!=", ">", "<", ">=", "<="):
            # For None values, convert to IS NULL or IS NOT NULL
            if self.val is None:
                if self.op == "=":
                    return f"{col_name} IS NULL"
                elif self.op == "!=":
                    return f"{col_name} IS NOT NULL"
                else:
                    raise ValueError(f"Invalid operator '{self.op}' for NULL value")

            # For other types (numbers, booleans), use as is
            elif isinstance(self.val, (int, float, bool)):
                return f"{col_name} {self.op} {self.val}"

            # For string values, add quotes
            elif isinstance(self.val, (str, UUID, datetime.datetime, datetime.date)):
                return f"{col_name} {self.op} '{str(self.val).replace("'", "'")}'"

            else:
                raise ValueError(f"Unsupported value type: {type(self.val)}")

        elif self.op.upper() == "IN":
            # Handle IN operator with list of values
            if not isinstance(self.val, (list, tuple)):
                raise ValueError("Value for IN operator must be a list or tuple")

            # Format each value based on its type
            formatted_values = []
            for v in self.val:
                if isinstance(v, (str, UUID, datetime.datetime, datetime.date)):
                    formatted_values.append(f"'{str(v).replace("'", "'")}'")
                elif v is None:
                    formatted_values.append("NULL")
                else:
                    formatted_values.append(str(v))

            values_str = ", ".join(formatted_values)
            return f"{col_name} IN ({values_str})"

        elif self.op.upper() == "BETWEEN":
            if (
                not isinstance(self.val, (list, tuple))
                or len(self.val) != FILTER_BETWEEN_VALUES_COUNT
            ):
                raise ValueError(
                    "Value for BETWEEN operator must be a list/tuple with exactly 2 values"
                )

            # Format the two values based on their type
            formatted_values = []
            for v in self.val:
                if isinstance(v, (str, UUID, datetime.datetime, datetime.date)):
                    formatted_values.append(f"'{str(v).replace("'", "'")}'")
                elif v is None:
                    raise ValueError("NULL values not supported for BETWEEN operator")
                else:
                    formatted_values.append(str(v))
            return f"{col_name} BETWEEN {formatted_values[0]} AND {formatted_values[1]}"

        elif self.op.upper() == "LIKE":
            if not isinstance(self.val, str):
                raise ValueError("Value for LIKE operator must be a string")
            return f"{col_name} LIKE '{str(self.val).replace("'", "'")}'"

        else:
            raise ValueError(f"Unsupported operator: {self.op}")
