from uuid import UUID

from salestech_be.common.type.patch_request import is_unset
from salestech_be.core.domain_crm_association.types import (
    CreateMeetingCrmAssociation,
    DeleteMeetingCrmAssociation,
)
from salestech_be.db.models.domain_crm_association import DomainCRMAssociationRole
from salestech_be.db.models.meeting import (
    Meeting,
)
from salestech_be.ree_logging import get_logger
from salestech_be.web.api.meeting.schema import (
    PatchMeetingRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


class _MeetingCrmAssociationMapper:
    """
    Internal class intended to be used only in meeting modules that provides static
    functions for crm association related operations.
    """

    @staticmethod
    async def map_meeting_to_user_associations_create(
        meeting: Meeting,
        organization_id: UUID,
        user_id: UUID,
        pipeline_id: UUID | None = None,
        account_id: UUID | None = None,
        user_calendar_event_id: UUID | None = None,
    ) -> list[CreateMeetingCrmAssociation]:
        user_associations_to_create: list[CreateMeetingCrmAssociation] = []
        meeting_user_ids = {
            (invitee.user_id, invitee.is_organizer)
            for invitee in meeting.invitees_or_empty_list()
            if invitee.user_id
        }
        for meeting_user_id, is_organizer in meeting_user_ids:
            user_associations_to_create.append(
                CreateMeetingCrmAssociation(
                    organization_id=organization_id,
                    meeting_id=meeting.id,
                    contact_id=None,
                    user_id=meeting_user_id,
                    pipeline_id=pipeline_id,
                    account_id=account_id,
                    created_by_user_id=user_id,
                    user_calendar_event_id=user_calendar_event_id,
                    association_role=DomainCRMAssociationRole.ORGANIZER
                    if is_organizer
                    else DomainCRMAssociationRole.PARTICIPANT,
                )
            )
        return user_associations_to_create

    @staticmethod
    async def map_meeting_to_contact_associations_create(
        meeting: Meeting,
        organization_id: UUID,
        user_id: UUID,
        pipeline_id: UUID | None = None,
        account_id: UUID | None = None,
        user_calendar_event_id: UUID | None = None,
    ) -> list[CreateMeetingCrmAssociation]:
        contact_associations_to_create: list[CreateMeetingCrmAssociation] = []
        contact_ids_to_email = {
            (invitee.contact_id, invitee.is_organizer): invitee.contact_email
            for invitee in meeting.invitees_or_empty_list()
            if invitee.contact_id
        }
        for (contact_id, is_organizer), email in contact_ids_to_email.items():
            contact_associations_to_create.append(
                CreateMeetingCrmAssociation(
                    organization_id=organization_id,
                    meeting_id=meeting.id,
                    contact_id=contact_id,
                    user_id=None,
                    pipeline_id=pipeline_id,
                    account_id=account_id,
                    created_by_user_id=user_id,
                    user_calendar_event_id=user_calendar_event_id,
                    email=email,
                    association_role=DomainCRMAssociationRole.ORGANIZER
                    if is_organizer
                    else DomainCRMAssociationRole.PARTICIPANT,
                )
            )
        return contact_associations_to_create

    @staticmethod
    async def map_meeting_to_user_associations_update(
        existing_meeting: Meeting,
        updated_meeting: Meeting,
        patch_request: PatchMeetingRequest,
        organization_id: UUID,
        user_id: UUID,
    ) -> tuple[list[CreateMeetingCrmAssociation], list[DeleteMeetingCrmAssociation]]:
        user_associations_to_create: list[CreateMeetingCrmAssociation] = []
        user_associations_to_delete: list[DeleteMeetingCrmAssociation] = []
        existing_user_ids = {
            (invitee.user_id, invitee.is_organizer)
            for invitee in existing_meeting.invitees_or_empty_list()
            if invitee.user_id
        }
        for invitee in updated_meeting.invitees_or_empty_list():
            if (
                invitee.user_id
                and (invitee.user_id, invitee.is_organizer) not in existing_user_ids
            ):
                user_associations_to_create.append(
                    CreateMeetingCrmAssociation(
                        organization_id=organization_id,
                        user_id=invitee.user_id,
                        contact_id=None,
                        created_by_user_id=user_id,
                        pipeline_id=updated_meeting.pipeline_id,
                        account_id=updated_meeting.account_id,
                        user_calendar_event_id=patch_request.user_calendar_event_id
                        if patch_request.user_calendar_event_id
                        and not is_unset(patch_request.user_calendar_event_id)
                        else None,
                        meeting_id=updated_meeting.id,
                        association_role=DomainCRMAssociationRole.ORGANIZER
                        if invitee.is_organizer
                        else DomainCRMAssociationRole.PARTICIPANT,
                    )
                )
            elif (
                invitee.user_id
                and (invitee.user_id, invitee.is_organizer) in existing_user_ids
            ):
                existing_user_ids.remove((invitee.user_id, invitee.is_organizer))
        for existing_user_id, is_organizer in existing_user_ids:
            user_associations_to_delete.append(
                DeleteMeetingCrmAssociation(
                    organization_id=organization_id,
                    meeting_id=updated_meeting.id,
                    user_id=existing_user_id,
                    deleted_by_user_id=user_id,
                    association_role=DomainCRMAssociationRole.ORGANIZER
                    if is_organizer
                    else DomainCRMAssociationRole.PARTICIPANT,
                )
            )
        return user_associations_to_create, user_associations_to_delete

    @staticmethod
    async def map_meeting_to_contact_associations_update(
        existing_meeting: Meeting,
        updated_meeting: Meeting,
        patch_request: PatchMeetingRequest,
        organization_id: UUID,
        user_id: UUID,
    ) -> tuple[list[CreateMeetingCrmAssociation], list[DeleteMeetingCrmAssociation]]:
        contact_associations_to_create: list[CreateMeetingCrmAssociation] = []
        contact_associations_to_delete: list[DeleteMeetingCrmAssociation] = []
        existing_contact_ids_to_email = {
            (invitee.contact_id, invitee.is_organizer): invitee.contact_email
            for invitee in existing_meeting.invitees_or_empty_list()
            if invitee.contact_id
        }
        for invitee in updated_meeting.invitees_or_empty_list():
            if (
                invitee.contact_id
                and (invitee.contact_id, invitee.is_organizer)
                not in existing_contact_ids_to_email
            ):
                contact_associations_to_create.append(
                    CreateMeetingCrmAssociation(
                        organization_id=organization_id,
                        meeting_id=updated_meeting.id,
                        contact_id=invitee.contact_id,
                        user_id=None,
                        created_by_user_id=user_id,
                        pipeline_id=updated_meeting.pipeline_id,
                        account_id=updated_meeting.account_id,
                        user_calendar_event_id=patch_request.user_calendar_event_id
                        if patch_request.user_calendar_event_id
                        and not is_unset(patch_request.user_calendar_event_id)
                        else None,
                        email=invitee.contact_email,
                        association_role=DomainCRMAssociationRole.ORGANIZER
                        if invitee.is_organizer
                        else DomainCRMAssociationRole.PARTICIPANT,
                    )
                )
            elif (
                invitee.contact_id
                and (invitee.contact_id, invitee.is_organizer)
                in existing_contact_ids_to_email
            ):
                existing_contact_ids_to_email.pop(
                    (invitee.contact_id, invitee.is_organizer)
                )
        for (_, is_organizer), email in existing_contact_ids_to_email.items():
            contact_associations_to_delete.append(
                DeleteMeetingCrmAssociation(
                    organization_id=organization_id,
                    meeting_id=updated_meeting.id,
                    email=email,
                    deleted_by_user_id=user_id,
                    association_role=DomainCRMAssociationRole.ORGANIZER
                    if is_organizer
                    else DomainCRMAssociationRole.PARTICIPANT,
                )
            )
        return contact_associations_to_create, contact_associations_to_delete
