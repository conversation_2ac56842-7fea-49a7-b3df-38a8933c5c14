from typing import Annotated, Literal, Self
from uuid import UUID

from pydantic import BaseModel, Field, model_validator

from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.common.type.shape_constrained_model import ShapeConstrainedModel
from salestech_be.core.opportunity_stage_criteria.base import (
    BaseCriteriaContext,
    PriorityScore,
)
from salestech_be.core.opportunity_stage_criteria.constant import CriteriaContextType
from salestech_be.db.models.crm_ai_rec import CrmAIRecType
from salestech_be.util.enum_util import NameValueStrEnum

PaperProcessItemUpdateFields = Literal["name", "process", "priority", "status", "note"]


class PaperProcessItemStatus(NameValueStrEnum):
    """
    The status of a paper process item.
    """

    # The paper process item is finished.
    FINISHED = "FINISHED"
    # The paper process item is in progress.
    IN_PROGRESS = "IN_PROGRESS"
    # The paper process item is not started.
    NOT_STARTED = "NOT_STARTED"
    # The paper process item is blocked.
    BLOCKED = "BLOCKED"


class PaperProcess(BaseModel):
    """
    The administrative and contractual procedures required to finalize agreements
    once a purchase decision has been made. This includes legal reviews, procurement
    negotiations, security assessments, and formal approval workflows necessary to
    execute a deal. The paper process encompasses all documentation requirements,
    contract terms, legal considerations, and internal bureaucratic steps that must
    be completed before implementation can begin. This component represents the
    often-overlooked administrative elements that frequently cause delays in
    finalizing deals that have otherwise received conceptual approval from
    decision-makers.
    """

    id: Annotated[UUID, Field(description="The ID of the paper process item")]
    name: Annotated[str, Field(description="The name of the paper process item")]
    process: Annotated[str, Field(description="The process of the decision")]
    priority: Annotated[
        PriorityScore | None,
        Field(description="The priority of the decision"),
    ] = None
    status: Annotated[
        PaperProcessItemStatus,
        Field(description="The status of the paper process item"),
    ]
    note: Annotated[
        str | None, Field(description="The additional note of the paper process item")
    ]


class PaperProcessContext(BaseCriteriaContext):
    """
    The administrative and contractual procedures required to finalize agreements
    once a purchase decision has been made. This includes legal reviews, procurement
    negotiations, security assessments, and formal approval workflows necessary to
    execute a deal. The paper process encompasses all documentation requirements,
    contract terms, legal considerations, and internal bureaucratic steps that must
    be completed before implementation can begin. This component represents the
    often-overlooked administrative elements that frequently cause delays in
    finalizing deals that have otherwise received conceptual approval from
    decision-makers.
    """

    context_type: Literal[CriteriaContextType.PAPER_PROCESS] = (
        CriteriaContextType.PAPER_PROCESS
    )

    ordered_paper_processes: Annotated[
        tuple[PaperProcess, ...],
        Field(
            description="The ordered paper processes of the opportunity, they are ordered by execution order from first to last"
        ),
    ]

    @model_validator(mode="after")
    def validate_paper_processes(self) -> Self:
        # sort the paper processes by priority
        self.ordered_paper_processes = tuple(
            sorted(
                self.ordered_paper_processes,
                key=lambda x: x.priority or PriorityScore.ONE,
                reverse=True,
            )
        )
        return self


class PaperProcessItemUpdate(ShapeConstrainedModel[PaperProcess]):
    id: UUID
    name: UnsetAware[str] = UNSET
    process: UnsetAware[str] = UNSET
    priority: UnsetAware[PriorityScore | None] = UNSET
    status: UnsetAware[PaperProcessItemStatus] = UNSET
    note: UnsetAware[str | None] = UNSET


class PaperProcessItemUpdateWithRecTypes(BaseModel):
    updated_item: PaperProcessItemUpdate
    field_update_types: dict[PaperProcessItemUpdateFields, CrmAIRecType] = {}


class PaperProcessItemUpdateMulti(BaseModel):
    create: list[PaperProcess] | None = None
    delete: list[UUID] | None = None
    update: list[PaperProcessItemUpdateWithRecTypes] | None = None
    delete_pending_recs: list[UUID] | None = None
