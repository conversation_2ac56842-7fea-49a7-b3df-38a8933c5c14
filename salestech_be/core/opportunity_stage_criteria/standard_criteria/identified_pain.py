from typing import Annotated, Literal, Self
from uuid import UUID

from pydantic import BaseModel, Field, model_validator

from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.common.type.shape_constrained_model import ShapeConstrainedModel
from salestech_be.core.opportunity_stage_criteria.base import (
    BaseCriteriaContext,
    PriorityScore,
)
from salestech_be.core.opportunity_stage_criteria.constant import CriteriaContextType
from salestech_be.db.models.crm_ai_rec import CrmAIRecType

IdentifiedPainItemUpdateFields = Literal["name", "priority", "note"]


class IdentifiedPainItem(BaseModel):
    """
    A specific business challenge, problem, or issue that an organization
    is experiencing that could potentially be addressed by a solution. Pain points
    manifest as operational inefficiencies, financial losses, competitive disadvantages,
    compliance risks, or other critical business issues that impact performance and results.
    """

    id: Annotated[UUID, Field(description="The ID of the identified pain item")]
    name: Annotated[str, Field(description="The name of the identified pain item")]
    priority: Annotated[
        PriorityScore | None,
        Field(description="The priority of the pain point"),
    ] = None
    note: Annotated[
        str | None, Field(description="The additional note of the pain point")
    ]


class IdentifiedPainPointContext(BaseCriteriaContext):
    """
    The specific business challenges, problems, and issues that an organization
    is experiencing that could potentially be addressed by a solution. Pain points
    manifest as operational inefficiencies, financial losses, competitive disadvantages,
    compliance risks, or other critical business issues that impact performance and results.
    These pains exist across multiple dimensions—from explicit, acknowledged problems to
    implicit, unrecognized issues—and have measurable business impacts that create motivation
    for change. Pain points represent the gap between current state and desired outcomes that
    drives interest in potential solutions.
    """

    context_type: Literal[CriteriaContextType.IDENTIFIED_PAIN] = (
        CriteriaContextType.IDENTIFIED_PAIN
    )

    ordered_pain_items: Annotated[
        tuple[IdentifiedPainItem, ...],
        Field(
            description="The ordered pain points of the opportunity, they are ordered by priority from high to low"
        ),
    ]

    @model_validator(mode="after")
    def validate_pain_points(self) -> Self:
        # sort the pain points by priority
        self.ordered_pain_items = tuple(
            sorted(
                self.ordered_pain_items,
                key=lambda x: x.priority or PriorityScore.ONE,
                reverse=True,
            )
        )
        return self


class IdentifiedPainItemUpdate(ShapeConstrainedModel[IdentifiedPainItem]):
    id: UUID
    priority: UnsetAware[PriorityScore | None] = UNSET
    note: UnsetAware[str | None] = UNSET


class IdentifiedPainItemUpdateWithRecTypes(BaseModel):
    updated_item: IdentifiedPainItemUpdate
    field_update_types: dict[IdentifiedPainItemUpdateFields, CrmAIRecType] = {}


class IdentifiedPainItemUpdateMulti(BaseModel):
    create: list[IdentifiedPainItem] | None = None
    delete: list[UUID] | None = None
    update: list[IdentifiedPainItemUpdateWithRecTypes] | None = None
    delete_pending_recs: list[UUID] | None = None
