# Economic Buyer
from typing import Annotated, Literal
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.common.type.shape_constrained_model import ShapeConstrainedModel
from salestech_be.core.opportunity_stage_criteria.base import (
    BaseCriteriaContext,
)
from salestech_be.core.opportunity_stage_criteria.constant import CriteriaContextType
from salestech_be.db.models.crm_ai_rec import CrmAIRecType

EconomicBuyerItemUpdateFields = Literal["contact_id", "note"]


class EconomicBuyer(BaseModel):
    id: Annotated[UUID, Field(description="The ID of the economic buyer")]
    contact_id: Annotated[UUID, Field(description="The ID of the contact")]
    note: Annotated[
        str | None, Field(description="The additional note of the economic buyer")
    ] = None


class EconomicBuyerContext(BaseCriteriaContext):
    """
    The ultimate decision-maker with financial authority to approve purchases
    without requiring permission from others. While buying committees typically
    include multiple stakeholders, the Economic Buyer possesses the final
    financial sign-off power. This person often operates at higher organizational
    levels with broader business concerns than technical evaluators or end-users.
    Economic Buyers focus primarily on business outcomes, strategic value, and
    overall impact rather than specific features or technical details. They are
    the individuals who control budgets and can authorize expenditures, making
    their identification crucial in complex sales situations.
    """

    context_type: Literal[CriteriaContextType.ECONOMIC_BUYER] = (
        CriteriaContextType.ECONOMIC_BUYER
    )

    economic_buyers: Annotated[
        tuple[EconomicBuyer, ...],
        Field(description="List of economic buyers of the opportunity"),
    ]


class EconomicBuyerItemUpdate(ShapeConstrainedModel[EconomicBuyer]):
    id: UUID
    contact_id: UnsetAware[UUID] = UNSET
    note: UnsetAware[str | None] = UNSET


class EconomicBuyerItemUpdateWithRecTypes(BaseModel):
    updated_item: EconomicBuyerItemUpdate
    field_update_types: dict[EconomicBuyerItemUpdateFields, CrmAIRecType] = {}


class EconomicBuyerItemUpdateMulti(BaseModel):
    create: list[EconomicBuyer] | None = None
    delete: list[UUID] | None = None
    update: list[EconomicBuyerItemUpdateWithRecTypes] | None = None
    delete_pending_recs: list[UUID] | None = None
