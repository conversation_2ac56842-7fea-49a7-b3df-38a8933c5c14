from typing import Annotated, Literal
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.common.type.shape_constrained_model import ShapeConstrainedModel
from salestech_be.core.opportunity_stage_criteria.base import (
    BaseCriteriaContext,
)
from salestech_be.core.opportunity_stage_criteria.constant import CriteriaContextType
from salestech_be.db.models.crm_ai_rec import CrmAIRecType

CompetitionContextUpdateFields = Literal["solution_differentiation"]
CompetitorItemUpdateFields = Literal[
    "competitor_name", "competitor_advantage", "competitor_disadvantage", "note"
]


class Competitor(BaseModel):
    """
    A competitor is a company that offers a similar product or service to the buyer. The
    competitor's advantage and disadvantage are from the buyer's perspective.
    """

    id: Annotated[UUID, Field(description="The ID of the competitor")]
    competitor_name: Annotated[str, Field(description="The name of the competitor")]
    competitor_advantage: Annotated[
        str | None,
        Field(description="The advantage of the competitor from buyer's perspective"),
    ] = None
    competitor_disadvantage: Annotated[
        str | None,
        Field(
            description="The disadvantage of the competitor from buyer's perspective"
        ),
    ] = None
    note: Annotated[
        str | None,
        Field(description="The additional note to add more details of the competition"),
    ] = None


class CompetitionContext(BaseCriteriaContext):
    """
    The alternative options customers consider when evaluating solutions, including
    direct competitors, internal development possibilities, maintaining status quo,
    or allocating resources to entirely different priorities. Competition represents
    all the factors and alternatives that could prevent a specific solution from being
    selected. This component encompasses understanding the strengths and weaknesses
    of competing options, how prospects perceive these alternatives, and the specific
    advantages competitors claim. Competition exists not just between similar products
    but between fundamentally different approaches to solving business problems.
    """

    context_type: Literal[CriteriaContextType.COMPETITION] = (
        CriteriaContextType.COMPETITION
    )

    solution_differentiation: Annotated[
        str | None,
        Field(
            description="The unique selling proposition of seller's solution, which makes it the best choice for the buyer compared to other competitors"
        ),
    ] = None

    competitors: Annotated[
        tuple[Competitor, ...],
        Field(
            description=" A list of competitors, each competitor is for a unique competitor of the opportunity."
        ),
    ]


class CompetitorItemUpdate(ShapeConstrainedModel[Competitor]):
    id: UUID
    competitor_name: UnsetAware[str] = UNSET
    competitor_advantage: UnsetAware[str | None] = UNSET
    competitor_disadvantage: UnsetAware[str | None] = UNSET
    note: UnsetAware[str | None] = UNSET


class CompetitorItemUpdateWithRecTypes(BaseModel):
    updated_item: CompetitorItemUpdate
    field_update_types: dict[CompetitorItemUpdateFields, CrmAIRecType] = {}


class CompetitorItemUpdateMulti(BaseModel):
    create: list[Competitor] | None = None
    delete: list[UUID] | None = None
    update: list[CompetitorItemUpdateWithRecTypes] | None = None
    delete_pending_recs: list[UUID] | None = None


class CompetitionContextUpdate(ShapeConstrainedModel[CompetitionContext]):
    solution_differentiation: UnsetAware[str | None] = UNSET


class CompetitionContextUpdateWithRecTypes(BaseModel):
    updated_context: CompetitionContextUpdate
    field_update_types: dict[CompetitionContextUpdateFields, CrmAIRecType] = {}
