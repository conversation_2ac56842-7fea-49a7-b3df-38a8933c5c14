from decimal import Decimal
from typing import Annotated, Literal
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.common.type.shape_constrained_model import ShapeConstrainedModel
from salestech_be.core.opportunity_stage_criteria.base import (
    BaseCriteriaContext,
    PriorityScore,
)
from salestech_be.core.opportunity_stage_criteria.constant import CriteriaContextType
from salestech_be.db.models.crm_ai_rec import CrmAIRecType
from salestech_be.util.enum_util import NameValueStrEnum

DecisionCriteriaContextUpdateFields = Literal[
    "product_solution_fit_note", "budget_amount", "budget_note"
]
DecisionCriteriaItemUpdateFields = Literal["name", "priority", "status", "note"]


class DecisionCriteriaItemStatus(NameValueStrEnum):
    """
    The qualification status of a decision criteria.
    """

    # The criteria is met.
    MET = "MET"
    # The criteria is not met.
    NOT_MET = "NOT_MET"
    # The criteria is in progress.
    IN_PROGRESS = "IN_PROGRESS"


class DecisionCriteriaItem(BaseModel):
    """
    A decision criteria item is a specific criteria that the buyer is evaluating the solution against. It is meant
    to provide an evaluation of the solution against the criteria.
    """

    id: Annotated[UUID, Field(description="The ID of the decision criteria item")]
    name: Annotated[str, Field(description="One criteria of the decision")]
    priority: Annotated[
        PriorityScore | None,
        Field(
            description="The priority of the decision, the higher the number, the more important the decision"
        ),
    ] = None
    status: Annotated[
        DecisionCriteriaItemStatus,
        Field(description="The qualification status of the decision"),
    ]
    note: Annotated[
        str | None, Field(description="The additional note of the decision criteria")
    ]


class DecisionCriteriaContext(BaseCriteriaContext):
    """
    The specific factors and requirements organizations use to evaluate potential solutions
    before making purchasing decisions. These criteria encompass technical requirements,
    business priorities, risk factors, and organizational preferences that determine which
    solution best fits their needs. Common decision criteria include cost considerations,
    expected ROI, technical compatibility, compliance requirements, scalability potential,
    integration capabilities, support options, and risk mitigation factors. Different
    stakeholders often prioritize different criteria based on their departmental perspectives
    and business concerns, creating a multi-dimensional evaluation framework that varies
    between organizations and industries.
    """

    context_type: Literal[CriteriaContextType.DECISION_CRITERIA] = (
        CriteriaContextType.DECISION_CRITERIA
    )

    product_solution_fit_note: Annotated[
        str | None,
        Field(
            description="The product solution fit note of the opportunity. Describe how and why the seller's product is a good fit for the buyer's needs."
        ),
    ] = None

    budget_amount: Annotated[
        Decimal | None,
        Field(
            description="The estimated budget amount that the buyer is willing to spend on the product"
        ),
    ] = None

    budget_note: Annotated[
        str | None,
        Field(
            description="The additional note of budget that the buyer is willing to spend on the product"
        ),
    ] = None

    decision_criteria_items: Annotated[
        tuple[DecisionCriteriaItem, ...],
        Field(description="List of individual decision criteria of the opportunity"),
    ]


class DecisionCriteriaItemUpdate(ShapeConstrainedModel[DecisionCriteriaItem]):
    id: UUID
    name: UnsetAware[str] = UNSET
    priority: UnsetAware[PriorityScore | None] = UNSET
    status: UnsetAware[DecisionCriteriaItemStatus] = UNSET
    note: UnsetAware[str | None] = UNSET


class DecisionCriteriaItemUpdateWithRecTypes(BaseModel):
    updated_item: DecisionCriteriaItemUpdate
    field_update_types: dict[DecisionCriteriaItemUpdateFields, CrmAIRecType] = {}


class DecisionCriteriaItemUpdateMulti(BaseModel):
    create: list[DecisionCriteriaItem] | None = None
    delete: list[UUID] | None = None
    update: list[DecisionCriteriaItemUpdateWithRecTypes] | None = None
    delete_pending_recs: list[UUID] | None = None


class DecisionCriteriaContextUpdate(ShapeConstrainedModel[DecisionCriteriaContext]):
    product_solution_fit_note: UnsetAware[str | None] = UNSET
    budget_amount: UnsetAware[Decimal | None] = UNSET
    budget_note: UnsetAware[str | None] = UNSET


class DecisionCriteriaContextUpdateWithRecTypes(BaseModel):
    """
    A decision criteria context update with rec types.
    """

    updated_context: DecisionCriteriaContextUpdate
    field_update_types: dict[DecisionCriteriaContextUpdateFields, CrmAIRecType] = {}
