from typing import Annotated, Literal
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.common.type.shape_constrained_model import ShapeConstrainedModel
from salestech_be.core.opportunity_stage_criteria.base import (
    BaseCriteriaContext,
)
from salestech_be.core.opportunity_stage_criteria.constant import CriteriaContextType
from salestech_be.db.models.crm_ai_rec import CrmAIRecType

ChampionItemUpdateFields = Literal["contact_id", "name", "note"]


class Champion(BaseModel):
    """
    An advocate within a prospect's organization who believes in a proposed
    solution and actively promotes it to influence the buying decision. An effective
    champion possesses conviction in the solution's value, credibility among
    decision-makers, and influence in the decision process. They provide critical
    intelligence about internal politics, help navigate organizational complexities,
    and advocate for solutions when vendors aren't present. A champion typically has
    personal stakes in the success of the initiative and serves as an internal ally
    who can articulate value propositions in terms that resonate with their colleagues.
    """

    id: Annotated[UUI<PERSON>, <PERSON>(description="The ID of the champion")]
    contact_id: Annotated[
        UUID, <PERSON>(description="The ID of the contact, who is the champion")
    ]
    name: Annotated[str, <PERSON>(description="The name of the champion")]
    note: Annotated[
        str | None, Field(description="The additional note of the champion")
    ] = None


class ChampionContext(BaseCriteriaContext):
    """
    Internal advocates within a prospect's organization who believe in a proposed
    solution and actively promote it to influence the buying decision. Effective
    champions possess conviction in the solution's value, credibility among
    decision-makers, and influence in the decision process. They provide critical
    intelligence about internal politics, help navigate organizational complexities,
    and advocate for solutions when vendors aren't present. Champions typically have
    personal stakes in the success of the initiative and serve as internal allies who
    can articulate value propositions in terms that resonate with their colleagues.
    """

    context_type: Literal[CriteriaContextType.CHAMPION] = CriteriaContextType.CHAMPION

    champions: Annotated[
        tuple[Champion, ...],
        Field(description="List of champions of the opportunity"),
    ]


class ChampionItemUpdate(ShapeConstrainedModel[Champion]):
    id: UUID
    contact_id: UnsetAware[UUID] = UNSET
    name: UnsetAware[str] = UNSET
    note: UnsetAware[str | None] = UNSET


class ChampionItemUpdateWithRecTypes(BaseModel):
    updated_item: ChampionItemUpdate
    field_update_types: dict[ChampionItemUpdateFields, CrmAIRecType] = {}


class ChampionItemUpdateMulti(BaseModel):
    create: list[Champion] | None = None
    delete: list[UUID] | None = None
    update: list[ChampionItemUpdateWithRecTypes] | None = None
    delete_pending_recs: list[UUID] | None = None
