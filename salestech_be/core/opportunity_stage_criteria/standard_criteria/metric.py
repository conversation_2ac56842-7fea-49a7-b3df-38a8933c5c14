from typing import Annotated, Literal
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.common.type.shape_constrained_model import ShapeConstrainedModel
from salestech_be.core.opportunity_stage_criteria.base import (
    BaseCriteriaContext,
)
from salestech_be.core.opportunity_stage_criteria.constant import CriteriaContextType
from salestech_be.db.models.crm_ai_rec import CrmAIRecType

MetricContextUpdateFields = Literal[
    "business_negative_consequences", "business_positive_outcomes"
]
MetricItemUpdateFields = Literal["name", "note"]


class MetricItem(BaseModel):
    """
    A quantifiable business outcome that demonstrates tangible value of a solution to customers. A metric
    transforms abstract value propositions into specific, measurable outcomes that can be tracked and validated.
    """

    id: Annotated[UUID, Field(description="The ID of the metric item")]
    name: Annotated[str, Field(description="The name of the metric")]
    note: Annotated[str | None, Field(description="The additional note of the metric")]


class MetricContext(BaseCriteriaContext):
    """
    Quantifiable business outcomes that demonstrate tangible value of a solution to customers.
    These key performance indicators represent measurable results such as cost savings, revenue growth,
    or efficiency improvements. Metrics serve as objective evidence of success and create a common language
    between organizations by establishing concrete targets. They connect directly to strategic objectives
    and provide a foundation for evaluating potential business impact. Metrics transform abstract value
    propositions into specific, measurable outcomes that can be tracked and validated, making them essential
    for articulating the quantifiable benefits a solution can deliver.
    """

    context_type: Literal[CriteriaContextType.METRIC] = CriteriaContextType.METRIC

    business_negative_consequences: Annotated[
        str | None,
        Field(
            description="The negative business impact, if buyer does not solve their problems"
        ),
    ] = None

    business_positive_outcomes: Annotated[
        str | None,
        Field(
            description="The positive business impact, if buyer solves their problems by adopting a solution"
        ),
    ] = None

    metrics: Annotated[
        tuple[MetricItem, ...],
        Field(description="List of quantifiable business benefits of the opportunity"),
    ]


class MetricItemUpdate(ShapeConstrainedModel[MetricItem]):
    id: UUID
    name: UnsetAware[str] = UNSET
    note: UnsetAware[str | None] = UNSET


class MetricItemUpdateWithRecTypes(BaseModel):
    updated_item: MetricItemUpdate
    field_update_types: dict[MetricItemUpdateFields, CrmAIRecType] = {}


class MetricItemUpdateMulti(BaseModel):
    create: list[MetricItem] | None = None
    delete: list[UUID] | None = None
    update: list[MetricItemUpdateWithRecTypes] | None = None
    delete_pending_recs: list[UUID] | None = None


class MetricContextUpdate(ShapeConstrainedModel[MetricContext]):
    business_negative_consequences: UnsetAware[str | None] = UNSET
    business_positive_outcomes: UnsetAware[str | None] = UNSET


class MetricContextUpdateWithRecTypes(BaseModel):
    updated_context: MetricContextUpdate
    field_update_types: dict[MetricContextUpdateFields, CrmAIRecType] = {}
