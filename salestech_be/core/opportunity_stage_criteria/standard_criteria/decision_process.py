from typing import Annotated, Literal
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.common.type.shape_constrained_model import ShapeConstrainedModel
from salestech_be.core.opportunity_stage_criteria.base import (
    BaseCriteriaContext,
    PriorityScore,
)
from salestech_be.core.opportunity_stage_criteria.constant import CriteriaContextType
from salestech_be.db.models.crm_ai_rec import CrmAIRecType
from salestech_be.util.enum_util import NameValueStrEnum
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime

DecisionProcessContextUpdateFields = Literal[
    "solution_launches_by",
    "implementation_starts_by",
    "decision_by",
    "testing_starts_by",
    "testing_ends_by",
]
DecisionProcessItemUpdateFields = Literal["name", "priority", "status", "note"]


class DecisionProcessItemStatus(NameValueStrEnum):
    """
    The status of a decision process item.
    """

    # The decision process item is finished.
    FINISHED = "FINISHED"
    # The decision process item is in progress.
    IN_PROGRESS = "IN_PROGRESS"
    # The decision process item is not started.
    NOT_STARTED = "NOT_STARTED"
    # The decision process item is blocked.
    BLOCKED = "BLOCKED"


class DecisionProcessItem(BaseModel):
    """
    A decision process item is a specific decision that the buyer is making. It is meant
    to provide an evaluation of the solution against the decision.
    """

    id: Annotated[UUID, Field(description="The ID of the decision process item")]
    name: Annotated[str, Field(description="The name of the decision process item")]
    priority: Annotated[
        PriorityScore | None,
        Field(
            description="The priority of the decision, the higher the number, the more important the decision"
        ),
    ] = None
    status: Annotated[
        DecisionProcessItemStatus,
        Field(description="The status of the decision process item"),
    ]
    note: Annotated[
        str | None,
        Field(description="The additional note of the decision process item"),
    ]


class DecisionProcessContext(BaseCriteriaContext):
    """
    The complete workflow that shows how buying decisions move from initial
    consideration to final approval within an organization. This process maps
    out who must be involved, what evaluation activities occur (demos, trials,
    references), approval chains, and critical timelines for each phase. The
    decision process reveals the sequence of events and internal procedures
    that must be navigated before a purchase can be finalized. It encompasses
    technical evaluation processes, budget approval mechanisms, and the formal
    and informal pathways a potential purchase follows as it moves through
    various organizational levels and departments.
    """

    context_type: Literal[CriteriaContextType.DECISION_PROCESS] = (
        CriteriaContextType.DECISION_PROCESS
    )

    solution_launches_by: Annotated[
        ZoneRequiredDateTime | None,
        Field(
            description="Buyer's expected launch date of seller's solution or product"
        ),
    ] = None

    implementation_starts_by: Annotated[
        ZoneRequiredDateTime | None,
        Field(
            description="Buyer's expected start date of implementation with seller's solution or product"
        ),
    ] = None

    decision_by: Annotated[
        ZoneRequiredDateTime | None,
        Field(
            description="Buyer's expected decision date of whether to purchase seller's solution or product"
        ),
    ] = None

    testing_starts_by: Annotated[
        ZoneRequiredDateTime | None,
        Field(
            description="Buyer's expected start date of testing and validating seller's solution or product"
        ),
    ] = None

    testing_ends_by: Annotated[
        ZoneRequiredDateTime | None,
        Field(
            description="Buyer's expected end date of testing and validating seller's solution or product"
        ),
    ] = None

    ordered_decision_processes: Annotated[
        tuple[DecisionProcessItem, ...],
        Field(
            description="The ordered decision processes of the opportunity, they are ordered by execution order from first to last"
        ),
    ]


class DecisionProcessItemUpdate(ShapeConstrainedModel[DecisionProcessItem]):
    id: UUID
    name: UnsetAware[str] = UNSET
    priority: UnsetAware[PriorityScore | None] = UNSET
    status: UnsetAware[DecisionProcessItemStatus] = UNSET
    note: UnsetAware[str | None] = UNSET


class DecisionProcessItemUpdateWithRecTypes(BaseModel):
    updated_item: DecisionProcessItemUpdate
    field_update_types: dict[DecisionProcessItemUpdateFields, CrmAIRecType] = {}


class DecisionProcessItemUpdateMulti(BaseModel):
    create: list[DecisionProcessItem] | None = None
    delete: list[UUID] | None = None
    update: list[DecisionProcessItemUpdateWithRecTypes] | None = None
    delete_pending_recs: list[UUID] | None = None


class DecisionProcessContextUpdate(ShapeConstrainedModel[DecisionProcessContext]):
    solution_launches_by: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    implementation_starts_by: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    decision_by: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    testing_starts_by: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    testing_ends_by: UnsetAware[ZoneRequiredDateTime | None] = UNSET


class DecisionProcessContextUpdateWithRecTypes(BaseModel):
    updated_context: DecisionProcessContextUpdate
    field_update_types: dict[DecisionProcessContextUpdateFields, CrmAIRecType] = {}
