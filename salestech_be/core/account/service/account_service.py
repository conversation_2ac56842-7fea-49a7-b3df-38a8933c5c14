import uuid
from typing import Annotated, cast
from uuid import UUID

from fastapi import Depends
from sqlalchemy.exc import IntegrityError

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import (
    ConflictResourceError,
)
from salestech_be.common.exception.exception import (
    ConflictErrorDetails,
    InvalidArgumentError,
    ReferentialViolationError,
    ReferentialViolationErrorDetails,
    ResourceNotFoundError,
)
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.schema_manager.std_object_field_identifier import (
    AccountField,
    StdObjectIdentifiers,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    AccountRelationship,
)
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import (
    UNSET,
    BasePatchRequest,
    UnsetAware,
    specified,
    specified_or_default,
)
from salestech_be.core.account.service.account_query_service import (
    AccountQueryService,
    get_account_query_service,
)
from salestech_be.core.account.service_api_schema import (
    ShiftAccountStatusRequest,
    ShiftAccountStatusResponse,
)
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.account.types_v2 import AccountV2, PatchAccountRequest
from salestech_be.core.approval_request.service.approval_service import (
    ApprovalService,
    get_approval_service_with_engine,
)
from salestech_be.core.common.domain_service import DomainService
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.contact.service.contact_query_service import (
    ContactQueryService,
    get_contact_query_service,
)
from salestech_be.core.contact.service.contact_service import (
    ContactService,
    get_contact_service,
)
from salestech_be.core.custom_object.service.custom_object_service import (
    CustomObjectService,
    get_custom_object_service,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.data.service.query_service import get_domain_object_query_service
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.core.imports.models.import_csv_fields import CUSTOM_FIELD_DATA
from salestech_be.core.imports.service.crm_sync_push_service import (
    CrmSyncPushService,
    get_crm_sync_push_service,
)
from salestech_be.core.metadata.service.stage_criteria_service import (
    StageCriteriaService,
    get_stage_criteria_service,
)
from salestech_be.core.metadata.types import StageCriteriaEvaluateResult
from salestech_be.core.notification.service.notification_service import (
    NotificationService,
    get_notification_service_by_db_engine,
)
from salestech_be.core.notification.types import SendNotificationRequest
from salestech_be.core.research_agent.research_agent_service import (
    ResearchAgentService,
    get_research_agent_service,
)
from salestech_be.core.research_agent.research_metric import (
    ResearchMetricsParams,
    research_metric,
)
from salestech_be.core.research_agent.types import IntelProviderTypeEnum, ResearchView
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.dao.address_repository import AddressRepository
from salestech_be.db.dao.event_schedule_repository import EventScheduleRepository
from salestech_be.db.dao.pipeline_repository import PipelineRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.research_dto import AccountResearchDto
from salestech_be.db.models.account import Account as DbAccount
from salestech_be.db.models.account import (
    AccountStatus,
    AccountUpdate,
)
from salestech_be.db.models.address import Address as DbAddress
from salestech_be.db.models.approval_request import (
    AccountStateBackwardTransitApprovalRequestReference,
    ApprovalRequestStatus,
    ApprovalRequestType,
)
from salestech_be.db.models.contact import Contact as DbContact
from salestech_be.db.models.core.types import EntityParticipant
from salestech_be.db.models.notification import (
    NotificationCRMChangeData,
    NotificationReferenceIdType,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.pydantic_types.str import validate_domain_name
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none, one_row_only
from salestech_be.web.api.common.container import (
    DeleteEntityResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger("AccountService")


class AccountService(DomainService[AccountV2]):
    def __init__(
        self,
        contact_service: Annotated[ContactService, Depends()],
        contact_query_service: Annotated[ContactQueryService, Depends()],
        account_repository: Annotated[AccountRepository, Depends()],
        address_repository: Annotated[AddressRepository, Depends()],
        custom_object_service: Annotated[CustomObjectService, Depends()],
        account_query_service: Annotated[AccountQueryService, Depends()],
        pipeline_repository: Annotated[PipelineRepository, Depends()],
        research_agent_service: Annotated[ResearchAgentService, Depends()],
        approval_service: Annotated[ApprovalService, Depends()],
        stage_criteria_service: Annotated[StageCriteriaService, Depends()],
        notification_service: Annotated[NotificationService, Depends()],
        event_schedule_repository: Annotated[EventScheduleRepository, Depends()],
        crm_sync_push_service: Annotated[
            CrmSyncPushService, Depends(get_crm_sync_push_service)
        ],
        feature_flag_service: Annotated[
            FeatureFlagService, Depends(get_feature_flag_service)
        ],
    ):
        super().__init__(feature_flag_service=feature_flag_service)
        self.contact_service = contact_service
        self.contact_query_service = contact_query_service
        self.account_repository = account_repository
        self.address_repository = address_repository
        self.custom_object_service = custom_object_service
        self.account_query_service = account_query_service
        self.research_agent_service = research_agent_service
        self.approval_service = approval_service
        self.stage_criteria_service = stage_criteria_service
        self.pipeline_repository = pipeline_repository
        self.notification_service = notification_service
        self.event_schedule_repository = event_schedule_repository
        self.crm_sync_push_service = crm_sync_push_service

    def get_relative_redirection_url(self, account_id: UUID) -> str:
        return f"/accounts/{account_id}"

    async def list_accounts_v2(
        self,
        organization_id: UUID,
        only_include_account_ids: UnsetAware[set[UUID]] = UNSET,
        include_custom_object: bool | None = False,
    ) -> list[AccountV2]:
        return await self.account_query_service.list_accounts_v2(
            organization_id=organization_id,
            only_include_account_ids=only_include_account_ids,
            include_custom_object=include_custom_object,
        )

    async def list_accounts_v2_paginated(
        self,
        organization_id: UUID,
        only_include_account_ids: UnsetAware[set[UUID]] = UNSET,
        include_custom_object: bool | None = False,
        offset: int = 0,
        limit: int | None = None,
    ) -> list[AccountV2]:
        """List accounts with offset-based pagination."""
        accounts = await self.account_query_service.list_accounts_v2(
            organization_id=organization_id,
            only_include_account_ids=only_include_account_ids,
            include_custom_object=include_custom_object,
        )

        if limit:
            return accounts[offset : offset + limit]
        return accounts[offset:]

    async def get_account_v2(
        self,
        account_id: UUID,
        organization_id: UUID,
        include_custom_object: bool | None = False,
    ) -> AccountV2:
        return await self.account_query_service.get_account_v2(
            account_id=account_id,
            organization_id=organization_id,
            include_custom_object=include_custom_object,
        )

    async def get_or_create_account(
        self,
        create_account_request: CreateAccountRequest,
        organization_id: uuid.UUID,
        user_id: uuid.UUID,
    ) -> AccountV2:
        # Find existing account by domain, keep the same logic as create account to get domain name
        domain_name: str | None = (
            create_account_request.domain_name
            or validate_domain_name(create_account_request.official_website)
        )
        if domain_name and (
            existing_db_account
            := await self.account_repository._find_unique_by_column_values(
                DbAccount,
                exclude_deleted_or_archived=False,
                organization_id=organization_id,
                domain_name=domain_name,
            )
        ):
            return await self.account_query_service.get_account_v2(
                account_id=existing_db_account.id,
                organization_id=organization_id,
            )
        return await self.create_account_v2(
            create_account_request=create_account_request,
            organization_id=organization_id,
            user_id=user_id,
        )

    async def create_account_v2(
        self,
        create_account_request: CreateAccountRequest,
        organization_id: uuid.UUID,
        user_id: uuid.UUID,
        import_record_id: uuid.UUID | None = None,
    ) -> AccountV2:
        dirty_address = (
            DbAddress(
                id=uuid.uuid4(),
                street_one=create_account_request.address.street_one,
                street_two=create_account_request.address.street_two,
                zip_code=create_account_request.address.zip_code,
                city=create_account_request.address.city,
                state=create_account_request.address.state,
                country=create_account_request.address.country,
                created_at=create_account_request.created_at or zoned_utc_now(),
                created_by_user_id=user_id,
                organization_id=organization_id,
            )
            if create_account_request.address
            else None
        )
        dirty_account = DbAccount(
            id=uuid.uuid4(),
            display_name=create_account_request.display_name,
            status=create_account_request.status,
            official_website=create_account_request.official_website,
            domain_name=create_account_request.domain_name
            or validate_domain_name(create_account_request.official_website),
            description=create_account_request.description,
            keyword_list=create_account_request.keyword_list,
            category_list=create_account_request.category_list,
            technology_list=create_account_request.technology_list,
            estimated_annual_revenue=create_account_request.estimated_annual_revenue,
            estimated_employee_count=create_account_request.estimated_employee_count,
            linkedin_url=create_account_request.linkedin_url,
            facebook_url=create_account_request.facebook_url,
            zoominfo_url=create_account_request.zoominfo_url,
            owner_user_id=create_account_request.owner_user_id,
            x_url=create_account_request.x_url,
            address_id=dirty_address.id if dirty_address else None,
            created_at=create_account_request.created_at or zoned_utc_now(),
            created_source=create_account_request.created_source,
            created_by_user_id=user_id,
            organization_id=organization_id,
            company_id=create_account_request.company_id,
        )
        # New account creation
        try:
            if (
                not create_account_request.created_source
                or create_account_request.created_source.is_not_crm()
            ):
                # Sync to CRM first
                await self.crm_sync_push_service.sync_push_obj_account_create(
                    organization_id=organization_id,
                    account=dirty_account,
                    address=dirty_address,
                    import_record_id=import_record_id,
                )

            db_account = await self.account_repository.insert_account(
                db_account=dirty_account,
                db_address=dirty_address,
            )
        except IntegrityError:
            if (
                existing_account
                := await self.account_repository._find_unique_by_column_values(
                    DbAccount,
                    exclude_deleted_or_archived=False,
                    organization_id=organization_id,
                    domain_name=dirty_account.domain_name,
                )
            ):
                if existing_account.archived_at:
                    logger.info(
                        "Account with domain_name and organization_id already "
                        "exists but is archived, updating and reactivating it",
                        organization_id=organization_id,
                        existing_account_id=existing_account.id,
                    )
                    db_account = (
                        await self.account_repository.override_existing_account(
                            overriding_account=dirty_account.model_copy(
                                update={"id": existing_account.id}
                            ),
                            new_address=dirty_address,
                        )
                    )
                else:
                    logger.warning(
                        "Account with domain_name and organization_id already exists",
                        organization_id=organization_id,
                        existing_account_id=existing_account.id,
                    )
                    raise ConflictResourceError(
                        additional_error_details=ConflictErrorDetails(
                            code=ErrorCode.ACCOUNT_ALREADY_EXISTS_WITH_DOMAIN_NAME,
                            details="Account with domain_name and organization_id "
                            "already exists",
                            reference_id=str(existing_account.id),
                            conflicted_existing_object=StdObjectIdentifiers.account.identifier,
                            conflicted_existing_object_attrs={
                                AccountField.organization_id: existing_account.organization_id,
                                AccountField.id: existing_account.id,
                                AccountField.domain_name: existing_account.domain_name,
                                AccountField.display_name: existing_account.display_name,
                            },
                        )
                    )
            raise

        logger.info(f"create_account_request: {create_account_request}")
        # Note: The CSV type must be "account" to create custom object data
        if create_account_request.custom_field_data and (
            create_account_request.csv_type == "account"
            or create_account_request.csv_type is None
        ):
            await self.custom_object_service.create_custom_object_data_by_extension_id(
                user_id=user_id,
                organization_id=organization_id,
                parent_object_name=ExtendableStandardObject.account,
                extension_id=db_account.id,
                custom_field_data_by_field_id=create_account_request.custom_field_data,
            )

        return await self.get_account_v2(
            account_id=db_account.id,
            organization_id=organization_id,
            include_custom_object=True,
        )

    async def reactivate_by_id(
        self,
        user_id: uuid.UUID,
        organization_id: uuid.UUID,
        account_id: uuid.UUID,
    ) -> list[DbAccount]:
        return await self.account_repository.reactivate_by_id(
            user_id=user_id, organization_id=organization_id, account_id=account_id
        )

    async def archive_by_id(
        self,
        user_id: uuid.UUID,
        organization_id: uuid.UUID,
        account_id: uuid.UUID,
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> list[DbAccount]:
        active_pipelines = (
            await self.pipeline_repository.map_pipelines_by_account_ids(
                organization_id=organization_id,
                account_ids=[account_id],
                exclude_archived=True,
            )
        ).get(account_id)
        if active_pipelines:
            raise ReferentialViolationError(
                "Account has active pipeline associations, cannot archive",
                additional_error_details=ReferentialViolationErrorDetails(
                    error_code=ErrorCode.ACCOUNT_HAS_ACTIVE_PIPELINE_ASSOCIATIONS,
                    violating_object=StdObjectIdentifiers.account.identifier,
                    violating_relationship_id=AccountRelationship.account__from__pipeline,
                    dependent_object=StdObjectIdentifiers.pipeline.identifier,
                    dependent_object_record_ids=[
                        pipeline.id for pipeline in active_pipelines
                    ],
                ),
            )
        return await self.account_repository._update_by_column_values(
            table_model=DbAccount,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            column_value_to_query={
                "id": account_id,
                "organization_id": organization_id,
            },
            exclude_deleted_or_archived=False,
            column_to_update=AccountUpdate(
                archived_by_user_id=user_id,
                archived_at=zoned_utc_now(),
                updated_by_user_id=user_id,
            ),
        )

    async def shift_account_status(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        account_id: UUID,
        req: ShiftAccountStatusRequest,
    ) -> ShiftAccountStatusResponse:
        # 00 fetch non-archived account
        # TODO: why is AccountV2.state a str not an AccountState?
        db_account = await self.account_query_service.get_db_account_by_id(
            account_id=account_id,
            organization_id=organization_id,
        )
        if db_account.archived_at:
            raise InvalidArgumentError("Cannot shift state of archived account")

        # 01 there are no select-list-value objs for account state, unlike contact stage
        # 02 there are no select-list objs for account state either, accordingly
        current_status = AccountStatus(db_account.status)
        target_status = req.target_status

        # 03 no-op if target and current state are the same
        if req.target_status == db_account.status:
            return ShiftAccountStatusResponse(
                account=await self.account_query_service.get_account_v2(
                    account_id=account_id,
                    organization_id=organization_id,
                ),
            )

        # 04 check stage shift criteria from current (exclusive) or beginning of sl (if current is missing) to target (inclusive)
        shifting_backward = current_status and target_status < current_status
        states = AccountStatus.range(
            le=target_status,
            gt=current_status if current_status else None,
        )
        # evaluate criteria for each state in range
        for account_state in states:
            eval_result = await self.stage_criteria_service.evaluate_stage_criteria(
                organization_id=organization_id,
                record_id=account_id,
                stage_selector=account_state,
                primary_object_identifier=AccountV2.object_id,
            )
            # fail and return if any stage in range fails criteria
            if not eval_result.is_success:
                return ShiftAccountStatusResponse(
                    account=await self.account_query_service.get_account_v2(
                        organization_id=organization_id,
                        account_id=account_id,
                    ),
                    stage_criteria_evaluation_result=eval_result,
                )

        # 05 check for approval needed in case of backward shift
        approval_all_clear = True
        approval_request = None
        if shifting_backward:  # approval needed for backward shift
            (
                approval_all_clear,
                approval_request,
            ) = await self.approval_service.check_approval_for_shift(
                organization_id=organization_id,
                user_id=user_id,
                approval_request_type=ApprovalRequestType.ACCOUNT_STATE_BACKWARD,
                approval_request_display_name="Backward Shift Approval",
                approval_request_description="Backward shift approval request",
                approval_request_ref=AccountStateBackwardTransitApprovalRequestReference(
                    reference_model=ApprovalRequestType.ACCOUNT_STATE_BACKWARD,
                    account_id=account_id,
                    target_status=target_status,
                ),
                existing_approval_request_id=req.approval_request_id,
            )

        # 06 update account state
        shifted = False
        if approval_all_clear:
            await self.account_repository.update_by_tenanted_primary_key(
                table_model=DbAccount,
                primary_key_to_value=db_account.primary_key_to_value(),
                organization_id=organization_id,
                column_to_update=AccountUpdate(
                    status=req.target_status,
                    updated_by_user_id=user_id,
                    updated_at=zoned_utc_now(),
                ),
            )
            if approval_request:
                await self.approval_service.update_approval_state(
                    organization_id=organization_id,
                    user_id=user_id,
                    approval_request=approval_request,
                    approval_request_status=ApprovalRequestStatus.EXECUTED,
                )
            shifted = True
        return ShiftAccountStatusResponse(
            account=await self.account_query_service.get_account_v2(
                account_id=account_id,
                organization_id=organization_id,
            ),
            shifted=shifted,
            stage_criteria_evaluation_result=StageCriteriaEvaluateResult(
                is_success=True,
                stage_selector=target_status,
                evaluate_details=[],
            ),
        )

    async def list_accounts_by_ids_untenanted(
        self,
        account_ids: list[UUID],
        exclude_deleted_or_archived: bool = True,
    ) -> list[DbAccount]:
        return await self.account_repository.list_by_ids_untenanted(
            ids=list(account_ids),
            exclude_deleted_or_archived=exclude_deleted_or_archived,
        )

    async def patch_by_id_v2(  # noqa: C901
        self,
        organization_id: uuid.UUID,
        user_id: uuid.UUID,
        account_id: uuid.UUID,
        request: PatchAccountRequest,
        import_record_id: uuid.UUID | None = None,
    ) -> AccountV2:
        logger.bind(
            request=request,
            user_id=user_id,
            organization_id=organization_id,
            account_id=account_id,
        ).info("Patch account request")
        fields_to_update = request.model_dump(
            exclude={"address", CUSTOM_FIELD_DATA, "participant_user_id_list"}
        )
        fields_to_update["updated_at"] = zoned_utc_now()
        fields_to_update["updated_by_user_id"] = user_id
        if specified(request.address) and request.address is None:
            fields_to_update["address_id"] = None

        # patch request does not have domain_name, here generates domain from website url.
        if specified(request.official_website):
            fields_to_update["domain_name"] = validate_domain_name(
                request.official_website
            )
        if (
            specified(request.participant_user_id_list)
            and request.participant_user_id_list is not None
        ):
            fields_to_update["participants"] = (
                EntityParticipant.list_from_request_field(
                    request.participant_user_id_list
                )
            )

        try:
            if not request.created_source or request.created_source.is_not_crm():
                await self.crm_sync_push_service.sync_push_obj_account_update(
                    organization_id=organization_id,
                    account_id=account_id,
                    fields_to_update=fields_to_update,
                    import_record_id=import_record_id,
                )
            patched_account = (
                await self.account_repository.update_by_tenanted_primary_key(
                    table_model=DbAccount,
                    primary_key_to_value={"id": account_id},
                    organization_id=organization_id,
                    column_to_update=fields_to_update,
                )
            )
        except IntegrityError:
            if (
                existing_account
                := await self.account_repository._find_unique_by_column_values(
                    DbAccount,
                    exclude_deleted_or_archived=False,
                    organization_id=organization_id,
                    domain_name=fields_to_update["domain_name"],
                )
            ):
                logger.warning(
                    "Another Account with domain_name and organization_id "
                    "already exists",
                    organization_id=organization_id,
                    existing_account_id=existing_account.id,
                )
                raise ConflictResourceError(
                    additional_error_details=ConflictErrorDetails(
                        code=ErrorCode.ACCOUNT_ALREADY_EXISTS_WITH_DOMAIN_NAME,
                        details="Account with domain_name and organization_id "
                        "already exists",
                        reference_id=str(existing_account.id),
                        conflicted_existing_object=StdObjectIdentifiers.account.identifier,
                        conflicted_existing_object_attrs={
                            AccountField.organization_id: existing_account.organization_id,
                            AccountField.id: existing_account.id,
                            AccountField.domain_name: existing_account.domain_name,
                            AccountField.display_name: existing_account.display_name,
                        },
                    )
                )
            raise
        if not patched_account:
            raise ResourceNotFoundError("Account not found")

        if specified(request.linkedin_url) or specified(request.official_website):
            await self.research_agent_service.delete_intel_company_association_by_account_id(
                account_id=account_id,
                user_id=user_id,
            )

        if specified(request.custom_field_data) and request.custom_field_data:
            await (
                self.custom_object_service.update_custom_object_data_by_extension_id_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    parent_object_name=ExtendableStandardObject.account,
                    extension_id=account_id,
                    custom_field_data_by_field_id=request.custom_field_data,
                )
            )
        if specified(request.address) and request.address:
            if patched_account.address_id:
                await self.address_repository.update_by_tenanted_primary_key(
                    table_model=DbAddress,
                    primary_key_to_value={"id": patched_account.address_id},
                    organization_id=organization_id,
                    column_to_update=request.address.to_db_address_update(user_id),
                )
            else:
                inserted_address = not_none(
                    await self.address_repository.insert(
                        DbAddress(
                            id=uuid.uuid4(),
                            street_one=specified_or_default(
                                request.address.street_one, None
                            ),
                            street_two=specified_or_default(
                                request.address.street_two, None
                            ),
                            zip_code=specified_or_default(
                                request.address.zip_code, None
                            ),
                            city=specified_or_default(request.address.city, None),
                            state=specified_or_default(request.address.state, None),
                            country=specified_or_default(request.address.country, None),
                            created_at=zoned_utc_now(),
                            created_by_user_id=user_id,
                            organization_id=organization_id,
                        )
                    )
                )
                await self.account_repository.update_by_tenanted_primary_key(
                    table_model=DbAccount,
                    primary_key_to_value={"id": account_id},
                    organization_id=organization_id,
                    column_to_update={"address_id": inserted_address.id},
                )
        return await self.get_account_v2(
            account_id=account_id,
            organization_id=organization_id,
        )

    async def get_account_research(
        self,
        account_id: UUID,
        organization_id: UUID,
        request_user_id: UUID | None = None,
        referer: str | None = None,
    ) -> AccountResearchDto | None:
        result = await self.research_agent_service.get_research_for_account_or_none(
            account_id=account_id,
            organization_id=organization_id,
            request_user_id=request_user_id,
        )
        provider = (
            IntelProviderTypeEnum.from_str(result.latest_provider_status.name)
            if result and result.latest_provider_status
            else None
        )

        research_metric.track_company_research_availability(
            account_id=account_id,
            is_available=result is not None,
            research_metrics_params=ResearchMetricsParams(
                view=ResearchView.from_referer(referer),
                provider=provider,
                latest_provider_status=result.latest_provider_status
                if result
                else None,
            ),
        )

        if result is None:
            return None

        company_research_dto = AccountResearchDto.from_company_research(
            account_id=account_id,
            organization_id=organization_id,
            company_research=result,
            is_feedback_positive=result.is_feedback_positive,
        )
        # Track metrics for all research fields
        research_metric.track_company_research_fields(
            account_id=account_id,
            company_research_dto=company_research_dto,
            research_metrics_params=ResearchMetricsParams(
                view=ResearchView.from_referer(referer),
                provider=provider,
                latest_provider_status=result.latest_provider_status,
            ),
        )
        return company_research_dto

    async def list_account_researches(
        self,
        account_ids: list[UUID],
        organization_id: UUID,
        request_user_id: UUID | None = None,
    ) -> list[AccountResearchDto]:
        if not account_ids:
            return []

        account_research_map = (
            await self.research_agent_service.get_research_or_none_for_accounts(
                account_ids=set(account_ids),
                request_user_id=request_user_id,
                organization_id=organization_id,
            )
        )
        # TODO: handle errors on missing researches
        return [
            AccountResearchDto.from_company_research(
                account_id=account_id,
                organization_id=organization_id,
                company_research=company_research,
                is_feedback_positive=company_research.is_feedback_positive,
            )
            for account_id, company_research in account_research_map.items()
            if company_research is not None
        ]

    async def send_account_change_notification(
        self, account_v2_before: AccountV2, account_v2_after: AccountV2
    ) -> None:
        if account_v2_before.state != account_v2_after.state:
            await self.notification_service.send_notification(
                send_notification_request=SendNotificationRequest(
                    data=NotificationCRMChangeData(
                        message=f"Account {account_v2_after.display_name} status changed from {account_v2_before.state} to {account_v2_after.state}",
                        action_url=f"/accounts/{account_v2_after.id}",
                    ),
                    reference_id=str(account_v2_after.id),
                    reference_id_type=NotificationReferenceIdType.ACCOUNT,
                    activity_id=None,
                    actor_user_id=account_v2_after.updated_by_user_id,
                    recipient_user_ids=[account_v2_after.owner_user_id],
                    idempotency_key=str(uuid.uuid4()),
                ),
                organization_id=account_v2_after.organization_id,
            )

    async def patch_public_domain_accounts(
        self,
        organization_id: UUID,
        user_id: UUID,
        domain: str,
        dry_run: bool = False,
    ) -> list[tuple[DbAccount, DbContact]]:
        """
        Patch accounts with public domain emails in an organization.
        Args:
            organization_id: The organization ID
            user_id: The user performing the action
            domain: The domain to patch
            dry_run: If True, only simulate the changes without applying them
        """
        logger.bind(
            organization_id=organization_id,
            user_id=user_id,
            domain=domain,
            dry_run=dry_run,
        ).info("Patching accounts with public domain emails")

        normalized_domain = domain.replace(".", "-")
        accounts = await self.account_repository._find_by_column_values(
            DbAccount,
            organization_id=organization_id,
            domain_name=normalized_domain,
            official_website=normalized_domain,
        )
        results = []
        for account in accounts:
            logger.bind(account=account).info("Processing account")
            contacts = (
                await self.contact_query_service.map_contact_by_primary_account_ids(
                    organization_id=organization_id,
                    primary_account_ids={account.id},
                )
            ).get(account.id) or []
            contact_email_maps = (
                await self.contact_query_service.get_primary_emails_by_contact_ids(
                    organization_id=organization_id,
                    contact_ids={contact.id for contact in contacts},
                )
            )
            for contact in contacts:
                contact_email = contact_email_maps.get(contact.id)
                if contact_email and contact_email.startswith(account.display_name):
                    logger.info(
                        f"Contact email {contact_email} matches account display name {account.display_name}, skipping",
                        contact_id=contact.id,
                        account_id=account.id,
                    )
                    continue
                if not contact_email:
                    logger.info(
                        f"Contact {contact.id} has no primary email, skipping",
                        contact_id=contact.id,
                        account_id=account.id,
                    )
                    continue

                # Make sure contact used scheduler
                event_schedules = await self.event_schedule_repository.get_event_schedule_bookings_by_guest_email(
                    organization_id=organization_id,
                    guest_email=contact_email,
                )
                if not event_schedules:
                    logger.bind(contact_id=contact.id).info(
                        "Contact has no event schedule record, skipping",
                    )
                    continue

                email_parts = contact_email.split("@")
                account_name = email_parts[0]
                normalized_account_name = account_name.replace(".", "-")
                now = zoned_utc_now()
                new_account = account.model_copy(
                    update={
                        "id": uuid.uuid4(),
                        "display_name": account_name,
                        "official_website": normalized_account_name,
                        "domain_name": normalized_account_name,
                        "created_at": now,
                        "updated_at": now,
                    }
                )

                if dry_run:
                    logger.info(
                        "Would create new account for contact with different email domain (dry run)",
                        contact_id=contact.id,
                        account_id=account.id,
                    )
                else:
                    logger.bind(new_account=new_account).info("Creating new account")
                    new_account = await self.account_repository.insert(new_account)
                    logger.bind(new_account_id=new_account.id).info(
                        "Created new account for contact with different email domain",
                    )

                    logger.bind(
                        new_account_id=new_account.id, contact_id=contact.id
                    ).info("Updating contact primary account id")
                    # Update contact primary account id, let CDC take care of propagating the change
                    updated_contact = not_none(
                        await self.contact_service.update_primary_account_id(
                            organization_id=organization_id,
                            user_id=user_id,
                            contact_id=contact.id,
                            new_account_id=new_account.id,
                        )
                    )
                    logger.bind(contact_id=contact.id).info(
                        "Updated contact primary account id"
                    )
                    results.append((new_account, updated_contact))
        return results

    async def create_entity(
        self,
        organization_id: UUID,
        user_id: UUID,
        request: CreateAccountRequest,
    ) -> AccountV2:
        return await self.create_account_v2(
            create_account_request=request,
            organization_id=organization_id,
            user_id=user_id,
        )

    async def get_entity(
        self,
        entity_id: UUID,
        organization_id: UUID,
        user_id: UUID,
    ) -> AccountV2:
        return await self.account_query_service.get_account_v2(
            account_id=entity_id,
            organization_id=organization_id,
        )

    async def update_entity(
        self,
        organization_id: UUID,
        user_id: UUID,
        entity: AccountV2,
        request: BasePatchRequest,
    ) -> AccountV2:
        account_patch_request = cast(PatchAccountRequest, request)
        return await self.patch_by_id_v2(
            organization_id=organization_id,
            user_id=user_id,
            account_id=entity.id,
            request=account_patch_request,
        )

    async def remove_entity(
        self, organization_id: UUID, user_id: UUID, entity_id: UUID
    ) -> DeleteEntityResponse:
        logger.bind(
            organization_id=organization_id, user_id=user_id, account_id=entity_id
        ).info("Archive account request")
        archive_reponse = one_row_only(
            await self.archive_by_id(
                user_id=user_id,
                organization_id=organization_id,
                account_id=entity_id,
            )
        )
        return DeleteEntityResponse(
            id=entity_id,
            deleted_at=archive_reponse.archived_at,
            deleted_by_user_id=archive_reponse.archived_by_user_id,
        )

    async def authed_shift_account_status(
        self,
        user_auth_context: UserAuthContext,
        organization_id: UUID,
        entity_id: UUID,
        req: ShiftAccountStatusRequest,
    ) -> ShiftAccountStatusResponse:
        logger.bind(
            user_auth_context=user_auth_context,
            organization_id=organization_id,
            account_id=entity_id,
            req=req,
        ).info("Shift account status request")
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=entity_id,
            access_check_function=self.can_access_entity_for_patch,
        )
        return await self.shift_account_status(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            account_id=entity_id,
            req=req,
        )

    async def list_account_by_company_ids(
        self,
        organization_id: UUID,
        company_ids: list[UUID],
    ) -> list[DbAccount]:
        return await self.account_repository.list_by_company_ids(
            organization_id=organization_id,
            company_ids=company_ids,
        )


class SingletonAccountService(Singleton, AccountService):
    pass


def get_account_service(
    db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)],
) -> AccountService:
    if SingletonAccountService.has_instance():
        return SingletonAccountService.get_singleton_instance()
    return SingletonAccountService(
        contact_service=get_contact_service(db_engine=db_engine),
        contact_query_service=get_contact_query_service(db_engine=db_engine),
        account_repository=AccountRepository(engine=db_engine),
        address_repository=AddressRepository(engine=db_engine),
        custom_object_service=get_custom_object_service(db_engine=db_engine),
        account_query_service=get_account_query_service(
            db_engine=db_engine,
        ),
        research_agent_service=get_research_agent_service(
            db_engine=db_engine,
        ),
        approval_service=get_approval_service_with_engine(db_engine=db_engine),
        stage_criteria_service=get_stage_criteria_service(
            engine=db_engine,
            domain_object_query_service=get_domain_object_query_service(
                db_engine=db_engine
            ),
        ),
        notification_service=get_notification_service_by_db_engine(db_engine=db_engine),
        pipeline_repository=PipelineRepository(engine=db_engine),
        event_schedule_repository=EventScheduleRepository(engine=db_engine),
        crm_sync_push_service=get_crm_sync_push_service(db_engine=db_engine),
        feature_flag_service=get_feature_flag_service(),
    )
