import re
from typing import Annotated
from urllib.parse import unquote

import phonenumbers
import tldextract
from phonenumbers import NumberParseException
from pydantic import (
    AfterValidator,
    BeforeValidator,
    Field,
    HttpUrl,
    StringConstraints,
    TypeAdapter,
)

from salestech_be.common.type.formatted_string import strip_and_lower
from salestech_be.util.temporal_patch import is_running_in_workflow


def url_unquote(value: str) -> str:
    return unquote(value)


UrlUnquotedStr = Annotated[str, BeforeValidator(url_unquote)]


def validate_e164(phone_number: str, default_region: str = "US") -> str:
    if not is_running_in_workflow():
        try:
            parsed_number = phonenumbers.parse(phone_number, default_region)

            if phonenumbers.is_possible_number(
                parsed_number
            ) and phonenumbers.is_valid_number(parsed_number):
                # Format the number to E.164
                return phonenumbers.format_number(
                    parsed_number, phonenumbers.PhoneNumberFormat.E164
                )
            else:
                raise ValueError(f"Invalid phone number: {phone_number}")
        except NumberParseException as e:
            raise ValueError(f"{phone_number}  Bad phone number format: {e}")
    else:
        return phone_number


def parse_phone_number(phone_number_raw: str) -> tuple[str, str, str]:
    """
    Parse phone number into country code, local number, and extension (if exists).
    Support str:
        "************"
        "************ ext. 103"
        "(************* ext. 5391"
        "******-566-6639"
        ******-253-0000 (1234)"
        "+86 27 87654321,4321"
        ...
    Args:
        phone_number_raw: The raw string of phone number
    Returns:
        country_code, local number, extension(if exists)
    Raises:
        ValueError: not is_possible_number or not is_valid_number
        NumberParseException: Exception when attempting to parse a putative phone number
    """
    try:
        # If no country code is specified, the default region is set to the US
        if not phone_number_raw.strip().startswith("+"):
            parsed_number = phonenumbers.parse(phone_number_raw, "US")
        else:
            parsed_number = phonenumbers.parse(phone_number_raw)

        if phonenumbers.is_possible_number(
            parsed_number
        ) and phonenumbers.is_valid_number(parsed_number):
            # Format the number to E.164
            phone_number_str = phonenumbers.format_number(
                parsed_number, phonenumbers.PhoneNumberFormat.E164
            )
            country_code = (
                f"+{parsed_number.country_code}" if parsed_number.country_code else ""
            )
            return (
                country_code,
                phone_number_str,
                parsed_number.extension if parsed_number.extension else "",
            )
        else:
            raise ValueError(f"Invalid phone number: {phone_number_raw}")
    except NumberParseException as e:
        raise ValueError(f"{phone_number_raw}  Bad phone number format: {e}")


def validate_e164_with_extension(phone_number: str) -> str:
    if not is_running_in_workflow():
        country_code, number_e164, extension = parse_phone_number(phone_number)
        if extension:
            return f"{number_e164},{extension}"
        return number_e164
    else:
        return phone_number


PhoneNumber = Annotated[str, AfterValidator(validate_e164)]
PhoneNumberWithExtension = Annotated[str, AfterValidator(validate_e164_with_extension)]
AreaCode = Annotated[str, StringConstraints(min_length=3, max_length=3)]
TwilioSID = Annotated[str, StringConstraints(min_length=34, max_length=34)]


def is_valid_domain(url_input: str) -> bool:
    """
    Check if the given string is a valid domain.

    Args:
        url (str | None): The URL or domain to validate

    Returns:
        bool: True if the input is a valid domain, False otherwise
    """
    # Strip and lowercase the input
    url = strip_and_lower(url_input)

    # Check if input is empty or None
    if not url:
        return False

    # Prepend http:// if no protocol is present
    if not url.startswith(("http://", "https://", "ftp://")):
        url = "http://" + url

    try:
        extracted = tldextract.extract(url)

        # Validate domain conditions
        # 1. Must have a suffix (TLD)
        # 2. Must have a domain name
        # 3. Ensure domain and suffix are not empty
        return bool(extracted.suffix and extracted.domain)

    except Exception:
        return False


def validate_domain_name(url: str | None) -> str | None:
    url = strip_and_lower(url)
    if not url:
        return None

    # Prepend protocol if missing for tldextract to work correctly
    if not url.startswith(("http://", "https://", "ftp://")):
        url = "http://" + url

    try:
        extracted = tldextract.extract(url)

        # Split the subdomain into parts and filter out "www"
        subdomain = None
        if extracted.subdomain:
            sub_parts = [
                part for part in extracted.subdomain.split(".") if part.lower() != "www"
            ]
            # If multiple valid subdomains are found, take only the last one
            if sub_parts:
                subdomain = sub_parts[-1]

        # Build the final domain parts: subdomain (if any), domain, and suffix.
        parts = []
        if subdomain:
            parts.append(subdomain)
        if extracted.domain:
            parts.append(extracted.domain)
        if extracted.suffix:
            parts.append(extracted.suffix)
        return ".".join(parts)
    except Exception:
        return None


def is_linkedin_domain(url: str) -> bool:
    domain = validate_domain_name(url)
    return domain == "linkedin.com"


def extract_linkedin_handle(url: str) -> str | None:
    """Extracts the LinkedIn company handle from a given URL."""
    pattern = re.compile(
        r"(?:https?:\/\/)?(?:www\.)?linkedin\.com\/company\/([^\/\?]+)"
    )
    match = pattern.search(url)
    return match.group(1) if match else None


def extract_facebook_handle(url: str) -> str | None:
    """Extracts the Facebook handle from a given URL."""
    pattern = re.compile(
        r"(?:https?:\/\/)?(?:www\.|m\.|fb\.|meta\.)?facebook\.com\/(?:pages\/)?([^\/\?#!]+)"
    )
    match = pattern.search(url)
    return match.group(1) if match else None


def extract_twitter_handle(url: str) -> str | None:
    """Extracts the Twitter (X) handle from a given URL."""
    pattern = re.compile(
        r"(?:https?:\/\/)?(?:www\.)?(?:twitter\.com|x\.com)\/([^\/\?]+)"
    )
    match = pattern.search(url)
    return match.group(1) if match else None


def check_same_email_domain(emails: list[str]) -> bool:
    if not emails:
        return True

    first_domain = emails[0].split("@")[1].lower()
    return all(email.split("@")[1].lower() == first_domain for email in emails)


DomainName = Annotated[str, AfterValidator(validate_domain_name)]


def validate_message_id(message_id: str) -> str:
    """
    Validates that message_id conforms to the RFC 5322 message-id format.
    Message-ID format should be: <local-part@domain>

    Args:
        message_id (str): The message ID to validate.

    Returns:
        str: The validated message ID.

    Raises:
        ValueError: If message_id does not conform to the message-id format.
    """
    # RFC 5322 message-id regex
    # <AUTHOR> <EMAIL>
    message_id_regex = re.compile(r"^<[^@\s<>]+@[^@\s<>]+>$")
    if not message_id_regex.match(message_id):
        raise ValueError(
            "message_id must be a valid RFC 5322 message-id format: <local-part@domain>"
        )
    return message_id


MessageId = Annotated[str, AfterValidator(validate_message_id)]

_http_url_type_adapter: TypeAdapter[HttpUrl] = TypeAdapter(HttpUrl)


def _validate_scheme_optional_http_url(url: str) -> str:
    _to_test = url
    if not url.startswith(("http://", "https://")):
        _to_test = "http://" + url
    _http_url_type_adapter.validate_python(_to_test, strict=True)
    return url


SchemeOptionalHttpUrlStr = Annotated[
    str,
    AfterValidator(_validate_scheme_optional_http_url),
    Field(
        description="""
        Represents a URL with an optional http / https scheme
        and required registered domain.

        Examples:
        - "example.com"
        - "https://example.com"
        """
    ),
]
