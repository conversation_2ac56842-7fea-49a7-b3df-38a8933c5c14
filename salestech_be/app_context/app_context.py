import dataclasses

from httpx import AsyncClient

from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.activity.service.activity_service import ActivityService
from salestech_be.core.ai.prompt.langfuse_prompt_service import LangfusePromptService
from salestech_be.core.business_process.sales_methodology.service import (
    BusinessProcessService,
)
from salestech_be.core.calendar.user_calendar_schedule_service import (
    UserCalendarScheduleService,
)
from salestech_be.core.calendar.user_calendar_webhook_service import (
    UserCalendarWebhookService,
)
from salestech_be.core.chat.service.chat_history_query_service import (
    ChatHistoryQueryService,
)
from salestech_be.core.chat.service.chat_history_service import ChatHistoryService
from salestech_be.core.chat.service.chat_service import ChatService
from salestech_be.core.citation.service.citation_query_service import (
    CitationQueryService,
)
from salestech_be.core.comment.service.comment_service import CommentService
from salestech_be.core.contact.service.contact_enrichment_service import (
    ContactEnrichmentService,
)
from salestech_be.core.contact.service.contact_query_service import ContactQueryService
from salestech_be.core.contact.service.contact_service import ContactService
from salestech_be.core.crm_ai_rec.crm_ai_rec_service import CrmAIRecService
from salestech_be.core.custom_object.service.association_service import (
    AssociationService,
)
from salestech_be.core.custom_object.service.custom_object_query_service import (
    CustomObjectQueryService,
)
from salestech_be.core.custom_object.service.custom_object_service import (
    CustomObjectService,
)
from salestech_be.core.data.service.query_service import DomainObjectQueryService
from salestech_be.core.domain_object_list.service.domain_object_list_query_service import (
    DomainObjectListQueryService,
)
from salestech_be.core.domain_object_list.service.domain_object_list_service import (
    DomainObjectListService,
)
from salestech_be.core.email.account.service_v2 import EmailAccountServiceV2
from salestech_be.core.email.global_email.global_thread_query_service import (
    GlobalThreadQueryService,
)
from salestech_be.core.email.global_email.global_thread_service import (
    GlobalThreadService,
)
from salestech_be.core.email.insight.email_insight_service import EmailInsightService
from salestech_be.core.email.outbound_domain.service import OutboundDomainService
from salestech_be.core.email.pool.service import EmailAccountPoolService
from salestech_be.core.email.service.imap_syncing_service import ImapSyncingService
from salestech_be.core.event_tracking.event_tracking_receiver_service import (
    EventTrackingReceiverService,
)
from salestech_be.core.event_tracking.event_tracking_service import EventTrackingService
from salestech_be.core.ff.feature_flag_service import FeatureFlagService
from salestech_be.core.goal.service.goal_service import GoalService
from salestech_be.core.imports.service.crm_sync_service import CrmSyncService
from salestech_be.core.imports.service.import_csv_job_review_service import (
    ImportCsvJobReviewService,
)
from salestech_be.core.job.service.job_runner_service import JobRunnerService
from salestech_be.core.job.service.job_service import JobService
from salestech_be.core.meeting.meeting_agent_service import MeetingAgentService
from salestech_be.core.meeting.meeting_realtime_service import MeetingRealtimeService
from salestech_be.core.meeting.meeting_service import MeetingService
from salestech_be.core.meeting.meeting_share_service import MeetingShareService
from salestech_be.core.meeting.service.meeting_query_service import MeetingQueryService
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
)
from salestech_be.core.metadata.service.pipeline_stage_service import (
    PipelineStageSelectListService,
)
from salestech_be.core.metadata.service.stage_criteria_service import (
    StageCriteriaService,
)
from salestech_be.core.note.service.note_service import NoteService
from salestech_be.core.notification.service.notification_service import (
    NotificationService,
)
from salestech_be.core.pipeline.service.pipeline_intel_service import (
    PipelineIntelService,
)
from salestech_be.core.pipeline.service.pipeline_qualification_property_service import (
    PipelineQualificationPropertyService,
)
from salestech_be.core.pipeline.service.pipeline_qualification_property_services import (
    CompetitorService,
    DecisionCriteriaItemService,
    DecisionProcessItemService,
    IdentifiedPainItemService,
    MetricItemService,
    PaperProcessItemService,
)
from salestech_be.core.pipeline.service.pipeline_service import PipelineService
from salestech_be.core.sequence.service.sequence_enrollment_service import (
    SequenceEnrollmentService,
)
from salestech_be.core.sequence.service.sequence_query_service import (
    SequenceQueryService,
)
from salestech_be.core.sequence.service.sequence_service import SequenceService
from salestech_be.core.sequence.service.sequence_step_service import (
    SequenceStepService,
)
from salestech_be.core.stage_criteria.stage_criteria_service_v2 import (
    StageCriteriaServiceV2,
)
from salestech_be.core.task.service.task_v2_service import TaskV2Service
from salestech_be.core.tracker.service.tracker_query_service import TrackerQueryService
from salestech_be.core.tracker.service.tracker_service import TrackerService
from salestech_be.core.user.service.user_service import UserService
from salestech_be.core.user.signature.service import SignatureService
from salestech_be.core.user_feedback.service import UserFeedbackService
from salestech_be.core.variable.variable_service import VariableService
from salestech_be.core.view_management.service.view_management_service import (
    ViewManagementService,
)
from salestech_be.core.workflow.service.workflow_trigger_service import (
    WorkflowTriggerService,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.falkordb.falkordb_factory import FalkorDBConnectionManager
from salestech_be.integrations.kafka.kafka_manager import MSKProducer
from salestech_be.search.es.search.client import AsyncElasticsearchSearchClient
from salestech_be.search.search.search_service import SearchService
from salestech_be.web.api.calendar.service import UserCalendarService
from salestech_be.web.api.connect.service import UserIntegrationService
from salestech_be.web.api.crm_integrity.service import CRMIntegrityService
from salestech_be.web.api.email.common.email_webhook_service import EmailWebhookService
from salestech_be.web.api.propagation_rule.service import PropagationRuleService
from salestech_be.web.api.prospecting.company.service import CompanyService
from salestech_be.web.api.prospecting.people.service import PeopleService
from salestech_be.web.api.reporting.dataset.service import ReportingDatasetService
from salestech_be.web.api.schedule.service import EventScheduleService
from salestech_be.web.api.user.platform_credential.service import (
    UserPlatformCredentialService,
)
from salestech_be.web.api.user_invite.service import UserInviteService
from salestech_be.web.api.webhook.service.nylas_webhook_service import (
    NylasWebhookService,
)
from salestech_be.web.api.workflow.block.service import WorkflowBlockService
from salestech_be.web.api.workflow.edge.service import WorkflowEdgeService
from salestech_be.web.api.workflow.node.service import WorkflowNodeService
from salestech_be.web.api.workflow.run.service import WorkflowRunService
from salestech_be.web.api.workflow.run_node.service import WorkflowRunNodeService
from salestech_be.web.api.workflow.snapshot.service import WorkflowSnapshotService
from salestech_be.web.api.workflow.workflow.service import WorkflowService


@dataclasses.dataclass(frozen=True)
class LifeSpanService:
    user_calendar_webhook_service: UserCalendarWebhookService
    email_webhook_service: EmailWebhookService
    user_calendar_service: UserCalendarService
    user_calendar_schedule_service: UserCalendarScheduleService
    user_platform_credential_service: UserPlatformCredentialService
    nylas_webhook_service: NylasWebhookService
    user_service: UserService
    user_integration_service: UserIntegrationService
    user_invite_service: UserInviteService
    crm_ai_rec_service: CrmAIRecService
    pipeline_service: PipelineService
    pipeline_intel_service: PipelineIntelService
    pipeline_qualification_property_service: PipelineQualificationPropertyService
    pipeline_stage_select_list_service: PipelineStageSelectListService
    identified_pain_item_service: IdentifiedPainItemService
    decision_criteria_item_service: DecisionCriteriaItemService
    metric_item_service: MetricItemService
    paper_process_item_service: PaperProcessItemService
    competitor_service: CompetitorService
    decision_process_item_service: DecisionProcessItemService
    stage_criteria_service: StageCriteriaService
    stage_criteria_service_v2: StageCriteriaServiceV2
    select_list_service: InternalSelectListService
    event_schedule_service: EventScheduleService
    meeting_service: MeetingService
    meeting_realtime_service: MeetingRealtimeService
    meeting_query_service: MeetingQueryService
    meeting_agent_service: MeetingAgentService
    meeting_share_service: MeetingShareService
    email_insight_service: EmailInsightService
    account_service: AccountService
    activity_service: ActivityService
    contact_service: ContactService
    contact_enrichment_service: ContactEnrichmentService
    contact_query_service: ContactQueryService
    domain_object_query_service: DomainObjectQueryService
    event_tracking_service: EventTrackingService
    event_tracking_receiver_service: EventTrackingReceiverService
    event_tracking_msk_producer: MSKProducer
    view_management_service: ViewManagementService
    people_service: PeopleService
    company_service: CompanyService
    email_outbound_domain_service: OutboundDomainService
    email_account_pool_service: EmailAccountPoolService
    email_account_service_v2: EmailAccountServiceV2
    global_thread_service: GlobalThreadService
    global_thread_query_service: GlobalThreadQueryService
    signature_service: SignatureService
    imap_syncing_service: ImapSyncingService
    job_runner_service: JobRunnerService
    note_service: NoteService
    goal_service: GoalService
    feature_flag_service: FeatureFlagService
    crm_sync_service: CrmSyncService
    import_csv_job_review_service: ImportCsvJobReviewService
    job_service: JobService
    notification_service: NotificationService
    langfuse_prompt_service: LangfusePromptService
    user_feedback_service: UserFeedbackService
    # sequence
    sequence_service: SequenceService
    sequence_query_service: SequenceQueryService
    sequence_step_service: SequenceStepService
    sequence_enrollment_service: SequenceEnrollmentService
    # workflow
    workflow_node_service: WorkflowNodeService
    workflow_snapshot_service: WorkflowSnapshotService
    workflow_block_service: WorkflowBlockService
    workflow_edge_service: WorkflowEdgeService
    workflow_run_service: WorkflowRunService
    workflow_run_node_service: WorkflowRunNodeService
    workflow_service: WorkflowService
    workflow_trigger_service: WorkflowTriggerService
    propagation_rule_service: PropagationRuleService
    crm_integrity_service: CRMIntegrityService
    tracker_service: TrackerService
    tracker_query_service: TrackerQueryService
    variable_service: VariableService
    custom_object_service: CustomObjectService
    association_service: AssociationService
    domain_object_list_service: DomainObjectListService
    domain_object_list_query_service: DomainObjectListQueryService
    custom_object_query_service: CustomObjectQueryService
    # search
    search_service: SearchService
    # chat
    chat_service: ChatService
    chat_history_service: ChatHistoryService
    chat_history_query_service: ChatHistoryQueryService
    # citation
    citation_query_service: CitationQueryService
    # business process
    business_process_service: BusinessProcessService
    # tasks
    task_v2_service: TaskV2Service
    comment_service: CommentService
    reporting_dataset_service: ReportingDatasetService


@dataclasses.dataclass(frozen=True)
class AppContext:
    """Application Context container.

    NOTE: you should make sure they are thread-safe. Avoid using shared but mutable
    states. Use locks (`asyncio.Lock`) to access/protect shared data.
    """

    # main_db: AsyncEngine
    main_db: DatabaseEngine

    # shared http async client from HTTPX (thread-safe)
    http_aclient: AsyncClient

    # es search client (only used for search)
    es_search_client: AsyncElasticsearchSearchClient

    # Initialize a falkordb client for the lifespan of the app
    # This can be None if initialization fails during startup.
    falkordb_conn_mgr: FalkorDBConnectionManager | None

    lifespan_service: LifeSpanService

    async def close(self) -> None:
        await self.main_db.close()
        await self.http_aclient.aclose()
        await self.es_search_client.close()
        # Gracefully handle potential None value during close
        if self.falkordb_conn_mgr:
            await self.falkordb_conn_mgr.close()
