import uuid
from uuid import UUID

from fastapi import Request

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import (
    ErrorDetails,
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.common.exception.exception import ForbiddenError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.type.patch_request import specified
from salestech_be.core.common.types import UserAuthContext
from salestech_be.db.dao.search_query_repository import SearchQueryRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.search_query import (
    ProspectingSavedSearchQueryPermission,
    ProspectingSearchQueryType,
    SearchQuery,
    SearchQueryUpdate,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.prospecting.query.schema import (
    CreateQueryRequest,
)

logger = get_logger()


class QueryService:
    def __init__(self, search_query_repository: SearchQueryRepository):
        self.search_query_repository = search_query_repository

    async def create_search_query(
        self,
        user_id: UUID,
        organization_id: UUID,
        create_query_request: CreateQueryRequest,
    ) -> SearchQuery:
        # check if name has been taken
        if await self.search_query_repository.find_by_name(
            name=create_query_request.name,
            organization_id=organization_id,
        ):
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.INVALID_PATCH_REQUEST,
                    details="The input name has already been used.",
                )
            )
        return await self.search_query_repository.insert(
            SearchQuery(
                id=uuid.uuid4(),
                name=create_query_request.name,
                type=create_query_request.type
                if create_query_request.type
                else ProspectingSearchQueryType.PEOPLE,
                filter_spec=create_query_request.filter_spec,
                permission=create_query_request.permission,
                user_id=user_id,
                organization_id=organization_id,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            )
        )

    async def get_search_query_by_id(
        self,
        search_query_id: UUID,
        organization_id: UUID,
        user_auth_context: UserAuthContext,
    ) -> SearchQuery:
        if db_search_query := (
            await self.search_query_repository.find_by_id(
                search_query_id=search_query_id, organization_id=organization_id
            )
        ):
            # if prospecting perms are not enabled, return the query.
            if not (
                settings.enable_prospecting_perms
                or str(organization_id) in settings.enable_prospecting_perms_org_ids
            ):
                return db_search_query

            # admin can view all queries.
            # if user owns the query, or the query is org-shared, return it.
            if (
                user_auth_context.is_admin
                or user_auth_context.user_id == db_search_query.user_id
                or db_search_query.permission
                == ProspectingSavedSearchQueryPermission.ORGANIZATION_SHARED
            ):
                return db_search_query

            raise ForbiddenError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.FORBIDDEN,
                    details="You do not have permission to access this query.",
                )
            )

        raise ResourceNotFoundError(
            additional_error_details=ErrorDetails(
                code=ErrorCode.RESOURCE_NOT_FOUND,
                details=f"Search query with id {search_query_id} not found under current org.",
            )
        )

    async def update_search_query(
        self,
        search_query_id: UUID,
        organization_id: UUID,
        search_query_update: SearchQueryUpdate,
        user_auth_context: UserAuthContext,
    ) -> SearchQuery:
        # permissions check is performed in get_search_query_by_id().
        db_search_query = await self.get_search_query_by_id(
            search_query_id=search_query_id,
            organization_id=organization_id,
            user_auth_context=user_auth_context,
        )
        if (  # noqa: SIM102
            settings.enable_prospecting_perms
            or str(organization_id) in settings.enable_prospecting_perms_org_ids
        ):
            # admin can update all queries.
            # users can only update their own queries.
            if not (
                user_auth_context.is_admin
                or user_auth_context.user_id == db_search_query.user_id
            ):
                raise ForbiddenError(
                    additional_error_details=ErrorDetails(
                        code=ErrorCode.FORBIDDEN,
                        details="You do not have permission to update this query.",
                    )
                )
        if specified(
            search_query_update.name
        ) and await self.search_query_repository.find_by_name(
            name=search_query_update.name,
            organization_id=organization_id,
        ):
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.INVALID_PATCH_REQUEST,
                    details="The input name has already been used.",
                )
            )

        return await self.search_query_repository.update_by_id(
            search_query_id=search_query_id,
            organization_id=organization_id,
            search_query_update=search_query_update,
        )

    async def delete_search_query_by_id(
        self,
        search_query_id: UUID,
        organization_id: UUID,
        user_auth_context: UserAuthContext,
    ) -> SearchQuery:
        # permissions check is performed in get_search_query_by_id().
        db_search_query = await self.get_search_query_by_id(
            search_query_id=search_query_id,
            organization_id=organization_id,
            user_auth_context=user_auth_context,
        )
        if (  # noqa: SIM102
            settings.enable_prospecting_perms
            or str(organization_id) in settings.enable_prospecting_perms_org_ids
        ):
            # admin can delete all queries.
            # users can only delete their own queries.
            if not (
                user_auth_context.is_admin
                or user_auth_context.user_id == db_search_query.user_id
            ):
                raise ForbiddenError(
                    additional_error_details=ErrorDetails(
                        code=ErrorCode.FORBIDDEN,
                        details="You do not have permission to delete this query.",
                    )
                )

        return await self.search_query_repository.update_by_id(
            search_query_id=search_query_id,
            organization_id=organization_id,
            search_query_update=SearchQueryUpdate(deleted_at=zoned_utc_now()),
        )


def get_query_service(request: Request) -> QueryService:
    db_engine = get_db_engine(request=request)

    return QueryService(search_query_repository=SearchQueryRepository(engine=db_engine))


def get_query_service_with_db_engine(db_engine: DatabaseEngine) -> QueryService:
    return QueryService(search_query_repository=SearchQueryRepository(engine=db_engine))
