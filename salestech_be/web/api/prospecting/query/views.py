from typing import Annotated
from uuid import UUID

from fastapi import Depends

from salestech_be.core.data.service.query_service import DomainObjectQueryService
from salestech_be.core.data.types import StandardRecord
from salestech_be.core.data.util import ObjectRecordFetchConditions
from salestech_be.core.prospecting.type.query import ProspectingSavedSearchQueryV2
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.perm_predicates import (
    require_create_prospecting_access,
    require_delete_prospecting_access,
    require_read_prospecting_access,
    require_update_prospecting_access,
)
from salestech_be.web.api.common.container import (
    ListEntityRequestV2,
    PaginatedListResponse,
)
from salestech_be.web.api.filter.logical_filter import (
    paginate_object_records,
    single_paginate_object_record,
)
from salestech_be.web.api.prospecting.common.util import validate_search_request
from salestech_be.web.api.prospecting.query.schema import (
    CreateQueryRequest,
    PatchProspectingQueryRequest,
)
from salestech_be.web.api.prospecting.query.service import (
    QueryService,
    get_query_service,
)
from salestech_be.web.api_router_ext import ReeAPIRouter
from salestech_be.web.lifespan_service import domain_object_query_service_from_lifespan
from salestech_be.web.middleware.types import (
    AnnotatedReevoOrganizationId,
    AnnotatedReevoUserAuthContext,
    AnnotatedReevoUserId,
)

logger = get_logger()

router = ReeAPIRouter()


@router.post(
    "",
    response_model=ProspectingSavedSearchQueryV2,
    dependencies=[Depends(require_create_prospecting_access)],
)
async def create_prospecting_query(
    create_query_request: CreateQueryRequest,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    query_service: Annotated[QueryService, Depends(get_query_service)],
) -> ProspectingSavedSearchQueryV2:
    validate_search_request(
        request=ListEntityRequestV2(filter_spec=create_query_request.filter_spec)
    )
    db_search_query = await query_service.create_search_query(
        user_id=user_id,
        organization_id=organization_id,
        create_query_request=create_query_request,
    )
    return ProspectingSavedSearchQueryV2.map_from_db(db_search_query=db_search_query)


@router.get(
    "/{query_id}",
    response_model=ProspectingSavedSearchQueryV2,
    dependencies=[Depends(require_read_prospecting_access)],
)
async def get_prospecting_query(
    query_id: UUID,
    organization_id: AnnotatedReevoOrganizationId,
    query_service: Annotated[QueryService, Depends(get_query_service)],
    user_auth_context: AnnotatedReevoUserAuthContext,
) -> ProspectingSavedSearchQueryV2:
    db_search_query = await query_service.get_search_query_by_id(
        organization_id=organization_id,
        search_query_id=query_id,
        user_auth_context=user_auth_context,
    )
    return ProspectingSavedSearchQueryV2.map_from_db(db_search_query=db_search_query)


@router.patch(
    "/{query_id}",
    response_model=ProspectingSavedSearchQueryV2,
    dependencies=[Depends(require_update_prospecting_access)],
)
async def update_prospecting_query(
    query_id: UUID,
    patch_prospecting_query_request: PatchProspectingQueryRequest,
    organization_id: AnnotatedReevoOrganizationId,
    query_service: Annotated[QueryService, Depends(get_query_service)],
    user_auth_context: AnnotatedReevoUserAuthContext,
) -> ProspectingSavedSearchQueryV2:
    db_search_query = await query_service.update_search_query(
        search_query_id=query_id,
        organization_id=organization_id,
        search_query_update=patch_prospecting_query_request.to_search_query_update(),
        user_auth_context=user_auth_context,
    )
    return ProspectingSavedSearchQueryV2.map_from_db(db_search_query=db_search_query)


@router.delete(
    "/{query_id}",
    response_model=ProspectingSavedSearchQueryV2,
    dependencies=[Depends(require_delete_prospecting_access)],
)
async def delete_prospecting_query(
    query_id: UUID,
    organization_id: AnnotatedReevoOrganizationId,
    query_service: Annotated[QueryService, Depends(get_query_service)],
    user_auth_context: AnnotatedReevoUserAuthContext,
) -> ProspectingSavedSearchQueryV2:
    db_search_query = await query_service.delete_search_query_by_id(
        organization_id=organization_id,
        search_query_id=query_id,
        user_auth_context=user_auth_context,
    )
    return ProspectingSavedSearchQueryV2.map_from_db(db_search_query=db_search_query)


@router.post(
    "/_list",
    response_model=PaginatedListResponse[StandardRecord[ProspectingSavedSearchQueryV2]],
    dependencies=[Depends(require_read_prospecting_access)],
)
async def list_prospecting_queries(
    list_query_request: ListEntityRequestV2,
    organization_id: AnnotatedReevoOrganizationId,
    domain_object_query_service: Annotated[
        DomainObjectQueryService, Depends(domain_object_query_service_from_lifespan)
    ],
    user_auth_context: AnnotatedReevoUserAuthContext,
) -> PaginatedListResponse[StandardRecord[ProspectingSavedSearchQueryV2]]:
    if list_query_request.record_id:
        prospecting_saved_queries = (
            await domain_object_query_service.list_prospecting_saved_query_records(
                organization_id=organization_id,
                only_include_prospecting_saved_query_ids={list_query_request.record_id},
                include_custom_object=False,
                user_auth_context=user_auth_context,
            )
        )
        return single_paginate_object_record(object_records=prospecting_saved_queries)

    prospecting_saved_queries = (
        await domain_object_query_service.list_prospecting_saved_query_records(
            organization_id=organization_id,
            include_custom_object=False,
            fetch_conditions=ObjectRecordFetchConditions(
                filter_spec=list_query_request.filter_spec,
                sorting_spec=list_query_request.sorting_spec,
                fields=list_query_request.ordered_object_fields,
            ),
            user_auth_context=user_auth_context,
        )
    )
    return paginate_object_records(
        object_records=prospecting_saved_queries, cursor=list_query_request.cursor
    )
