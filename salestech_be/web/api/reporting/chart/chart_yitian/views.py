from typing import Annotated
from uuid import UUID

from fastapi import Depends
from starlette import status

from salestech_be.common.results import <PERSON>ursor
from salestech_be.db.models.reporting import ReportingDashboardChartAssociation
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.perm_predicates import require_read_placeholder_access
from salestech_be.web.api.common.container import (
    ListEntityRequest,
    PaginatedListResponse,
)
from salestech_be.web.api.filter.logical_filter import (
    paginate_entities,
    sort_entities,
    standard_filter_entities,
)
from salestech_be.web.api.reporting.chart.schema import (
    CreateReportingChartRequest,
    CreateReportingDashboardChartAssociationRequest,
    PatchReportingChartRequest,
    ReportingChartDTO,
)
from salestech_be.web.api.reporting.chart.service import ReportingChartService
from salestech_be.web.api_router_ext import ReeAPIRouter
from salestech_be.web.lifespan_service import reporting_chart_service_from_lifespan
from salestech_be.web.middleware.types import (
    AnnotatedReevoOrganizationId,
    AnnotatedReevoUserId,
)

router = ReeAPIRouter()
logger = get_logger("reporting.chart")


ReportingChartServiceDI = Annotated[
    ReportingChartService, Depends(reporting_chart_service_from_lifespan)
]


@router.post(
    "",
    response_model=ReportingChartDTO,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def create_chart(
    request: CreateReportingChartRequest,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_chart_service: ReportingChartServiceDI,
) -> ReportingChartDTO:
    return await reporting_chart_service.create_chart(
        request=request,
        user_id=user_id,
        organization_id=organization_id,
    )


@router.get(
    "/{chart_id}",
    response_model=ReportingChartDTO,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def get_chart(
    chart_id: UUID,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_chart_service: ReportingChartServiceDI,
) -> ReportingChartDTO:
    return await reporting_chart_service.get_chart(
        chart_id=chart_id,
        organization_id=organization_id,
    )


@router.patch(
    "/{chart_id}",
    response_model=ReportingChartDTO,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def patch_chart(
    chart_id: UUID,
    request: PatchReportingChartRequest,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_chart_service: ReportingChartServiceDI,
) -> ReportingChartDTO:
    return await reporting_chart_service.patch_chart(
        chart_id=chart_id,
        request=request,
        user_id=user_id,
        organization_id=organization_id,
    )


@router.delete(
    "/{chart_id}",
    response_model=None,
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def delete_chart(
    chart_id: UUID,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_chart_service: ReportingChartServiceDI,
) -> None:
    await reporting_chart_service.delete_chart(
        chart_id=chart_id,
        user_id=user_id,
        organization_id=organization_id,
    )


@router.post(
    "/_list",
    response_model=PaginatedListResponse[ReportingChartDTO],
    dependencies=[Depends(require_read_placeholder_access)],
)
async def list_charts(
    list_request: ListEntityRequest,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_chart_service: ReportingChartServiceDI,
) -> PaginatedListResponse[ReportingChartDTO]:
    db_chart_dtos = await reporting_chart_service.list_charts(
        organization_id=organization_id,
    )

    filtered_chart_dtos = standard_filter_entities(
        db_chart_dtos,
        list_request.filters,
    )

    if not filtered_chart_dtos:
        return PaginatedListResponse(list_data=[], cursor=Cursor())

    sorted_chart_dtos = sort_entities(
        filtered_chart_dtos,
        list_request.sorters,
    )

    paginated_chart_dtos, response_cursor = paginate_entities(
        sorted_chart_dtos,
        list_request.cursor,
    )

    return PaginatedListResponse[ReportingChartDTO](
        list_data=paginated_chart_dtos,
        cursor=response_cursor,
    )


@router.post(
    "/{chart_id}/dashboard",
    response_model=list[ReportingDashboardChartAssociation],
    dependencies=[Depends(require_read_placeholder_access)],
)
async def upsert_dashboard_chart_association(
    chart_id: UUID,
    request: CreateReportingDashboardChartAssociationRequest,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_chart_service: ReportingChartServiceDI,
) -> ReportingDashboardChartAssociation:
    return await reporting_chart_service.upsert_dashboard_chart_association(
        chart_id=chart_id,
        request=request,
        user_id=user_id,
        organization_id=organization_id,
    )


@router.delete(
    "/{chart_id}/dashboard/{dashboard_id}",
    response_model=None,
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def delete_dashboard_chart_association(
    chart_id: UUID,
    dashboard_id: UUID,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_chart_service: ReportingChartServiceDI,
) -> None:
    await reporting_chart_service.delete_dashboard_chart_association(
        chart_id=chart_id,
        dashboard_id=dashboard_id,
        user_id=user_id,
        organization_id=organization_id,
    )
