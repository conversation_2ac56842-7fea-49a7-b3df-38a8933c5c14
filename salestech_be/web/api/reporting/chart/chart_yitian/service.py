from uuid import UUID, uuid4

from fastapi import Request

from salestech_be.common.exception import InvalidArgumentError, ResourceNotFoundError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.type.patch_request import specified
from salestech_be.db.dao.reporting_chart_repository import ReportingChartRepository
from salestech_be.db.dao.reporting_dashboard_repository import (
    ReportingDashboardRepository,
)
from salestech_be.db.dao.reporting_dataset_repository import ReportingDatasetRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.reporting import (
    ReportingChart,
    ReportingChartUpdate,
    ReportingDashboard,
    ReportingDashboardChartAssociation,
    ReportingDashboardChartAssociationUpdate,
    ReportingDataset,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.reporting.chart.schema import (
    CreateReportingChartRequest,
    CreateReportingDashboardChartAssociationRequest,
    PatchReportingChartRequest,
    ReportingChartDTO,
)

logger = get_logger(__name__)


class ReportingChartService:
    def __init__(
        self,
        reporting_chart_repository: ReportingChartRepository,
        reporting_dashboard_repository: ReportingDashboardRepository,
        reporting_dataset_repository: ReportingDatasetRepository,
    ):
        self.reporting_chart_repository = reporting_chart_repository
        self.reporting_dashboard_repository = reporting_dashboard_repository
        self.reporting_dataset_repository = reporting_dataset_repository

    async def get_chart(
        self,
        *,
        chart_id: UUID,
        organization_id: UUID,
    ) -> ReportingChartDTO:
        db_chart = (
            await self.reporting_chart_repository.find_by_tenanted_primary_key_or_fail(
                ReportingChart,
                id=chart_id,
                organization_id=organization_id,
            )
        )

        return ReportingChartDTO.from_db(
            db_chart=db_chart,
            db_dashboard_chart_associations=await self.find_dashboard_chart_associations_by_chart_id(
                chart_id=chart_id,
                organization_id=organization_id,
            ),
        )

    async def create_chart(
        self,
        *,
        request: CreateReportingChartRequest,
        user_id: UUID,
        organization_id: UUID,
    ) -> ReportingChartDTO:
        # todo: add more validation for dataset
        await self.reporting_dataset_repository.find_by_tenanted_primary_key_or_fail(
            ReportingDataset,
            id=request.dataset_id,
            organization_id=organization_id,
        )

        # todo: refactor to transactional db op

        created_db_chart = await self.reporting_chart_repository.insert(
            ReportingChart(
                id=uuid4(),
                name=request.name,
                description=request.description,
                dataset_id=request.dataset_id,
                layout_config=request.layout_config,
                organization_id=organization_id,
                is_published=request.is_published,
                created_by_user_id=user_id,
                created_at=zoned_utc_now(),
                updated_by_user_id=user_id,
                updated_at=zoned_utc_now(),
            )
        )

        if request.is_published and request.create_dashboard_chart_association_request:
            await self.upsert_dashboard_chart_association(
                chart_id=created_db_chart.id,
                request=request.create_dashboard_chart_association_request,
                user_id=user_id,
                organization_id=organization_id,
            )

        return ReportingChartDTO.from_db(
            db_chart=created_db_chart,
            db_dashboard_chart_associations=await self.find_dashboard_chart_associations_by_chart_id(
                chart_id=created_db_chart.id,
                organization_id=organization_id,
            ),
        )

    async def patch_chart(
        self,
        *,
        chart_id: UUID,
        request: PatchReportingChartRequest,
        user_id: UUID,
        organization_id: UUID,
    ) -> ReportingChartDTO:
        chart_dto = await self.get_chart(
            chart_id=chart_id,
            organization_id=organization_id,
        )

        if (
            specified(request.is_published)
            and request.is_published is False
            and chart_dto.dashboard_ids
        ):
            raise InvalidArgumentError(
                f"chart {chart_id} was added to other dashboards and cannot be unpublished"
            )

        if (
            specified(request.dataset_id)
            and request.dataset_id != chart_dto.dataset_id
            and not await self.reporting_dataset_repository.find_by_tenanted_primary_key(
                ReportingDataset,
                id=request.dataset_id,
                organization_id=organization_id,
            )
        ):
            raise InvalidArgumentError(
                f"dataset {request.dataset_id} to patch chart {chart_id} not found"
            )

        # todo: add validation for layout_config if needed

        not_none(
            await self.reporting_chart_repository.update_by_primary_key(
                ReportingChart,
                primary_key_to_value={"id": chart_id},
                column_to_update=ReportingChartUpdate(
                    name=request.name,
                    description=request.description,
                    dataset_id=request.dataset_id,
                    layout_config=request.layout_config,
                    is_published=request.is_published,
                    updated_at=zoned_utc_now(),
                    updated_by_user_id=user_id,
                ),
            )
        )

        return await self.get_chart(
            chart_id=chart_id,
            organization_id=organization_id,
        )

    async def delete_chart(
        self,
        *,
        chart_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> ReportingChartDTO:
        deleted_chart_result = await self.reporting_chart_repository.delete_chart(
            chart_id=chart_id,
            user_id=user_id,
            organization_id=organization_id,
        )

        return ReportingChartDTO.from_db(
            db_chart=deleted_chart_result.deleted_chart,
            db_dashboard_chart_associations=deleted_chart_result.deleted_dashboard_chart_associations,
        )

    async def list_charts(
        self,
        *,
        organization_id: UUID,
    ) -> list[ReportingChartDTO]:
        db_charts = await self.reporting_chart_repository.list_charts(
            organization_id=organization_id
        )

        db_associations = await self.reporting_chart_repository.find_dashboard_chart_associations_by_chart_ids(
            chart_ids=[db_chart.id for db_chart in db_charts],
            organization_id=organization_id,
        )

        return [
            ReportingChartDTO.from_db(
                db_chart=db_chart,
                db_dashboard_chart_associations=db_associations.get(db_chart.id, []),
            )
            for db_chart in db_charts
        ]

    async def find_dashboard_chart_associations_by_chart_id(
        self,
        *,
        chart_id: UUID,
        dashboard_id: UUID | None = None,
        organization_id: UUID,
    ) -> list[ReportingDashboardChartAssociation]:
        return await self.reporting_chart_repository.find_dashboard_chart_associations_by_chart_id(
            chart_id=chart_id,
            dashboard_id=dashboard_id,
            organization_id=organization_id,
        )

    async def upsert_dashboard_chart_association(
        self,
        *,
        chart_id: UUID,
        request: CreateReportingDashboardChartAssociationRequest,
        user_id: UUID,
        organization_id: UUID,
    ) -> ReportingDashboardChartAssociation:
        chart_dto = await self.get_chart(
            chart_id=chart_id,
            organization_id=organization_id,
        )

        if not chart_dto.is_published:
            raise InvalidArgumentError(
                f"chart {chart_id} is not published and cannot be added to a dashboard"
            )

        await self.reporting_dashboard_repository.find_by_tenanted_primary_key_or_fail(
            ReportingDashboard,
            id=request.dashboard_id,
            organization_id=organization_id,
        )

        if await self.find_dashboard_chart_associations_by_chart_id(
            chart_id=chart_id,
            dashboard_id=request.dashboard_id,
            organization_id=organization_id,
        ):
            raise InvalidArgumentError(
                f"dashboard_chart_association with chart_id {chart_id} and dashboard_id {request.dashboard_id} already exists"
            )

        return await self.reporting_chart_repository.insert(
            ReportingDashboardChartAssociation(
                id=uuid4(),
                chart_id=chart_id,
                dashboard_id=request.dashboard_id,
                layout_config=request.layout_config,
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=zoned_utc_now(),
                updated_by_user_id=user_id,
                updated_at=zoned_utc_now(),
            )
        )

    async def delete_dashboard_chart_association(
        self,
        *,
        chart_id: UUID,
        dashboard_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> ReportingDashboardChartAssociation:
        deleted_association = (
            await self.reporting_chart_repository.update_by_primary_key(
                ReportingDashboardChartAssociation,
                primary_key_to_value={"id": chart_id},
                column_to_update=ReportingDashboardChartAssociationUpdate(
                    deleted_at=zoned_utc_now(),
                    deleted_by_user_id=user_id,
                ),
            )
        )

        if not deleted_association:
            raise ResourceNotFoundError(
                f"dashboard_chart_association with chart_id {chart_id} and dashboard_id {dashboard_id} not found"
            )

        return deleted_association


def get_reporting_chart_service(
    request: Request,
) -> ReportingChartService:
    db_engine = get_db_engine(request=request)
    return ReportingChartService(
        reporting_chart_repository=ReportingChartRepository(engine=db_engine),
        reporting_dashboard_repository=ReportingDashboardRepository(engine=db_engine),
        reporting_dataset_repository=ReportingDatasetRepository(engine=db_engine),
    )


def get_reporting_chart_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> ReportingChartService:
    return ReportingChartService(
        reporting_chart_repository=ReportingChartRepository(engine=db_engine),
        reporting_dashboard_repository=ReportingDashboardRepository(engine=db_engine),
        reporting_dataset_repository=ReportingDatasetRepository(engine=db_engine),
    )
