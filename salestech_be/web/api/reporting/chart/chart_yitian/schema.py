from __future__ import annotations

from typing import Any, Self
from uuid import UUID

from pydantic import BaseModel, model_validator

from salestech_be.common.exception import InvalidArgumentError
from salestech_be.common.type.patch_request import UNSET, BasePatchRequest, UnsetAware
from salestech_be.db.models.reporting import (
    ReportingChart,
    ReportingDashboardChartAssociation,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class ReportingChartDTO(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    id: UUID
    name: str
    description: str | None = None
    dataset_id: UUID

    # todo: replace with ChartLayoutConfig
    layout_config: dict[Any, Any] | None = None  # type: ignore[explicit-any] # TODO: fix-any-annotation
    dashboard_ids: list[UUID] = []
    is_published: bool
    organization_id: UUID | None = None

    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID | None = None
    updated_at: ZoneRequiredDateTime | None = None
    updated_by_user_id: UUID | None = None
    deleted_at: ZoneRequiredDateTime | None = None
    deleted_by_user_id: UUID | None = None

    @staticmethod
    def from_db(
        db_chart: ReportingChart,
        db_dashboard_chart_associations: list[ReportingDashboardChartAssociation],
    ) -> ReportingChartDTO:
        return ReportingChartDTO(
            id=db_chart.id,
            name=db_chart.name,
            description=db_chart.description,
            dataset_id=db_chart.dataset_id,
            layout_config=db_chart.layout_config,
            dashboard_ids=[
                association.dashboard_id
                for association in db_dashboard_chart_associations
            ],
            organization_id=db_chart.organization_id,
            is_published=db_chart.is_published,
            created_at=db_chart.created_at,
            created_by_user_id=db_chart.created_by_user_id,
            updated_at=db_chart.updated_at,
            updated_by_user_id=db_chart.updated_by_user_id,
            deleted_at=db_chart.deleted_at,
            deleted_by_user_id=db_chart.deleted_by_user_id,
        )


class CreateReportingDashboardChartAssociationRequest(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    dashboard_id: UUID
    # todo: replace with DashboardChartLayoutConfig
    layout_config: dict[Any, Any] | None = None  # type: ignore[explicit-any] # TODO: fix-any-annotation


class CreateReportingChartRequest(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    name: str
    description: str | None = None
    dataset_id: UUID
    # todo: replace with ChartLayoutConfig
    layout_config: dict[Any, Any] | None = None  # type: ignore[explicit-any] # TODO: fix-any-annotation

    is_published: bool = False
    create_dashboard_chart_association_request: (
        CreateReportingDashboardChartAssociationRequest | None
    ) = None

    @model_validator(mode="after")
    def validate_dashboard_id(self) -> Self:
        if not self.is_published and self.create_dashboard_chart_association_request:
            raise InvalidArgumentError(
                "create_dashboard_chart_association_request is not allowed when creating an unpublished chart"
            )
        return self


class PatchReportingChartRequest(BasePatchRequest):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    require_at_least_one_specified_field = True

    name: UnsetAware[str] = UNSET
    description: UnsetAware[str | None] = UNSET
    dataset_id: UnsetAware[UUID] = UNSET
    # todo: replace with ChartLayoutConfig
    layout_config: UnsetAware[dict[Any, Any] | None] = UNSET  # type: ignore[explicit-any] # TODO: fix-any-annotation
    is_published: UnsetAware[bool] = UNSET
