from __future__ import annotations

from typing import Self
from uuid import UUID

from pydantic import BaseModel, Field, model_validator

from salestech_be.common.exception import InvalidArgumentError
from salestech_be.common.type.patch_request import UNSET, BasePatchRequest, UnsetAware
from salestech_be.core.reporting.type.query_config import QueryConfig
from salestech_be.db.models.reporting import (
    ReportingDataset,
    ReportingDatasetField,
    ReportingDatasetFieldDataType,
    ReportingDatasetSource,
    ReportingDatasetType,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class ReportingDatasetDTO(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    id: UUID
    name: str
    description: str | None = None
    source: ReportingDatasetSource
    type: ReportingDatasetType
    fields: list[DatasetFieldResponse] = Field(default_factory=list)
    query_config: QueryConfig | None = None
    table_reference: str | None = None

    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID | None = None
    updated_at: ZoneRequiredDateTime | None = None
    updated_by_user_id: UUID | None = None
    deleted_at: ZoneRequiredDateTime | None = None
    deleted_by_user_id: UUID | None = None

    @staticmethod
    def from_db(
        db_dataset: ReportingDataset,
        db_dataset_fields: list[ReportingDatasetField],
    ) -> Self:
        return ReportingDatasetDTO(
            id=db_dataset.id,
            name=db_dataset.name,
            description=db_dataset.description,
            source=db_dataset.source,
            type=db_dataset.type,
            fields=[
                DatasetFieldResponse(
                    id=field.id,
                    dataset_id=field.dataset_id,
                    name=field.name,
                    display_name=field.display_name,
                    data_type=field.data_type,
                )
                for field in db_dataset_fields
            ],
            query_config=db_dataset.query_config,
            table_reference=db_dataset.table_reference,
            created_at=db_dataset.created_at,
            created_by_user_id=db_dataset.created_by_user_id,
            updated_at=db_dataset.updated_at,
            updated_by_user_id=db_dataset.updated_by_user_id,
            deleted_at=db_dataset.deleted_at,
            deleted_by_user_id=db_dataset.deleted_by_user_id,
        )


class DatasetResponse(BaseModel):
    id: UUID
    name: str
    description: str | None = None
    source: str
    type: str
    owner_user_id: UUID
    created_at: str
    updated_at: str | None = None


class DatasetFieldResponse(BaseModel):
    id: UUID
    dataset_id: UUID
    name: str
    display_name: str | None = None
    data_type: ReportingDatasetFieldDataType


class DatasetDetailResponse(DatasetResponse):
    fields: list[DatasetFieldResponse] = Field(default_factory=list)
    query_config: QueryConfig | None = None
    sql_statement: str | None = None
    table_reference: str | None = None


class DatasetListResponse(BaseModel):
    datasets: list[DatasetResponse]
    total: int
    page: int
    page_size: int


class CreateDatasetFieldRequest(BaseModel):
    name: str
    display_name: str | None = None
    data_type: ReportingDatasetFieldDataType


class CreateReportingDatasetRequest(BaseModel):
    name: str
    description: str | None = None
    query_config: QueryConfig


class PatchReportingDatasetRequest(BasePatchRequest):
    require_at_least_one_specified_field = True

    name: UnsetAware[str] = UNSET
    description: UnsetAware[str | None] = UNSET
    query_config: UnsetAware[QueryConfig | None] = UNSET
    fields: UnsetAware[list[CreateDatasetFieldRequest]] = UNSET
