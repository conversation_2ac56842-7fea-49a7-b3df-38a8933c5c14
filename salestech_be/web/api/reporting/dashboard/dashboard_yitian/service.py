from uuid import UUID, uuid4

from fastapi import Request

from salestech_be.common.exception import InvalidArgumentError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.db.dao.reporting_dashboard_repository import (
    ReportingDashboardRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.reporting import (
    ReportingDashboard,
    ReportingDashboardUpdate,
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.reporting.dashboard.schema import (
    CreateReportingDashboardRequest,
    PatchReportingDashboardRequest,
    ReportingDashboardDTO,
)


class ReportingDashboardService:
    def __init__(self, reporting_dashboard_repository: ReportingDashboardRepository):
        self.reporting_dashboard_repository = reporting_dashboard_repository

    async def get_dashboard(
        self, dashboard_id: UUID, organization_id: UUID
    ) -> ReportingDashboardDTO:
        dashboard = await self.reporting_dashboard_repository.find_by_tenanted_primary_key_or_fail(
            ReportingDashboard, id=dashboard_id, organization_id=organization_id
        )
        return ReportingDashboardDTO.from_db(
            db_dashboard=dashboard,
            db_chart_associations=await self.reporting_dashboard_repository.find_dashboard_chart_associations_by_dashboard_id(
                dashboard_id=dashboard_id,
                organization_id=organization_id,
            ),
        )

    async def create_dashboard(
        self,
        request: CreateReportingDashboardRequest,
        user_id: UUID,
        organization_id: UUID,
    ) -> ReportingDashboardDTO:
        created_dashboard = await self.reporting_dashboard_repository.insert(
            ReportingDashboard(
                id=uuid4(),
                name=request.name,
                description=request.description,
                layout_config=request.layout_config,
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=zoned_utc_now(),
                updated_by_user_id=user_id,
                updated_at=zoned_utc_now(),
            )
        )
        return ReportingDashboardDTO.from_db(
            db_dashboard=created_dashboard, db_chart_associations=[]
        )

    async def patch_dashboard(
        self,
        dashboard_id: UUID,
        request: PatchReportingDashboardRequest,
        user_id: UUID,
        organization_id: UUID,
    ) -> ReportingDashboardDTO:
        await self.reporting_dashboard_repository.update_by_primary_key(
            ReportingDashboard,
            primary_key_to_value={"id": dashboard_id},
            column_to_update=ReportingDashboardUpdate(
                name=request.name,
                description=request.description,
                layout_config=request.layout_config,
                updated_at=zoned_utc_now(),
                updated_by_user_id=user_id,
            ),
        )
        return await self.get_dashboard(dashboard_id, organization_id)

    async def delete_dashboard(
        self, dashboard_id: UUID, user_id: UUID, organization_id: UUID
    ) -> ReportingDashboardDTO:
        if await self.reporting_dashboard_repository.find_dashboard_chart_associations_by_dashboard_id(
            dashboard_id=dashboard_id,
            organization_id=organization_id,
        ):
            raise InvalidArgumentError(
                f"dashboard {dashboard_id} has charts associated with it and cannot be deleted"
            )

        await self.reporting_dashboard_repository.update_by_primary_key(
            ReportingDashboard,
            primary_key_to_value={"id": dashboard_id},
            column_to_update=ReportingDashboardUpdate(
                deleted_at=zoned_utc_now(),
                deleted_by_user_id=user_id,
            ),
        )
        return await self.get_dashboard(dashboard_id, organization_id)

    async def list_dashboards(
        self, organization_id: UUID
    ) -> list[ReportingDashboardDTO]:
        db_dashboards = await self.reporting_dashboard_repository.list_dashboards(
            organization_id=organization_id,
        )
        db_associations = await self.reporting_dashboard_repository.find_dashboard_chart_associations_by_dashboard_ids(
            dashboard_ids=[db_dashboard.id for db_dashboard in db_dashboards],
            organization_id=organization_id,
        )
        return [
            ReportingDashboardDTO.from_db(
                db_dashboard, db_associations.get(db_dashboard.id, [])
            )
            for db_dashboard in db_dashboards
        ]


def get_reporting_dashboard_service(
    request: Request,
) -> ReportingDashboardService:
    db_engine = get_db_engine(request=request)
    return ReportingDashboardService(
        reporting_dashboard_repository=ReportingDashboardRepository(engine=db_engine),
    )


def get_reporting_dashboard_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> ReportingDashboardService:
    return ReportingDashboardService(
        reporting_dashboard_repository=ReportingDashboardRepository(engine=db_engine),
    )
