from __future__ import annotations

from typing import Any
from uuid import UUID

from pydantic import BaseModel

from salestech_be.common.type.patch_request import UNSET, BasePatchRequest, UnsetAware
from salestech_be.db.models.reporting import (
    ReportingDashboard,
    ReportingDashboardChartAssociation,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class ReportingDashboardDTO(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    id: UUID
    name: str
    description: str | None = None
    # todo: replace with DashboardLayoutConfig
    layout_config: dict[Any, Any] | None = None  # type: ignore[explicit-any] # TODO: fix-any-annotation
    organization_id: UUID | None = None
    chart_ids: list[UUID] = []

    created_at: ZoneRequiredDateTime
    created_by_user_id: UUID | None = None
    updated_at: ZoneRequiredDateTime | None = None
    updated_by_user_id: UUID | None = None
    deleted_at: ZoneRequiredDateTime | None = None
    deleted_by_user_id: UUID | None = None

    @staticmethod
    def from_db(
        db_dashboard: ReportingDashboard,
        db_chart_associations: list[ReportingDashboardChartAssociation],
    ) -> ReportingDashboardDTO:
        return ReportingDashboardDTO(
            id=db_dashboard.id,
            name=db_dashboard.name,
            description=db_dashboard.description,
            layout_config=db_dashboard.layout_config,
            organization_id=db_dashboard.organization_id,
            chart_ids=[association.chart_id for association in db_chart_associations],
            created_at=db_dashboard.created_at,
            created_by_user_id=db_dashboard.created_by_user_id,
            updated_at=db_dashboard.updated_at,
            updated_by_user_id=db_dashboard.updated_by_user_id,
            deleted_at=db_dashboard.deleted_at,
            deleted_by_user_id=db_dashboard.deleted_by_user_id,
        )


class CreateReportingDashboardRequest(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    name: str
    description: str | None = None
    # todo: replace with DashboardLayoutConfig
    layout_config: dict[Any, Any] | None = None  # type: ignore[explicit-any] # TODO: fix-any-annotation


class PatchReportingDashboardRequest(BasePatchRequest):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    require_at_least_one_specified_field = True

    name: UnsetAware[str] = UNSET
    description: UnsetAware[str | None] = UNSET
    # todo: replace with DashboardLayoutConfig
    layout_config: UnsetAware[dict[Any, Any] | None] = UNSET  # type: ignore[explicit-any] # TODO: fix-any-annotation
