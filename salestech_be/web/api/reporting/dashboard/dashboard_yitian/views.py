from typing import Annotated
from uuid import UUID

from fastapi import Depends
from starlette import status

from salestech_be.common.results import <PERSON>urs<PERSON>
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.perm_predicates import require_read_placeholder_access
from salestech_be.web.api.common.container import (
    ListEntityRequest,
    PaginatedListResponse,
)
from salestech_be.web.api.filter.logical_filter import (
    paginate_entities,
    sort_entities,
    standard_filter_entities,
)
from salestech_be.web.api.reporting.dashboard.schema import (
    CreateReportingDashboardRequest,
    PatchReportingDashboardRequest,
    ReportingDashboardDTO,
)
from salestech_be.web.api.reporting.dashboard.service import ReportingDashboardService
from salestech_be.web.api_router_ext import ReeAPIRouter
from salestech_be.web.lifespan_service import reporting_dashboard_service_from_lifespan
from salestech_be.web.middleware.types import (
    AnnotatedReevoOrganizationId,
    AnnotatedReevoUserId,
)

router = ReeAPIRouter()
logger = get_logger("reporting.dashboard")


ReportingDashboardServiceDI = Annotated[
    ReportingDashboardService, Depends(reporting_dashboard_service_from_lifespan)
]


@router.post(
    "",
    response_model=ReportingDashboardDTO,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def create_dashboard(
    request: CreateReportingDashboardRequest,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_dashboard_service: ReportingDashboardServiceDI,
) -> ReportingDashboardDTO:
    return await reporting_dashboard_service.create_dashboard(
        request=request,
        user_id=user_id,
        organization_id=organization_id,
    )


@router.get(
    "/{dashboard_id}",
    response_model=ReportingDashboardDTO,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def get_dashboard(
    dashboard_id: UUID,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_dashboard_service: ReportingDashboardServiceDI,
) -> ReportingDashboardDTO:
    return await reporting_dashboard_service.get_dashboard(
        dashboard_id=dashboard_id,
        organization_id=organization_id,
    )


@router.patch(
    "/{dashboard_id}",
    response_model=ReportingDashboardDTO,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def patch_dashboard(
    dashboard_id: UUID,
    request: PatchReportingDashboardRequest,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_dashboard_service: ReportingDashboardServiceDI,
) -> ReportingDashboardDTO:
    return await reporting_dashboard_service.patch_dashboard(
        dashboard_id=dashboard_id,
        request=request,
        user_id=user_id,
        organization_id=organization_id,
    )


@router.delete(
    "/{dashboard_id}",
    response_model=None,
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def delete_dashboard(
    dashboard_id: UUID,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_dashboard_service: ReportingDashboardServiceDI,
) -> None:
    await reporting_dashboard_service.delete_dashboard(
        dashboard_id=dashboard_id,
        user_id=user_id,
        organization_id=organization_id,
    )


@router.post(
    "/_list",
    response_model=PaginatedListResponse[ReportingDashboardDTO],
    dependencies=[Depends(require_read_placeholder_access)],
)
async def list_dashboards(
    list_request: ListEntityRequest,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_dashboard_service: ReportingDashboardServiceDI,
) -> PaginatedListResponse[ReportingDashboardDTO]:
    db_dashboard_dtos = await reporting_dashboard_service.list_dashboards(
        organization_id=organization_id,
    )

    filtered_dashboard_dtos = standard_filter_entities(
        db_dashboard_dtos,
        list_request.filters,
    )

    if not filtered_dashboard_dtos:
        return PaginatedListResponse(list_data=[], cursor=Cursor())

    sorted_dashboard_dtos = sort_entities(
        filtered_dashboard_dtos,
        list_request.sorters,
    )

    paginated_dashboard_dtos, response_cursor = paginate_entities(
        sorted_dashboard_dtos,
        list_request.cursor,
    )

    return PaginatedListResponse[ReportingDashboardDTO](
        list_data=paginated_dashboard_dtos,
        cursor=response_cursor,
    )
