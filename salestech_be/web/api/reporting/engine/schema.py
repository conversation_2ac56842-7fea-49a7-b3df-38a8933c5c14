from typing import Any, Self
from uuid import UUID

from pydantic import BaseModel, model_validator

from salestech_be.common.exception.exception import InvalidArgumentError
from salestech_be.core.reporting.type.filter import Filter
from salestech_be.core.reporting.type.query_config import QueryConfig


class QueryResponse(BaseModel):  # type: ignore[explicit-any]
    data: list[dict[str, Any]]  # type: ignore[explicit-any]
    columns: list[str]


class QueryPreviewRequest(BaseModel):
    query_config: QueryConfig


class EngineQueryRequest(BaseModel):
    """Request for querying a dataset or chart with optional filters"""

    dataset_id: UUID | None = None
    chart_id: UUID | None = None
    filters: list[Filter] | None = None

    @model_validator(mode="after")
    def validate_request(self) -> Self:
        if self.dataset_id is None and self.chart_id is None:
            raise InvalidArgumentError("Either dataset_id or chart_id must be provided")
        if self.dataset_id is not None and self.chart_id is not None:
            raise InvalidArgumentError(
                "Only one of dataset_id or chart_id should be provided, not both"
            )
        return self
