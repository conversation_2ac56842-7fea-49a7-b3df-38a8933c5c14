from salestech_be.web.api.reporting.dataset.views import router as dataset_router
from salestech_be.web.api.reporting.engine.views import router as engine_router
from salestech_be.web.api_router_ext import ReeAPIRouter

router = ReeAPIRouter()
router.include_router(dataset_router, prefix="/dataset", tags=["reporting"])
router.include_router(engine_router, prefix="/engine", tags=["reporting"])

__all__ = ["router"]
