from temporalio import activity, workflow

with workflow.unsafe.imports_passed_through():
    import time
    from uuid import uuid4

    from salestech_be.common.exception import ConflictResourceError
    from salestech_be.common.stats.metric import custom_metric
    from salestech_be.core.sequence.service.sequence_execution_service import (
        get_sequence_execution_service_by_db_engine,
    )
    from salestech_be.core.sequence.service.sequence_manual_task_execution_service import (
        SequenceManualTaskExecutionService,
    )
    from salestech_be.core.sequence.type.sequence_activity import (
        CompleteEnrollmentWorkflowParams,
        EnrollmentResult,
        FailEnrollmentWorkflowParams,
        FetchSequenceEnrollmentParams,
        MoveToNextStepParams,
        ProcessSequenceStepParams,
        SequenceStepResult,
        StartExecuteStepParams,
        StartExecuteStepResult,
    )
    from salestech_be.core.sequence.type.sequence_execution import StepExecutionDto
    from salestech_be.db.dao.sequence_enrollment_repo import (
        SequenceEnrollmentRepository,
    )
    from salestech_be.db.dao.sequence_repository import (
        SequenceExecutionRepository,
        SequenceRepository,
        SequenceStepRepository,
    )
    from salestech_be.db.models.sequence import (
        SequenceEnrollment,
        SequenceEnrollmentExitReasonCode,
        SequenceEnrollmentStatus,
        SequenceErrorCode,
        SequenceStatus,
        SequenceStepExecution,
        SequenceStepExecutionStatus,
        SequenceStepType,
        SequenceStepVariantStatus,
        SequenceV2,
    )
    from salestech_be.ree_logging import get_logger
    from salestech_be.temporal.activity_decorator import with_tracing
    from salestech_be.temporal.database import get_or_init_db_engine
    from salestech_be.util.time import zoned_utc_now
    from salestech_be.util.validation import not_none

logger = get_logger(__name__)

TIMING_METRIC_NAME = "sequence_enrollment_workflow_timing"


@activity.defn
@with_tracing
async def fetch_sequence_enrollment(
    params: FetchSequenceEnrollmentParams,
) -> SequenceEnrollment | None:
    db_engine = await get_or_init_db_engine()
    sequence_repository = SequenceRepository(engine=db_engine)
    return await sequence_repository.find_by_tenanted_primary_key_or_fail(
        SequenceEnrollment,
        organization_id=params.organization_id,
        id=params.sequence_enrollment_id,
    )


@activity.defn
@with_tracing
async def start_execute_step(  # noqa: C901, PLR0911, PLR0912, PLR0915
    params: StartExecuteStepParams,
) -> StepExecutionDto:
    """
    Starts the execution of a sequence step for a given enrollment.

    This activity:
    1. Validates the sequence enrollment and retrieves related step and variant information
    2. Checks if the enrollment is active and the step is valid
    3. Ensures the step has an active variant or assigns a new active variant
    4. Creates a step execution record and schedules it for execution

    Args:
        params: StartExecuteStepParams containing organization_id and sequence_enrollment_id

    Returns:
        StepExecutionDto: A DTO containing:
            - step_execution: The ID of the created step execution record (or None if not created)
            - step: The sequence step being executed
            - enrollment: The enrollment being processed

    Raises:
        ValueError: If the current step is not found
    """
    start_time = time.perf_counter()
    db_engine = await get_or_init_db_engine()
    sequence_step_repository = SequenceStepRepository(engine=db_engine)
    sequence_execution_service = get_sequence_execution_service_by_db_engine(
        db_engine=db_engine
    )

    validation_result = (
        await sequence_execution_service.validate_current_enrollment_and_get_variant(
            organization_id=params.organization_id,
            sequence_enrollment_id=params.sequence_enrollment_id,
        )
    )
    custom_metric.timing(
        metric_name=TIMING_METRIC_NAME,
        value=(time.perf_counter() - start_time) * 1000,
        tags=["activity_part:validation_result"],
    )
    if not validation_result.step:
        raise ValueError(f"Current step not found:{validation_result}")
    now = zoned_utc_now()

    start_time = time.perf_counter()
    scheduled_to_execute_at = (
        await sequence_execution_service.get_scheduled_to_execute_time(
            sequence=validation_result.sequence,
            sequence_step=validation_result.step,
        )
    )
    custom_metric.timing(
        metric_name=TIMING_METRIC_NAME,
        value=(time.perf_counter() - start_time) * 1000,
        tags=["activity_part:scheduled_to_execute_at"],
    )

    result = StepExecutionDto(
        step=validation_result.step,
        enrollment=validation_result.enrollment,
        scheduled_to_execute_at=scheduled_to_execute_at,
    )
    if (
        not validation_result.sequence
        or validation_result.sequence.status != SequenceStatus.ACTIVE
        or not validation_result.enrollment
        or validation_result.enrollment.status != SequenceEnrollmentStatus.ACTIVE
        or validation_result.step.deleted_at
        or not validation_result.has_active_step
    ):
        return result

    step = validation_result.step
    variant = validation_result.variant
    enrollment = validation_result.enrollment
    sequence = not_none(validation_result.sequence)

    if not variant or variant.status != SequenceStepVariantStatus.ACTIVE:
        start_time = time.perf_counter()
        variant = await sequence_execution_service.assign_step_to_active_variant(
            organization_id=params.organization_id,
            sequence_enrollment_id=params.sequence_enrollment_id,
            sequence_step_id=step.id,
            is_reassign=variant is not None,
        )
        custom_metric.timing(
            metric_name=TIMING_METRIC_NAME,
            value=(time.perf_counter() - start_time) * 1000,
            tags=["activity_part:assign_step_to_active_variant"],
        )
        if not variant:
            return result

    if step and variant.status == SequenceStepVariantStatus.ACTIVE:
        start_time = time.perf_counter()
        step_execution = (
            await sequence_step_repository.insert_or_get_sequence_step_execution(
                SequenceStepExecution(
                    id=uuid4(),
                    organization_id=params.organization_id,
                    sequence_id=step.sequence_id,
                    sequence_step_id=step.id,
                    sequence_step_variant_id=variant.id,
                    sequence_enrollment_id=params.sequence_enrollment_id,
                    contact_id=enrollment.contact_id,
                    mailbox_id=enrollment.email_account_id
                    if step.type == SequenceStepType.AUTO_EMAIL
                    else None,
                    contact_owner_user_id=enrollment.enrolled_by_user_id,
                    status=SequenceStepExecutionStatus.QUEUED,
                    scheduled_at=scheduled_to_execute_at,
                    created_at=now,
                )
            )
        )
        custom_metric.timing(
            metric_name=TIMING_METRIC_NAME,
            value=(time.perf_counter() - start_time) * 1000,
            tags=["activity_part:step_execution"],
        )
        result.step_execution = step_execution.id
        if step_execution.is_retry:
            result.scheduled_to_execute_at = now
            scheduled_to_execute_at = now
        if step.type == SequenceStepType.AUTO_EMAIL:
            # Only proceed with scheduling if this execution is still in QUEUED state
            # and doesn't already have a global_message_id (no message scheduled yet)

            need_scheduling = (
                step_execution.status == SequenceStepExecutionStatus.QUEUED
                and not step_execution.global_message_id
            )
            if need_scheduling:
                try:
                    start_time = time.perf_counter()
                    (
                        email_account_id,
                        send_at,
                    ) = await sequence_execution_service.get_auto_email_sending_info(
                        send_after=scheduled_to_execute_at,
                        email_account_pool_id=not_none(
                            enrollment.email_account_pool_id
                        ),
                        email_account_id=enrollment.email_account_id,
                        sequence_schedule=sequence.schedule,
                        organization_id=params.organization_id,
                    )
                    custom_metric.timing(
                        metric_name=TIMING_METRIC_NAME,
                        value=(time.perf_counter() - start_time) * 1000,
                        tags=["activity_part:get_auto_email_sending_info"],
                    )

                    if email_account_id:
                        enrollment = not_none(
                            await sequence_step_repository.update_instance(
                                enrollment.model_copy(
                                    update={"email_account_id": email_account_id},
                                )
                            )
                        )
                    await sequence_step_repository.update_instance(
                        step_execution.model_copy(
                            update={"scheduled_at": send_at},
                        )
                    )
                    result.scheduled_to_execute_at = send_at
                # Retry when mailbox slot is allocated
                except ConflictResourceError:
                    # Special case for manual email steps.  If we can't assign an email account,
                    # we need to return a response that will cause the workflow to sleep.
                    return StepExecutionDto(
                        step=validation_result.step,
                        enrollment=None,
                        scheduled_to_execute_at=None,
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to schedule message for step_execution={step_execution.id}",
                        exc_info=e,
                    )
                    (
                        _,
                        enrollment,
                    ) = await sequence_execution_service.update_failed_step_execution(
                        organization_id=params.organization_id,
                        step_execution_id=step_execution.id,
                        enrollment_id=params.sequence_enrollment_id,
                        error_code=SequenceErrorCode.EMAIL_ACCOUNT_NOT_AVAILABLE,
                        error_detail="Failed to get available email account for sending email",
                        execution_status=SequenceStepExecutionStatus.FAILED,
                    )
                    result.enrollment = enrollment
                    return result

                # Create a scheduled message using the sequence execution service
                start_time = time.perf_counter()
                (
                    step_execution,
                    enrollment,
                ) = await sequence_execution_service.create_scheduled_message(
                    organization_id=params.organization_id,
                    step_execution_id=step_execution.id,
                    enrollment=enrollment,
                    variant=variant,
                    scheduled_at=result.scheduled_to_execute_at,
                )
                custom_metric.timing(
                    metric_name=TIMING_METRIC_NAME,
                    value=(time.perf_counter() - start_time) * 1000,
                    tags=["activity_part:create_scheduled_message"],
                )
                result.enrollment = enrollment
                result.step_execution = step_execution.id
            else:
                logger.info(
                    f"Skipping message scheduling for existing execution: status={step_execution.status}, "
                    f"has_global_message_id={step_execution.global_message_id is not None}"
                )
                if step_execution.scheduled_at:
                    result.scheduled_to_execute_at = None
        elif step.type == SequenceStepType.MANUAL_EMAIL:
            if enrollment.email_account_id is None:
                try:
                    (
                        email_account_id,
                        _,
                    ) = await sequence_execution_service.get_auto_email_sending_info(
                        send_after=scheduled_to_execute_at,
                        email_account_pool_id=not_none(
                            enrollment.email_account_pool_id
                        ),
                        email_account_id=enrollment.email_account_id,
                        sequence_schedule=sequence.schedule,
                        organization_id=params.organization_id,
                    )
                    if email_account_id:
                        enrollment = not_none(
                            await sequence_step_repository.update_instance(
                                enrollment.model_copy(
                                    update={
                                        "email_account_id": email_account_id,
                                        "updated_at": now,
                                    },
                                )
                            )
                        )
                    else:
                        # No email account could be assigned
                        logger.error(
                            f"No email account available for manual step_execution={step_execution.id}"
                        )
                        return result
                # Retry when mailbox slot is allocated
                except ConflictResourceError:
                    # Special case for manual email steps.  If we can't assign an email account,
                    # we need to return a response that will cause the workflow to sleep.
                    return StepExecutionDto(
                        step=validation_result.step,
                        enrollment=None,
                        scheduled_to_execute_at=None,
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to assign email account for manual step_execution={step_execution.id}",
                        exc_info=e,
                    )
                    return result

            # Only proceed to create draft message if we have an email account
            if enrollment.email_account_id:
                # create a draft message
                (
                    step_execution,
                    enrollment,
                ) = await sequence_execution_service.create_draft_message(
                    organization_id=params.organization_id,
                    step_execution_id=step_execution.id,
                    enrollment=enrollment,
                    variant=variant,
                )
                result.enrollment = enrollment
                result.step_execution = step_execution.id
                logger.bind(step_execution=step_execution.id).info(
                    "Created draft message for step_execution"
                )
            else:
                logger.error(
                    f"Cannot create draft message - no email account assigned for step_execution={step_execution.id}"
                )

    return result


@activity.defn
@with_tracing
async def process_sequence_step(
    params: ProcessSequenceStepParams,
) -> SequenceEnrollment | None:
    db_engine = await get_or_init_db_engine()
    execution_repository = SequenceExecutionRepository(engine=db_engine)
    execution = not_none(
        await execution_repository.find_by_tenanted_primary_key(
            SequenceStepExecution,
            id=params.step_execution_id,
            organization_id=params.organization_id,
        )
    )
    sequence_execution_service = get_sequence_execution_service_by_db_engine(
        db_engine=db_engine
    )
    sequence_manual_task_execution = SequenceManualTaskExecutionService(
        db_engine=db_engine
    )
    if execution.status != SequenceStepExecutionStatus.QUEUED:
        return await sequence_execution_service.move_enrollment_to_next_step(
            organization_id=params.organization_id,
            enrollment_id=params.sequence_enrollment_id,
            next_step_id=params.next_step_id,
        )

    if params.step_type is SequenceStepType.AUTO_EMAIL:
        return await sequence_execution_service.execute_auto_email_step(
            organization_id=params.organization_id,
            step_execution_id=params.step_execution_id,
            sequence_enrollment_id=params.sequence_enrollment_id,
        )
    elif params.step_type in SequenceStepType.get_manual_task_types():
        return await sequence_manual_task_execution.execute_manual_step(
            organization_id=params.organization_id,
            step_execution_id=params.step_execution_id,
            sequence_enrollment_id=params.sequence_enrollment_id,
            step_type=params.step_type,
        )
    else:
        _, enrollment = await sequence_execution_service.update_failed_step_execution(
            organization_id=params.organization_id,
            step_execution_id=params.step_execution_id,
            enrollment_id=params.sequence_enrollment_id,
            error_code=SequenceErrorCode.UNSUPPORTED_STEP_TYPE,
            error_detail="Unsupported sequence step type",
        )
        return enrollment


@activity.defn
@with_tracing
async def move_to_next_step(
    params: MoveToNextStepParams,
) -> SequenceEnrollment:
    db_engine = await get_or_init_db_engine()
    sequence_execution_service = get_sequence_execution_service_by_db_engine(
        db_engine=db_engine
    )
    return await sequence_execution_service.move_enrollment_to_next_step(
        organization_id=params.organization_id,
        enrollment_id=params.sequence_enrollment_id,
        next_step_id=params.next_step_id,
    )


@activity.defn
@with_tracing
async def complete_enrollment_workflow(
    params: CompleteEnrollmentWorkflowParams,
) -> None:
    """Complete a sequence enrollment workflow by marking it as exited.

    Args:
        params: The parameters for completing the enrollment workflow.
    """
    db_engine = await get_or_init_db_engine()
    sequence_enrollment_repository = SequenceEnrollmentRepository(engine=db_engine)
    await sequence_enrollment_repository.update_by_tenanted_primary_key(
        SequenceEnrollment,
        organization_id=params.organization_id,
        primary_key_to_value={"id": params.sequence_enrollment_id},
        column_to_update={
            "status": SequenceEnrollmentStatus.EXITED,
            "exited_at": zoned_utc_now(),
            "exited_reason": SequenceEnrollmentExitReasonCode.COMPLETED,
        },
    )


@activity.defn
@with_tracing
async def fail_enrollment_workflow(
    params: FailEnrollmentWorkflowParams,
) -> None:
    db_engine = await get_or_init_db_engine()
    sequence_enrollment_repository = SequenceEnrollmentRepository(engine=db_engine)
    await sequence_enrollment_repository.update_failed_enrollment(
        organization_id=params.organization_id,
        enrollment_id=params.sequence_enrollment_id,
        error_code=SequenceErrorCode.SYSTEM_ERROR,
        error_detail="Internal system error",
    )


@activity.defn
@with_tracing
async def fetch_sequence_enrollment_v2(
    params: FetchSequenceEnrollmentParams,
) -> EnrollmentResult | None:
    """Fetch sequence enrollment and return only essential information.

    Args:
        params: The parameters for fetching the enrollment.

    Returns:
        EnrollmentResult: Essential enrollment information or None if not found.
    """
    db_engine = await get_or_init_db_engine()
    sequence_repository = SequenceRepository(engine=db_engine)
    enrollment = await sequence_repository.find_by_tenanted_primary_key_or_fail(
        SequenceEnrollment,
        organization_id=params.organization_id,
        id=params.sequence_enrollment_id,
    )

    if enrollment is None:
        return None

    return EnrollmentResult(
        id=enrollment.id,
        organization_id=enrollment.organization_id,
        status=enrollment.status,
        current_step_id=enrollment.current_step_id,
    )


@activity.defn
@with_tracing
async def start_execute_step_v2(  # noqa: C901, PLR0911, PLR0912, PLR0915
    params: StartExecuteStepParams,
) -> StartExecuteStepResult:
    """
    Starts the execution of a sequence step for a given enrollment.

    This activity:
    1. Validates the sequence enrollment and retrieves related step and variant information
    2. Checks if the enrollment is active and the step is valid
    3. Ensures the step has an active variant or assigns a new active variant
    4. Creates a step execution record and schedules it for execution

    Args:
        params: StartExecuteStepParams containing organization_id and sequence_enrollment_id

    Returns:
        StartExecuteStepResult: A result object containing:
            - step_execution: The ID of the created step execution record (or None if not created)
            - step: The essential sequence step information
            - enrollment: The essential enrollment information
            - scheduled_to_execute_at: When the step is scheduled to execute

    Raises:
        ValueError: If the current step is not found
    """
    start_time = time.perf_counter()
    db_engine = await get_or_init_db_engine()
    sequence_step_repository = SequenceStepRepository(engine=db_engine)
    sequence_execution_service = get_sequence_execution_service_by_db_engine(
        db_engine=db_engine
    )

    validation_result = (
        await sequence_execution_service.validate_current_enrollment_and_get_variant(
            organization_id=params.organization_id,
            sequence_enrollment_id=params.sequence_enrollment_id,
        )
    )
    custom_metric.timing(
        metric_name=TIMING_METRIC_NAME,
        value=(time.perf_counter() - start_time) * 1000,
        tags=["activity_part:validation_result_v2"],
    )
    if not validation_result.step:
        raise ValueError(f"Current step not found:{validation_result}")
    now = zoned_utc_now()

    start_time = time.perf_counter()
    scheduled_to_execute_at = (
        await sequence_execution_service.get_scheduled_to_execute_time(
            sequence=validation_result.sequence,
            sequence_step=validation_result.step,
        )
    )
    custom_metric.timing(
        metric_name=TIMING_METRIC_NAME,
        value=(time.perf_counter() - start_time) * 1000,
        tags=["activity_part:scheduled_to_execute_at_v2"],
    )

    # Convert step to SequenceStepResult
    step_result = SequenceStepResult(
        id=validation_result.step.id,
        type=validation_result.step.type,
        next_step_id=validation_result.step.next_step_id,
    )

    # Convert enrollment to EnrollmentResult if it exists
    enrollment_result = None
    if validation_result.enrollment:
        enrollment_result = EnrollmentResult(
            id=validation_result.enrollment.id,
            organization_id=validation_result.enrollment.organization_id,
            status=validation_result.enrollment.status,
            current_step_id=validation_result.enrollment.current_step_id,
        )

    result = StartExecuteStepResult(
        step=step_result,
        enrollment=enrollment_result,
        scheduled_to_execute_at=scheduled_to_execute_at,
    )

    if (
        not validation_result.sequence
        or validation_result.sequence.status != SequenceStatus.ACTIVE
        or not validation_result.enrollment
        or validation_result.enrollment.status != SequenceEnrollmentStatus.ACTIVE
        or validation_result.step.deleted_at
        or not validation_result.has_active_step
    ):
        return result

    step = validation_result.step
    variant = validation_result.variant
    enrollment = validation_result.enrollment
    sequence = not_none(validation_result.sequence)

    if not variant or variant.status != SequenceStepVariantStatus.ACTIVE:
        start_time = time.perf_counter()
        variant = await sequence_execution_service.assign_step_to_active_variant(
            organization_id=params.organization_id,
            sequence_enrollment_id=params.sequence_enrollment_id,
            sequence_step_id=step.id,
            is_reassign=variant is not None,
        )
        custom_metric.timing(
            metric_name=TIMING_METRIC_NAME,
            value=(time.perf_counter() - start_time) * 1000,
            tags=["activity_part:assign_step_to_active_variant_v2"],
        )
        if not variant:
            return result

    if step and variant.status == SequenceStepVariantStatus.ACTIVE:
        start_time = time.perf_counter()
        step_execution = (
            await sequence_step_repository.insert_or_get_sequence_step_execution(
                SequenceStepExecution(
                    id=uuid4(),
                    organization_id=params.organization_id,
                    sequence_id=step.sequence_id,
                    sequence_step_id=step.id,
                    sequence_step_variant_id=variant.id,
                    sequence_enrollment_id=params.sequence_enrollment_id,
                    contact_id=enrollment.contact_id,
                    mailbox_id=enrollment.email_account_id
                    if step.type == SequenceStepType.AUTO_EMAIL
                    else None,
                    contact_owner_user_id=enrollment.enrolled_by_user_id,
                    status=SequenceStepExecutionStatus.QUEUED,
                    scheduled_at=scheduled_to_execute_at,
                    created_at=now,
                )
            )
        )
        custom_metric.timing(
            metric_name=TIMING_METRIC_NAME,
            value=(time.perf_counter() - start_time) * 1000,
            tags=["activity_part:step_execution_v2"],
        )
        result.step_execution = step_execution.id
        if step_execution.is_retry:
            result.scheduled_to_execute_at = now
            scheduled_to_execute_at = now
        if step.type == SequenceStepType.AUTO_EMAIL:
            # Only proceed with scheduling if this execution is still in QUEUED state
            # and doesn't already have a global_message_id (no message scheduled yet)

            need_scheduling = (
                step_execution.status == SequenceStepExecutionStatus.QUEUED
                and not step_execution.global_message_id
            )
            if need_scheduling:
                try:
                    start_time = time.perf_counter()
                    (
                        email_account_id,
                        send_at,
                    ) = await sequence_execution_service.get_auto_email_sending_info(
                        send_after=scheduled_to_execute_at,
                        email_account_pool_id=not_none(
                            enrollment.email_account_pool_id
                        ),
                        email_account_id=enrollment.email_account_id,
                        sequence_schedule=sequence.schedule,
                        organization_id=params.organization_id,
                    )
                    custom_metric.timing(
                        metric_name=TIMING_METRIC_NAME,
                        value=(time.perf_counter() - start_time) * 1000,
                        tags=["activity_part:get_auto_email_sending_info_v2"],
                    )

                    if email_account_id:
                        enrollment = not_none(
                            await sequence_step_repository.update_instance(
                                enrollment.model_copy(
                                    update={"email_account_id": email_account_id},
                                )
                            )
                        )
                    await sequence_step_repository.update_instance(
                        step_execution.model_copy(
                            update={"scheduled_at": send_at},
                        )
                    )
                    result.scheduled_to_execute_at = send_at
                # Retry when mailbox slot is allocated
                except ConflictResourceError:
                    # Special case for manual email steps.  If we can't assign an email account,
                    # we need to return a response that will cause the workflow to sleep.
                    return StartExecuteStepResult(
                        step=step_result,
                        enrollment=None,
                        scheduled_to_execute_at=None,
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to schedule message for step_execution={step_execution.id}",
                        exc_info=e,
                    )
                    (
                        _,
                        enrollment,
                    ) = await sequence_execution_service.update_failed_step_execution(
                        organization_id=params.organization_id,
                        step_execution_id=step_execution.id,
                        enrollment_id=params.sequence_enrollment_id,
                        error_code=SequenceErrorCode.EMAIL_ACCOUNT_NOT_AVAILABLE,
                        error_detail="Failed to get available email account for sending email",
                        execution_status=SequenceStepExecutionStatus.FAILED,
                    )
                    # Update enrollment result
                    if enrollment:
                        result.enrollment = EnrollmentResult(
                            id=enrollment.id,
                            organization_id=enrollment.organization_id,
                            status=enrollment.status,
                            current_step_id=enrollment.current_step_id,
                        )
                    return result

                # Create a scheduled message using the sequence execution service
                start_time = time.perf_counter()
                (
                    step_execution,
                    enrollment,
                ) = await sequence_execution_service.create_scheduled_message(
                    organization_id=params.organization_id,
                    step_execution_id=step_execution.id,
                    enrollment=enrollment,
                    variant=variant,
                    scheduled_at=result.scheduled_to_execute_at,
                )
                custom_metric.timing(
                    metric_name=TIMING_METRIC_NAME,
                    value=(time.perf_counter() - start_time) * 1000,
                    tags=["activity_part:create_scheduled_message_v2"],
                )
                # Update the enrollment result
                if enrollment:
                    result.enrollment = EnrollmentResult(
                        id=enrollment.id,
                        organization_id=enrollment.organization_id,
                        status=enrollment.status,
                        current_step_id=enrollment.current_step_id,
                    )
                result.step_execution = step_execution.id
            else:
                logger.info(
                    f"Skipping message scheduling for existing execution: status={step_execution.status}, "
                    f"has_global_message_id={step_execution.global_message_id is not None}"
                )
                if step_execution.scheduled_at:
                    result.scheduled_to_execute_at = None
        elif step.type == SequenceStepType.MANUAL_EMAIL:
            # Logic for manual email steps similar to original implementation
            # but updating result objects appropriately
            if enrollment.email_account_id is None:
                try:
                    (
                        email_account_id,
                        _,
                    ) = await sequence_execution_service.get_auto_email_sending_info(
                        send_after=scheduled_to_execute_at,
                        email_account_pool_id=not_none(
                            enrollment.email_account_pool_id
                        ),
                        email_account_id=enrollment.email_account_id,
                        sequence_schedule=sequence.schedule,
                        organization_id=params.organization_id,
                    )
                    if email_account_id:
                        enrollment = not_none(
                            await sequence_step_repository.update_instance(
                                enrollment.model_copy(
                                    update={
                                        "email_account_id": email_account_id,
                                        "updated_at": now,
                                    },
                                )
                            )
                        )
                    else:
                        # No email account could be assigned
                        logger.error(
                            f"No email account available for manual step_execution={step_execution.id}"
                        )
                        return result
                # Retry when mailbox slot is allocated
                except ConflictResourceError:
                    # Special case for manual email steps.  If we can't assign an email account,
                    # we need to return a response that will cause the workflow to sleep.
                    return StartExecuteStepResult(
                        step=step_result,
                        enrollment=None,
                        scheduled_to_execute_at=None,
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to assign email account for manual step_execution={step_execution.id}",
                        exc_info=e,
                    )
                    return result

            # Only proceed to create draft message if we have an email account
            if enrollment.email_account_id:
                # create a draft message
                (
                    step_execution,
                    enrollment,
                ) = await sequence_execution_service.create_draft_message(
                    organization_id=params.organization_id,
                    step_execution_id=step_execution.id,
                    enrollment=enrollment,
                    variant=variant,
                )
                # Update enrollment result
                if enrollment:
                    result.enrollment = EnrollmentResult(
                        id=enrollment.id,
                        organization_id=enrollment.organization_id,
                        status=enrollment.status,
                        current_step_id=enrollment.current_step_id,
                    )
                result.step_execution = step_execution.id
                logger.bind(step_execution=step_execution.id).info(
                    "Created draft message for step_execution"
                )
            else:
                logger.error(
                    f"Cannot create draft message - no email account assigned for step_execution={step_execution.id}"
                )

    return result


@activity.defn
@with_tracing
async def process_sequence_step_v2(
    params: ProcessSequenceStepParams,
) -> EnrollmentResult | None:
    """Process a sequence step.

    Args:
        params: The parameters for processing the step.

    Returns:
        EnrollmentResult: Essential enrollment information or None if not found.
    """
    db_engine = await get_or_init_db_engine()
    execution_repository = SequenceExecutionRepository(engine=db_engine)
    execution = not_none(
        await execution_repository.find_by_tenanted_primary_key(
            SequenceStepExecution,
            id=params.step_execution_id,
            organization_id=params.organization_id,
        )
    )
    sequence_execution_service = get_sequence_execution_service_by_db_engine(
        db_engine=db_engine
    )
    sequence_manual_task_execution = SequenceManualTaskExecutionService(
        db_engine=db_engine
    )
    enrollment: (
        SequenceEnrollment | None
    ) = await execution_repository.find_by_tenanted_primary_key(
        SequenceEnrollment,
        id=params.sequence_enrollment_id,
        organization_id=params.organization_id,
    )
    if execution.status is SequenceStepExecutionStatus.SENT:
        enrollment = await sequence_execution_service.move_enrollment_to_next_step(
            organization_id=params.organization_id,
            enrollment_id=params.sequence_enrollment_id,
            next_step_id=params.next_step_id,
        )
        return (
            EnrollmentResult(
                id=enrollment.id,
                organization_id=enrollment.organization_id,
                status=enrollment.status,
                current_step_id=enrollment.current_step_id,
            )
            if enrollment
            else None
        )

    seq = await execution_repository.find_by_tenanted_primary_key(
        SequenceV2,
        id=execution.sequence_id,
        organization_id=params.organization_id,
    )
    if not seq:
        raise ValueError(f"Sequence not found: {execution.sequence_id}")

    if execution.status is SequenceStepExecutionStatus.QUEUED:
        if params.step_type is SequenceStepType.AUTO_EMAIL:
            enrollment = await sequence_execution_service.execute_auto_email_step(
                organization_id=params.organization_id,
                step_execution_id=params.step_execution_id,
                sequence_enrollment_id=params.sequence_enrollment_id,
                embed_tracking_url=seq.enable_pixel_tracking,
            )
        elif params.step_type in SequenceStepType.get_manual_task_types():
            enrollment = await sequence_manual_task_execution.execute_manual_step(
                organization_id=params.organization_id,
                step_execution_id=params.step_execution_id,
                sequence_enrollment_id=params.sequence_enrollment_id,
                step_type=params.step_type,
            )
        else:
            (
                _,
                enrollment,
            ) = await sequence_execution_service.update_failed_step_execution(
                organization_id=params.organization_id,
                step_execution_id=params.step_execution_id,
                enrollment_id=params.sequence_enrollment_id,
                error_code=SequenceErrorCode.UNSUPPORTED_STEP_TYPE,
                error_detail="Unsupported sequence step type",
            )
    return (
        EnrollmentResult(
            id=enrollment.id,
            organization_id=enrollment.organization_id,
            status=enrollment.status,
            current_step_id=enrollment.current_step_id,
        )
        if enrollment
        else None
    )


@activity.defn
@with_tracing
async def move_to_next_step_v2(
    params: MoveToNextStepParams,
) -> EnrollmentResult:
    db_engine = await get_or_init_db_engine()
    sequence_execution_service = get_sequence_execution_service_by_db_engine(
        db_engine=db_engine
    )
    enrollment = await sequence_execution_service.move_enrollment_to_next_step(
        organization_id=params.organization_id,
        enrollment_id=params.sequence_enrollment_id,
        next_step_id=params.next_step_id,
    )
    return EnrollmentResult(
        id=enrollment.id,
        organization_id=enrollment.organization_id,
        status=enrollment.status,
        current_step_id=enrollment.current_step_id,
    )
