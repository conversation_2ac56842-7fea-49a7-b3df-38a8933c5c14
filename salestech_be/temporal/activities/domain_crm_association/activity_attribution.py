from typing import TypeVar
from uuid import UUID

from temporalio import activity

from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.core.contact.service.contact_query_service import (
    get_contact_query_service,
)
from salestech_be.core.domain_crm_association.types import (
    BasicDomainCrmActivityInput,
    DomainActivityCaptureAttributionResult,
    UpdateDomainCrmAssociation,
)
from salestech_be.core.pipeline.service.pipeline_query_service import (
    get_pipeline_query_service,
)
from salestech_be.db.models.domain_crm_association import (
    AttributionState,
    DomainCRMAssociation,
)
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.activity_decorator import with_tracing
from salestech_be.temporal.database import get_or_init_db_engine
from salestech_be.util.validation import not_none

logger = get_logger(__name__)

T = TypeVar("T")


def should_skip_association(
    association: DomainCRMAssociation,
    field_name: str,
    updated_associations: list[DomainCRMAssociation],
) -> bool:
    """
    Helper function to check if an association should be skipped based on common validation rules.

    Args:
        association: The domain CRM association to validate
        field_name: The field name to check (e.g., "account_id", "pipeline_id")
        updated_associations: List to append the association to if it should be skipped

    Returns:
        True if the association should be skipped, False otherwise
    """
    # Skip if user_id is set
    if association.user_id is not None:
        logger.info(
            f"skipping domain CRM association {association.id} because user_id is set"
        )
        updated_associations.append(association)
        return True

    # Skip if contact_id is not set
    if association.contact_id is None:
        logger.bind(association_id=str(association.id)).error(
            "Contact ID is not set for domain CRM association"
        )
        updated_associations.append(association)
        return True

    # Skip if the specific field is already set
    field_value = getattr(association, field_name, None)
    if field_value is not None:
        logger.info(
            f"skipping domain CRM association {association.id} because {field_name} is set"
        )
        updated_associations.append(association)
        return True

    return False


@activity.defn
@with_tracing
async def phone_number_to_contact_capture_activity(
    input_data: BasicDomainCrmActivityInput,
) -> DomainActivityCaptureAttributionResult:
    """
    Acticity capture for phone number
    only find contact_id in here
    """
    logger.bind(
        association_ids=[association.id for association in input_data.associations],
    ).info("activity capture for phone number started")

    try:
        # Initialize database engine
        db_engine = await get_or_init_db_engine()

        # Get services - import here to avoid circular import
        from salestech_be.core.domain_crm_association.domain_crm_association_service import (
            get_domain_crm_association_service,
        )

        domain_association_service = get_domain_crm_association_service(db_engine)
        contact_query_service = get_contact_query_service(db_engine)

        updated_associations: list[DomainCRMAssociation] = []
        # capture for each association
        for association in input_data.associations:
            if (
                association.contact_id is None
                and association.user_id is None
                and association.phone_number is not None
            ):
                contacts_ids = (
                    await contact_query_service.list_contact_ids_by_phone_number(
                        organization_id=input_data.organization_id,
                        phone_number=association.phone_number,
                    )
                )
                for i, contact_id in enumerate(contacts_ids):
                    if i == 0:
                        # Update the first association
                        updated_association = await domain_association_service.update_domain_crm_association(
                            association.id,
                            UpdateDomainCrmAssociation(
                                contact_id=contact_id,
                                organization_id=input_data.organization_id,
                                updated_by_user_id=input_data.user_id,
                            ),
                        )
                        updated_associations.append(updated_association)
                    else:
                        # Clone for the rest
                        cloned_association = await domain_association_service.clone_domain_crm_association_with_updates(
                            association_id=association.id,
                            updates={
                                "contact_id": contact_id,
                                "updated_by_user_id": input_data.user_id,
                            },
                        )
                        updated_associations.append(cloned_association)
            else:
                updated_associations.append(association)

        return DomainActivityCaptureAttributionResult(
            success=True,
            associations=updated_associations if updated_associations else [],
        )

    except ResourceNotFoundError as e:
        logger.bind(
            association_ids=[association.id for association in input_data.associations],
            error=e,
        ).error("Resource not found for phone number activity capture")
        return DomainActivityCaptureAttributionResult(
            success=False,
            associations=input_data.associations,
            error_message=str(e),
        )
    except Exception as e:
        logger.bind(
            association_ids=[association.id for association in input_data.associations],
            error=e,
        ).error("Failed to attribute domain activity for phone number")
        return DomainActivityCaptureAttributionResult(
            success=False,
            associations=input_data.associations,
            error_message=str(e),
        )


def select_first_association(
    associations: list[DomainCRMAssociation], field_name: str
) -> tuple[
    list[tuple[UUID, UpdateDomainCrmAssociation]],
    list[tuple[UUID, UpdateDomainCrmAssociation]],
]:
    """
    Helper function to select the first association and mark the rest as not selected.
    Returns a tuple of (selected_associations, not_selected_associations) updates.

    Args:
        associations: List of domain CRM associations to process
        field_name: The attribution field name (contact, account, or pipeline)

    Returns:
        Tuple containing lists of (association_id, update) tuples for selected and not selected associations
    """
    if not associations:
        return ([], [])

    selected_updates = []
    not_selected_updates = []

    # Map field_name to the appropriate attribution state field
    field_map = {
        "contact": "contact_attribution_state",
        "account": "account_attribution_state",
        "pipeline": "pipeline_attribution_state",
    }

    field_key = field_map.get(field_name)
    if not field_key:
        raise ValueError(f"Invalid field_name: {field_name}")

    for i, association in enumerate(associations):
        # First association is selected, others are not
        attribution_state = (
            AttributionState.PROCESSED_SELECTED
            if i == 0
            else AttributionState.PROCESSED_NOT_SELECTED
        )

        # Create base update
        base_update = UpdateDomainCrmAssociation(
            organization_id=association.organization_id,
        )

        # Use copy with update to set the specific field
        update_kwargs = {field_key: attribution_state}
        update = base_update.copy(update=update_kwargs)

        if i == 0:
            selected_updates.append((association.id, update))
        else:
            not_selected_updates.append((association.id, update))

    return (selected_updates, not_selected_updates)


@activity.defn
@with_tracing
async def contact_attribution_activity(
    input_data: BasicDomainCrmActivityInput,
) -> DomainActivityCaptureAttributionResult:
    """
    Activity attribution for contact
    Filters/selects domain CRM associations based on contact attribution logic
    """
    logger.bind(
        associations=[association.id for association in input_data.associations],
    ).info("contact attribution activity started")

    try:
        # Initialize database engine
        db_engine = await get_or_init_db_engine()

        # Get services - import here to avoid circular import
        from salestech_be.core.domain_crm_association.domain_crm_association_service import (
            get_domain_crm_association_service,
        )

        domain_association_service = get_domain_crm_association_service(db_engine)

        # Only work with associations that have contact_id set
        valid_associations = [
            a for a in input_data.associations if a.contact_id is not None
        ]

        if not valid_associations:
            logger.info("No valid associations with contact_id found")
            return DomainActivityCaptureAttributionResult(
                success=True,
                associations=input_data.associations,
            )

        # Select first association and mark others as not selected
        selected_updates, not_selected_updates = select_first_association(
            valid_associations, "contact"
        )

        # Add user_id to all updates
        selected_updates = [
            (assoc_id, update.copy(update={"updated_by_user_id": input_data.user_id}))
            for assoc_id, update in selected_updates
        ]
        not_selected_updates = [
            (assoc_id, update.copy(update={"updated_by_user_id": input_data.user_id}))
            for assoc_id, update in not_selected_updates
        ]

        # Combine all updates into one list for bulk processing
        all_updates = selected_updates + not_selected_updates

        # Perform bulk update
        updated_associations = (
            await domain_association_service.bulk_update_domain_crm_associations(
                all_updates
            )
        )

        # Return all associations (both selected and not selected)
        return DomainActivityCaptureAttributionResult(
            success=True,
            associations=updated_associations if updated_associations else [],
        )
    except Exception as e:
        logger.bind(
            association_ids=[
                str(association.id) for association in input_data.associations
            ],
            error=e,
        ).error("Failed to attribute domain activity for contact")
        return DomainActivityCaptureAttributionResult(
            success=False,
            associations=input_data.associations,
            error_message=str(e),
        )


@activity.defn
@with_tracing
async def account_capture_activity(
    input_data: BasicDomainCrmActivityInput,
) -> DomainActivityCaptureAttributionResult:
    """
    Activity capture for domain activity - account only
    Creates a new domain CRM association for each account
    associated with a contact.
    """
    logger.bind(
        associations=input_data.associations,
        organization_id=str(input_data.organization_id),
    ).info("account capture activity started")

    try:
        # Initialize database engine
        db_engine = await get_or_init_db_engine()

        # Get services - import here to avoid circular import
        from salestech_be.core.domain_crm_association.domain_crm_association_service import (
            get_domain_crm_association_service,
        )

        domain_association_service = get_domain_crm_association_service(db_engine)
        contact_query_service = get_contact_query_service(db_engine)

        # Check all records with the given call_id in domain_crm_association table
        associations = input_data.associations
        updated_associations: list[DomainCRMAssociation] = []

        for association in associations:
            # Use helper function to check if association should be skipped
            if should_skip_association(association, "account_id", updated_associations):
                continue

            # Get all account associations for this contact
            account_associations = await contact_query_service.list_active_contact_associations_for_contact(
                organization_id=input_data.organization_id,
                contact_id=not_none(association.contact_id),
            )
            account_ids = [
                association.account_id for association in account_associations
            ]

            # Process account IDs
            for i, account_id in enumerate(account_ids):
                if i == 0:
                    # Update the first association
                    updated_association = (
                        await domain_association_service.update_domain_crm_association(
                            association.id,
                            UpdateDomainCrmAssociation(
                                account_id=account_id,
                                organization_id=association.organization_id,
                                updated_by_user_id=input_data.user_id,
                            ),
                        )
                    )
                    updated_associations.append(updated_association)
                else:
                    # Clone for the rest
                    cloned_association = await domain_association_service.clone_domain_crm_association_with_updates(
                        association_id=association.id,
                        updates={
                            "account_id": account_id,
                            "updated_by_user_id": input_data.user_id,
                        },
                    )
                    updated_associations.append(cloned_association)

        # Return success
        return DomainActivityCaptureAttributionResult(
            success=True,
            associations=updated_associations if updated_associations else [],
        )
    except ResourceNotFoundError as e:
        logger.bind(
            id=str(input_data.associations[0].id) if input_data.associations else None,
            error=e,
        ).error("Resource not found for account capture activity")
        return DomainActivityCaptureAttributionResult(
            success=False,
            associations=input_data.associations,
            error_message=str(e),
        )
    except Exception as e:
        logger.bind(
            id=str(input_data.associations[0].id) if input_data.associations else None,
            error=e,
        ).error("Failed to attribute domain activity for account")
        return DomainActivityCaptureAttributionResult(
            success=False,
            associations=input_data.associations,
            error_message=str(e),
        )


@activity.defn
@with_tracing
async def pipeline_capture_activity(
    input_data: BasicDomainCrmActivityInput,
) -> DomainActivityCaptureAttributionResult:
    """
    Activity capture for domain activity - pipeline only
    Creates a new domain CRM association for each pipeline
    associated with a contact and account.
    """
    logger.bind(
        associations=input_data.associations,
        organization_id=str(input_data.organization_id),
    ).info("pipeline capture activity started")

    try:
        # Initialize database engine
        db_engine = await get_or_init_db_engine()

        # Get services - import here to avoid circular import
        from salestech_be.core.domain_crm_association.domain_crm_association_service import (
            get_domain_crm_association_service,
        )

        domain_association_service = get_domain_crm_association_service(db_engine)
        pipeline_query_service = get_pipeline_query_service(db_engine)

        # Check all records with the given call_id in domain_crm_association table
        associations = input_data.associations
        updated_associations: list[DomainCRMAssociation] = []

        for association in associations:
            # Use helper function to check if association should be skipped
            if should_skip_association(
                association, "pipeline_id", updated_associations
            ):
                continue

            # Get pipeline associations - prioritize account-based lookup when account_id is available
            pipeline_ids = []

            if association.account_id is not None:
                # Primary method: Get pipelines directly by account_id
                account_pipelines = (
                    await pipeline_query_service.list_pipelines_by_account_id(
                        organization_id=input_data.organization_id,
                        account_id=association.account_id,
                    )
                )
                pipeline_ids.extend([pipeline.id for pipeline in account_pipelines])

            # Fallback method: Get pipeline associations by contact_id if no pipelines found via account
            if not pipeline_ids:
                pipeline_associations = await pipeline_query_service.list_contact_pipeline_associations_by_contact_id(
                    organization_id=input_data.organization_id,
                    contact_id=not_none(association.contact_id),
                )
                pipeline_ids.extend(
                    [assoc.pipeline_id for assoc in pipeline_associations]
                )

            # Process pipeline IDs
            for i, pipeline_id in enumerate(pipeline_ids):
                if i == 0:
                    # Update the first association
                    updated_association = (
                        await domain_association_service.update_domain_crm_association(
                            association.id,
                            UpdateDomainCrmAssociation(
                                pipeline_id=pipeline_id,
                                organization_id=association.organization_id,
                                updated_by_user_id=input_data.user_id,
                            ),
                        )
                    )
                    updated_associations.append(updated_association)
                else:
                    # Clone for the rest
                    cloned_association = await domain_association_service.clone_domain_crm_association_with_updates(
                        association_id=association.id,
                        updates={
                            "pipeline_id": pipeline_id,
                            "updated_by_user_id": input_data.user_id,
                        },
                    )
                    updated_associations.append(cloned_association)

        # Return success
        return DomainActivityCaptureAttributionResult(
            success=True,
            associations=updated_associations if updated_associations else [],
        )
    except ResourceNotFoundError as e:
        logger.bind(
            id=str(input_data.associations[0].id) if input_data.associations else None,
            error=e,
        ).error("Resource not found for pipeline capture activity")
        return DomainActivityCaptureAttributionResult(
            success=False,
            associations=input_data.associations,
            error_message=str(e),
        )
    except Exception as e:
        logger.bind(
            id=str(input_data.associations[0].id) if input_data.associations else None,
            error=e,
        ).error("Failed to attribute domain activity for pipeline")
        return DomainActivityCaptureAttributionResult(
            success=False,
            associations=input_data.associations,
            error_message=str(e),
        )


@activity.defn
@with_tracing
async def account_attribution_activity(
    input_data: BasicDomainCrmActivityInput,
) -> DomainActivityCaptureAttributionResult:
    """
    Activity attribution for account
    Filters/selects domain CRM associations based on account attribution logic
    """
    logger.bind(
        associations=[association.id for association in input_data.associations],
    ).info("account attribution activity started")

    try:
        # Initialize database engine
        db_engine = await get_or_init_db_engine()

        # Get services - import here to avoid circular import
        from salestech_be.core.domain_crm_association.domain_crm_association_service import (
            get_domain_crm_association_service,
        )

        domain_association_service = get_domain_crm_association_service(db_engine)

        # Only work with associations that have account_id set
        valid_associations = [
            a for a in input_data.associations if a.account_id is not None
        ]

        if not valid_associations:
            logger.info("No valid associations with account_id found")
            return DomainActivityCaptureAttributionResult(
                success=True,
                associations=input_data.associations,
            )

        # Select first association and mark others as not selected
        selected_updates, not_selected_updates = select_first_association(
            valid_associations, "account"
        )

        # Add user_id to all updates
        selected_updates = [
            (assoc_id, update.copy(update={"updated_by_user_id": input_data.user_id}))
            for assoc_id, update in selected_updates
        ]
        not_selected_updates = [
            (assoc_id, update.copy(update={"updated_by_user_id": input_data.user_id}))
            for assoc_id, update in not_selected_updates
        ]

        # Combine all updates into one list for bulk processing
        all_updates = selected_updates + not_selected_updates

        # Perform bulk update
        updated_associations = (
            await domain_association_service.bulk_update_domain_crm_associations(
                all_updates
            )
        )

        # Return all associations (both selected and not selected)
        return DomainActivityCaptureAttributionResult(
            success=True,
            associations=updated_associations if updated_associations else [],
        )
    except Exception as e:
        logger.bind(
            association_ids=[
                str(association.id) for association in input_data.associations
            ],
            error=e,
        ).error("Failed to attribute domain activity for account")
        return DomainActivityCaptureAttributionResult(
            success=False,
            associations=input_data.associations,
            error_message=str(e),
        )


@activity.defn
@with_tracing
async def pipeline_attribution_activity(
    input_data: BasicDomainCrmActivityInput,
) -> DomainActivityCaptureAttributionResult:
    """
    Activity attribution for pipeline
    Filters/selects domain CRM associations based on pipeline attribution logic
    """
    logger.bind(
        associations=[association.id for association in input_data.associations],
    ).info("pipeline attribution activity started")

    try:
        # Initialize database engine
        db_engine = await get_or_init_db_engine()

        # Get services - import here to avoid circular import
        from salestech_be.core.domain_crm_association.domain_crm_association_service import (
            get_domain_crm_association_service,
        )

        domain_association_service = get_domain_crm_association_service(db_engine)

        # Only work with associations that have pipeline_id set
        valid_associations = [
            a for a in input_data.associations if a.pipeline_id is not None
        ]

        if not valid_associations:
            logger.info("No valid associations with pipeline_id found")
            return DomainActivityCaptureAttributionResult(
                success=True,
                associations=input_data.associations,
            )

        # Select first association and mark others as not selected
        selected_updates, not_selected_updates = select_first_association(
            valid_associations, "pipeline"
        )

        # Add user_id to all updates
        selected_updates = [
            (assoc_id, update.copy(update={"updated_by_user_id": input_data.user_id}))
            for assoc_id, update in selected_updates
        ]
        not_selected_updates = [
            (assoc_id, update.copy(update={"updated_by_user_id": input_data.user_id}))
            for assoc_id, update in not_selected_updates
        ]

        # Combine all updates into one list for bulk processing
        all_updates = selected_updates + not_selected_updates

        # Perform bulk update
        updated_associations = (
            await domain_association_service.bulk_update_domain_crm_associations(
                all_updates
            )
        )

        # Return all associations (both selected and not selected)
        return DomainActivityCaptureAttributionResult(
            success=True,
            associations=updated_associations if updated_associations else [],
        )
    except Exception as e:
        logger.bind(
            association_ids=[
                str(association.id) for association in input_data.associations
            ],
            error=e,
        ).error("Failed to attribute domain activity for pipeline")
        return DomainActivityCaptureAttributionResult(
            success=False,
            associations=input_data.associations,
            error_message=str(e),
        )
