from datetime import timedelta

from temporalio import workflow
from temporalio.common import RetryPolicy

with workflow.unsafe.imports_passed_through():
    from salestech_be.integrations.temporal.config import DEFAULT_TASK_MAX_RETRY
    from salestech_be.ree_logging import get_logger
    from salestech_be.temporal.activities.domain_crm_association.activity_attribution import (
        BasicDomainCrmActivityInput,
        account_attribution_activity,
        account_capture_activity,
        contact_attribution_activity,
        phone_number_to_contact_capture_activity,
        pipeline_attribution_activity,
        pipeline_capture_activity,
    )

logger = get_logger(__name__)


@workflow.defn
class DomainCrmAssociationAttributionWorkflow:
    """Workflow for attributing activities to CRM entities."""

    RETRY_POLICY: RetryPolicy = RetryPolicy(
        initial_interval=timedelta(seconds=1),
        maximum_interval=timedelta(minutes=1),
        maximum_attempts=DEFAULT_TASK_MAX_RETRY,
        non_retryable_error_types=[
            "ValueError",
            "ResourceNotFoundError",
            "IllegalStateError",
            "WorkflowFailError",
        ],
    )

    DEFAULT_ACTIVITY_TIMEOUT: timedelta = timedelta(seconds=60)

    @workflow.run
    async def run(
        self,
        data: BasicDomainCrmActivityInput,
    ) -> bool:
        """
        Run the workflow to attribute an activity to CRM entities.

        Args:
            data: The input data for the workflow
                activity_id: The ID of the activity to attribute
                organization_id: The organization ID
                user_id: The user ID who created the activity
                contact_ids: Optional list of contact IDs to attribute
                account_ids: Optional list of account IDs to attribute

        Returns:
            bool: Whether the attribution was successful
        """
        workflow.logger.info("Starting activity attribution workflow for activity")
        try:
            phone_number_activity_capture_data = BasicDomainCrmActivityInput(
                organization_id=data.organization_id,
                user_id=data.user_id,
                associations=data.associations,
            )
            phone_number_activity_capture_result = await workflow.execute_activity(
                phone_number_to_contact_capture_activity,
                args=[phone_number_activity_capture_data],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=self.RETRY_POLICY,
            )

            contact_activity_attribution_data = BasicDomainCrmActivityInput(
                organization_id=data.organization_id,
                user_id=data.user_id,
                associations=phone_number_activity_capture_result.associations,
            )

            contact_activity_attribution_result = await workflow.execute_activity(
                contact_attribution_activity,
                args=[contact_activity_attribution_data],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=self.RETRY_POLICY,
            )

            account_activity_capture_data = BasicDomainCrmActivityInput(
                organization_id=data.organization_id,
                user_id=data.user_id,
                associations=contact_activity_attribution_result.associations,
            )

            account_activity_capture_result = await workflow.execute_activity(
                account_capture_activity,
                args=[account_activity_capture_data],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=self.RETRY_POLICY,
            )

            account_activity_attribution_data = BasicDomainCrmActivityInput(
                organization_id=data.organization_id,
                user_id=data.user_id,
                associations=account_activity_capture_result.associations,
            )

            account_activity_attribution_result = await workflow.execute_activity(
                account_attribution_activity,
                args=[account_activity_attribution_data],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=self.RETRY_POLICY,
            )

            pipeline_activity_capture_data = BasicDomainCrmActivityInput(
                organization_id=data.organization_id,
                user_id=data.user_id,
                associations=account_activity_attribution_result.associations,
            )

            pipeline_activity_capture_result = await workflow.execute_activity(
                pipeline_capture_activity,
                args=[pipeline_activity_capture_data],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=self.RETRY_POLICY,
            )

            pipeline_activity_attribution_data = BasicDomainCrmActivityInput(
                organization_id=data.organization_id,
                user_id=data.user_id,
                associations=pipeline_activity_capture_result.associations,
            )

            pipeline_activity_attribution_result = await workflow.execute_activity(
                pipeline_attribution_activity,
                args=[pipeline_activity_attribution_data],
                start_to_close_timeout=self.DEFAULT_ACTIVITY_TIMEOUT,
                retry_policy=self.RETRY_POLICY,
            )

            return pipeline_activity_attribution_result.success

        except Exception as e:
            workflow.logger.error(f"Failed to attribute activity: {e}")
            raise
