from temporalio import workflow
from temporalio.common import RetryPolicy
from temporalio.exceptions import TimeoutError as TemporalTimeoutError

with workflow.unsafe.imports_passed_through():
    from datetime import timedelta

    from salestech_be.core.sequence.type.sequence_activity import (
        CompleteEnrollmentWorkflowParams,
        EnrollmentResult,
        FailEnrollmentWorkflowParams,
        FetchSequenceEnrollmentParams,
        MoveToNextStepParams,
        ProcessSequenceStepParams,
        StartExecuteStepParams,
    )
    from salestech_be.core.workflow.types.schema import WorkflowExecutionResult
    from salestech_be.db.models.sequence import (
        SequenceEnrollmentStatus,
        SequenceStepType,
    )
    from salestech_be.db.models.workflow import WorkflowRunNodeStatus
    from salestech_be.temporal.activities.sequence.sequence_enrollment_activities import (
        complete_enrollment_workflow,
        fail_enrollment_workflow,
        fetch_sequence_enrollment_v2,
        move_to_next_step_v2,
        process_sequence_step_v2,
        start_execute_step_v2,
    )
    from salestech_be.temporal.workflows.sequence.sequence_enrollment_workflow import (
        SequenceEnrollmentWorkflowInput,
    )
    from salestech_be.util.validation import not_none


@workflow.defn
class SequenceEnrollmentWorkflowV2:
    RETRY_POLICY: RetryPolicy = RetryPolicy(
        maximum_interval=timedelta(minutes=1),
        non_retryable_error_types=[
            "ValueError",
            "ResourceNotFoundError",
            "IllegalStateError",
            "WorkflowFailError",
        ],
    )
    SEQUENCE_STEP_ACTIVITY_TIMEOUT: timedelta = timedelta(minutes=15)

    def __init__(self) -> None:
        self.is_paused = False
        self.is_cancelled = False
        self.is_task_completed = False

    @workflow.signal
    async def pause(self) -> None:
        """Signal to pause the sequence enrollment workflow."""
        self.is_paused = True

    @workflow.signal
    async def resume(self) -> None:
        """Signal to resume the sequence enrollment workflow."""
        self.is_paused = False

    @workflow.signal
    async def cancel(self) -> None:
        """Signal to cancel the sequence enrollment workflow."""
        self.is_cancelled = True

    @workflow.signal
    async def complete_task(self) -> None:
        """Signal to complete task that generated from the sequence enrollment workflow."""
        self.is_task_completed = True

    @workflow.run
    async def run(  # noqa: C901, PLR0912, PLR0911, PLR0915
        self,
        enrollment_input: SequenceEnrollmentWorkflowInput,
    ) -> WorkflowExecutionResult:
        try:
            enrollment = await workflow.execute_activity(
                fetch_sequence_enrollment_v2,
                args=(
                    FetchSequenceEnrollmentParams(
                        organization_id=enrollment_input.organization_id,
                        sequence_enrollment_id=enrollment_input.sequence_enrollment_id,
                    ),
                ),
                start_to_close_timeout=self.SEQUENCE_STEP_ACTIVITY_TIMEOUT,
                retry_policy=self.RETRY_POLICY,
            )

            if enrollment_input.is_paused_on_create:
                await self.pause()

            # Main loop through steps
            while (
                enrollment
                and enrollment.current_step_id
                and enrollment.status
                not in (
                    SequenceEnrollmentStatus.FAILED,
                    SequenceEnrollmentStatus.EXITED,
                    SequenceEnrollmentStatus.REMOVED,
                )
            ):
                if self.is_cancelled:
                    return self.cancel_enrollment(enrollment)

                if self.is_paused:
                    await workflow.wait_condition(
                        lambda: not self.is_paused or self.is_cancelled
                    )
                    if self.is_cancelled:
                        return self.cancel_enrollment(enrollment)
                    # Fetch the current enrollment after pausing
                    enrollment = await workflow.execute_activity(
                        fetch_sequence_enrollment_v2,
                        args=(
                            FetchSequenceEnrollmentParams(
                                organization_id=enrollment_input.organization_id,
                                sequence_enrollment_id=enrollment_input.sequence_enrollment_id,
                            ),
                        ),
                        start_to_close_timeout=self.SEQUENCE_STEP_ACTIVITY_TIMEOUT,
                        retry_policy=self.RETRY_POLICY,
                    )
                    if (
                        not enrollment
                        or not enrollment.current_step_id
                        or enrollment.status
                        in (
                            SequenceEnrollmentStatus.FAILED,
                            SequenceEnrollmentStatus.EXITED,
                            SequenceEnrollmentStatus.REMOVED,
                        )
                    ):
                        return self.end_enrollment(enrollment)

                # Start executing the new step
                is_start_execute_done = False
                backoff = 10  # seconds
                max_backoff = 600  # 10 minutes (600 seconds)
                max_attempts = 150  # Estimate for number of attempts in 1 day
                num_attempts = 0
                while not is_start_execute_done and num_attempts < max_attempts:
                    if self.is_paused:
                        # If we were paused during this loop to start the step, wait for resume and then start again
                        await workflow.wait_condition(lambda: not self.is_paused)
                        num_attempts = 0
                        continue

                    num_attempts += 1
                    is_start_execute_step_v2_timed_out = False
                    try:
                        execution_result = await workflow.execute_activity(
                            start_execute_step_v2,
                            args=(
                                StartExecuteStepParams(
                                    organization_id=enrollment_input.organization_id,
                                    sequence_enrollment_id=enrollment_input.sequence_enrollment_id,
                                ),
                            ),
                            start_to_close_timeout=timedelta(minutes=30),
                            retry_policy=self.RETRY_POLICY,
                        )
                    except TemporalTimeoutError as e:
                        is_start_execute_step_v2_timed_out = True
                        workflow.logger.info(
                            f"Error invoking start_execute_step_v2 activity: {e}"
                        )

                    if is_start_execute_step_v2_timed_out or (
                        execution_result.scheduled_to_execute_at is None
                        and execution_result.step.type
                        in [SequenceStepType.AUTO_EMAIL, SequenceStepType.MANUAL_EMAIL]
                    ):
                        # We tried to schedule the email but failed.  We need to wait for a while and try again.
                        # Due to concurrency controls for mailbox slots, we may end up waiting a while and trying many times.
                        jitter = workflow.random().uniform(-0.2, 0.2)
                        sleep_time = backoff * (1 + jitter)
                        await workflow.sleep(sleep_time)
                        backoff = min(backoff * 2, max_backoff)
                        continue
                    # Other step types, or we got scheduled_to_execute_at: bail out of while loop
                    is_start_execute_done = True

                if (
                    not execution_result.enrollment
                    or not execution_result.enrollment.current_step_id
                    or execution_result.enrollment.status
                    in (
                        SequenceEnrollmentStatus.FAILED,
                        SequenceEnrollmentStatus.EXITED,
                        SequenceEnrollmentStatus.REMOVED,
                    )
                ):
                    return self.end_enrollment(execution_result.enrollment)
                if execution_result.scheduled_to_execute_at is None:
                    workflow.logger.info(
                        f"Skip the step execution because the step is not scheduled to execute. step: {execution_result.step.id}"
                    )
                    enrollment = await workflow.execute_activity(
                        move_to_next_step_v2,
                        args=(
                            MoveToNextStepParams(
                                organization_id=enrollment_input.organization_id,
                                sequence_enrollment_id=enrollment.id,
                                next_step_id=execution_result.step.next_step_id,
                            ),
                        ),
                        start_to_close_timeout=self.SEQUENCE_STEP_ACTIVITY_TIMEOUT,
                        retry_policy=self.RETRY_POLICY,
                    )
                    continue

                scheduled_to_execute_at = not_none(
                    execution_result.scheduled_to_execute_at
                )
                delay_seconds = (
                    (scheduled_to_execute_at - workflow.now()).total_seconds()
                    if scheduled_to_execute_at > workflow.now()
                    else 0
                )
                await workflow.sleep(delay_seconds)

                # no available variant scenario - move to next step after delay
                if not execution_result.step_execution:
                    workflow.logger.info(
                        "Skip the step execution because no variant is available."
                    )
                    enrollment = await workflow.execute_activity(
                        move_to_next_step_v2,
                        args=(
                            MoveToNextStepParams(
                                organization_id=enrollment_input.organization_id,
                                sequence_enrollment_id=enrollment.id,
                                next_step_id=execution_result.step.next_step_id,
                            ),
                        ),
                        start_to_close_timeout=self.SEQUENCE_STEP_ACTIVITY_TIMEOUT,
                        retry_policy=self.RETRY_POLICY,
                    )
                    continue
                if self.is_cancelled:
                    return self.cancel_enrollment(enrollment)
                if self.is_paused:
                    await workflow.wait_condition(
                        lambda: not self.is_paused or self.is_cancelled
                    )
                    if self.is_cancelled:
                        return self.cancel_enrollment(enrollment)
                    enrollment = await workflow.execute_activity(
                        fetch_sequence_enrollment_v2,
                        args=(
                            FetchSequenceEnrollmentParams(
                                organization_id=enrollment_input.organization_id,
                                sequence_enrollment_id=enrollment_input.sequence_enrollment_id,
                            ),
                        ),
                        start_to_close_timeout=self.SEQUENCE_STEP_ACTIVITY_TIMEOUT,
                        retry_policy=self.RETRY_POLICY,
                    )
                    if (
                        not enrollment
                        or not enrollment.current_step_id
                        or enrollment.status
                        in (
                            SequenceEnrollmentStatus.FAILED,
                            SequenceEnrollmentStatus.EXITED,
                            SequenceEnrollmentStatus.REMOVED,
                        )
                    ):
                        return self.end_enrollment(enrollment)
                    # When resuming, we'll start the current step over
                    # by continuing to the new iteration of the loop
                    # without executing the step
                    continue
                if (
                    not enrollment
                    or not enrollment.current_step_id
                    or enrollment.status
                    in (
                        SequenceEnrollmentStatus.FAILED,
                        SequenceEnrollmentStatus.EXITED,
                        SequenceEnrollmentStatus.REMOVED,
                    )
                ):
                    return self.end_enrollment(enrollment)

                enrollment = await workflow.execute_activity(
                    process_sequence_step_v2,
                    args=(
                        ProcessSequenceStepParams(
                            step_type=execution_result.step.type,
                            organization_id=enrollment_input.organization_id,
                            step_execution_id=execution_result.step_execution,
                            sequence_enrollment_id=enrollment.id,
                            next_step_id=execution_result.step.next_step_id,
                        ),
                    ),
                    start_to_close_timeout=self.SEQUENCE_STEP_ACTIVITY_TIMEOUT,
                    retry_policy=self.RETRY_POLICY,
                )

                if (
                    enrollment
                    and execution_result.step.type
                    in SequenceStepType.get_manual_task_types()
                ):
                    await self.pause()
                    await workflow.wait_condition(
                        lambda: self.is_task_completed or self.is_cancelled
                    )
                    self.is_task_completed = False
                    enrollment = await workflow.execute_activity(
                        fetch_sequence_enrollment_v2,
                        args=(
                            FetchSequenceEnrollmentParams(
                                organization_id=enrollment_input.organization_id,
                                sequence_enrollment_id=enrollment_input.sequence_enrollment_id,
                            ),
                        ),
                        start_to_close_timeout=self.SEQUENCE_STEP_ACTIVITY_TIMEOUT,
                        retry_policy=self.RETRY_POLICY,
                    )
                    if (
                        not enrollment
                        or not enrollment.current_step_id
                        or enrollment.status
                        in (
                            SequenceEnrollmentStatus.FAILED,
                            SequenceEnrollmentStatus.EXITED,
                            SequenceEnrollmentStatus.REMOVED,
                        )
                    ):
                        return self.end_enrollment(enrollment)
                    workflow.logger.info(
                        f"Waiting for manual email step to be completed. step: {execution_result.step.id}"
                    )
                    if enrollment.current_step_id == execution_result.step.id:
                        enrollment = await workflow.execute_activity(
                            move_to_next_step_v2,
                            args=(
                                MoveToNextStepParams(
                                    organization_id=enrollment_input.organization_id,
                                    sequence_enrollment_id=enrollment.id,
                                    next_step_id=execution_result.step.next_step_id,
                                ),
                            ),
                            start_to_close_timeout=self.SEQUENCE_STEP_ACTIVITY_TIMEOUT,
                            retry_policy=self.RETRY_POLICY,
                        )

            if enrollment is None:
                workflow.logger.info(
                    f"Quit the sequence enrollment workflow because the enrollment is not found or not active. enrollment_id: {enrollment_input.sequence_enrollment_id}"
                )
                return WorkflowExecutionResult(
                    status=WorkflowRunNodeStatus.SUCCESS,
                    output={"enrollment_id": enrollment_input.sequence_enrollment_id},
                    message="Quit the sequence enrollment workflow because the enrollment is not found or not active",
                )
            if enrollment.status not in (
                SequenceEnrollmentStatus.REMOVED,
                SequenceEnrollmentStatus.FAILED,
            ):
                await workflow.execute_activity(
                    complete_enrollment_workflow,
                    args=(
                        CompleteEnrollmentWorkflowParams(
                            organization_id=enrollment.organization_id,
                            sequence_enrollment_id=enrollment.id,
                        ),
                    ),
                    start_to_close_timeout=self.SEQUENCE_STEP_ACTIVITY_TIMEOUT,
                )
                workflow.logger.info(
                    f"Sequence enrollment workflow is exited. enrollment_id: {enrollment.id}"
                )
                return WorkflowExecutionResult(
                    status=WorkflowRunNodeStatus.SUCCESS,
                    output={"enrollment_id": enrollment.id},
                    message="Sequence enrollment workflow is successfully completed all steps.",
                )

            return WorkflowExecutionResult(
                status=WorkflowRunNodeStatus.FAIL,
                output={"enrollment_id": enrollment.id},
                message="Sequence enrollment completed with issue.",
            )
        except Exception as e:
            await workflow.execute_activity(
                fail_enrollment_workflow,
                args=(
                    FailEnrollmentWorkflowParams(
                        organization_id=enrollment_input.organization_id,
                        sequence_enrollment_id=enrollment_input.sequence_enrollment_id,
                    ),
                ),
                start_to_close_timeout=self.SEQUENCE_STEP_ACTIVITY_TIMEOUT,
            )
            raise e

    def cancel_enrollment(
        self, enrollment: EnrollmentResult | None
    ) -> WorkflowExecutionResult:
        enrollment_details = {}
        message = "Sequence enrollment workflow is cancelled by external signal"

        if enrollment:
            enrollment_details = {
                "enrollment_id": enrollment.id,
                "status": enrollment.status,
                "current_step_id": enrollment.current_step_id,
            }
        workflow.logger.info(f"{message} {enrollment_details}")

        return WorkflowExecutionResult(
            status=WorkflowRunNodeStatus.SUCCESS,
            output=enrollment_details,
            message="Sequence enrollment workflow is cancelled by external signal.",
        )

    def end_enrollment(
        self, enrollment: EnrollmentResult | None
    ) -> WorkflowExecutionResult:
        enrollment_details = {}
        message = "Sequence enrollment workflow is ended."

        if enrollment:
            enrollment_details = {
                "enrollment_id": enrollment.id,
                "status": enrollment.status,
                "current_step_id": enrollment.current_step_id,
            }

        workflow.logger.info(f"{message} {enrollment_details}")

        return WorkflowExecutionResult(
            status=WorkflowRunNodeStatus.FAIL,
            output=enrollment_details,
            message="Sequence enrollment workflow is ended",
        )
