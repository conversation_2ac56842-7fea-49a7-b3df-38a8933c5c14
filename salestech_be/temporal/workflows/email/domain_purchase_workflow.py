from temporalio import workflow
from temporalio.common import RetryPolicy

with workflow.unsafe.imports_passed_through():
    from datetime import timedelta

    from salestech_be.core.email.outbound_domain.types_v2 import (
        DomainPurchaseWorkflowInput,
    )
    from salestech_be.integrations.temporal.config import DEFAULT_TASK_MAX_RETRY
    from salestech_be.temporal.activities.email.domain_purchase_activity import (
        get_pending_email_accounts_by_domain_id,
        perform_domain_health_checks,
        purchase_domains,
        signal_email_account_create_workflows,
    )


@workflow.defn
class DomainPurchaseWorkflow:
    PURCHASE_DOMAINS_ACTIVITY_TIMEOUT: timedelta = timedelta(minutes=10)
    GET_EMAIL_ACCOUNTS_TIMEOUT: timedelta = timedelta(minutes=5)
    SIGNAL_DOMAIN_ACTIVE_TIMEOUT: timedelta = timedelta(minutes=5)
    RETRY_POLICY: RetryPolicy = RetryPolicy(
        maximum_interval=timedelta(minutes=1),
        maximum_attempts=DEFAULT_TASK_MAX_RETRY,
        non_retryable_error_types=[
            "NotImplementedError",
            "ResourceNotFoundError",
            "IllegalStateError",
            "ValueError",
        ],
    )

    @workflow.run
    async def run(
        self,
        domain_purchase_input: DomainPurchaseWorkflowInput,
    ) -> None:
        purchased_domain = await workflow.execute_activity(
            purchase_domains,
            args=(domain_purchase_input,),
            start_to_close_timeout=self.PURCHASE_DOMAINS_ACTIVITY_TIMEOUT,
            retry_policy=self.RETRY_POLICY,
        )
        if purchased_domain:
            # Find email accounts using this domain
            email_accounts = await workflow.execute_activity(
                get_pending_email_accounts_by_domain_id,
                args=(purchased_domain.id, domain_purchase_input.organization_id),
                start_to_close_timeout=self.GET_EMAIL_ACCOUNTS_TIMEOUT,
                retry_policy=self.RETRY_POLICY,
            )

            # signal email account create workflows
            await workflow.execute_activity(
                signal_email_account_create_workflows,
                args=(email_accounts,),
                start_to_close_timeout=self.SIGNAL_DOMAIN_ACTIVE_TIMEOUT,
                retry_policy=self.RETRY_POLICY,
            )
            # sleep for 5 minutes for domain to be propagated
            await workflow.sleep(timedelta(minutes=5))
            await workflow.execute_activity(
                perform_domain_health_checks,
                args=(purchased_domain,),
                start_to_close_timeout=self.PURCHASE_DOMAINS_ACTIVITY_TIMEOUT,
                retry_policy=self.RETRY_POLICY,
            )
