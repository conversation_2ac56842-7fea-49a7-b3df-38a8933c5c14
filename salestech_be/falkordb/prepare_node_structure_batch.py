"""
Module for preparing batch data for complex node structures with schema awareness.

This module adapts the logic from create_complex_node_structure to generate
lists of node and relationship data suitable for batch processing with UNWIND,
instead of executing individual Cypher queries.
"""

import datetime
import uuid
from enum import Enum
from typing import Any, TypedDict

from salestech_be.common.type.metadata.common import (
    CustomFieldIdentifier,
    CustomObjectIdentifier,
    StandardObjectIdentifier,
)
from salestech_be.common.type.metadata.schema import (
    ObjectDescriptor,
)
from salestech_be.falkordb.schema_registry import (
    FieldInstruction,
    SchemaRegistry,
)
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


# Define expected structure for node and relationship data used in batch updates
class NodeTypeData(TypedDict):
    label: str
    id: str
    properties: dict[str, Any]


class RelationshipTypeData(TypedDict):
    from_label: str
    from_id: str
    to_label: str
    to_id: str
    type: str
    from_id_field_name: str | None
    to_id_field_name: str | None
    properties: dict[str, Any]


# Helper function to convert values to FalkorDB compatible types
def _convert_param_value(value: Any) -> Any:  # noqa: PLR0911
    if isinstance(value, uuid.UUID):
        return str(value)
    if isinstance(value, datetime.datetime):
        # Convert datetime to epoch milliseconds (integer)
        return int(value.timestamp() * 1000)
    if isinstance(value, Enum):
        # Convert enums to their string values
        return str(value.value)
    if isinstance(value, list):
        # Recursively convert items in lists
        return [_convert_param_value(item) for item in value]
    if isinstance(value, dict):
        # Recursively convert values in dictionaries
        return {k: _convert_param_value(v) for k, v in value.items()}
    # Basic types (str, int, float, bool) are generally fine
    # Return other types as string as a fallback
    if not isinstance(value, (str, int, float, bool)) and value is not None:
        return str(value)
    return value


# Helper function to prepare node data dictionary
def _prepare_node_data(
    label: str,
    node_id: str,
    properties: dict[str, Any],
) -> NodeTypeData:
    # Ensure ID is in properties and is a string
    sanitized_props = properties.copy()  # Create a copy first
    sanitized_props["id"] = str(node_id)

    # Sanitize all property values
    for key, value in sanitized_props.items():
        sanitized_props[key] = _convert_param_value(value)

    return NodeTypeData(
        label=label,
        id=str(node_id),
        properties=sanitized_props,
    )


# Helper function to prepare relationship data dictionary
def _prepare_relationship_data(
    from_label: str,
    from_id: str,
    to_label: str,
    to_id: str,
    relationship_type: str,
    properties: dict[str, Any] | None = None,
    from_id_field_name: str = "id",
    to_id_field_name: str = "id",
) -> RelationshipTypeData:
    # Default to empty dict if properties are None
    rel_props = properties or {}

    # Sanitize all property values
    sanitized_props = {k: _convert_param_value(v) for k, v in rel_props.items()}

    return RelationshipTypeData(
        from_label=from_label,
        from_id=str(from_id),
        to_label=to_label,
        to_id=str(to_id),
        type=relationship_type,
        properties=sanitized_props,
        from_id_field_name=from_id_field_name,
        to_id_field_name=to_id_field_name,
    )


def _map_data_using_field_plan(  # noqa: C901, PLR0912, PLR0915
    node_type: str,  # Label of the current node being processed
    node_id: str,  # ID of the current node being processed
    data: dict[str, Any],  # Raw data for this single node
    plan: list[FieldInstruction],  # The pre-computed plan for this node_type
    current_obj_descriptor: ObjectDescriptor,  # Pass the descriptor for the current node_type
    registry: SchemaRegistry,
) -> tuple[dict[str, Any], list[NodeTypeData], list[RelationshipTypeData]]:
    """
    Maps data for a single node based on a pre-computed field processing plan.
    Handles primitive properties, nested nodes, and relationships.
    """
    primitive_props: dict[str, Any] = {}
    item_nested_nodes: list[NodeTypeData] = []
    item_nested_rels: list[RelationshipTypeData] = []

    if not isinstance(data, dict):
        logger.warning(
            f"Input data for {node_type}[{node_id}] is not a dict ({type(data)}). Skipping mapping."
        )
        return primitive_props, item_nested_nodes, item_nested_rels

    for instruction in plan:
        field_name = instruction["field_name"]
        field_id_for_prop = instruction["field_id_for_prop"]
        field_descriptor = instruction["field_descriptor"]
        identifier_type = instruction["identifier_type"]
        relationship_type = instruction["relationship_type"]

        if identifier_type == "unknown":
            continue

        field_value = None
        if identifier_type == "custom":
            custom_field_map = None
            if isinstance(
                current_obj_descriptor.object_identifier, StandardObjectIdentifier
            ):
                custom_field_map = data.get("custom_field_data")
            elif isinstance(
                current_obj_descriptor.object_identifier, CustomObjectIdentifier
            ):
                custom_field_map = data.get("field_values")
            else:
                # This path should ideally not be hit if current_obj_descriptor is always valid
                logger.error(
                    f"Unknown object identifier type in current_obj_descriptor for {node_type}[{node_id}]. Cannot determine custom field source."
                )
                # Continue to next instruction, as field_value will remain None
                continue

            if isinstance(custom_field_map, dict):
                # If identifier_type is "custom", then field_descriptor.field_identifier MUST be CustomFieldIdentifier.
                # And CustomFieldIdentifier has .field_id
                if isinstance(field_descriptor.field_identifier, CustomFieldIdentifier):
                    field_uuid = field_descriptor.field_identifier.field_id
                    # Use current_obj_descriptor directly
                    if isinstance(
                        current_obj_descriptor.object_identifier,
                        StandardObjectIdentifier,
                    ):
                        field_value = custom_field_map.get(field_uuid)
                    elif isinstance(
                        current_obj_descriptor.object_identifier, CustomObjectIdentifier
                    ):
                        field_value = custom_field_map.get(str(field_uuid))
                else:
                    # This case should not be reached if plan generation is correct
                    logger.error(
                        f"Type mismatch: field '{field_name}' is custom but its identifier in plan is not CustomFieldIdentifier for {node_type}[{node_id}]."
                    )
            else:
                logger.warning(
                    f"Expected dict for custom field data source for '{field_name}' in {node_type}[{node_id}], got {type(custom_field_map)}."
                )
        elif identifier_type == "standard":
            field_value = data.get(field_name)

        if field_value is None:
            continue

        if relationship_type == "domain_relationship":
            continue

        if relationship_type == "unknown_rel_type":
            primitive_props[field_id_for_prop] = field_value  # Fallback
            continue

        if relationship_type == "nested_structure":
            is_collection = instruction["is_collection"]
            nested_target_type = instruction["nested_target_type"]
            # nested_desc_for_field = instruction["nested_descriptor_for_field"]

            if not nested_target_type:  # If target type couldn't be determined in plan
                logger.warning(
                    f"Field '{field_name}' for {node_type}[{node_id}] is nested, but target type is missing in plan. Storing as primitive."
                )
                primitive_props[field_id_for_prop] = field_value
                continue

            try:
                if is_collection:
                    if not isinstance(field_value, list):
                        primitive_props[field_id_for_prop] = field_value
                        continue

                    for i, item_in_collection in enumerate(field_value):
                        if not isinstance(item_in_collection, dict):
                            logger.warning(
                                f"Expected dict item in collection '{field_name}'[{i}] for {node_type}[{node_id}], got {type(item_in_collection)}. Skipping."
                            )
                            continue
                        item_coll_node_id = item_in_collection.get(
                            "id", f"{node_id}_{field_name}_{i}"
                        )

                        # Recursive call for collection item
                        nested_nodes, nested_rels = prepare_node_structure_batch(
                            node_type=nested_target_type,
                            node_id=str(item_coll_node_id),
                            data=item_in_collection,
                            schema_registry=registry,  # Pass the main registry
                        )
                        item_nested_nodes.extend(nested_nodes)
                        item_nested_rels.extend(nested_rels)
                        item_nested_rels.append(
                            _prepare_relationship_data(
                                from_label=node_type,
                                from_id=node_id,
                                to_label=nested_target_type,
                                to_id=str(item_coll_node_id),
                                relationship_type=field_name,  # Use original field name for rel type
                            )
                        )
                else:  # Single nested object
                    if not isinstance(field_value, dict):
                        logger.warning(
                            f"Expected dict for single nested field '{field_name}' in {node_type}[{node_id}], got {type(field_value)}. Storing as primitive."
                        )
                        primitive_props[field_id_for_prop] = field_value
                        continue

                    sub_node_id = field_value.get("id", f"{node_id}_{field_name}")

                    # Recursive call for single nested object
                    nested_nodes, nested_rels = prepare_node_structure_batch(
                        node_type=nested_target_type,
                        node_id=str(sub_node_id),
                        data=field_value,
                        schema_registry=registry,  # Pass the main registry
                    )
                    item_nested_nodes.extend(nested_nodes)
                    item_nested_rels.extend(nested_rels)
                    item_nested_rels.append(
                        _prepare_relationship_data(
                            from_label=node_type,
                            from_id=node_id,
                            from_id_field_name="id",
                            to_label=nested_target_type,
                            to_id=str(sub_node_id),
                            to_id_field_name="id",
                            relationship_type=field_name,  # Use original field name for rel type
                        )
                    )
            except Exception as e:
                logger.error(
                    f"Error processing nested field '{field_name}' for {node_type}[{node_id}] using plan: {e}",
                    exc_info=True,
                )
                primitive_props[field_id_for_prop] = (
                    field_value  # Fallback to primitive
                )

        elif relationship_type == "primitive":
            primitive_props[field_id_for_prop] = field_value
        else:
            primitive_props[field_id_for_prop] = field_value

    return primitive_props, item_nested_nodes, item_nested_rels


# Function to process fields based on schema
def _process_fields_by_schema_for_batch(
    node_type: str,
    node_id: str,
    data: dict[str, Any],
    obj_descriptor: ObjectDescriptor,
    registry: SchemaRegistry,
) -> tuple[dict[str, Any], list[NodeTypeData], list[RelationshipTypeData]]:
    """
    Processes fields for a given node based on its schema descriptor.
    Separates primitive properties, nested nodes, and relationships.
    Uses a processing plan obtained from the SchemaRegistry.

    Args:
        node_type: The label/type of the node.
        node_id: The ID of the node.
        data: The raw data dictionary for the node.
        obj_descriptor: The specific ObjectDescriptor for this node_type.
        registry: The SchemaRegistry instance.

    Returns:
        A tuple containing:
        - primitive_props: Dictionary of processed primitive properties.
        - all_nested_nodes: List of NodeTypeData for nested nodes to be created.
        - all_nested_rels: List of RelationshipTypeData for relationships to be created.
    """

    object_name_for_plan = registry.get_object_name(obj_descriptor)
    if not object_name_for_plan:
        logger.error(
            f"Could not determine object name for descriptor to get processing plan for {node_type}[{node_id}]. Bailing out."
        )
        return {}, [], []

    field_processing_plan = registry.get_field_processing_plan(object_name_for_plan)

    if (
        field_processing_plan is None
    ):  # Plan could be None if object_name not found, or [] if valid but no fields
        logger.warning(
            f"Could not retrieve field processing plan for {object_name_for_plan} ({node_type}[{node_id}]). Processing will be limited or skipped."
        )
        # If plan is None (meaning object not found by name perhaps), return empty.
        # If plan is [], means valid object with no processable fields (e.g. only domain rels), which is fine.
        return {}, [], []

    if (
        not field_processing_plan
    ):  # Explicitly check for empty list too, as per comment above
        logger.info(
            f"Field processing plan for {object_name_for_plan} ({node_type}[{node_id}]) is empty. No fields to map based on plan."
        )
        # Return empty props, nodes, rels, as no fields will be mapped by the plan.
        # The root node itself will still be created by the caller with these empty props.
        return {}, [], []

    primitive_props, all_nested_nodes, all_nested_rels = _map_data_using_field_plan(
        node_type, node_id, data, field_processing_plan, obj_descriptor, registry
    )

    return primitive_props, all_nested_nodes, all_nested_rels


# Function to process fields for a list of items based on schema (Refactored)
def _process_fields_for_list_by_schema_for_batch(
    node_type: str,
    items_data: list[tuple[str, dict[str, Any]]],  # List of (node_id, data_dict)
    obj_descriptor: ObjectDescriptor,
    registry: SchemaRegistry,
) -> tuple[
    list[tuple[str, dict[str, Any]]],
    list[NodeTypeData],
    list[RelationshipTypeData],
]:
    """
    Processes fields for a list of nodes of the same type based on their schema descriptor.
    Schema analysis is done once (plan retrieved from SchemaRegistry), then mapping is applied to all items.

    Args:
        node_type: The label/type of the nodes.
        items_data: A list of tuples, where each tuple contains (node_id, raw_data_dict) for an item.
        obj_descriptor: The specific ObjectDescriptor for this node_type (used to get the plan).
        registry: The SchemaRegistry instance.

    Returns:
        A tuple containing:
        - item_primitive_props_list: List of (node_id, primitive_props_dict) for each processed item.
        - all_nested_nodes: Aggregated list of NodeTypeData for nested nodes from all items.
        - all_nested_rels: Aggregated list of RelationshipTypeData for relationships from all items.
    """
    all_item_primitive_props_list: list[tuple[str, dict[str, Any]]] = []
    all_accumulated_nested_nodes: list[NodeTypeData] = []
    all_accumulated_nested_rels: list[RelationshipTypeData] = []

    if not items_data:
        return [], [], []

    object_name_for_plan = registry.get_object_name(obj_descriptor)
    if not object_name_for_plan:
        logger.error(
            f"Could not determine object name for descriptor to get processing plan for batch of {node_type}. Bailing out."
        )
        return [], [], []

    field_processing_plan = registry.get_field_processing_plan(object_name_for_plan)

    if field_processing_plan is None:  # Plan is None if object_name not in schema
        logger.warning(
            f"Could not retrieve field processing plan for {object_name_for_plan} (batch of {node_type}). Processing will be skipped for all items."
        )
        return [], [], []

    if not field_processing_plan:  # Plan is [] if object has no processable fields
        logger.info(
            f"Field processing plan for {object_name_for_plan} (batch of {node_type}) is empty. No fields to map for items."
        )
        # For each item, we'll effectively have no primitive props from field mapping
        for item_id, _ in items_data:
            all_item_primitive_props_list.append((item_id, {}))
        return all_item_primitive_props_list, [], []

    for item_id, item_data_dict in items_data:
        # Ensure item_data_dict is a dict, though _map_data_using_field_plan also checks
        if not isinstance(item_data_dict, dict):
            logger.warning(
                f"Item data for {node_type}[{item_id}] in batch is not a dict ({type(item_data_dict)}). Skipping this item."
            )
            all_item_primitive_props_list.append(
                (item_id, {})
            )  # Add empty props for this item
            continue

        primitive_props, item_nested_nodes, item_nested_rels = (
            _map_data_using_field_plan(
                node_type,
                item_id,
                item_data_dict,
                field_processing_plan,
                obj_descriptor,
                registry,
            )
        )
        all_item_primitive_props_list.append((item_id, primitive_props))
        all_accumulated_nested_nodes.extend(item_nested_nodes)
        all_accumulated_nested_rels.extend(item_nested_rels)

    return (
        all_item_primitive_props_list,
        all_accumulated_nested_nodes,
        all_accumulated_nested_rels,
    )


# --- Main Function ---
def prepare_node_structure_batch(
    node_type: str,
    node_id: str,
    data: dict[str, Any],
    schema_registry: SchemaRegistry,
) -> tuple[list[NodeTypeData], list[RelationshipTypeData]]:
    """
    Prepare batch data for a complex node structure based on a schema.

    Recursively processes nested structures defined in the schema, generating
    lists of node data and relationship data suitable for batch insertion/update
    using UNWIND. Domain relationships defined in the schema are ignored.

    Args:
        node_type: Type/label of the root node to process.
        node_id: ID of the root node.
        data: Data dictionary for the root node, potentially including nested data.
        schema_registry: The SchemaRegistry instance to guide processing.

    Returns:
        A tuple containing two lists:
        1. List of node data dictionaries (`NodeTypeData`).
        2. List of relationship data dictionaries (`RelationshipTypeData`).
    """
    # Log entry into the main function

    if not data or not isinstance(data, dict):  # Ensure data is a dict
        logger.warning(
            f"Input data for {node_type}[{node_id}] is empty or not a dict. Returning empty lists."
        )
        return [], []

    source_descriptor = schema_registry.get_object_descriptor(node_type)

    all_nodes_data: list[NodeTypeData] = []
    all_relationships_data: list[RelationshipTypeData] = []
    primitive_props: dict[str, Any] = {}

    if source_descriptor:
        # Use the refactored single-item processing function
        processed_props, nested_nodes, nested_rels = (
            _process_fields_by_schema_for_batch(
                node_type=node_type,
                node_id=node_id,
                data=data,
                obj_descriptor=source_descriptor,
                registry=schema_registry,
            )
        )
        primitive_props.update(processed_props)  # Ensure update, not assignment
        all_nodes_data.extend(nested_nodes)
        all_relationships_data.extend(nested_rels)
    else:
        logger.info(
            f"No schema descriptor found for type {node_type}. Node will be created with primitive properties only from top-level non-collection data (if any)."
        )
        # Fallback: attempt to store all top-level non-dict/list items as primitives
        primitive_props = {
            key: value
            for key, value in data.items()
            if not isinstance(value, (dict, list, tuple, set))
        }

    # Prepare the data for the root node itself, including its primitive properties
    root_node_data = _prepare_node_data(
        label=node_type,
        node_id=node_id,
        properties=primitive_props,
    )
    all_nodes_data.insert(0, root_node_data)  # Add root node data at the beginning

    return all_nodes_data, all_relationships_data
