from enum import StrEnum
from typing import Any

from pydantic import BaseModel, Field


class DebeziumOp(StrEnum):
    CREATE = "c"
    UPDATE = "u"
    DELETE = "d"
    READ = "r"
    TRUNCATE = "t"
    MESSAGE = "m"


class DebeziumSource(BaseModel):
    connector: str  # ex: postgresql
    name: str  # debezium.salestech_be
    version: str  # ex: 3.1.0.Final

    ts_us: int  # timestamp from database (in microseconds)
    ts_ns: int  # timestamp from database (in nanoseconds)

    db: str  # ex: reevo_main
    db_schema: str = Field(alias="schema")  # ex: public
    table: str  # ex: account

    snapshot: str  # ex: false | last | ???
    lsn: int  # ex: ***********


class DebeziumCDCEvent(BaseModel):
    source: DebeziumSource  # Source of the change (e.g., database name)
    op: DebeziumOp  # Operation type (insert, update, delete)
    before: dict[str, Any] | None  # State before the change (None for create)
    after: dict[str, Any] | None  # State after the change (None for delete)
    ts_ms: int  # timestamp from debezium (in milliseconds)
    ts_us: int  # timestamp from debezium (in microseconds)
    ts_ns: int  # timestamp from debezium (in nanoseconds)


JsonType = None | int | str | bool | list["JsonType"] | dict[str, "JsonType"]
Query = str
Params = dict[str, JsonType]
