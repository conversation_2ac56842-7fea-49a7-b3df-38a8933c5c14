from datetime import UTC, datetime
from uuid import UUID

from pydantic import BaseModel

from salestech_be.common.util import convert_to_compact_amount
from salestech_be.core.research_agent.models.company_activity import (
    CompanyResearchHeadcountActivity,
    CompanyResearchNewsActivity,
    CompanyResearchSocialActivity,
    CompanySiteContent,
)
from salestech_be.core.research_agent.models.company_info import (
    CompanyResearch,
    CompanyResearchInfo,
)
from salestech_be.core.research_agent.models.person_activity import (
    PersonResearchSocialActivity,
)
from salestech_be.core.research_agent.models.person_info import (
    EmployerInfo,
    PersonResearch,
    PersonResearchInfo,
)
from salestech_be.integrations.crustdata.model import (
    CrustdataCompanyResearchFundingActivity,
)

#######################
# Contact Research
#######################


class ContactSocial(BaseModel):
    content: str
    source: str | None
    url: str | None
    published_at: datetime | None
    media_urls: list[str] | None

    @classmethod
    def from_person_research_social_activity(
        cls, person_research_social_activity: PersonResearchSocialActivity
    ) -> "ContactSocial":
        return cls(
            content=person_research_social_activity.text or "",
            source=person_research_social_activity.info_source_repr,
            url=str(person_research_social_activity.feed_url)
            if person_research_social_activity.feed_url
            else None,
            published_at=person_research_social_activity.date_posted,
            media_urls=person_research_social_activity.hyperlinks.all_urls
            if person_research_social_activity.hyperlinks
            else None,
        )


class ContactExperience(BaseModel):
    title: str | None
    company_name: str
    company_linkedin_url: str | None
    start: datetime | None
    end: datetime | None

    @classmethod
    def from_employer_info(cls, employer_info: EmployerInfo) -> "ContactExperience":
        return cls(
            title=employer_info.employee_title,
            company_name=employer_info.employer_name,
            company_linkedin_url=str(employer_info.employer_linkedin_profile_url)
            if employer_info.employer_linkedin_profile_url
            else None,
            start=employer_info.start_date,
            end=employer_info.end_date,
        )


class ContactSnippet(BaseModel):
    first_name: str | None
    last_name: str | None
    full_name: str
    photo_url: str | None
    title: str | None
    current_company_name: str | None
    current_company_linkedin_url: str | None
    linkedin_url: str | None
    experience: list[ContactExperience] | None

    @classmethod
    def from_person_research_info(
        cls, person_research_info: PersonResearchInfo
    ) -> "ContactSnippet":
        return cls(
            first_name=person_research_info.first_name,
            last_name=person_research_info.last_name,
            full_name=person_research_info.name,
            photo_url=str(person_research_info.profile_picture_url)
            if person_research_info.profile_picture_url
            else None,
            title=person_research_info.title,
            current_company_name=person_research_info.current_employers[0].employer_name
            if person_research_info.current_employers
            else None,
            current_company_linkedin_url=str(
                person_research_info.current_employers[0].employer_linkedin_profile_url
            )
            if person_research_info.current_employers
            and person_research_info.current_employers[0].employer_linkedin_profile_url
            else None,
            linkedin_url=str(person_research_info.linkedin_profile_url)
            if person_research_info.linkedin_profile_url
            else None,
            experience=[
                ContactExperience.from_employer_info(employer_info)
                for employer_info in [
                    *(person_research_info.current_employers or []),
                    *(person_research_info.past_employers or []),
                ]
            ],
        )


class ContactResearchDto(BaseModel):
    id: UUID
    organization_id: UUID
    intel_person_id: UUID | None = None

    snippet: ContactSnippet
    socials: list[ContactSocial] | None

    is_feedback_positive: bool | None = None

    @classmethod
    def from_person_research(
        cls,
        contact_id: UUID,
        organization_id: UUID,
        person_research: PersonResearch,
        is_feedback_positive: bool | None = None,
    ) -> "ContactResearchDto":
        return cls(
            id=contact_id,
            organization_id=organization_id,
            intel_person_id=person_research.intel_person_id,
            snippet=ContactSnippet.from_person_research_info(person_research.info),
            socials=[
                ContactSocial.from_person_research_social_activity(social_activity)
                for social_activity in person_research.social_activities
            ],
            is_feedback_positive=is_feedback_positive,
        )


#######################
# Account Research
#######################
class AccountSnippet(BaseModel):
    about: str | None
    location: str | None
    company_name: str | None
    website: str | None
    linkedin_url: str | None
    linkedin_logo_url: str | None
    employee_range: str | None
    revenue_range: str | None
    industries: list[str] | None

    @classmethod
    def from_company_research_info(
        cls, company_research_info: CompanyResearchInfo
    ) -> "AccountSnippet":
        return cls(
            about=company_research_info.linkedin_company_description,
            location=company_research_info.headquarters
            or company_research_info.hq_country,
            company_name=company_research_info.company_name,
            website=company_research_info.company_website,
            linkedin_url=str(company_research_info.linkedin_profile_url)
            if company_research_info.linkedin_profile_url
            else None,
            linkedin_logo_url=str(company_research_info.linkedin_logo_url)
            if company_research_info.linkedin_logo_url
            else None,
            employee_range=company_research_info.employee_count_range,
            revenue_range=company_research_info.estimated_revenue_range,
            industries=company_research_info.taxonomy.linkedin_industries
            if company_research_info.taxonomy
            else None,
        )


class AccountNews(BaseModel):
    title: str | None
    summary: str | None
    source: str | None
    url: str
    published_at: datetime | None

    @classmethod
    def from_company_research_account_news_activity(
        cls, company_research_news_activity: CompanyResearchNewsActivity
    ) -> "AccountNews":
        return cls(
            title=company_research_news_activity.title,
            summary=company_research_news_activity.summary,
            source=company_research_news_activity.info_source_repr,
            url=company_research_news_activity.url,
            published_at=None
            if company_research_news_activity.published_date is None
            else datetime.fromtimestamp(
                company_research_news_activity.published_date,
                tz=UTC,
            ),
        )


class AccountSocial(BaseModel):
    content: str
    source: str | None
    url: str | None
    published_at: datetime | None
    media_urls: list[str] | None

    @classmethod
    def from_company_research_social_activity(
        cls, company_research_social_activity: CompanyResearchSocialActivity
    ) -> "AccountSocial":
        return cls(
            content=company_research_social_activity.text,
            source=company_research_social_activity.info_source_repr,
            url=str(company_research_social_activity.share_url)
            if company_research_social_activity.share_url
            else None,
            published_at=None
            if company_research_social_activity.date_posted is None
            else datetime.fromtimestamp(
                company_research_social_activity.date_posted,
                tz=UTC,
            ),
            media_urls=company_research_social_activity.hyperlinks.all_urls
            if company_research_social_activity.hyperlinks
            else None,
        )


class HeadCount(BaseModel):
    count: int
    date: datetime | None

    @classmethod
    def from_company_research_headcount_activity(
        cls, company_research_headcount_activity: CompanyResearchHeadcountActivity
    ) -> "HeadCount":
        return cls(
            count=company_research_headcount_activity.headcount,
            date=datetime.fromtimestamp(
                company_research_headcount_activity.date,
                tz=UTC,
            )
            if company_research_headcount_activity.date
            else None,
        )


class FundingRound(BaseModel):
    round: str | None
    amount: str | None
    currency: str | None
    date: datetime | None
    investors: list[str] | None

    @classmethod
    def from_company_research_funding_activity(
        cls, company_research_funding_activity: CrustdataCompanyResearchFundingActivity
    ) -> "FundingRound":
        return cls(
            round=company_research_funding_activity.funding_round,
            amount=convert_to_compact_amount(
                company_research_funding_activity.funding_milestone_amount_usd
            ),
            currency="USD"
            if company_research_funding_activity.funding_milestone_amount_usd
            else None,
            date=datetime.fromtimestamp(
                company_research_funding_activity.date,
                tz=UTC,
            )
            if company_research_funding_activity.date
            else None,
            investors=[company_research_funding_activity.funding_milestone_investors]
            if company_research_funding_activity.funding_milestone_investors
            else None,
        )


class AccountResearchDto(BaseModel):
    id: UUID
    organization_id: UUID
    intel_company_id: UUID | None = None

    snippet: AccountSnippet
    company_site_content: CompanySiteContent | None
    head_count_history: list[HeadCount]
    funding: list[FundingRound] | None
    news: list[AccountNews] | None
    socials: list[AccountSocial] | None

    is_feedback_positive: bool | None = None

    @classmethod
    def from_company_research(
        cls,
        account_id: UUID,
        organization_id: UUID,
        company_research: CompanyResearch,
        is_feedback_positive: bool | None = None,
    ) -> "AccountResearchDto":
        return cls(
            id=account_id,
            organization_id=organization_id,
            intel_company_id=company_research.intel_company_id,
            snippet=AccountSnippet.from_company_research_info(
                company_research_info=company_research.info,
            ),
            company_site_content=company_research.site_content,
            head_count_history=[
                HeadCount.from_company_research_headcount_activity(headcount_activity)
                for headcount_activity in company_research.headcount_activities
            ],
            funding=[
                FundingRound.from_company_research_funding_activity(funding_activity)
                for funding_activity in company_research.funding_activities
            ],
            news=[
                AccountNews.from_company_research_account_news_activity(news_activity)
                for news_activity in company_research.news_activities
            ],
            socials=[
                AccountSocial.from_company_research_social_activity(social_activity)
                for social_activity in company_research.social_activities
            ],
            is_feedback_positive=is_feedback_positive,
        )


class MeetingResearchDto(BaseModel):
    contact_researches: list[ContactResearchDto]
    account_researches: list[AccountResearchDto]
