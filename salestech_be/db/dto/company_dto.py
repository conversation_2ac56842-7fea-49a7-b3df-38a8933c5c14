from uuid import UUID

from pydantic import BaseModel

from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.common.types import Address
from salestech_be.db.models.company import Company
from salestech_be.db.models.core.types import CreatedSource
from salestech_be.util.pydantic_types.str import is_linkedin_domain


class CompanyDto(BaseModel):
    db_company: Company

    def to_create_account_request(self, owner_user_id: UUID) -> CreateAccountRequest:
        """
        Convert company data to CreateAccountRequest.

        Args:
            owner_user_id: The user ID who will own the account

        Returns:
            CreateAccountRequest: Account creation request with valid domain and website
        """
        # Get domain name - use primary_domain if it's not a LinkedIn URL, otherwise None
        domain_name = (
            self.db_company.primary_domain
            if self.db_company.primary_domain
            and not is_linkedin_domain(self.db_company.primary_domain)
            else None
        )

        # Get official website - use website_url if it's not a LinkedIn URL, otherwise None
        official_website = (
            self.db_company.website_url
            if self.db_company.website_url
            and not is_linkedin_domain(self.db_company.website_url)
            else None
        )

        # Build address if available
        address_data = None
        if self.db_company.address:
            address_data = Address(
                street_one=self.db_company.address,
                street_two=None,
                city=self.db_company.city,
                state=self.db_company.state,
                country=self.db_company.country,
                zip_code=self.db_company.postal_code,
            )

        return CreateAccountRequest(
            company_id=self.db_company.id,
            display_name=self.db_company.name,
            domain_name=domain_name,
            official_website=official_website,
            linkedin_url=self.db_company.linkedin_url,
            estimated_employee_count=self.db_company.estimated_num_employees,
            keyword_list=self.db_company.keywords,
            category_list=self.db_company.industries,
            address=address_data,
            owner_user_id=owner_user_id,
            created_source=CreatedSource.PROSPECTING_IMPORT,
        )
