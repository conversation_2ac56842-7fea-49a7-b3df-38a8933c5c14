from datetime import datetime
from uuid import UUID

from pydantic import BaseModel

from salestech_be.db.models.core.base import DBModel
from salestech_be.db.models.prospecting_run import ProspectingRun, ProspectingRunResult
from salestech_be.db.models.prospecting_tag import ProspectingFilterFieldType
from salestech_be.db.models.quota import QuotaConsumingResource
from salestech_be.db.models.search_query import ProspectingSearchQueryType
from salestech_be.util.enum_util import NameValueStrEnum


class PeopleEnrichmentSummaryDTO(DBModel):
    prospecting_run_id: UUID
    total_enrichment_requests_count: int
    successful_email_enrichments_count: int
    successful_phone_enrichments_count: int


class ProspectingRunDTO(BaseModel):
    db_run: ProspectingRun
    db_run_results: list[ProspectingRunResult] | None = None
    people_enrichment_summary: PeopleEnrichmentSummaryDTO | None = None


class ProspectingCreditType(NameValueStrEnum):
    email_enrichment = "email_enrichment"
    phone_enrichment = "phone_enrichment"

    @classmethod
    def to_quota_consuming_resource(
        cls,
    ) -> dict["ProspectingCreditType", QuotaConsumingResource]:
        return {
            cls.email_enrichment: QuotaConsumingResource.PROSPECTING_EMAIL_ENRICHMENT,
            cls.phone_enrichment: QuotaConsumingResource.PROSPECTING_PHONE_NUMBER_ENRICHMENT,
        }


class ProspectingCreditUsagePointDTO(BaseModel):
    date: datetime
    credits_used: int
    usage_type: ProspectingCreditType | None = None
    user_id: UUID | None = None
    user_name: str | None = None


BULK_ENRICH_MAX_SIZE = 50
BULK_IMPORT_PERSON_MAX_SIZE = 8000
BULK_IMPORT_COMPANY_MAX_SIZE = 8000
MAX_NUMBER_OF_ACCOUNT_IN_PEOPLE_SEARCH = 1000
EMAIL_ENRICH_CREDITS_PER_ENRICHMENT: int = 1
MOBILE_ENRICH_CREDITS_PER_ENRICHMENT: int = 3
PDL_SEARCH_CREDITS_PER_RECORD: int = 1

PROSPECTING_DEFAULT_MONTHLY_PEOPLE_SEARCH_QUOTA_LIMIT_PER_SEAT = 4800
PROSPECTING_DEFAULT_MONTHLY_PEOPLE_SEARCH_QUOTA_LIMIT_PER_ORG = 24000
PROSPECTING_DEFAULT_MONTHLY_COMPANY_SEARCH_QUOTA_LIMIT_PER_SEAT = 1600
PROSPECTING_DEFAULT_MONTHLY_COMPANY_SEARCH_QUOTA_LIMIT_PER_ORG = 8000
PROSPECTING_DEFAULT_MONTHLY_PEOPLE_ENRICHMENT_QUOTA_LIMIT_PER_SEAT = 1600
PROSPECTING_DEFAULT_MONTHLY_PEOPLE_ENRICHMENT_QUOTA_LIMIT_PER_ORG = 8000


class ContactSimpleInfo(BaseModel):
    id: UUID
    person_id: UUID
    primary_account_id: UUID | None = None
    primary_email: str | None = None


class AccountSimpleInfo(BaseModel):
    id: UUID
    company_id: UUID


class FilterFieldValue(BaseModel):
    display_name: str
    value: str
    count: int


# Person-specific filter field values

# NOTE: The location filter field has been renamed to "City" in UI
PERSON_LOCATION_FILTER_FIELD_VALUES = [
    FilterFieldValue(
        display_name="New York, New York, United States",
        value="New York, New York, United States",
        count=********,
    ),
    FilterFieldValue(
        display_name="Los Angeles, California, United States",
        value="Los Angeles, California, United States",
        count=9611162,
    ),
    FilterFieldValue(
        display_name="Chicago, Illinois, United States",
        value="Chicago, Illinois, United States",
        count=8822698,
    ),
    FilterFieldValue(
        display_name="Houston, Texas, United States",
        value="Houston, Texas, United States",
        count=8147340,
    ),
    FilterFieldValue(
        display_name="Phoenix, Arizona, United States",
        value="Phoenix, Arizona, United States",
        count=3890912,
    ),
    FilterFieldValue(
        display_name="Philadelphia, Pennsylvania, United States",
        value="Philadelphia, Pennsylvania, United States",
        count=5186587,
    ),
    FilterFieldValue(
        display_name="San Antonio, Texas, United States",
        value="San Antonio, Texas, United States",
        count=3516459,
    ),
    FilterFieldValue(
        display_name="San Diego, California, United States",
        value="San Diego, California, United States",
        count=3973825,
    ),
    FilterFieldValue(
        display_name="Dallas, Texas, United States",
        value="Dallas, Texas, United States",
        count=3981988,
    ),
    FilterFieldValue(
        display_name="San Jose, California, United States",
        value="San Jose, California, United States",
        count=2376241,
    ),
]


PERSON_TITLE_FILTER_FIELD_VALUES = [
    FilterFieldValue(
        display_name="Chief Executive Officer",
        value="Chief Executive Officer",
        count=3685791,
    ),
    FilterFieldValue(
        display_name="Chief Technology Officer",
        value="Chief Technology Officer",
        count=186066,
    ),
    FilterFieldValue(
        display_name="Chief Financial Officer",
        value="Chief Financial Officer",
        count=371542,
    ),
    FilterFieldValue(
        display_name="Vice President",
        value="Vice President",
        count=793050,
    ),
    FilterFieldValue(
        display_name="Director Of Engineering",
        value="Director Of Engineering",
        count=37776,
    ),
    FilterFieldValue(
        display_name="Software Engineer",
        value="Software Engineer",
        count=1723460,
    ),
    FilterFieldValue(
        display_name="Product Manager",
        value="Product Manager",
        count=356580,
    ),
    FilterFieldValue(
        display_name="Marketing Director",
        value="Marketing Director",
        count=175581,
    ),
    FilterFieldValue(
        display_name="Sales Manager",
        value="Sales Manager",
        count=1654481,
    ),
    FilterFieldValue(
        display_name="Founder",
        value="Founder",
        count=1408096,
    ),
]


PERSON_LINKEDIN_INDUSTRY_FILTER_FIELD_VALUES = [
    FilterFieldValue(display_name="Accounting", value="Accounting", count=9787071),
    FilterFieldValue(
        display_name="Airlines/Aviation", value="Airlines/Aviation", count=2555773
    ),
    FilterFieldValue(
        display_name="Alternative Dispute Resolution",
        value="Alternative Dispute Resolution",
        count=236849,
    ),
    FilterFieldValue(
        display_name="Alternative Medicine", value="Alternative Medicine", count=836120
    ),
    FilterFieldValue(display_name="Animation", value="Animation", count=888275),
    FilterFieldValue(
        display_name="Apparel & Fashion", value="Apparel & Fashion", count=2955367
    ),
    FilterFieldValue(
        display_name="Architecture & Planning",
        value="Architecture & Planning",
        count=3228441,
    ),
    FilterFieldValue(
        display_name="Arts And Crafts", value="Arts And Crafts", count=2549812
    ),
    FilterFieldValue(display_name="Automotive", value="Automotive", count=5756273),
    FilterFieldValue(
        display_name="Aviation & Aerospace", value="Aviation & Aerospace", count=1770355
    ),
    FilterFieldValue(display_name="Banking", value="Banking", count=8880332),
    FilterFieldValue(
        display_name="Biotechnology", value="Biotechnology", count=1642111
    ),
    FilterFieldValue(
        display_name="Broadcast Media", value="Broadcast Media", count=1541958
    ),
    FilterFieldValue(
        display_name="Building Materials", value="Building Materials", count=3842399
    ),
    FilterFieldValue(
        display_name="Business Supplies And Equipment",
        value="Business Supplies And Equipment",
        count=1544383,
    ),
    FilterFieldValue(
        display_name="Capital Markets", value="Capital Markets", count=531229
    ),
    FilterFieldValue(display_name="Chemicals", value="Chemicals", count=2867442),
    FilterFieldValue(
        display_name="Civic & Social Organization",
        value="Civic & Social Organization",
        count=1577050,
    ),
    FilterFieldValue(
        display_name="Civil Engineering", value="Civil Engineering", count=4486206
    ),
    FilterFieldValue(
        display_name="Commercial Real Estate",
        value="Commercial Real Estate",
        count=625231,
    ),
    FilterFieldValue(
        display_name="Computer & Network Security",
        value="Computer & Network Security",
        count=1152106,
    ),
    FilterFieldValue(
        display_name="Computer Games", value="Computer Games", count=751334
    ),
    FilterFieldValue(
        display_name="Computer Hardware", value="Computer Hardware", count=1281698
    ),
    FilterFieldValue(
        display_name="Computer Networking", value="Computer Networking", count=1342299
    ),
    FilterFieldValue(
        display_name="Computer Software", value="Computer Software", count=8439877
    ),
    FilterFieldValue(display_name="Construction", value="Construction", count=14595865),
    FilterFieldValue(
        display_name="Consumer Electronics", value="Consumer Electronics", count=1204834
    ),
    FilterFieldValue(
        display_name="Consumer Goods", value="Consumer Goods", count=4924632
    ),
    FilterFieldValue(
        display_name="Consumer Services", value="Consumer Services", count=4106303
    ),
    FilterFieldValue(display_name="Cosmetics", value="Cosmetics", count=1757676),
    FilterFieldValue(display_name="Dairy", value="Dairy", count=217932),
    FilterFieldValue(
        display_name="Defense & Space", value="Defense & Space", count=1370361
    ),
    FilterFieldValue(display_name="Design", value="Design", count=3508876),
    FilterFieldValue(display_name="E-Learning", value="E-Learning", count=1129055),
    FilterFieldValue(
        display_name="Education Management",
        value="Education Management",
        count=10388043,
    ),
    FilterFieldValue(
        display_name="Electrical/Electronic Manufacturing",
        value="Electrical/Electronic Manufacturing",
        count=4460503,
    ),
    FilterFieldValue(
        display_name="Entertainment", value="Entertainment", count=2769291
    ),
    FilterFieldValue(
        display_name="Environmental Services",
        value="Environmental Services",
        count=2580662,
    ),
    FilterFieldValue(
        display_name="Events Services", value="Events Services", count=1697102
    ),
    FilterFieldValue(
        display_name="Executive Office", value="Executive Office", count=918709
    ),
    FilterFieldValue(
        display_name="Facilities Services", value="Facilities Services", count=3363046
    ),
    FilterFieldValue(display_name="Farming", value="Farming", count=2683104),
    FilterFieldValue(
        display_name="Financial Services", value="Financial Services", count=12137878
    ),
    FilterFieldValue(display_name="Fine Art", value="Fine Art", count=1180023),
    FilterFieldValue(display_name="Fishery", value="Fishery", count=244712),
    FilterFieldValue(
        display_name="Food & Beverages", value="Food & Beverages", count=80514
    ),
    FilterFieldValue(
        display_name="Food Production", value="Food Production", count=9157823
    ),
    FilterFieldValue(display_name="Fund-Raising", value="Fund-Raising", count=261298),
    FilterFieldValue(display_name="Furniture", value="Furniture", count=1203644),
    FilterFieldValue(
        display_name="Gambling & Casinos", value="Gambling & Casinos", count=424214
    ),
    FilterFieldValue(
        display_name="Glass, Ceramics & Concrete",
        value="Glass, Ceramics & Concrete",
        count=320008,
    ),
    FilterFieldValue(
        display_name="Government Administration",
        value="Government Administration",
        count=9409326,
    ),
    FilterFieldValue(
        display_name="Government Relations", value="Government Relations", count=935736
    ),
    FilterFieldValue(
        display_name="Graphic Design", value="Graphic Design", count=1944505
    ),
    FilterFieldValue(
        display_name="Health, Wellness And Fitness",
        value="Health, Wellness And Fitness",
        count=6968290,
    ),
    FilterFieldValue(
        display_name="Higher Education", value="Higher Education", count=10747412
    ),
    FilterFieldValue(
        display_name="Hospital & Health Care",
        value="Hospital & Health Care",
        count=17948605,
    ),
    FilterFieldValue(display_name="Hospitality", value="Hospitality", count=6286975),
    FilterFieldValue(
        display_name="Human Resources", value="Human Resources", count=4111918
    ),
    FilterFieldValue(
        display_name="Import And Export", value="Import And Export", count=1444883
    ),
    FilterFieldValue(
        display_name="Individual & Family Services",
        value="Individual & Family Services",
        count=2027154,
    ),
    FilterFieldValue(
        display_name="Industrial Automation",
        value="Industrial Automation",
        count=1501647,
    ),
    FilterFieldValue(
        display_name="Information Services", value="Information Services", count=1127631
    ),
    FilterFieldValue(
        display_name="Information Technology And Services",
        value="Information Technology And Services",
        count=18357794,
    ),
    FilterFieldValue(display_name="Insurance", value="Insurance", count=6054348),
    FilterFieldValue(
        display_name="International Affairs",
        value="International Affairs",
        count=782358,
    ),
    FilterFieldValue(
        display_name="International Trade And Development",
        value="International Trade And Development",
        count=1415631,
    ),
    FilterFieldValue(display_name="Internet", value="Internet", count=3951142),
    FilterFieldValue(
        display_name="Investment Banking", value="Investment Banking", count=746160
    ),
    FilterFieldValue(
        display_name="Investment Management",
        value="Investment Management",
        count=1161799,
    ),
    FilterFieldValue(display_name="Judiciary", value="Judiciary", count=729119),
    FilterFieldValue(
        display_name="Law Enforcement", value="Law Enforcement", count=1177292
    ),
    FilterFieldValue(display_name="Law Practice", value="Law Practice", count=4082030),
    FilterFieldValue(
        display_name="Legal Services", value="Legal Services", count=3024960
    ),
    FilterFieldValue(
        display_name="Legislative Office", value="Legislative Office", count=110198
    ),
    FilterFieldValue(
        display_name="Leisure, Travel & Tourism",
        value="Leisure, Travel & Tourism",
        count=2275912,
    ),
    FilterFieldValue(display_name="Libraries", value="Libraries", count=518548),
    FilterFieldValue(
        display_name="Logistics And Supply Chain",
        value="Logistics And Supply Chain",
        count=3605577,
    ),
    FilterFieldValue(
        display_name="Luxury Goods & Jewelry",
        value="Luxury Goods & Jewelry",
        count=675534,
    ),
    FilterFieldValue(display_name="Machinery", value="Machinery", count=5425313),
    FilterFieldValue(
        display_name="Management Consulting",
        value="Management Consulting",
        count=4369360,
    ),
    FilterFieldValue(display_name="Maritime", value="Maritime", count=1109643),
    FilterFieldValue(
        display_name="Market Research", value="Market Research", count=723990
    ),
    FilterFieldValue(
        display_name="Marketing And Advertising",
        value="Marketing And Advertising",
        count=7618118,
    ),
    FilterFieldValue(
        display_name="Mechanical Or Industrial Engineering",
        value="Mechanical Or Industrial Engineering",
        count=2785240,
    ),
    FilterFieldValue(
        display_name="Media Production", value="Media Production", count=1814534
    ),
    FilterFieldValue(
        display_name="Medical Devices", value="Medical Devices", count=2097516
    ),
    FilterFieldValue(
        display_name="Medical Practice", value="Medical Practice", count=4457829
    ),
    FilterFieldValue(
        display_name="Mental Health Care", value="Mental Health Care", count=2361841
    ),
    FilterFieldValue(display_name="Military", value="Military", count=1552919),
    FilterFieldValue(
        display_name="Mining & Metals", value="Mining & Metals", count=2674806
    ),
    FilterFieldValue(
        display_name="Motion Pictures And Film",
        value="Motion Pictures And Film",
        count=709807,
    ),
    FilterFieldValue(
        display_name="Museums And Institutions",
        value="Museums And Institutions",
        count=354339,
    ),
    FilterFieldValue(display_name="Music", value="Music", count=2130426),
    FilterFieldValue(
        display_name="Nanotechnology", value="Nanotechnology", count=132664
    ),
    FilterFieldValue(display_name="Newspapers", value="Newspapers", count=624284),
    FilterFieldValue(
        display_name="Non-Profit Organization Management",
        value="Non-Profit Organization Management",
        count=3192657,
    ),
    FilterFieldValue(display_name="Oil & Energy", value="Oil & Energy", count=6130481),
    FilterFieldValue(display_name="Online Media", value="Online Media", count=723919),
    FilterFieldValue(
        display_name="Outsourcing/Offshoring",
        value="Outsourcing/Offshoring",
        count=1223128,
    ),
    FilterFieldValue(
        display_name="Package/Freight Delivery",
        value="Package/Freight Delivery",
        count=427476,
    ),
    FilterFieldValue(
        display_name="Packaging And Containers",
        value="Packaging And Containers",
        count=756324,
    ),
    FilterFieldValue(
        display_name="Paper & Forest Products",
        value="Paper & Forest Products",
        count=547532,
    ),
    FilterFieldValue(
        display_name="Performing Arts", value="Performing Arts", count=833794
    ),
    FilterFieldValue(
        display_name="Pharmaceuticals", value="Pharmaceuticals", count=4208574
    ),
    FilterFieldValue(display_name="Philanthropy", value="Philanthropy", count=342110),
    FilterFieldValue(display_name="Photography", value="Photography", count=1471247),
    FilterFieldValue(display_name="Plastics", value="Plastics", count=791927),
    FilterFieldValue(
        display_name="Political Organization",
        value="Political Organization",
        count=289663,
    ),
    FilterFieldValue(
        display_name="Primary/Secondary Education",
        value="Primary/Secondary Education",
        count=4790521,
    ),
    FilterFieldValue(display_name="Printing", value="Printing", count=1180703),
    FilterFieldValue(
        display_name="Professional Training & Coaching",
        value="Professional Training & Coaching",
        count=1993494,
    ),
    FilterFieldValue(
        display_name="Program Development", value="Program Development", count=728543
    ),
    FilterFieldValue(display_name="Public Policy", value="Public Policy", count=542426),
    FilterFieldValue(
        display_name="Public Relations And Communications",
        value="Public Relations And Communications",
        count=1573934,
    ),
    FilterFieldValue(display_name="Public Safety", value="Public Safety", count=876882),
    FilterFieldValue(display_name="Publishing", value="Publishing", count=1157209),
    FilterFieldValue(
        display_name="Railroad Manufacture", value="Railroad Manufacture", count=188721
    ),
    FilterFieldValue(display_name="Ranching", value="Ranching", count=348351),
    FilterFieldValue(display_name="Real Estate", value="Real Estate", count=7679902),
    FilterFieldValue(
        display_name="Recreational Facilities And Services",
        value="Recreational Facilities And Services",
        count=451026,
    ),
    FilterFieldValue(
        display_name="Religious Institutions",
        value="Religious Institutions",
        count=1345656,
    ),
    FilterFieldValue(
        display_name="Renewables & Environment",
        value="Renewables & Environment",
        count=1483456,
    ),
    FilterFieldValue(display_name="Research", value="Research", count=3758267),
    FilterFieldValue(display_name="Restaurants", value="Restaurants", count=3355952),
    FilterFieldValue(display_name="Retail", value="Retail", count=12302867),
    FilterFieldValue(
        display_name="Security And Investigations",
        value="Security And Investigations",
        count=1903514,
    ),
    FilterFieldValue(
        display_name="Semiconductors", value="Semiconductors", count=815162
    ),
    FilterFieldValue(display_name="Shipbuilding", value="Shipbuilding", count=361266),
    FilterFieldValue(
        display_name="Sporting Goods", value="Sporting Goods", count=564435
    ),
    FilterFieldValue(display_name="Sports", value="Sports", count=2351277),
    FilterFieldValue(
        display_name="Staffing And Recruiting",
        value="Staffing And Recruiting",
        count=2302053,
    ),
    FilterFieldValue(display_name="Supermarkets", value="Supermarkets", count=812422),
    FilterFieldValue(
        display_name="Telecommunications", value="Telecommunications", count=6984061
    ),
    FilterFieldValue(display_name="Textiles", value="Textiles", count=1502190),
    FilterFieldValue(display_name="Think Tanks", value="Think Tanks", count=335239),
    FilterFieldValue(display_name="Tobacco", value="Tobacco", count=172098),
    FilterFieldValue(
        display_name="Translation And Localization",
        value="Translation And Localization",
        count=704028,
    ),
    FilterFieldValue(
        display_name="Transportation/Trucking/Railroad",
        value="Transportation/Trucking/Railroad",
        count=4195153,
    ),
    FilterFieldValue(display_name="Utilities", value="Utilities", count=2914844),
    FilterFieldValue(
        display_name="Venture Capital & Private Equity",
        value="Venture Capital & Private Equity",
        count=315510,
    ),
    FilterFieldValue(display_name="Veterinary", value="Veterinary", count=804749),
    FilterFieldValue(display_name="Warehousing", value="Warehousing", count=938090),
    FilterFieldValue(display_name="Wholesale", value="Wholesale", count=2660208),
    FilterFieldValue(
        display_name="Wine And Spirits", value="Wine And Spirits", count=606481
    ),
    FilterFieldValue(display_name="Wireless", value="Wireless", count=357332),
    FilterFieldValue(
        display_name="Writing And Editing", value="Writing And Editing", count=1693631
    ),
]

PERSON_COMPANY_NAME_FILTER_FIELD_VALUES = [
    FilterFieldValue(display_name="Google", value="Google", count=347274),
    FilterFieldValue(display_name="Microsoft", value="Microsoft", count=473145),
    FilterFieldValue(display_name="Amazon", value="Amazon", count=852769),
    FilterFieldValue(display_name="Apple", value="Apple", count=399252),
    FilterFieldValue(display_name="Meta", value="Meta", count=460536),
    FilterFieldValue(display_name="IBM", value="IBM", count=472680),
    FilterFieldValue(display_name="Oracle", value="Oracle", count=183599),
    FilterFieldValue(display_name="Salesforce", value="Salesforce", count=81023),
    FilterFieldValue(display_name="Nvidia", value="Nvidia", count=34812),
    FilterFieldValue(
        display_name="Intel Corporation", value="Intel Corporation", count=135605
    ),
]

PERSON_COUNTRY_FILTER_FIELD_VALUES = [
    FilterFieldValue(
        display_name="United States", value="United States", count=*********
    ),
    FilterFieldValue(
        display_name="United Kingdom", value="United Kingdom", count=51669986
    ),
    FilterFieldValue(display_name="Germany", value="Germany", count=20664875),
    FilterFieldValue(display_name="Japan", value="Japan", count=5443561),
    FilterFieldValue(display_name="Canada", value="Canada", count=34797016),
    FilterFieldValue(display_name="France", value="France", count=34597391),
    FilterFieldValue(display_name="Australia", value="Australia", count=21007755),
    FilterFieldValue(display_name="Denmark", value="Denmark", count=3772953),
    FilterFieldValue(display_name="Singapore", value="Singapore", count=4981404),
    FilterFieldValue(display_name="Austria", value="Austria", count=3053279),
]

# NOTE: The state filter field values are not used in UI
PERSON_STATE_FILTER_FIELD_VALUES = [
    FilterFieldValue(
        display_name="New York, New York", value="New York, New York", count=45706841
    ),
    FilterFieldValue(
        display_name="Los Angeles, California",
        value="Los Angeles, California",
        count=32286683,
    ),
    FilterFieldValue(
        display_name="Chicago, Illinois", value="Chicago, Illinois", count=22453353
    ),
    FilterFieldValue(
        display_name="Houston, Texas", value="Houston, Texas", count=14842103
    ),
    FilterFieldValue(
        display_name="Phoenix, Arizona", value="Phoenix, Arizona", count=10296237
    ),
    FilterFieldValue(
        display_name="Philadelphia, Pennsylvania",
        value="Philadelphia, Pennsylvania",
        count=14559571,
    ),
    FilterFieldValue(
        display_name="San Antonio, Texas", value="San Antonio, Texas", count=4724537
    ),
    FilterFieldValue(
        display_name="San Diego, California",
        value="San Diego, California",
        count=7718541,
    ),
    FilterFieldValue(
        display_name="Dallas, Texas", value="Dallas, Texas", count=15865028
    ),
    FilterFieldValue(
        display_name="San Jose, California", value="San Jose, California", count=4468201
    ),
    FilterFieldValue(
        display_name="Austin, Texas", value="Austin, Texas", count=4803480
    ),
    FilterFieldValue(
        display_name="Jacksonville, Florida",
        value="Jacksonville, Florida",
        count=3720411,
    ),
    FilterFieldValue(
        display_name="Fort Worth, Texas", value="Fort Worth, Texas", count=0
    ),
    FilterFieldValue(
        display_name="Columbus, Ohio", value="Columbus, Ohio", count=4654787
    ),
    FilterFieldValue(
        display_name="San Francisco, California",
        value="San Francisco, California",
        count=12097274,
    ),
    FilterFieldValue(
        display_name="Charlotte, North Carolina",
        value="Charlotte, North Carolina",
        count=5944969,
    ),
    FilterFieldValue(
        display_name="Indianapolis, Indiana",
        value="Indianapolis, Indiana",
        count=4729931,
    ),
    FilterFieldValue(
        display_name="Seattle, Washington", value="Seattle, Washington", count=8770683
    ),
    FilterFieldValue(
        display_name="Denver, Colorado", value="Denver, Colorado", count=6584237
    ),
    FilterFieldValue(
        display_name="Boston, Massachusetts",
        value="Boston, Massachusetts",
        count=11740514,
    ),
]

# NOTE: The city filter field values are not used in UI
PERSON_CITY_FILTER_FIELD_VALUES = [
    FilterFieldValue(display_name="New York", value="New York", count=11225381),
    FilterFieldValue(display_name="Los Angeles", value="Los Angeles", count=9617898),
    FilterFieldValue(display_name="Chicago", value="Chicago", count=8823836),
    FilterFieldValue(display_name="Houston", value="Houston", count=8210833),
    FilterFieldValue(display_name="Phoenix", value="Phoenix", count=3943969),
    FilterFieldValue(display_name="Philadelphia", value="Philadelphia", count=5252151),
    FilterFieldValue(display_name="San Antonio", value="San Antonio", count=3647703),
    FilterFieldValue(display_name="San Diego", value="San Diego", count=3998376),
    FilterFieldValue(display_name="Dallas", value="Dallas", count=4273201),
    FilterFieldValue(display_name="San Jose", value="San Jose", count=2583490),
]

PERSON_REGION_FILTER_FIELD_VALUES = [
    FilterFieldValue(display_name="California", value="California", count=90450040),
    FilterFieldValue(display_name="Texas", value="Texas", count=60653583),
    FilterFieldValue(display_name="Florida", value="Florida", count=52041584),
    FilterFieldValue(display_name="New York", value="New York", count=48502591),
    FilterFieldValue(display_name="Pennsylvania", value="Pennsylvania", count=30239381),
    FilterFieldValue(display_name="Illinois", value="Illinois", count=29659338),
    FilterFieldValue(display_name="Ohio", value="Ohio", count=26809078),
    FilterFieldValue(display_name="Georgia", value="Georgia", count=25782099),
    FilterFieldValue(
        display_name="North Carolina", value="North Carolina", count=23825861
    ),
    FilterFieldValue(display_name="Michigan", value="Michigan", count=23092579),
]

PERSON_POSTAL_CODE_FILTER_FIELD_VALUES = [
    FilterFieldValue(display_name="10001", value="10001", count=112765),  # New York
    FilterFieldValue(display_name="90001", value="90001", count=91846),  # Los Angeles
    FilterFieldValue(display_name="60601", value="60601", count=27081),  # Chicago
    FilterFieldValue(display_name="77001", value="77001", count=15314),  # Houston
    FilterFieldValue(display_name="85001", value="85001", count=11626),  # Phoenix
    FilterFieldValue(display_name="19101", value="19101", count=2547),  # Philadelphia
    FilterFieldValue(display_name="78201", value="78201", count=73769),  # San Antonio
    FilterFieldValue(display_name="92101", value="92101", count=83417),  # San Diego
    FilterFieldValue(display_name="75201", value="75201", count=35444),  # Dallas
    FilterFieldValue(display_name="95101", value="95101", count=1860),  # San Jose
]


PERSON_NUMBER_OF_EMPLOYEE_FILTER_FIELD_VALUES = [
    FilterFieldValue(display_name="1-10", value="1-10", count=53827175),
    FilterFieldValue(display_name="11-50", value="11-50", count=34554464),
    FilterFieldValue(display_name="51-200", value="51-200", count=37125698),
    FilterFieldValue(display_name="201-500", value="201-500", count=27524573),
    FilterFieldValue(display_name="501-1000", value="501-1000", count=22333169),
    FilterFieldValue(display_name="1001-5000", value="1001-5000", count=48867804),
    FilterFieldValue(display_name="5001-10000", value="5001-10000", count=20671803),
    FilterFieldValue(display_name="10001+", value="10001+", count=85670295),
]


PERSON_SENIORITIES_FILTER_FIELD_VALUES = [
    FilterFieldValue(display_name="Owner", value="Owner", count=19533067),
    FilterFieldValue(display_name="Partner", value="Partner", count=2350707),
    FilterFieldValue(display_name="CxO", value="CxO", count=10715682),
    FilterFieldValue(display_name="VP", value="VP", count=3129033),
    FilterFieldValue(display_name="Director", value="Director", count=22778022),
    FilterFieldValue(display_name="Manager", value="Manager", count=54476780),
    FilterFieldValue(display_name="Senior", value="Senior", count=19912382),
    FilterFieldValue(display_name="Entry", value="Entry", count=1626698),
    FilterFieldValue(display_name="Training", value="Training", count=1551373),
    FilterFieldValue(display_name="Unpaid", value="Unpaid", count=398287),
]

PERSON_JOB_TITLE_ROLE_FILTER_FIELD_VALUES = [
    FilterFieldValue(display_name="Advisory", value="Advisory", count=2369717),
    FilterFieldValue(display_name="Analyst", value="Analyst", count=4902989),
    FilterFieldValue(display_name="Creative", value="Creative", count=14653333),
    FilterFieldValue(display_name="Education", value="Education", count=20339033),
    FilterFieldValue(display_name="Engineering", value="Engineering", count=27276896),
    FilterFieldValue(display_name="Finance", value="Finance", count=11868663),
    FilterFieldValue(display_name="Fulfillment", value="Fulfillment", count=16600039),
    FilterFieldValue(display_name="Health", value="Health", count=21099763),
    FilterFieldValue(display_name="Hospitality", value="Hospitality", count=18537575),
    FilterFieldValue(
        display_name="Human Resources", value="human_resources", count=7294954
    ),
    FilterFieldValue(display_name="Legal", value="Legal", count=2094736),
    FilterFieldValue(
        display_name="Manufacturing", value="Manufacturing", count=3356154
    ),
    FilterFieldValue(display_name="Marketing", value="Marketing", count=6406836),
    FilterFieldValue(display_name="Operations", value="Operations", count=66199142),
    FilterFieldValue(display_name="Partnerships", value="Partnerships", count=2391269),
    FilterFieldValue(display_name="Product", value="Product", count=2093252),
    FilterFieldValue(
        display_name="Professional Service",
        value="professional_service",
        count=20726600,
    ),
    FilterFieldValue(
        display_name="Public Service", value="public_service", count=4889324
    ),
    FilterFieldValue(display_name="Research", value="Research", count=5042566),
    FilterFieldValue(display_name="Sales", value="Sales", count=15408231),
    FilterFieldValue(
        display_name="Sales Engineering", value="sales_engineering", count=669794
    ),
    FilterFieldValue(display_name="Support", value="Support", count=6548438),
    FilterFieldValue(display_name="Trade", value="Trade", count=6163280),
    FilterFieldValue(display_name="Unemployed", value="Unemployed", count=490931),
]

PERSON_TECHNOLOGY_FILTER_FIELD_VALUES = [
    FilterFieldValue(
        display_name="Mobile Friendly", value="Mobile Friendly", count=1000000
    ),
    FilterFieldValue(
        display_name="WordPress.org", value="WordPress.org", count=1000000
    ),
    FilterFieldValue(
        display_name="Google Font API", value="Google Font API", count=1000000
    ),
    FilterFieldValue(
        display_name="Google Tag Manager", value="Google Tag Manager", count=1000000
    ),
    FilterFieldValue(display_name="Apache", value="Apache", count=1000000),
    FilterFieldValue(display_name="Outlook", value="Outlook", count=1000000),
    FilterFieldValue(display_name="Nginx", value="Nginx", count=1000000),
    FilterFieldValue(display_name="Gmail", value="Gmail", count=1000000),
    FilterFieldValue(display_name="Google Apps", value="Google Apps", count=1000000),
    FilterFieldValue(
        display_name="Google Analytics", value="Google Analytics", count=1000000
    ),
    FilterFieldValue(display_name="reCAPTCHA", value="reCAPTCHA", count=1000000),
    FilterFieldValue(
        display_name="Bootstrap Framework", value="Bootstrap Framework", count=1000000
    ),
    FilterFieldValue(display_name="Amazon AWS", value="Amazon AWS", count=1000000),
    FilterFieldValue(display_name="Remote", value="Remote", count=1000000),
    FilterFieldValue(
        display_name="Microsoft Office 365", value="Microsoft Office 365", count=1000000
    ),
    FilterFieldValue(
        display_name="Facebook Login (Connect)",
        value="Facebook Login (Connect)",
        count=1000000,
    ),
    FilterFieldValue(display_name="YouTube", value="YouTube", count=1000000),
    FilterFieldValue(
        display_name="Facebook Widget", value="Facebook Widget", count=1000000
    ),
    FilterFieldValue(
        display_name="Cloudflare DNS", value="Cloudflare DNS", count=1000000
    ),
    FilterFieldValue(
        display_name="Google Cloud Hosting", value="Google Cloud Hosting", count=1000000
    ),
    FilterFieldValue(
        display_name="Facebook Custom Audiences",
        value="Facebook Custom Audiences",
        count=1000000,
    ),
    FilterFieldValue(
        display_name="CloudFlare Hosting", value="CloudFlare Hosting", count=1000000
    ),
    FilterFieldValue(display_name="Varnish", value="Varnish", count=1000000),
    FilterFieldValue(display_name="AI", value="AI", count=1000000),
    FilterFieldValue(display_name="Google Maps", value="Google Maps", count=1000000),
    FilterFieldValue(display_name="Vimeo", value="Vimeo", count=1000000),
    FilterFieldValue(display_name="Wix", value="Wix", count=1000000),
    FilterFieldValue(display_name="Android", value="Android", count=1000000),
    FilterFieldValue(display_name="Woo Commerce", value="Woo Commerce", count=1000000),
    FilterFieldValue(display_name="Typekit", value="Typekit", count=1000000),
    FilterFieldValue(display_name="DoubleClick", value="DoubleClick", count=1000000),
    FilterFieldValue(display_name="Basis", value="Basis", count=1000000),
    FilterFieldValue(
        display_name="Google AdSense", value="Google AdSense", count=1000000
    ),
    FilterFieldValue(
        display_name="DoubleClick Conversion",
        value="DoubleClick Conversion",
        count=1000000,
    ),
    FilterFieldValue(
        display_name="Google Dynamic Remarketing",
        value="Google Dynamic Remarketing",
        count=1000000,
    ),
    FilterFieldValue(
        display_name="Google Maps (Non Paid Users)",
        value="Google Maps (Non Paid Users)",
        count=1000000,
    ),
    FilterFieldValue(display_name="Render", value="Render", count=1000000),
    FilterFieldValue(display_name="Circle", value="Circle", count=1000000),
    FilterFieldValue(display_name="Python", value="Python", count=1000000),
    FilterFieldValue(display_name="ASP.NET", value="ASP.NET", count=1000000),
    FilterFieldValue(
        display_name="Gravity Forms", value="Gravity Forms", count=1000000
    ),
    FilterFieldValue(display_name="Reviews", value="Reviews", count=1000000),
    FilterFieldValue(
        display_name="GoDaddy Hosting", value="GoDaddy Hosting", count=1000000
    ),
    FilterFieldValue(display_name="Flutter", value="Flutter", count=1000000),
    FilterFieldValue(display_name="SharePoint", value="SharePoint", count=1000000),
    FilterFieldValue(
        display_name="Microsoft-IIS", value="Microsoft-IIS", count=1000000
    ),
    FilterFieldValue(display_name="IoT", value="IoT", count=1000000),
    FilterFieldValue(
        display_name="Squarespace ECommerce",
        value="Squarespace ECommerce",
        count=1000000,
    ),
    FilterFieldValue(display_name="Route 53", value="Route 53", count=1000000),
    FilterFieldValue(display_name="Shopify", value="Shopify", count=1000000),
    FilterFieldValue(display_name="Node.js", value="Node.js", count=1000000),
    FilterFieldValue(display_name="Shutterstock", value="Shutterstock", count=1000000),
    FilterFieldValue(display_name="Hubspot", value="Hubspot", count=1000000),
    FilterFieldValue(display_name="Google Play", value="Google Play", count=1000000),
    FilterFieldValue(display_name="Hotjar", value="Hotjar", count=1000000),
    FilterFieldValue(
        display_name="Adobe Media Optimizer",
        value="Adobe Media Optimizer",
        count=1000000,
    ),
    FilterFieldValue(
        display_name="Google AdWords Conversion",
        value="Google AdWords Conversion",
        count=1000000,
    ),
    FilterFieldValue(display_name="SAP", value="SAP", count=1000000),
    FilterFieldValue(display_name="Ubuntu", value="Ubuntu", count=1000000),
    FilterFieldValue(display_name="NSOne", value="NSOne", count=1000000),
    FilterFieldValue(
        display_name="Cedexis Radar", value="Cedexis Radar", count=1000000
    ),
    FilterFieldValue(display_name="Quantcast", value="Quantcast", count=1000000),
    FilterFieldValue(display_name="TikTok", value="TikTok", count=1000000),
    FilterFieldValue(display_name="Google Plus", value="Google Plus", count=1000000),
    FilterFieldValue(display_name="Stripe", value="Stripe", count=1000000),
    FilterFieldValue(display_name="DigitalOcean", value="DigitalOcean", count=1000000),
    FilterFieldValue(display_name="Sendgrid", value="Sendgrid", count=1000000),
    FilterFieldValue(
        display_name="Linkedin Marketing Solutions",
        value="Linkedin Marketing Solutions",
        count=1000000,
    ),
    FilterFieldValue(
        display_name="Google translate API", value="Google translate API", count=1000000
    ),
    FilterFieldValue(display_name="Paypal", value="Paypal", count=1000000),
    FilterFieldValue(display_name="Blue Host", value="Blue Host", count=1000000),
    FilterFieldValue(display_name="Yelp", value="Yelp", count=1000000),
    FilterFieldValue(display_name="New Relic", value="New Relic", count=1000000),
    FilterFieldValue(display_name="MailChimp", value="MailChimp", count=1000000),
    FilterFieldValue(
        display_name="Google translate widget",
        value="Google translate widget",
        count=1000000,
    ),
    FilterFieldValue(display_name="Gusto", value="Gusto", count=1000000),
    FilterFieldValue(display_name="Micro", value="Micro", count=1000000),
    FilterFieldValue(display_name="AddThis", value="AddThis", count=1000000),
    FilterFieldValue(
        display_name="MailChimp SPF", value="MailChimp SPF", count=1000000
    ),
    FilterFieldValue(
        display_name="Rackspace MailGun", value="Rackspace MailGun", count=1000000
    ),
    FilterFieldValue(display_name="Amazon SES", value="Amazon SES", count=1000000),
    FilterFieldValue(display_name="Bing Ads", value="Bing Ads", count=1000000),
    FilterFieldValue(
        display_name="Data Analytics", value="Data Analytics", count=1000000
    ),
    FilterFieldValue(display_name="React Native", value="React Native", count=1000000),
    FilterFieldValue(
        display_name="Wordpress.com", value="Wordpress.com", count=1000000
    ),
    FilterFieldValue(
        display_name="JQuery 1.11.1", value="JQuery 1.11.1", count=1000000
    ),
    FilterFieldValue(
        display_name="Ruby On Rails", value="Ruby On Rails", count=1000000
    ),
    FilterFieldValue(
        display_name="Mailchimp Mandrill", value="Mailchimp Mandrill", count=1000000
    ),
    FilterFieldValue(display_name="OpenSSL", value="OpenSSL", count=1000000),
    FilterFieldValue(
        display_name="Microsoft Azure Hosting",
        value="Microsoft Azure Hosting",
        count=1000000,
    ),
    FilterFieldValue(display_name="ShareThis", value="ShareThis", count=1000000),
    FilterFieldValue(display_name="Multilingual", value="Multilingual", count=1000000),
    FilterFieldValue(display_name="Weebly", value="Weebly", count=1000000),
    FilterFieldValue(
        display_name="Facebook Like Button", value="Facebook Like Button", count=1000000
    ),
    FilterFieldValue(display_name="SendInBlue", value="SendInBlue", count=1000000),
    FilterFieldValue(display_name="Salesforce", value="Salesforce", count=1000000),
    FilterFieldValue(display_name="MailJet", value="MailJet", count=1000000),
    FilterFieldValue(
        display_name="Facebook Comments", value="Facebook Comments", count=1000000
    ),
    FilterFieldValue(display_name="Trustpilot", value="Trustpilot", count=1000000),
    FilterFieldValue(display_name="Centro", value="Centro", count=1000000),
]


# Company-specific filter field values

# NOTE: The location filter field has been renamed to "City" in UI
COMPANY_LOCATION_FILTER_FIELD_VALUES = [
    FilterFieldValue(
        display_name="New York, New York, United States",
        value="New York, New York, United States",
        count=318099,
    ),
    FilterFieldValue(
        display_name="Los Angeles, California, United States",
        value="Los Angeles, California, United States",
        count=376850,
    ),
    FilterFieldValue(
        display_name="Chicago, Illinois, United States",
        value="Chicago, Illinois, United States",
        count=184654,
    ),
    FilterFieldValue(
        display_name="Houston, Texas, United States",
        value="Houston, Texas, United States",
        count=237132,
    ),
    FilterFieldValue(
        display_name="Phoenix, Arizona, United States",
        value="Phoenix, Arizona, United States",
        count=76812,
    ),
    FilterFieldValue(
        display_name="Philadelphia, Pennsylvania, United States",
        value="Philadelphia, Pennsylvania, United States",
        count=105469,
    ),
    FilterFieldValue(
        display_name="San Antonio, Texas, United States",
        value="San Antonio, Texas, United States",
        count=86790,
    ),
    FilterFieldValue(
        display_name="San Diego, California, United States",
        value="San Diego, California, United States",
        count=138165,
    ),
    FilterFieldValue(
        display_name="Dallas, Texas, United States",
        value="Dallas, Texas, United States",
        count=129286,
    ),
    FilterFieldValue(
        display_name="San Jose, California, United States",
        value="San Jose, California, United States",
        count=53724,
    ),
]


COMPANY_LINKEDIN_INDUSTRY_FILTER_FIELD_VALUES = [
    FilterFieldValue(display_name="Accounting", value="Accounting", count=473138),
    FilterFieldValue(
        display_name="Airlines/Aviation", value="Airlines/Aviation", count=82462
    ),
    FilterFieldValue(
        display_name="Alternative Dispute Resolution",
        value="Alternative Dispute Resolution",
        count=8866,
    ),
    FilterFieldValue(
        display_name="Alternative Medicine", value="Alternative Medicine", count=37040
    ),
    FilterFieldValue(display_name="Animation", value="Animation", count=26738),
    FilterFieldValue(
        display_name="Apparel & Fashion", value="Apparel & Fashion", count=431901
    ),
    FilterFieldValue(
        display_name="Architecture & Planning",
        value="Architecture & Planning",
        count=486315,
    ),
    FilterFieldValue(
        display_name="Arts And Crafts", value="Arts And Crafts", count=101219
    ),
    FilterFieldValue(display_name="Automotive", value="Automotive", count=514051),
    FilterFieldValue(
        display_name="Aviation & Aerospace", value="Aviation & Aerospace", count=46504
    ),
    FilterFieldValue(display_name="Banking", value="Banking", count=143866),
    FilterFieldValue(display_name="Biotechnology", value="Biotechnology", count=90862),
    FilterFieldValue(
        display_name="Broadcast Media", value="Broadcast Media", count=156515
    ),
    FilterFieldValue(
        display_name="Building Materials", value="Building Materials", count=250299
    ),
    FilterFieldValue(
        display_name="Business Supplies And Equipment",
        value="Business Supplies And Equipment",
        count=343605,
    ),
    FilterFieldValue(
        display_name="Capital Markets", value="Capital Markets", count=43751
    ),
    FilterFieldValue(display_name="Chemicals", value="Chemicals", count=187687),
    FilterFieldValue(
        display_name="Civic & Social Organization",
        value="Civic & Social Organization",
        count=879611,
    ),
    FilterFieldValue(
        display_name="Civil Engineering", value="Civil Engineering", count=255607
    ),
    FilterFieldValue(
        display_name="Commercial Real Estate",
        value="Commercial Real Estate",
        count=71773,
    ),
    FilterFieldValue(
        display_name="Computer & Network Security",
        value="Computer & Network Security",
        count=67636,
    ),
    FilterFieldValue(
        display_name="Computer Games", value="Computer Games", count=71176
    ),
    FilterFieldValue(
        display_name="Computer Hardware", value="Computer Hardware", count=44597
    ),
    FilterFieldValue(
        display_name="Computer Networking", value="Computer Networking", count=32939
    ),
    FilterFieldValue(
        display_name="Computer Software", value="Computer Software", count=1706327
    ),
    FilterFieldValue(display_name="Construction", value="Construction", count=2824254),
    FilterFieldValue(
        display_name="Consumer Electronics", value="Consumer Electronics", count=126031
    ),
    FilterFieldValue(
        display_name="Consumer Goods", value="Consumer Goods", count=599669
    ),
    FilterFieldValue(
        display_name="Consumer Services", value="Consumer Services", count=578757
    ),
    FilterFieldValue(display_name="Cosmetics", value="Cosmetics", count=180228),
    FilterFieldValue(display_name="Dairy", value="Dairy", count=59503),
    FilterFieldValue(
        display_name="Defense & Space", value="Defense & Space", count=56357
    ),
    FilterFieldValue(display_name="Design", value="Design", count=427795),
    FilterFieldValue(display_name="E-Learning", value="E-Learning", count=329946),
    FilterFieldValue(
        display_name="Education Management",
        value="Education Management",
        count=427382,
    ),
    FilterFieldValue(
        display_name="Electrical/Electronic Manufacturing",
        value="Electrical/Electronic Manufacturing",
        count=589844,
    ),
    FilterFieldValue(display_name="Entertainment", value="Entertainment", count=324264),
    FilterFieldValue(
        display_name="Environmental Services",
        value="Environmental Services",
        count=281419,
    ),
    FilterFieldValue(
        display_name="Events Services", value="Events Services", count=284631
    ),
    FilterFieldValue(
        display_name="Executive Office", value="Executive Office", count=78946
    ),
    FilterFieldValue(
        display_name="Facilities Services", value="Facilities Services", count=645377
    ),
    FilterFieldValue(display_name="Farming", value="Farming", count=592896),
    FilterFieldValue(
        display_name="Financial Services", value="Financial Services", count=840555
    ),
    FilterFieldValue(display_name="Fine Art", value="Fine Art", count=90402),
    FilterFieldValue(display_name="Fishery", value="Fishery", count=37057),
    FilterFieldValue(
        display_name="Food & Beverages", value="Food & Beverages", count=575270
    ),
    FilterFieldValue(
        display_name="Food Production", value="Food Production", count=458020
    ),
    FilterFieldValue(display_name="Fund-Raising", value="Fund-Raising", count=26272),
    FilterFieldValue(display_name="Furniture", value="Furniture", count=244051),
    FilterFieldValue(
        display_name="Gambling & Casinos", value="Gambling & Casinos", count=19598
    ),
    FilterFieldValue(
        display_name="Glass, Ceramics & Concrete",
        value="Glass, Ceramics & Concrete",
        count=69871,
    ),
    FilterFieldValue(
        display_name="Government Administration",
        value="Government Administration",
        count=280011,
    ),
    FilterFieldValue(
        display_name="Government Relations", value="Government Relations", count=23649
    ),
    FilterFieldValue(
        display_name="Graphic Design", value="Graphic Design", count=175170
    ),
    FilterFieldValue(
        display_name="Health, Wellness And Fitness",
        value="Health, Wellness And Fitness",
        count=840109,
    ),
    FilterFieldValue(
        display_name="Higher Education", value="Higher Education", count=394949
    ),
    FilterFieldValue(
        display_name="Hospital & Health Care",
        value="Hospital & Health Care",
        count=1115999,
    ),
    FilterFieldValue(display_name="Hospitality", value="Hospitality", count=768926),
    FilterFieldValue(
        display_name="Human Resources", value="Human Resources", count=272064
    ),
    FilterFieldValue(
        display_name="Import And Export", value="Import And Export", count=79510
    ),
    FilterFieldValue(
        display_name="Individual & Family Services",
        value="Individual & Family Services",
        count=1767945,
    ),
    FilterFieldValue(
        display_name="Industrial Automation",
        value="Industrial Automation",
        count=83411,
    ),
    FilterFieldValue(
        display_name="Information Services", value="Information Services", count=151568
    ),
    FilterFieldValue(
        display_name="Information Technology And Services",
        value="Information Technology And Services",
        count=1915549,
    ),
    FilterFieldValue(display_name="Insurance", value="Insurance", count=420843),
    FilterFieldValue(
        display_name="International Affairs",
        value="International Affairs",
        count=20523,
    ),
    FilterFieldValue(
        display_name="International Trade And Development",
        value="International Trade And Development",
        count=119175,
    ),
    FilterFieldValue(display_name="Internet", value="Internet", count=713747),
    FilterFieldValue(
        display_name="Investment Banking", value="Investment Banking", count=93206
    ),
    FilterFieldValue(
        display_name="Investment Management",
        value="Investment Management",
        count=295692,
    ),
    FilterFieldValue(display_name="Judiciary", value="Judiciary", count=14691),
    FilterFieldValue(
        display_name="Law Enforcement", value="Law Enforcement", count=26584
    ),
    FilterFieldValue(display_name="Law Practice", value="Law Practice", count=241448),
    FilterFieldValue(
        display_name="Legal Services", value="Legal Services", count=472810
    ),
    FilterFieldValue(
        display_name="Legislative Office", value="Legislative Office", count=4387
    ),
    FilterFieldValue(
        display_name="Leisure, Travel & Tourism",
        value="Leisure, Travel & Tourism",
        count=361866,
    ),
    FilterFieldValue(display_name="Libraries", value="Libraries", count=15709),
    FilterFieldValue(
        display_name="Logistics And Supply Chain",
        value="Logistics And Supply Chain",
        count=176349,
    ),
    FilterFieldValue(
        display_name="Luxury Goods & Jewelry",
        value="Luxury Goods & Jewelry",
        count=114286,
    ),
    FilterFieldValue(display_name="Machinery", value="Machinery", count=550579),
    FilterFieldValue(
        display_name="Management Consulting",
        value="Management Consulting",
        count=1909005,
    ),
    FilterFieldValue(display_name="Maritime", value="Maritime", count=60448),
    FilterFieldValue(
        display_name="Market Research", value="Market Research", count=52471
    ),
    FilterFieldValue(
        display_name="Marketing And Advertising",
        value="Marketing And Advertising",
        count=1380514,
    ),
    FilterFieldValue(
        display_name="Mechanical Or Industrial Engineering",
        value="Mechanical Or Industrial Engineering",
        count=403562,
    ),
    FilterFieldValue(
        display_name="Media Production", value="Media Production", count=287084
    ),
    FilterFieldValue(
        display_name="Medical Devices", value="Medical Devices", count=194887
    ),
    FilterFieldValue(
        display_name="Medical Practice", value="Medical Practice", count=2998331
    ),
    FilterFieldValue(
        display_name="Mental Health Care", value="Mental Health Care", count=125740
    ),
    FilterFieldValue(display_name="Military", value="Military", count=11119),
    FilterFieldValue(
        display_name="Mining & Metals", value="Mining & Metals", count=460834
    ),
    FilterFieldValue(
        display_name="Motion Pictures And Film",
        value="Motion Pictures And Film",
        count=158177,
    ),
    FilterFieldValue(
        display_name="Museums And Institutions",
        value="Museums And Institutions",
        count=87756,
    ),
    FilterFieldValue(display_name="Music", value="Music", count=177072),
    FilterFieldValue(display_name="Nanotechnology", value="Nanotechnology", count=6674),
    FilterFieldValue(display_name="Newspapers", value="Newspapers", count=37641),
    FilterFieldValue(
        display_name="Non-Profit Organization Management",
        value="Non-Profit Organization Management",
        count=559646,
    ),
    FilterFieldValue(display_name="Oil & Energy", value="Oil & Energy", count=204318),
    FilterFieldValue(display_name="Online Media", value="Online Media", count=150865),
    FilterFieldValue(
        display_name="Outsourcing/Offshoring",
        value="Outsourcing/Offshoring",
        count=64337,
    ),
    FilterFieldValue(
        display_name="Package/Freight Delivery",
        value="Package/Freight Delivery",
        count=251308,
    ),
    FilterFieldValue(
        display_name="Packaging And Containers",
        value="Packaging And Containers",
        count=56492,
    ),
    FilterFieldValue(
        display_name="Paper & Forest Products",
        value="Paper & Forest Products",
        count=198758,
    ),
    FilterFieldValue(
        display_name="Performing Arts", value="Performing Arts", count=377473
    ),
    FilterFieldValue(
        display_name="Pharmaceuticals", value="Pharmaceuticals", count=114468
    ),
    FilterFieldValue(display_name="Philanthropy", value="Philanthropy", count=37601),
    FilterFieldValue(display_name="Photography", value="Photography", count=233366),
    FilterFieldValue(display_name="Plastics", value="Plastics", count=173740),
    FilterFieldValue(
        display_name="Political Organization",
        value="Political Organization",
        count=32222,
    ),
    FilterFieldValue(
        display_name="Primary/Secondary Education",
        value="Primary/Secondary Education",
        count=200770,
    ),
    FilterFieldValue(display_name="Printing", value="Printing", count=142837),
    FilterFieldValue(
        display_name="Professional Training & Coaching",
        value="Professional Training & Coaching",
        count=409814,
    ),
    FilterFieldValue(
        display_name="Program Development", value="Program Development", count=42785
    ),
    FilterFieldValue(display_name="Public Policy", value="Public Policy", count=15263),
    FilterFieldValue(
        display_name="Public Relations And Communications",
        value="Public Relations And Communications",
        count=151190,
    ),
    FilterFieldValue(display_name="Public Safety", value="Public Safety", count=35645),
    FilterFieldValue(display_name="Publishing", value="Publishing", count=303282),
    FilterFieldValue(
        display_name="Railroad Manufacture", value="Railroad Manufacture", count=13357
    ),
    FilterFieldValue(display_name="Ranching", value="Ranching", count=184449),
    FilterFieldValue(display_name="Real Estate", value="Real Estate", count=3357482),
    FilterFieldValue(
        display_name="Recreational Facilities And Services",
        value="Recreational Facilities And Services",
        count=276282,
    ),
    FilterFieldValue(
        display_name="Religious Institutions",
        value="Religious Institutions",
        count=283985,
    ),
    FilterFieldValue(
        display_name="Renewables & Environment",
        value="Renewables & Environment",
        count=153640,
    ),
    FilterFieldValue(display_name="Research", value="Research", count=320485),
    FilterFieldValue(display_name="Restaurants", value="Restaurants", count=1632943),
    FilterFieldValue(display_name="Retail", value="Retail", count=2633336),
    FilterFieldValue(
        display_name="Security And Investigations",
        value="Security And Investigations",
        count=148372,
    ),
    FilterFieldValue(
        display_name="Semiconductors", value="Semiconductors", count=52517
    ),
    FilterFieldValue(display_name="Shipbuilding", value="Shipbuilding", count=36420),
    FilterFieldValue(
        display_name="Sporting Goods", value="Sporting Goods", count=47182
    ),
    FilterFieldValue(display_name="Sports", value="Sports", count=515522),
    FilterFieldValue(
        display_name="Staffing And Recruiting",
        value="Staffing And Recruiting",
        count=275950,
    ),
    FilterFieldValue(display_name="Supermarkets", value="Supermarkets", count=266327),
    FilterFieldValue(
        display_name="Telecommunications", value="Telecommunications", count=250863
    ),
    FilterFieldValue(display_name="Textiles", value="Textiles", count=209573),
    FilterFieldValue(display_name="Think Tanks", value="Think Tanks", count=30745),
    FilterFieldValue(display_name="Tobacco", value="Tobacco", count=23055),
    FilterFieldValue(
        display_name="Translation And Localization",
        value="Translation And Localization",
        count=39234,
    ),
    FilterFieldValue(
        display_name="Transportation/Trucking/Railroad",
        value="Transportation/Trucking/Railroad",
        count=567041,
    ),
    FilterFieldValue(display_name="Utilities", value="Utilities", count=226095),
    FilterFieldValue(
        display_name="Venture Capital & Private Equity",
        value="Venture Capital & Private Equity",
        count=65328,
    ),
    FilterFieldValue(display_name="Veterinary", value="Veterinary", count=239376),
    FilterFieldValue(display_name="Warehousing", value="Warehousing", count=44149),
    FilterFieldValue(display_name="Wholesale", value="Wholesale", count=914405),
    FilterFieldValue(
        display_name="Wine And Spirits", value="Wine And Spirits", count=78980
    ),
    FilterFieldValue(display_name="Wireless", value="Wireless", count=14249),
    FilterFieldValue(
        display_name="Writing And Editing", value="Writing And Editing", count=92111
    ),
]

COMPANY_COMPANY_NAME_FILTER_FIELD_VALUES = [
    FilterFieldValue(display_name="Google", value="Google", count=28),
    FilterFieldValue(display_name="Microsoft", value="Microsoft", count=10),
    FilterFieldValue(display_name="Amazon", value="Amazon", count=18),
    FilterFieldValue(display_name="Apple", value="Apple", count=12),
    FilterFieldValue(display_name="Meta", value="Meta", count=39),
    FilterFieldValue(display_name="IBM", value="IBM", count=9),
    FilterFieldValue(display_name="Oracle", value="Oracle", count=12),
    FilterFieldValue(display_name="Salesforce", value="Salesforce", count=9),
    FilterFieldValue(display_name="Nvidia", value="Nvidia", count=2),
    FilterFieldValue(
        display_name="Intel Corporation", value="Intel Corporation", count=1
    ),
]

COMPANY_COUNTRY_FILTER_FIELD_VALUES = [
    FilterFieldValue(
        display_name="United States", value="United States", count=17282476
    ),
    FilterFieldValue(
        display_name="United Kingdom", value="United Kingdom", count=5823287
    ),
    FilterFieldValue(display_name="Germany", value="Germany", count=3308218),
    FilterFieldValue(display_name="Japan", value="Japan", count=652437),
    FilterFieldValue(display_name="Canada", value="Canada", count=1237286),
    FilterFieldValue(display_name="France", value="France", count=9269110),
    FilterFieldValue(display_name="Australia", value="Australia", count=1150269),
    FilterFieldValue(display_name="Denmark", value="Denmark", count=135281),
    FilterFieldValue(display_name="Singapore", value="Singapore", count=109142),
    FilterFieldValue(display_name="Austria", value="Austria", count=182448),
]

# NOTE: The state filter field values are not used in UI
COMPANY_STATE_FILTER_FIELD_VALUES = [
    FilterFieldValue(
        display_name="New York, New York", value="New York, New York", count=1123784
    ),
    FilterFieldValue(
        display_name="Los Angeles, California",
        value="Los Angeles, California",
        count=1166810,
    ),
    FilterFieldValue(
        display_name="Chicago, Illinois", value="Chicago, Illinois", count=470550
    ),
    FilterFieldValue(
        display_name="Houston, Texas", value="Houston, Texas", count=412204
    ),
    FilterFieldValue(
        display_name="Phoenix, Arizona", value="Phoenix, Arizona", count=218401
    ),
    FilterFieldValue(
        display_name="Philadelphia, Pennsylvania",
        value="Philadelphia, Pennsylvania",
        count=318997,
    ),
    FilterFieldValue(
        display_name="San Antonio, Texas", value="San Antonio, Texas", count=116902
    ),
    FilterFieldValue(
        display_name="San Diego, California",
        value="San Diego, California",
        count=244311,
    ),
    FilterFieldValue(display_name="Dallas, Texas", value="Dallas, Texas", count=460606),
    FilterFieldValue(
        display_name="San Jose, California", value="San Jose, California", count=127552
    ),
    FilterFieldValue(display_name="Austin, Texas", value="Austin, Texas", count=169209),
    FilterFieldValue(
        display_name="Jacksonville, Florida", value="Jacksonville, Florida", count=62092
    ),
    FilterFieldValue(
        display_name="Fort Worth, Texas", value="Fort Worth, Texas", count=0
    ),
    FilterFieldValue(
        display_name="Columbus, Ohio", value="Columbus, Ohio", count=107335
    ),
    FilterFieldValue(
        display_name="San Francisco, California",
        value="San Francisco, California",
        count=464492,
    ),
    FilterFieldValue(
        display_name="Charlotte, North Carolina",
        value="Charlotte, North Carolina",
        count=110636,
    ),
    FilterFieldValue(
        display_name="Indianapolis, Indiana", value="Indianapolis, Indiana", count=84938
    ),
    FilterFieldValue(
        display_name="Seattle, Washington", value="Seattle, Washington", count=265255
    ),
    FilterFieldValue(
        display_name="Denver, Colorado", value="Denver, Colorado", count=159450
    ),
    FilterFieldValue(
        display_name="Boston, Massachusetts",
        value="Boston, Massachusetts",
        count=288832,
    ),
]

# NOTE: The city filter field values are not used in UI
COMPANY_CITY_FILTER_FIELD_VALUES = [
    FilterFieldValue(display_name="New York", value="New York", count=318268),
    FilterFieldValue(display_name="Los Angeles", value="Los Angeles", count=376953),
    FilterFieldValue(display_name="Chicago", value="Chicago", count=184656),
    FilterFieldValue(display_name="Houston", value="Houston", count=238131),
    FilterFieldValue(display_name="Phoenix", value="Phoenix", count=80519),
    FilterFieldValue(display_name="Philadelphia", value="Philadelphia", count=106109),
    FilterFieldValue(display_name="San Antonio", value="San Antonio", count=87978),
    FilterFieldValue(display_name="San Diego", value="San Diego", count=138500),
    FilterFieldValue(display_name="Dallas", value="Dallas", count=133920),
    FilterFieldValue(display_name="San Jose", value="San Jose", count=54090),
]

COMPANY_REGION_FILTER_FIELD_VALUES = [
    FilterFieldValue(display_name="California", value="California", count=2707230),
    FilterFieldValue(display_name="Texas", value="Texas", count=1602562),
    FilterFieldValue(display_name="Florida", value="Florida", count=1132440),
    FilterFieldValue(display_name="New York", value="New York", count=1183101),
    FilterFieldValue(display_name="Pennsylvania", value="Pennsylvania", count=575268),
    FilterFieldValue(display_name="Illinois", value="Illinois", count=595814),
    FilterFieldValue(display_name="Ohio", value="Ohio", count=575439),
    FilterFieldValue(display_name="Georgia", value="Georgia", count=485482),
    FilterFieldValue(
        display_name="North Carolina", value="North Carolina", count=411753
    ),
    FilterFieldValue(display_name="Michigan", value="Michigan", count=451479),
]

COMPANY_POSTAL_CODE_FILTER_FIELD_VALUES = [
    FilterFieldValue(display_name="10001", value="10001", count=24100),  # New York
    FilterFieldValue(display_name="90001", value="90001", count=3685),  # Los Angeles
    FilterFieldValue(display_name="60601", value="60601", count=6209),  # Chicago
    FilterFieldValue(display_name="77001", value="77001", count=421),  # Houston
    FilterFieldValue(display_name="85001", value="85001", count=282),  # Phoenix
    FilterFieldValue(display_name="19101", value="19101", count=268),  # Philadelphia
    FilterFieldValue(display_name="78201", value="78201", count=1909),  # San Antonio
    FilterFieldValue(display_name="92101", value="92101", count=13480),  # San Diego
    FilterFieldValue(display_name="75201", value="75201", count=8772),  # Dallas
    FilterFieldValue(display_name="95101", value="95101", count=47),  # San Jose
]


COMPANY_NUMBER_OF_EMPLOYEE_FILTER_FIELD_VALUES = [
    FilterFieldValue(display_name="1-10", value="1-10", count=57530491),
    FilterFieldValue(display_name="11-50", value="11-50", count=9955943),
    FilterFieldValue(display_name="51-200", value="51-200", count=2539867),
    FilterFieldValue(display_name="201-500", value="201-500", count=852041),
    FilterFieldValue(display_name="501-1000", value="501-1000", count=259066),
    FilterFieldValue(display_name="1001-5000", value="1001-5000", count=207314),
    FilterFieldValue(display_name="5001-10000", value="5001-10000", count=52851),
    FilterFieldValue(display_name="10001+", value="10001+", count=82986),
]

COMPANY_INFERRED_REVENUE_FILTER_FIELD_VALUES = [
    FilterFieldValue(display_name="$0-$1m", value="$0-$1m", count=19757962),
    FilterFieldValue(display_name="$1m-$10m", value="$1m-$10m", count=3662503),
    FilterFieldValue(display_name="$10m-$25m", value="$10m-$25m", count=632727),
    FilterFieldValue(display_name="$25m-$50m", value="$25m-$50m", count=290884),
    FilterFieldValue(display_name="$50m-$100m", value="$50m-$100m", count=179185),
    FilterFieldValue(display_name="$100m-$250m", value="$100m-$250m", count=143107),
    FilterFieldValue(display_name="$250m-$500m", value="$250m-$500m", count=63863),
    FilterFieldValue(display_name="$500m-$1b", value="$500m-$1b", count=36179),
    FilterFieldValue(display_name="$1b-$10b", value="$1b-$10b", count=38074),
    FilterFieldValue(display_name="$10b+", value="$10b+", count=4457),
]

COMPANY_LATEST_FUNDING_STAGE_VALUES = [
    FilterFieldValue(display_name="Angel", value="angel", count=7133),
    FilterFieldValue(
        display_name="Convertible Note", value="convertible_note", count=3316
    ),
    FilterFieldValue(
        display_name="Corporate Round", value="corporate_round", count=3169
    ),
    FilterFieldValue(
        display_name="Debt Financing", value="debt_financing", count=11163
    ),
    FilterFieldValue(
        display_name="Equity Crowdfunding", value="equity_crowdfunding", count=3428
    ),
    FilterFieldValue(display_name="Funding Round", value="funding_round", count=0),
    FilterFieldValue(display_name="Grant", value="grant", count=27488),
    FilterFieldValue(
        display_name="Initial Coin Offering", value="initial_coin_offering", count=481
    ),
    FilterFieldValue(
        display_name="Non Equity Assistance", value="non_equity_assistance", count=9838
    ),
    FilterFieldValue(display_name="Post IPO Debt", value="post_ipo_debt", count=3420),
    FilterFieldValue(
        display_name="Post IPO Equity", value="post_ipo_equity", count=7112
    ),
    FilterFieldValue(
        display_name="Post IPO Secondary", value="post_ipo_secondary", count=334
    ),
    FilterFieldValue(display_name="Pre Seed", value="pre_seed", count=25498),
    FilterFieldValue(
        display_name="Private Equity", value="private_equity", count=14184
    ),
    FilterFieldValue(
        display_name="Product Crowdfunding", value="product_crowdfunding", count=416
    ),
    FilterFieldValue(
        display_name="Secondary Market", value="secondary_market", count=928
    ),
    FilterFieldValue(display_name="Seed", value="seed", count=65902),
    FilterFieldValue(display_name="Series A", value="series_a", count=19820),
    FilterFieldValue(display_name="Series B", value="series_b", count=8869),
    FilterFieldValue(display_name="Series C", value="series_c", count=3946),
    FilterFieldValue(display_name="Series D", value="series_d", count=1546),
    FilterFieldValue(display_name="Series E", value="series_e", count=595),
    FilterFieldValue(display_name="Series F", value="series_f", count=227),
    FilterFieldValue(display_name="Series G", value="series_g", count=62),
    FilterFieldValue(display_name="Series H", value="series_h", count=29),
    FilterFieldValue(display_name="Series I", value="series_i", count=2),
    FilterFieldValue(display_name="Series J", value="series_j", count=1),
    FilterFieldValue(
        display_name="Series Unknown", value="series_unknown", count=37129
    ),
    FilterFieldValue(display_name="Undisclosed", value="undisclosed", count=3253),
]

COMPANY_TECHNOLOGY_FILTER_FIELD_VALUES = [
    FilterFieldValue(
        display_name="Mobile Friendly", value="Mobile Friendly", count=1000000
    ),
    FilterFieldValue(
        display_name="WordPress.org", value="WordPress.org", count=1000000
    ),
    FilterFieldValue(
        display_name="Google Font API", value="Google Font API", count=1000000
    ),
    FilterFieldValue(
        display_name="Google Tag Manager", value="Google Tag Manager", count=1000000
    ),
    FilterFieldValue(display_name="Apache", value="Apache", count=1000000),
    FilterFieldValue(display_name="Outlook", value="Outlook", count=1000000),
    FilterFieldValue(display_name="Nginx", value="Nginx", count=1000000),
    FilterFieldValue(display_name="Gmail", value="Gmail", count=1000000),
    FilterFieldValue(display_name="Google Apps", value="Google Apps", count=1000000),
    FilterFieldValue(
        display_name="Google Analytics", value="Google Analytics", count=1000000
    ),
    FilterFieldValue(display_name="reCAPTCHA", value="reCAPTCHA", count=1000000),
    FilterFieldValue(
        display_name="Bootstrap Framework", value="Bootstrap Framework", count=1000000
    ),
    FilterFieldValue(display_name="Amazon AWS", value="Amazon AWS", count=1000000),
    FilterFieldValue(display_name="Remote", value="Remote", count=1000000),
    FilterFieldValue(
        display_name="Microsoft Office 365", value="Microsoft Office 365", count=1000000
    ),
    FilterFieldValue(
        display_name="Facebook Login (Connect)",
        value="Facebook Login (Connect)",
        count=1000000,
    ),
    FilterFieldValue(display_name="YouTube", value="YouTube", count=1000000),
    FilterFieldValue(
        display_name="Facebook Widget", value="Facebook Widget", count=1000000
    ),
    FilterFieldValue(
        display_name="Cloudflare DNS", value="Cloudflare DNS", count=1000000
    ),
    FilterFieldValue(
        display_name="Google Cloud Hosting", value="Google Cloud Hosting", count=1000000
    ),
    FilterFieldValue(
        display_name="Facebook Custom Audiences",
        value="Facebook Custom Audiences",
        count=1000000,
    ),
    FilterFieldValue(
        display_name="CloudFlare Hosting", value="CloudFlare Hosting", count=1000000
    ),
    FilterFieldValue(display_name="Varnish", value="Varnish", count=1000000),
    FilterFieldValue(display_name="AI", value="AI", count=1000000),
    FilterFieldValue(display_name="Google Maps", value="Google Maps", count=1000000),
    FilterFieldValue(display_name="Vimeo", value="Vimeo", count=1000000),
    FilterFieldValue(display_name="Wix", value="Wix", count=1000000),
    FilterFieldValue(display_name="Android", value="Android", count=1000000),
    FilterFieldValue(display_name="Woo Commerce", value="Woo Commerce", count=1000000),
    FilterFieldValue(display_name="Typekit", value="Typekit", count=1000000),
    FilterFieldValue(display_name="DoubleClick", value="DoubleClick", count=1000000),
    FilterFieldValue(display_name="Basis", value="Basis", count=1000000),
    FilterFieldValue(
        display_name="Google AdSense", value="Google AdSense", count=1000000
    ),
    FilterFieldValue(
        display_name="DoubleClick Conversion",
        value="DoubleClick Conversion",
        count=1000000,
    ),
    FilterFieldValue(
        display_name="Google Dynamic Remarketing",
        value="Google Dynamic Remarketing",
        count=1000000,
    ),
    FilterFieldValue(
        display_name="Google Maps (Non Paid Users)",
        value="Google Maps (Non Paid Users)",
        count=1000000,
    ),
    FilterFieldValue(display_name="Render", value="Render", count=1000000),
    FilterFieldValue(display_name="Circle", value="Circle", count=1000000),
    FilterFieldValue(display_name="Python", value="Python", count=1000000),
    FilterFieldValue(display_name="ASP.NET", value="ASP.NET", count=1000000),
    FilterFieldValue(
        display_name="Gravity Forms", value="Gravity Forms", count=1000000
    ),
    FilterFieldValue(display_name="Reviews", value="Reviews", count=1000000),
    FilterFieldValue(
        display_name="GoDaddy Hosting", value="GoDaddy Hosting", count=1000000
    ),
    FilterFieldValue(display_name="Flutter", value="Flutter", count=1000000),
    FilterFieldValue(display_name="SharePoint", value="SharePoint", count=1000000),
    FilterFieldValue(
        display_name="Microsoft-IIS", value="Microsoft-IIS", count=1000000
    ),
    FilterFieldValue(display_name="IoT", value="IoT", count=1000000),
    FilterFieldValue(
        display_name="Squarespace ECommerce",
        value="Squarespace ECommerce",
        count=1000000,
    ),
    FilterFieldValue(display_name="Route 53", value="Route 53", count=1000000),
    FilterFieldValue(display_name="Shopify", value="Shopify", count=1000000),
    FilterFieldValue(display_name="Node.js", value="Node.js", count=1000000),
    FilterFieldValue(display_name="Shutterstock", value="Shutterstock", count=1000000),
    FilterFieldValue(display_name="Hubspot", value="Hubspot", count=1000000),
    FilterFieldValue(display_name="Google Play", value="Google Play", count=1000000),
    FilterFieldValue(display_name="Hotjar", value="Hotjar", count=1000000),
    FilterFieldValue(
        display_name="Adobe Media Optimizer",
        value="Adobe Media Optimizer",
        count=1000000,
    ),
    FilterFieldValue(
        display_name="Google AdWords Conversion",
        value="Google AdWords Conversion",
        count=1000000,
    ),
    FilterFieldValue(display_name="SAP", value="SAP", count=1000000),
    FilterFieldValue(display_name="Ubuntu", value="Ubuntu", count=1000000),
    FilterFieldValue(display_name="NSOne", value="NSOne", count=1000000),
    FilterFieldValue(
        display_name="Cedexis Radar", value="Cedexis Radar", count=1000000
    ),
    FilterFieldValue(display_name="Quantcast", value="Quantcast", count=1000000),
    FilterFieldValue(display_name="TikTok", value="TikTok", count=1000000),
    FilterFieldValue(display_name="Google Plus", value="Google Plus", count=1000000),
    FilterFieldValue(display_name="Stripe", value="Stripe", count=1000000),
    FilterFieldValue(display_name="DigitalOcean", value="DigitalOcean", count=1000000),
    FilterFieldValue(display_name="Sendgrid", value="Sendgrid", count=1000000),
    FilterFieldValue(
        display_name="Linkedin Marketing Solutions",
        value="Linkedin Marketing Solutions",
        count=1000000,
    ),
    FilterFieldValue(
        display_name="Google translate API", value="Google translate API", count=1000000
    ),
    FilterFieldValue(display_name="Paypal", value="Paypal", count=1000000),
    FilterFieldValue(display_name="Blue Host", value="Blue Host", count=1000000),
    FilterFieldValue(display_name="Yelp", value="Yelp", count=1000000),
    FilterFieldValue(display_name="New Relic", value="New Relic", count=1000000),
    FilterFieldValue(display_name="MailChimp", value="MailChimp", count=1000000),
    FilterFieldValue(
        display_name="Google translate widget",
        value="Google translate widget",
        count=1000000,
    ),
    FilterFieldValue(display_name="Gusto", value="Gusto", count=1000000),
    FilterFieldValue(display_name="Micro", value="Micro", count=1000000),
    FilterFieldValue(display_name="AddThis", value="AddThis", count=1000000),
    FilterFieldValue(
        display_name="MailChimp SPF", value="MailChimp SPF", count=1000000
    ),
    FilterFieldValue(
        display_name="Rackspace MailGun", value="Rackspace MailGun", count=1000000
    ),
    FilterFieldValue(display_name="Amazon SES", value="Amazon SES", count=1000000),
    FilterFieldValue(display_name="Bing Ads", value="Bing Ads", count=1000000),
    FilterFieldValue(
        display_name="Data Analytics", value="Data Analytics", count=1000000
    ),
    FilterFieldValue(display_name="React Native", value="React Native", count=1000000),
    FilterFieldValue(
        display_name="Wordpress.com", value="Wordpress.com", count=1000000
    ),
    FilterFieldValue(
        display_name="JQuery 1.11.1", value="JQuery 1.11.1", count=1000000
    ),
    FilterFieldValue(
        display_name="Ruby On Rails", value="Ruby On Rails", count=1000000
    ),
    FilterFieldValue(
        display_name="Mailchimp Mandrill", value="Mailchimp Mandrill", count=1000000
    ),
    FilterFieldValue(display_name="OpenSSL", value="OpenSSL", count=1000000),
    FilterFieldValue(
        display_name="Microsoft Azure Hosting",
        value="Microsoft Azure Hosting",
        count=1000000,
    ),
    FilterFieldValue(display_name="ShareThis", value="ShareThis", count=1000000),
    FilterFieldValue(display_name="Multilingual", value="Multilingual", count=1000000),
    FilterFieldValue(display_name="Weebly", value="Weebly", count=1000000),
    FilterFieldValue(
        display_name="Facebook Like Button", value="Facebook Like Button", count=1000000
    ),
    FilterFieldValue(display_name="SendInBlue", value="SendInBlue", count=1000000),
    FilterFieldValue(display_name="Salesforce", value="Salesforce", count=1000000),
    FilterFieldValue(display_name="MailJet", value="MailJet", count=1000000),
    FilterFieldValue(
        display_name="Facebook Comments", value="Facebook Comments", count=1000000
    ),
    FilterFieldValue(display_name="Trustpilot", value="Trustpilot", count=1000000),
    FilterFieldValue(display_name="Centro", value="Centro", count=1000000),
]


# Mapping of filter field types to their respective values
FILTER_FIELD_TO_VALUES = {
    # Person filter fields
    (
        ProspectingSearchQueryType.PEOPLE,
        ProspectingFilterFieldType.location,
    ): PERSON_LOCATION_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.PEOPLE,
        ProspectingFilterFieldType.person_title,
    ): PERSON_TITLE_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.PEOPLE,
        ProspectingFilterFieldType.linkedin_industry,
    ): PERSON_LINKEDIN_INDUSTRY_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.PEOPLE,
        ProspectingFilterFieldType.company_name,
    ): PERSON_COMPANY_NAME_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.PEOPLE,
        ProspectingFilterFieldType.number_of_employees,
    ): PERSON_NUMBER_OF_EMPLOYEE_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.PEOPLE,
        ProspectingFilterFieldType.person_seniorities,
    ): PERSON_SENIORITIES_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.PEOPLE,
        ProspectingFilterFieldType.country,
    ): PERSON_COUNTRY_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.PEOPLE,
        ProspectingFilterFieldType.state,
    ): PERSON_STATE_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.PEOPLE,
        ProspectingFilterFieldType.city,
    ): PERSON_CITY_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.PEOPLE,
        ProspectingFilterFieldType.region,
    ): PERSON_REGION_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.PEOPLE,
        ProspectingFilterFieldType.postal_code,
    ): PERSON_POSTAL_CODE_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.PEOPLE,
        ProspectingFilterFieldType.job_title_role,
    ): PERSON_JOB_TITLE_ROLE_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.PEOPLE,
        ProspectingFilterFieldType.technology,
    ): PERSON_TECHNOLOGY_FILTER_FIELD_VALUES,
    # Company filter fields
    (
        ProspectingSearchQueryType.COMPANY,
        ProspectingFilterFieldType.company_name,
    ): COMPANY_COMPANY_NAME_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.COMPANY,
        ProspectingFilterFieldType.location,
    ): COMPANY_LOCATION_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.COMPANY,
        ProspectingFilterFieldType.number_of_employees,
    ): COMPANY_NUMBER_OF_EMPLOYEE_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.COMPANY,
        ProspectingFilterFieldType.linkedin_industry,
    ): COMPANY_LINKEDIN_INDUSTRY_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.COMPANY,
        ProspectingFilterFieldType.inferred_revenue,
    ): COMPANY_INFERRED_REVENUE_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.COMPANY,
        ProspectingFilterFieldType.country,
    ): COMPANY_COUNTRY_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.COMPANY,
        ProspectingFilterFieldType.state,
    ): COMPANY_STATE_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.COMPANY,
        ProspectingFilterFieldType.city,
    ): COMPANY_CITY_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.COMPANY,
        ProspectingFilterFieldType.region,
    ): COMPANY_REGION_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.COMPANY,
        ProspectingFilterFieldType.postal_code,
    ): COMPANY_POSTAL_CODE_FILTER_FIELD_VALUES,
    (
        ProspectingSearchQueryType.COMPANY,
        ProspectingFilterFieldType.latest_funding_stage,
    ): COMPANY_LATEST_FUNDING_STAGE_VALUES,
    (
        ProspectingSearchQueryType.COMPANY,
        ProspectingFilterFieldType.technology,
    ): COMPANY_TECHNOLOGY_FILTER_FIELD_VALUES,
}

# Dynamically construct SAMPLE_VALUES using get_filter_field_list_by_pdl
SAMPLE_VALUES: dict[
    ProspectingSearchQueryType, dict[ProspectingFilterFieldType, list[FilterFieldValue]]
] = {
    query_type: {
        filter_field: FILTER_FIELD_TO_VALUES.get((query_type, filter_field), [])
        for filter_field in filter_fields
    }
    for query_type, filter_fields in ProspectingFilterFieldType.get_filter_field_list_by_pdl().items()
}
