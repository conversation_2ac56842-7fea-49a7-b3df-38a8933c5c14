import re
from datetime import datetime
from typing import Any
from uuid import UUID

from salestech_be.db.models.core.base import <PERSON>umn, JsonColumn, TableModel
from salestech_be.db.models.core.constants import TableName
from salestech_be.db.models.search_query import ProspectingSearchQueryType
from salestech_be.util.enum_util import NameValueStrEnum


class ProspectingFilterFieldDateType(NameValueStrEnum):
    option = "option"
    range = "range"
    text = "text"
    date_range = "date_range"
    number_range = "number_range"


class ProspectingFilterFieldType(NameValueStrEnum):
    location = "location"
    person_title = "person_title"
    linkedin_industry = "linkedin_industry"
    company_name = "company_name"
    number_of_employees = "number_of_employees"
    person_seniorities = "person_seniorities"
    inferred_revenue = "inferred_revenue"
    company_ids = "company_ids"
    website = "website"
    country = "country"
    state = "state"
    city = "city"
    region = "region"
    postal_code = "postal_code"
    latest_funding_stage = "latest_funding_stage"
    latest_funding_date = "latest_funding_date"
    job_title_role = "job_title_role"
    technology = "technology"
    employee_growth_rate_12_month = "employee_growth_rate_12_month"
    account_ids = "account_ids"
    sequence_ids = "sequence_ids"
    domain_object_list_contact_ids = "domain_object_list_contact_ids"
    domain_object_list_account_ids = "domain_object_list_account_ids"
    person_id = "person_id"
    company_id = "company_id"
    company_url = "company_url"

    @classmethod
    def get_filter_field_list_by_pdl(
        cls,
    ) -> dict[ProspectingSearchQueryType, list["ProspectingFilterFieldType"]]:
        return {
            ProspectingSearchQueryType.PEOPLE: [
                cls.location,
                cls.person_title,
                cls.linkedin_industry,
                cls.company_name,
                cls.number_of_employees,
                cls.person_seniorities,
                cls.country,
                cls.state,
                cls.city,
                cls.region,
                cls.postal_code,
                cls.job_title_role,
                cls.technology,
            ],
            ProspectingSearchQueryType.COMPANY: [
                cls.company_name,
                cls.location,
                cls.number_of_employees,
                cls.linkedin_industry,
                cls.inferred_revenue,
                cls.country,
                cls.state,
                cls.city,
                cls.region,
                cls.postal_code,
                cls.latest_funding_stage,
                cls.latest_funding_date,
                cls.technology,
                cls.employee_growth_rate_12_month,
                cls.company_url,
            ],
        }

    @property
    def is_searchable(self) -> bool:
        return self in (
            ProspectingFilterFieldType.location,
            ProspectingFilterFieldType.linkedin_industry,
            ProspectingFilterFieldType.person_title,
            ProspectingFilterFieldType.company_name,
            ProspectingFilterFieldType.country,
            ProspectingFilterFieldType.region,
            ProspectingFilterFieldType.company_url,
            ProspectingFilterFieldType.technology,
        )

    @property
    def can_autocomplete_from_db(self) -> bool:
        return self in (ProspectingFilterFieldType.technology,)

    @property
    def value_format_list(self) -> list[str] | None:
        match self:
            case ProspectingFilterFieldType.latest_funding_date:
                return ["YYYY-MM-DD", "YYYY-MM", "YYYY"]
            case ProspectingFilterFieldType.employee_growth_rate_12_month:
                return ["N.N-N.N", "N.N"]
            case _:
                return None

    def validate_format(self, value: str) -> bool:
        """
        Validate if the value matches the format requirement

        Args:
            value: The value to validate

        Returns:
            bool: Whether the value matches the format requirement
        """

        format_list = self.value_format_list
        if not format_list:
            # If there is no format requirement, any value is valid
            return True

        # Parse the format requirement and validate
        match self:
            case ProspectingFilterFieldType.latest_funding_date:
                patterns = [
                    r"^\d{4}-\d{2}-\d{2}$",  # YYYY-MM-DD
                    r"^\d{4}-\d{2}$",  # YYYY-MM
                    r"^\d{4}$",  # YYYY
                ]
                return any(re.match(pattern, value) for pattern in patterns)
            case ProspectingFilterFieldType.employee_growth_rate_12_month:
                # Patterns: single number (1), range (1-2), or range with decimals (1-2.999)
                patterns = [
                    r"^\d+(\.\d+)?$",  # Single number with optional decimal
                    r"^\d+(\.\d+)?-\d+(\.\d+)?$",  # Range where both numbers can have decimals
                ]
                return any(re.match(pattern, value) for pattern in patterns)

        # By default, there is no specific validation rule, return True
        return True

    @property
    def data_type(self) -> ProspectingFilterFieldDateType:
        match self:
            case ProspectingFilterFieldType.latest_funding_date:
                return ProspectingFilterFieldDateType.date_range
            case ProspectingFilterFieldType.employee_growth_rate_12_month:
                return ProspectingFilterFieldDateType.number_range
            case (
                ProspectingFilterFieldType.technology
                | ProspectingFilterFieldType.company_url
            ):
                return ProspectingFilterFieldDateType.text
            case _:
                return ProspectingFilterFieldDateType.option


class ProspectingTag(TableModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    table_name = TableName.prospecting_tag
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    ext_id: Column[str | None] = None
    kind: Column[ProspectingFilterFieldType]
    name: Column[str]
    value: Column[str]
    category: Column[str | None] = None
    data: JsonColumn[dict[str, Any] | None] = None  # type: ignore[explicit-any] # TODO: fix-any-annotation
    created_at: Column[datetime]
    updated_at: Column[datetime]
