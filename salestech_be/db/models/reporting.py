from __future__ import annotations

import re
from typing import Self
from uuid import UUID

from pydantic import field_validator, model_validator

from salestech_be.core.reporting.type.layout import (
    ChartLayoutConfig,
    DashboardChartLayoutConfig,
    DashboardLayoutConfig,
)
from pydantic import ConfigDict, Field

from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.core.reporting.type.query_config import QueryConfig
from salestech_be.db.models.core.base import Column, JsonColumn, TableBoundedModel, TableModel
from salestech_be.db.models.core.constants import TableName
from salestech_be.util.enum_util import NameValueStrEnum
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now


class ReportingDatasetSource(NameValueStrEnum):
    DATABASE = "DATABASE"
    CUSTOM = "CUSTOM"
    INTERNAL = "INTERNAL"
    TEMPLATE = "TEMPLATE"


class ReportingDatasetType(NameValueStrEnum):
    TABLE = "TABLE"
    QUERY = "QUERY"
    SQL = "SQL"


class ReportingDataset(TableModel):
    table_name = TableName.reporting_dataset
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    name: Column[str]
    description: Column[str | None] = None
    source: Column[ReportingDatasetSource]
    type: Column[ReportingDatasetType]
    table_reference: Column[str | None] = None
    query_config: JsonColumn[QueryConfig | None] = None
    sql_statement: Column[str | None] = None

    organization_id: Column[UUID | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID | None] = None
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None

    @field_validator("table_reference", mode="after")
    @classmethod
    def validate_table_reference(cls, v: str | None) -> str | None:
        if v is None:
            return v
        # Check for special characters or SQL injection risks
        if v and not re.match(r"^[a-zA-Z0-9_\.]+$", v):
            raise ValueError(
                "table_reference can only contain alphanumeric characters, underscores and dots"
            )
        return v

    @model_validator(mode="after")
    def validate_dataset_config(self) -> Self:
        if self.type == ReportingDatasetType.TABLE and self.table_reference is None:
            raise ValueError("table_reference must be provided when type is TABLE")

        if self.type == ReportingDatasetType.QUERY and self.query_config is None:
            raise ValueError("query_config must be provided when type is QUERY")

        if self.type == ReportingDatasetType.SQL and self.sql_statement is None:
            raise ValueError("sql_statement must be provided when type is SQL")

        return self


class ReportingDatasetFieldDataType(NameValueStrEnum):
    STRING = "STRING"
    NUMBER = "NUMBER"
    BOOLEAN = "BOOLEAN"
    DATE = "DATE"
    DATETIME = "DATETIME"
    FLOAT = "FLOAT"
    INTEGER = "INTEGER"
    DECIMAL = "DECIMAL"
    TEXT = "TEXT"
    JSON = "JSON"


class ReportingDatasetField(TableModel):
    table_name = TableName.reporting_dataset_field
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    dataset_id: Column[UUID]
    name: Column[str]
    display_name: Column[str | None] = None
    data_type: Column[ReportingDatasetFieldDataType]

    organization_id: Column[UUID | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID | None] = None
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None


class ReportingDatasetJoinType(NameValueStrEnum):
    LEFT = "LEFT"
    RIGHT = "RIGHT"
    INNER = "INNER"
    OUTER = "OUTER"


class ReportingDatasetRelation(TableModel):
    table_name = TableName.reporting_dataset_relation
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    source_dataset_id: Column[UUID]
    target_dataset_id: Column[UUID]
    source_field_id: Column[UUID]
    target_field_id: Column[UUID]
    join_type: Column[ReportingDatasetJoinType] = ReportingDatasetJoinType.LEFT

    organization_id: Column[UUID | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID | None] = None
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None


class ReportingChart(TableModel):
    table_name = TableName.reporting_chart
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    name: Column[str]
    description: Column[str | None] = None
    dataset_id: Column[UUID]  # Reference to the dataset used by this chart
    layout_config: JsonColumn[ChartLayoutConfig | None] = None
    is_published: Column[bool]

    organization_id: Column[UUID | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID | None] = None
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None


class ReportingDashboard(TableModel):
    table_name = TableName.reporting_dashboard
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    name: Column[str]
    description: Column[str | None] = None
    layout_config: JsonColumn[DashboardLayoutConfig | None] = None

    organization_id: Column[UUID | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID | None] = None
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None


class ReportingDashboardChartAssociation(TableModel):
    table_name = TableName.reporting_dashboard_chart_association
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    dashboard_id: Column[UUID]
    chart_id: Column[UUID]
    layout_config: JsonColumn[DashboardChartLayoutConfig | None] = None

    organization_id: Column[UUID | None] = None
    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID | None] = None
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None


# Update Models
class ReportingDatasetUpdate(TableBoundedModel[ReportingDataset]):
    model_config = ConfigDict(frozen=False)

    name: UnsetAware[str] = UNSET
    description: UnsetAware[str | None] = UNSET
    query_config: UnsetAware[QueryConfig | None] = UNSET
    updated_at: ZoneRequiredDateTime = Field(default_factory=zoned_utc_now)
    updated_by_user_id: UnsetAware[UUID | None] = UNSET


class ReportingChartUpdate(TableBoundedModel[ReportingChart]):
    model_config = ConfigDict(frozen=False)

    name: UnsetAware[str] = UNSET
    description: UnsetAware[str | None] = UNSET
    dataset_id: UnsetAware[UUID] = UNSET
    layout_config: UnsetAware[ChartLayoutConfig | None] = UNSET
    is_published: UnsetAware[bool] = UNSET
    updated_at: ZoneRequiredDateTime = Field(default_factory=zoned_utc_now)
    updated_by_user_id: UnsetAware[UUID | None] = UNSET


class ReportingDashboardUpdate(TableBoundedModel[ReportingDashboard]):
    model_config = ConfigDict(frozen=False)

    name: UnsetAware[str] = UNSET
    description: UnsetAware[str | None] = UNSET
    layout_config: UnsetAware[DashboardLayoutConfig | None] = UNSET
    updated_at: ZoneRequiredDateTime = Field(default_factory=zoned_utc_now)
    updated_by_user_id: UnsetAware[UUID | None] = UNSET


class ReportingDashboardChartAssociationUpdate(TableBoundedModel[ReportingDashboardChartAssociation]):
    model_config = ConfigDict(frozen=False)

    layout_config: UnsetAware[DashboardChartLayoutConfig | None] = UNSET
    updated_at: ZoneRequiredDateTime = Field(default_factory=zoned_utc_now)
    updated_by_user_id: UnsetAware[UUID | None] = UNSET
    deleted_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    deleted_by_user_id: UnsetAware[UUID | None] = UNSET
