"""migrate active users quota to lifetime

Revision ID: 647b6249d493
Revises: 94f0e00d88ce
Create Date: 2025-05-28 21:01:44.497512+00:00

"""

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "647b6249d493"
down_revision: str | tuple[str, ...] | None = "94f0e00d88ce"
branch_labels: set[str] | str | None = None
depends_on: str | None = None


def upgrade() -> None:
    op.execute(
        """
        UPDATE quota_policy
         SET   period = 'LIFETIME'
         WHERE resource = 'USERS'
          AND  period = 'ANNUAL'
          AND  entity_type = 'ORGANIZATION';
        """
    )


def downgrade() -> None:
    pass
