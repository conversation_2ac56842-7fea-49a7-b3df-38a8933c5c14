"""drop the workspace id foreign key constraint for outbound_domain

Revision ID: 36b173c24d49
Revises: 647b6249d493
Create Date: 2025-05-28 22:33:49.227539+00:00

"""

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "36b173c24d49"
down_revision: str | tuple[str, ...] | None = "647b6249d493"
branch_labels: set[str] | str | None = None
depends_on: str | None = None


def upgrade() -> None:
    op.execute(
        "ALTER TABLE outbound_domain DROP CONSTRAINT IF EXISTS fk_outbound_workspace_id;"
    )


def downgrade() -> None:
    pass
