from collections.abc import Mapping
from contextlib import nullcontext
from typing import Any, Literal, NamedTuple
from uuid import UUID, uuid4

from frozendict import frozendict
from sqlalchemy import BindParameter, TextClause, text
from sqlalchemy.dialects.postgresql import JSONB

from salestech_be.common.exception import ResourceNotFoundError
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)
from salestech_be.common.type.patch_request import (
    UNSET,
    UnsetAware,
    specified_or_default,
)
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.models.crm_ai_rec import (
    CrmObjectAiRec,
    CrmObjectAiRecCreate,
    CrmPropertyAiRec,
    CrmPropertyAiRecCreate,
    CrmPropertyAiRecUpdate,
    CrmPropertyMetadata,
    CrmPropertyMetadataCreate,
    CrmPropertyMetadataUpdate,
    CrmPropertyStageSnapshot,
    ParentRecordIds,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none

logger = get_logger()


class AcceptCrmPropertyAiRecResult(NamedTuple):
    ai_rec: CrmPropertyAiRec
    property_metadata: CrmPropertyMetadata


class SobjectNameRecordIdAndFieldPathMappingRequest(NamedTuple):
    record_ids: set[UUID]
    field_paths: set[tuple[str | UUID, ...]]

    def to_sobject_field_paths(self) -> set[str]:
        return {".".join(map(str, fp)) for fp in self.field_paths}


class SobjectNameRecordIdAndFieldPathMappingResult(NamedTuple):
    crm_property_ai_rec_by_record_id_and_field_path: Mapping[
        UUID, Mapping[tuple[str | UUID, ...], CrmPropertyAiRec]
    ]


class CrmAIRecRepository(GenericRepository):
    async def create_crm_object_ai_rec(
        self,
        *,
        create: CrmObjectAiRecCreate,
    ) -> CrmObjectAiRec:
        dedupe_key = create.dedupe_key
        async with self.engine.begin() if dedupe_key else nullcontext():
            if dedupe_key is not None:
                stmt = text(
                    """
                    update crm_object_ai_recs
                    set is_deduped_group_latest = false
                    where organization_id = :organization_id
                      and sobject_name = :sobject_name
                      and dedupe_key = :dedupe_key
                      and is_deduped_group_latest
                      and deleted_at is null
                    """
                ).bindparams(
                    organization_id=create.organization_id,
                    sobject_name=create.sobject_name,
                    dedupe_key=dedupe_key,
                )
                await self.engine.execute(stmt)
            return await self.insert(CrmObjectAiRec.model_validate(create.model_dump()))

    async def find_crm_property_ai_recs_by_id(
        self,
        *,
        organization_id: UUID,
        ai_rec_ids: set[UUID],
        sobject_name: StdObjectIdentifiers,
        record_id: UUID,
    ) -> list[CrmPropertyAiRec]:
        return await self._find_by_column_values(
            CrmPropertyAiRec,
            organization_id=organization_id,
            id=list(ai_rec_ids),
            sobject_name=sobject_name,
            record_id=record_id,
        )

    async def accept_crm_property_ai_rec(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        sobject_name: StdObjectIdentifiers,
        record_id: UUID,
        ai_rec_ids: set[UUID],
    ) -> list[AcceptCrmPropertyAiRecResult]:
        stmt = text(
            """
            update crm_property_ai_recs
            set accepted_at         = now(),
                accepted_by_user_id = :user_id
            where organization_id = :organization_id
              and id = any (:ai_rec_ids)
              and sobject_name = :sobject_name
              and record_id = :record_id
            returning *;
            """
        ).bindparams(
            organization_id=organization_id,
            user_id=user_id,
            ai_rec_ids=list(ai_rec_ids),
            sobject_name=sobject_name,
            record_id=record_id,
        )
        result: list[AcceptCrmPropertyAiRecResult] = []
        async with self.engine.begin():
            accepted_rows = await self.engine.all(stmt)
            accepted_ai_recs = await CrmPropertyAiRec.bulk_from_rows(rows=accepted_rows)
            for ai_rec in accepted_ai_recs:
                updated_metadata = await self.upsert_crm_property_metadata(
                    organization_id=organization_id,
                    sobject_name=sobject_name,
                    record_id=record_id,
                    sobject_field_path=ai_rec.sobject_field_path,
                    user_id=user_id,
                    field_last_updated_by_user_id=user_id,
                    field_last_updated_at=zoned_utc_now(),
                    field_last_updated_by_ai_rec_id=ai_rec.id,
                    citation_ids=ai_rec.rec_citation_ids,
                    override_existing_citation_ids=False,
                    field_last_updated_source="ai",
                )
                result.append(
                    AcceptCrmPropertyAiRecResult(
                        ai_rec=ai_rec, property_metadata=updated_metadata
                    )
                )
        return result

    async def accept_crm_object_ai_rec(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        ai_rec_id: UUID,
        record_id: UUID,
        sobject_name: StdObjectIdentifiers,
    ) -> CrmObjectAiRec:
        stmt = text(
            """
            update crm_object_ai_recs
            set created_record_id = :record_id
            where organization_id = :organization_id
              and id = :ai_rec_id
              and sobject_name = :sobject_name
            returning *;
            """
        ).bindparams(
            organization_id=organization_id,
            ai_rec_id=ai_rec_id,
            sobject_name=sobject_name,
            record_id=record_id,
        )
        accepted_rows = await self.engine.one(stmt)
        return CrmObjectAiRec.from_row(r=accepted_rows)

    async def reject_crm_property_ai_rec(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        sobject_name: StdObjectIdentifiers,
        record_id: UUID,
        ai_rec_ids: set[UUID],
    ) -> Mapping[UUID, CrmPropertyAiRec]:
        if not ai_rec_ids:
            return frozendict[UUID, CrmPropertyAiRec]()
        stmt = text(
            """
            update crm_property_ai_recs
            set rejected_at         = now(),
                rejected_by_user_id = :user_id
            where organization_id = :organization_id
              and id = any (:ai_rec_ids)
              and sobject_name = :sobject_name
              and record_id = :record_id
            returning *;
            """
        ).bindparams(
            organization_id=organization_id,
            user_id=user_id,
            ai_rec_ids=list(ai_rec_ids),
            sobject_name=sobject_name,
            record_id=record_id,
        )
        accepted_rows = await self.engine.all(stmt)
        result = await CrmPropertyAiRec.bulk_from_rows(rows=accepted_rows)
        return frozendict[UUID, CrmPropertyAiRec]({r.id: r for r in result})

    async def get_unrejected_crm_object_ai_rec(
        self,
        *,
        organization_id: UUID,
        sobject_name: StdObjectIdentifiers,
        rec_id: UUID,
    ) -> CrmObjectAiRec:
        if not (
            ai_rec := await self._find_unique_by_column_values(
                CrmObjectAiRec,
                organization_id=organization_id,
                sobject_name=sobject_name,
                id=rec_id,
                rejected_at=None,
            )
        ):
            raise ResourceNotFoundError(
                f"AI rec with sobject_name {sobject_name} and id {rec_id} not found in organization {organization_id}"
            )
        return ai_rec

    async def reject_crm_object_ai_rec(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        ai_rec_ids: set[UUID],
        sobject_name: StdObjectIdentifiers,
    ) -> Mapping[UUID, CrmObjectAiRec]:
        if not ai_rec_ids:
            return frozendict[UUID, CrmObjectAiRec]()
        stmt = text(
            """
            update crm_object_ai_recs
            set rejected_at         = now(),
                rejected_by_user_id = :user_id
            where organization_id = :organization_id
              and id = any (:ai_rec_ids)
              and sobject_name = :sobject_name
            returning *;
            """
        ).bindparams(
            organization_id=organization_id,
            user_id=user_id,
            ai_rec_ids=list(ai_rec_ids),
            sobject_name=sobject_name,
        )
        accepted_rows = await self.engine.all(stmt)
        result = await CrmObjectAiRec.bulk_from_rows(rows=accepted_rows)
        return frozendict[UUID, CrmObjectAiRec]({r.id: r for r in result})

    async def delete_crm_object_ai_recs(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        ai_rec_ids: set[UUID],
    ) -> Mapping[UUID, CrmObjectAiRec]:
        if not ai_rec_ids:
            return frozendict[UUID, CrmObjectAiRec]()
        stmt = text(
            """
            update crm_object_ai_recs
            set deleted_at         = now(),
                deleted_by_user_id = :user_id
            where organization_id = :organization_id
              and id = any (:ai_rec_ids)
            returning *;
            """
        ).bindparams(
            organization_id=organization_id,
            user_id=user_id,
            ai_rec_ids=list(ai_rec_ids),
        )
        accepted_rows = await self.engine.all(stmt)
        result = await CrmObjectAiRec.bulk_from_rows(rows=accepted_rows)
        return frozendict[UUID, CrmObjectAiRec]({r.id: r for r in result})

    async def insert_crm_property_ai_rec(
        self, rec: CrmPropertyAiRecCreate, _in_nested_transaction: bool = False
    ) -> CrmPropertyAiRec:
        rec_db_object = rec.as_unpersisted_db_object()
        async with self.engine.begin() if not _in_nested_transaction else nullcontext():
            if rec_db_object.is_latest:
                await self.engine.execute(
                    self.unset_latest_crm_property_ai_rec_stmt(
                        organization_id=rec.organization_id,
                        record_id=rec.record_id,
                        sobject_name=rec.sobject_name,
                        sobject_field_path=rec.sobject_field_path,
                    )
                )
            return await self.insert(
                instance=rec_db_object,
            )

    async def update_crm_property_ai_rec(
        self, *, organization_id: UUID, ai_rec_id: UUID, rec: CrmPropertyAiRecUpdate
    ) -> CrmPropertyAiRec:
        if not (
            updated := await self.update_by_tenanted_primary_key(
                table_model=CrmPropertyAiRec,
                organization_id=organization_id,
                primary_key_to_value={"id": ai_rec_id},
                column_to_update=rec,
            )
        ):
            raise ResourceNotFoundError("No AI recommendation found to update")
        return updated

    async def map_latest_crm_property_ai_rec_by_sobject_name_record_id_and_field_path(
        self,
        *,
        organization_id: UUID,
        mapping_request_by_sobject_name: Mapping[
            str, SobjectNameRecordIdAndFieldPathMappingRequest
        ],
    ) -> Mapping[str, SobjectNameRecordIdAndFieldPathMappingResult]:
        result: dict[str, SobjectNameRecordIdAndFieldPathMappingResult] = {}
        for sobject_name, mapping_request in mapping_request_by_sobject_name.items():
            sobject_result = (
                await self.map_latest_crm_property_ai_rec_by_record_and_field_path(
                    organization_id=organization_id,
                    sobject_name=sobject_name,
                    record_ids=mapping_request.record_ids,
                    sobject_field_paths=mapping_request.to_sobject_field_paths(),
                )
            )
            result[sobject_name] = SobjectNameRecordIdAndFieldPathMappingResult(
                crm_property_ai_rec_by_record_id_and_field_path={
                    record_id: {
                        tuple(path.split(".")): ai_rec
                        for path, ai_rec in rec_by_path.items()
                    }
                    for record_id, rec_by_path in sobject_result.items()
                },
            )
        return result

    async def map_latest_crm_property_ai_rec_by_record_and_field_path(
        self,
        *,
        organization_id: UUID,
        sobject_name: str,
        record_ids: set[UUID],
        sobject_field_paths: set[str] | None = None,
    ) -> Mapping[UUID, Mapping[str, CrmPropertyAiRec]]:
        if not record_ids:
            return frozendict[UUID, Mapping[str, CrmPropertyAiRec]]()

        field_path_filter = (
            "AND sobject_field_path = ANY(:sobject_field_paths)"
            if sobject_field_paths
            else ""
        )

        stmt = text(
            f"""
            SELECT * FROM crm_property_ai_recs
            WHERE organization_id = :organization_id
            AND sobject_name = :sobject_name
            AND record_id = ANY(:record_ids)
            {field_path_filter}
            AND is_latest
            AND rejected_at is null
            """  # noqa: S608
        ).bindparams(
            organization_id=organization_id,
            sobject_name=sobject_name,
            record_ids=list(record_ids),
        )
        if sobject_field_paths:
            stmt = stmt.bindparams(
                sobject_field_paths=[sp.lower() for sp in sobject_field_paths],
            )

        rows = await self.engine.all(stmt)
        deserialized_rows = await CrmPropertyAiRec.bulk_from_rows(rows=rows)

        result: dict[UUID, dict[str, CrmPropertyAiRec]] = {}
        for row in deserialized_rows:
            result.setdefault(row.record_id, {})[row.sobject_field_path] = row
        return frozendict[UUID, Mapping[str, CrmPropertyAiRec]](result)

    async def list_crm_object_ai_rec_by_sobject_name_and_parent_record_ids(
        self,
        *,
        organization_id: UUID,
        sobject_name_and_parent_record_ids: Mapping[
            StdObjectIdentifiers, set[ParentRecordIds]
        ],
        only_latest: bool = True,
        limit: int | None = None,
    ) -> list[CrmObjectAiRec]:
        """
        List AI recommendations for creating objects based on the specified sobject names and parent record IDs.

        Args:
            organization_id: The organization ID
            sobject_name_and_parent_record_ids: Mapping of sobject names to sets of parent record IDs
            only_latest: Whether to only return the latest AI recommendations
            limit: Maximum number of results to return

        Notes:
            - Any deleted recommendations are not returned.
            - When `limit` is set, the results are ordered by creation time in descending order.
                which also includes the accepted recommendations. Hence, if the most recent `limit` number of recommendations
                are all accepted, no more results will be returned.
        """
        top_level_sobject_scoped_clauses: list[str] = []
        simple_params: dict[str, Any] = {}  # type: ignore[explicit-any] # need to support dynamic payload
        json_params: dict[str, Any] = {}  # type: ignore[explicit-any] # need to support dynamic payload
        for (
            sobject_name,
            parent_record_ids,
        ) in sobject_name_and_parent_record_ids.items():
            local_clauses: list[str] = []
            local_clauses.append(f"sobject_name = :s_{sobject_name}")
            simple_params[f"s_{sobject_name}"] = sobject_name
            local_parent_record_ids_clauses: list[str] = []
            for idx, parent_record_id in enumerate(parent_record_ids):
                if not parent_record_id.model_dump(exclude_none=True):
                    continue
                parent_record_id_json = parent_record_id.model_dump(exclude_none=True)
                param_name = f"prid_{sobject_name}_{idx}"
                json_params[param_name] = parent_record_id_json
                local_parent_record_ids_clauses.append(
                    f"parent_record_ids @> :{param_name}"
                )
            if local_parent_record_ids_clauses:
                local_clauses.append(
                    f"({' OR '.join(local_parent_record_ids_clauses)})"
                )
            top_level_sobject_scoped_clauses.append(f"({' AND '.join(local_clauses)})")
        top_level_sobject_scoped_clause = (
            f"({' OR '.join(top_level_sobject_scoped_clauses)})"
            if top_level_sobject_scoped_clauses
            else ""
        )
        final_top_level_sobject_scoped_params = (
            f"AND {top_level_sobject_scoped_clause}"
            if top_level_sobject_scoped_clause
            else ""
        )

        is_latest_filter = (
            "AND (is_deduped_group_latest or is_deduped_group_latest is null)"
            if only_latest
            else ""
        )
        limit_clause = "limit :limit" if limit else ""
        stmt = (
            text(
                f"""
            SELECT * FROM crm_object_ai_recs
            WHERE organization_id = :organization_id
            {final_top_level_sobject_scoped_params}
            AND deleted_at is null
            {is_latest_filter}
            order by created_at desc, id
            {limit_clause}
            """  # noqa: S608
            )
            .bindparams(
                organization_id=organization_id,
                **simple_params,
            )
            .bindparams(
                *[
                    BindParameter(k, value=v, type_=JSONB)
                    for k, v in json_params.items()
                ]
            )
        )
        if limit:
            stmt = stmt.bindparams(limit=limit)
        rows = await self.engine.all(stmt)
        return await CrmObjectAiRec.bulk_from_rows(rows=rows)

    async def insert_crm_property_metadata(
        self, metadata: CrmPropertyMetadataCreate
    ) -> CrmPropertyMetadata:
        return await self.insert(
            instance=CrmPropertyMetadata.model_validate(
                {"id": uuid4(), **metadata.model_dump()}
            ),
        )

    async def upsert_crm_property_metadata(
        self,
        *,
        organization_id: UUID,
        sobject_name: str,
        record_id: UUID,
        sobject_field_path: str,
        user_id: UUID,
        citation_ids: list[UUID] | None = None,
        stage_history: list[CrmPropertyStageSnapshot] | None = None,
        override_existing_citation_ids: bool = False,
        override_existing_stage_history: bool = False,
        field_last_updated_by_user_id: UnsetAware[UUID] = UNSET,
        field_last_updated_at: UnsetAware[ZoneRequiredDateTime] = UNSET,
        field_last_updated_by_ai_rec_id: UnsetAware[UUID | None] = UNSET,
        field_last_updated_source: UnsetAware[Literal["user", "ai"] | None] = UNSET,
    ) -> CrmPropertyMetadata:
        if not (
            existing_metadata := await self.find_crm_property_metadata(
                organization_id=organization_id,
                sobject_name=sobject_name,
                record_id=record_id,
                sobject_field_path=sobject_field_path,
            )
        ):
            return await self.insert_crm_property_metadata(
                CrmPropertyMetadataCreate(
                    organization_id=organization_id,
                    record_id=record_id,
                    sobject_name=sobject_name,
                    sobject_field_path=sobject_field_path,
                    citation_ids=citation_ids,
                    stage_history=stage_history,
                    created_at=zoned_utc_now(),
                    field_last_updated_at=specified_or_default(
                        field_last_updated_at, None
                    ),
                    field_last_updated_by_user_id=specified_or_default(
                        field_last_updated_by_user_id, None
                    ),
                    field_last_updated_by_ai_rec_id=specified_or_default(
                        field_last_updated_by_ai_rec_id, None
                    ),
                    field_last_updated_source=specified_or_default(
                        field_last_updated_source, None
                    ),
                    updated_at=zoned_utc_now(),
                    created_by_user_id=user_id,
                    updated_by_user_id=user_id,
                )
            )
        else:
            desired_citation_ids = (
                [*existing_metadata.citation_ids]
                if existing_metadata.citation_ids
                else []
            )
            if citation_ids:
                if override_existing_citation_ids:
                    desired_citation_ids = citation_ids
                else:
                    desired_citation_ids.extend(citation_ids)
            desired_stage_history = (
                [*existing_metadata.stage_history]
                if existing_metadata.stage_history
                else []
            )
            if stage_history:
                if override_existing_stage_history:
                    desired_stage_history = stage_history
                else:
                    desired_stage_history.extend(stage_history)
            update_metadata = CrmPropertyMetadataUpdate(
                citation_ids=desired_citation_ids,
                stage_history=desired_stage_history,
                field_last_updated_at=field_last_updated_at,
                field_last_updated_by_user_id=field_last_updated_by_user_id,
                field_last_updated_by_ai_rec_id=field_last_updated_by_ai_rec_id,
                updated_at=zoned_utc_now(),
                field_last_updated_source=field_last_updated_source,
            )
            return not_none(
                await self.update_by_tenanted_primary_key(
                    table_model=CrmPropertyMetadata,
                    organization_id=organization_id,
                    primary_key_to_value=existing_metadata.primary_key_to_value(),
                    column_to_update=update_metadata,
                )
            )

    async def find_crm_property_metadata(
        self,
        *,
        organization_id: UUID,
        sobject_name: str,
        record_id: UUID,
        sobject_field_path: str,
    ) -> CrmPropertyMetadata | None:
        normalized_sobject_field_path = sobject_field_path.lower()
        mapped_result = await self.map_crm_property_metadata_by_record_and_field_path(
            organization_id=organization_id,
            sobject_name=sobject_name,
            record_ids={record_id},
            sobject_field_paths={normalized_sobject_field_path},
        )
        return mapped_result.get(record_id, {}).get(normalized_sobject_field_path, None)

    async def map_crm_property_metadata_by_record_and_field_path(
        self,
        *,
        organization_id: UUID,
        sobject_name: str,
        record_ids: set[UUID],
        sobject_field_paths: set[str] | None = None,
    ) -> Mapping[UUID, Mapping[str, CrmPropertyMetadata]]:
        if not record_ids:
            return frozendict[UUID, Mapping[str, CrmPropertyMetadata]]()

        field_path_filter = (
            "AND sobject_field_path = ANY(:sobject_field_paths)"
            if sobject_field_paths
            else ""
        )

        stmt = text(
            f"""
            SELECT * FROM crm_property_metadata
            WHERE organization_id = :organization_id
            AND sobject_name = :sobject_name
            AND record_id = ANY(:record_ids)
            {field_path_filter}
            """  # noqa: S608
        ).bindparams(
            organization_id=organization_id,
            sobject_name=sobject_name,
            record_ids=list(record_ids),
        )
        if sobject_field_paths:
            stmt = stmt.bindparams(
                sobject_field_paths=[sp.lower() for sp in sobject_field_paths],
            )

        rows = await self.engine.all(stmt)
        deserialized_rows = await CrmPropertyMetadata.bulk_from_rows(rows=rows)

        result: dict[UUID, dict[str, CrmPropertyMetadata]] = {}
        for row in deserialized_rows:
            result.setdefault(row.record_id, {})[row.sobject_field_path] = row
        return frozendict[UUID, Mapping[str, CrmPropertyMetadata]](result)

    async def list_active_crm_property_ai_recs_by_source(
        self,
        *,
        organization_id: UUID,
        source: Literal["MEETING", "EMAIL"],
        source_id: UUID,
    ) -> list[CrmPropertyAiRec]:
        """
        List all the active AI recommendations for a given source (e.g. meeting or email).

        Active means:
        - Not deleted
        - Not rejected
        - Either the pending-latest rec for the record or a previously accepted rec
        """
        stmt = text(
            """
            select distinct on (cpar.id) cpar.*
            from crm_property_ai_recs cpar
                     join citation c
                          on cpar.rec_citation_ids @> array [c.id]
                              and cpar.deleted_at is null
                              and cpar.rejected_at is null
                              and (cpar.is_latest or cpar.accepted_at is not null)
                              and cpar.organization_id = c.organization_id
            where c.source_type = :source
              and c.source_id = :source_id
              and c.organization_id = :organization_id
            """
        ).bindparams(
            organization_id=organization_id,
            source_id=source_id,
            source=source,
        )
        rows = await self.engine.all(stmt)
        return await CrmPropertyAiRec.bulk_from_rows(rows=rows)

    async def list_active_crm_object_ai_recs_by_source(
        self,
        *,
        organization_id: UUID,
        source: Literal["MEETING", "EMAIL"],
        source_id: UUID,
    ) -> list[CrmObjectAiRec]:
        """
        List all the active AI recommendations for a given source (e.g. meeting or email).

        Active means:
        - Not deleted
        - Not rejected
        """
        stmt = text(
            """
            select distinct on (coar.id) coar.*
            from crm_object_ai_recs coar
                     join citation c
                          on coar.rec_citation_ids @> array [c.id] and
                             coar.deleted_at is null and
                             coar.rejected_at is null
            where c.source_type = :source
              and c.source_id = :source_id
              and c.organization_id = :organization_id
            """
        ).bindparams(
            organization_id=organization_id,
            source_id=source_id,
            source=source,
        )
        rows = await self.engine.all(stmt)
        return await CrmObjectAiRec.bulk_from_rows(rows=rows)

    @classmethod
    def unset_latest_crm_property_ai_rec_stmt(
        cls,
        *,
        organization_id: UUID,
        record_id: UUID,
        sobject_name: str,
        sobject_field_path: str,
    ) -> TextClause:
        return text(
            """
            UPDATE crm_property_ai_recs
            SET is_latest = FALSE
            WHERE organization_id = :organization_id
            AND record_id = :record_id
            AND sobject_name = :sobject_name
            AND sobject_field_path = :sobject_field_path
            AND is_latest
            """
        ).bindparams(
            organization_id=organization_id,
            record_id=record_id,
            sobject_name=sobject_name,
            sobject_field_path=sobject_field_path,
        )
