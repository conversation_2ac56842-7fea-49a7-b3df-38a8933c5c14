from typing import TypeVar
from uuid import UUID

from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.models.core.base import TableModel
from salestech_be.db.models.reporting import ReportingDataset, ReportingDatasetField
from salestech_be.util.time import zoned_utc_now

T = TypeVar("T", bound=TableModel)


class ReportingDatasetRepository(GenericRepository):
    async def list_datasets(
        self,
        *,
        organization_id: UUID,
    ) -> list[ReportingDataset]:
        return await self._find_by_column_values(
            ReportingDataset,
            organization_id=[organization_id, None],
        )

    async def list_dataset_fields(
        self,
        *,
        dataset_id: UUID,
        organization_id: UUID,
    ) -> list[ReportingDatasetField]:
        return await self._find_by_column_values(
            ReportingDatasetField,
            dataset_id=dataset_id,
            organization_id=[organization_id, None],
        )

    async def get_dataset(
        self,
        *,
        dataset_id: UUID,
        organization_id: UUID,
    ) -> ReportingDataset:
        return await self._find_unique_by_column_values_or_fail(
            ReportingDataset,
            id=dataset_id,
            organization_id=[organization_id, None],
        )

    async def delete_dataset(
        self,
        *,
        dataset_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> None:
        async with self.engine.begin():
            # Create update data with soft delete fields
            update_data = {
                "deleted_at": zoned_utc_now(),
                "deleted_by_user_id": user_id,
            }

            # Delete dataset field
            await self._update_by_column_values(
                ReportingDatasetField,
                column_value_to_query={
                    "dataset_id": dataset_id,
                    "organization_id": organization_id,
                },
                column_to_update=update_data,
            )

            # Delete dataset
            await self.update_by_tenanted_primary_key(
                ReportingDataset,
                primary_key_to_value={"id": dataset_id},
                column_to_update=update_data,
                organization_id=organization_id,
            )

