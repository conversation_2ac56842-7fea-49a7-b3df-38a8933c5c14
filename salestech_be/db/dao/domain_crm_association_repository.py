from collections.abc import Sequence
from datetime import datetime
from uuid import UUID

from salestech_be.common.type.patch_request import specified
from salestech_be.core.domain_crm_association.types import (
    DeleteDomainCrmAssociation,
    DeleteMeetingCrmAssociation,
)
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.domain_crm_association import (
    DomainCRMAssociation,
    DomainType,
)
from salestech_be.util.time import zoned_utc_now


class DomainCRMAssociationRepository(GenericRepository):
    def __init__(self, engine: DatabaseEngine):
        super().__init__(engine=engine)

    async def find_by_related_id_and_domain_type(
        self,
        domain_id: UUID,
        domain_type: DomainType,
    ) -> list[DomainCRMAssociation]:
        # Map the related_id to the appropriate domain-specific column
        match domain_type:
            case DomainType.EMAIL:
                return await self._find_by_column_values(
                    table_model=DomainCRMAssociation,
                    domain_type=domain_type,
                    message_id=domain_id,
                )
            case DomainType.MEETING:
                return await self._find_by_column_values(
                    table_model=DomainCRMAssociation,
                    domain_type=domain_type,
                    meeting_id=domain_id,
                )
            case DomainType.SEQUENCE:
                return await self._find_by_column_values(
                    table_model=DomainCRMAssociation,
                    domain_type=domain_type,
                    sequence_enrollment_id=domain_id,
                )
            case DomainType.NOTE:
                return await self._find_by_column_values(
                    table_model=DomainCRMAssociation,
                    domain_type=domain_type,
                    note_id=domain_id,
                )
            case DomainType.TASK:
                return await self._find_by_column_values(
                    table_model=DomainCRMAssociation,
                    domain_type=domain_type,
                    task_id=domain_id,
                )
            case DomainType.VOICE_CALL:
                return await self._find_by_column_values(
                    table_model=DomainCRMAssociation,
                    domain_type=domain_type,
                    call_id=domain_id,
                )

    async def delete_meeting_domain_crm_association(
        self,
        domain_crm_association: DeleteMeetingCrmAssociation,
    ) -> None:
        now = zoned_utc_now()
        update_values: dict[str, datetime | UUID] = {
            "deleted_at": now,
            "updated_at": now,
        }

        if domain_crm_association.user_id:
            update_values["deleted_by_user_id"] = domain_crm_association.user_id
            update_values["updated_by_user_id"] = domain_crm_association.user_id

        await self._update_by_column_values(
            table_model=DomainCRMAssociation,
            column_value_to_query={
                "domain_type": domain_crm_association.domain_type,
                "meeting_id": domain_crm_association.meeting_id,
                "association_role": domain_crm_association.association_role,
                "deleted_at": None,
                "email": domain_crm_association.email,
                "user_id": domain_crm_association.user_id,
            },
            column_to_update=update_values,
        )

    async def bulk_delete_domain_crm_associations(
        self,
        domain_crm_associations: Sequence[DeleteDomainCrmAssociation],
        deleted_by_user_id: UUID,
    ) -> None:
        """Bulk soft delete domain CRM associations using safe repository methods.

        This method is domain agnostic - it can handle any type of domain association
        (task, meeting, email, etc.) by checking the domain_type and using the
        appropriate ID field.
        """
        if not domain_crm_associations:
            return

        now = zoned_utc_now()
        update_values: dict[str, datetime | UUID] = {
            "deleted_at": now,
            "updated_at": now,
            "deleted_by_user_id": deleted_by_user_id,
            "updated_by_user_id": deleted_by_user_id,
        }

        async with self.engine.begin():
            for req in (
                domain_crm_associations
            ):  # todo: possibly use bulk update sql with chunks
                column_value_to_query: dict[str, str | UUID | datetime | None] = {
                    "organization_id": req.organization_id,
                    "domain_type": req.domain_type,
                    "deleted_at": None,
                }

                domain_id_field = self._get_domain_id_field_by_domain_type(
                    req.domain_type
                )
                column_value_to_query[domain_id_field] = getattr(req, domain_id_field)

                self._add_optional_query_conditions_for_delete(
                    req, column_value_to_query
                )

                await self._update_by_column_values(
                    table_model=DomainCRMAssociation,
                    column_value_to_query=column_value_to_query,
                    column_to_update=update_values,
                )

    def _get_domain_id_field_by_domain_type(
        self,
        domain_type: DomainType,
    ) -> str:
        """Get the appropriate ID field name and value based on domain type."""
        match domain_type:
            case DomainType.TASK:
                return "task_id"
            case DomainType.MEETING:
                return "meeting_id"
            case DomainType.SEQUENCE:
                return "sequence_enrollment_id"
            case DomainType.NOTE:
                return "note_id"
            case DomainType.VOICE_CALL:
                return "call_id"
            case DomainType.EMAIL:
                return "global_thread_id"
            case _:
                raise ValueError(f"Unsupported domain type: {domain_type}")

    def _add_optional_query_conditions_for_delete(
        self,
        domain_crm_association: DeleteDomainCrmAssociation,
        column_value_to_query: dict[str, str | UUID | datetime | None],
    ) -> None:
        """Add optional field conditions based on the specific delete request type."""
        # Common optional fields
        if specified(domain_crm_association.user_id):
            column_value_to_query["user_id"] = domain_crm_association.user_id
        if specified(domain_crm_association.contact_id):
            column_value_to_query["contact_id"] = domain_crm_association.contact_id
        if specified(domain_crm_association.account_id):
            column_value_to_query["account_id"] = domain_crm_association.account_id
        if specified(domain_crm_association.pipeline_id):
            column_value_to_query["pipeline_id"] = domain_crm_association.pipeline_id


def get_domain_crm_association_repository(
    engine: DatabaseEngine,
) -> DomainCRMAssociationRepository:
    return DomainCRMAssociationRepository(engine=engine)
