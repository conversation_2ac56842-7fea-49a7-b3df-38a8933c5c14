import uuid

from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.models.reporting import (
    ReportingChart,
    ReportingDashboard,
    ReportingDataset,
    ReportingDatasetField,
    ReportingDatasetRelation,
)


class ReportingRepository(GenericRepository):
    async def get_charts(
        self,
        organization_id: uuid.UUID,
    ) -> list[ReportingChart]:
        return await self._find_by_column_values(
            table_model=ReportingChart,
            organization_id=organization_id,
        )

    async def find_chart_by_id(
        self,
        chart_id: uuid.UUID,
        organization_id: uuid.UUID,
    ) -> ReportingChart:
        chart = await self._find_unique_by_column_values_or_fail(
            table_model=ReportingChart,
            id=chart_id,
        )
        if chart.organization_id and chart.organization_id != organization_id:
            raise ResourceNotFoundError(
                f"Chart {chart_id} not found for organization {organization_id}"
            )
        return chart

    async def get_dashboards(self) -> list[ReportingDashboard]:
        return await self._find_by_column_values(
            table_model=ReportingDashboard,
            exclude_deleted_or_archived=True,
        )

    # Dataset methods
    async def find_dataset_by_id(
        self,
        dataset_id: uuid.UUID,
        organization_id: uuid.UUID,
    ) -> ReportingDataset:
        dataset = await self._find_unique_by_column_values_or_fail(
            table_model=ReportingDataset,
            id=dataset_id,
        )
        if dataset.organization_id and dataset.organization_id != organization_id:
            raise ResourceNotFoundError(
                f"Dataset {dataset_id} not found for organization {organization_id}"
            )
        return dataset

    async def get_datasets(
        self,
        organization_id: uuid.UUID,
    ) -> list[ReportingDataset]:
        return await self._find_by_column_values(
            table_model=ReportingDataset,
            organization_id=organization_id,
        )

    async def create_dataset(
        self,
        dataset: ReportingDataset,
    ) -> ReportingDataset:
        return await self.insert(dataset)

    async def update_dataset(
        self,
        dataset: ReportingDataset,
    ) -> ReportingDataset:
        updated_dataset = await self.update_instance(dataset)
        if updated_dataset is None:
            raise ResourceNotFoundError(f"Dataset {dataset.id} not found")
        return updated_dataset

    # Dataset Field methods
    async def get_dataset_fields(
        self,
        dataset_id: uuid.UUID,
    ) -> list[ReportingDatasetField]:
        return await self._find_by_column_values(
            table_model=ReportingDatasetField,
            dataset_id=dataset_id,
            exclude_deleted_or_archived=True,
        )

    async def create_dataset_field(
        self,
        field: ReportingDatasetField,
    ) -> ReportingDatasetField:
        return await self.insert(field)

    # Dataset Relation methods
    async def get_dataset_relations(
        self,
        source_dataset_id: uuid.UUID,
    ) -> list[ReportingDatasetRelation]:
        return await self._find_by_column_values(
            table_model=ReportingDatasetRelation,
            source_dataset_id=source_dataset_id,
            exclude_deleted_or_archived=True,
        )

    async def create_dataset_relation(
        self,
        relation: ReportingDatasetRelation,
    ) -> ReportingDatasetRelation:
        return await self.insert(relation)
