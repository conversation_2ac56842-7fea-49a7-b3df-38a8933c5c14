from typing import Named<PERSON>up<PERSON>
from uuid import UUID

from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.models.reporting import (
    ReportingChart,
    ReportingChartUpdate,
    ReportingDashboardChartAssociation,
    ReportingDashboardChartAssociationUpdate,
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none


class DeleteReportingChartResult(NamedTuple):
    deleted_chart: ReportingChart
    deleted_dashboard_chart_associations: list[ReportingDashboardChartAssociation]


class ReportingChartRepository(GenericRepository):
    async def find_dashboard_chart_associations_by_chart_id(
        self, *, chart_id: UUID, dashboard_id: UUID | None = None, organization_id: UUID
    ) -> list[ReportingDashboardChartAssociation]:
        if dashboard_id:
            return await self._find_by_column_values(
                ReportingDashboardChartAssociation,
                chart_id=chart_id,
                dashboard_id=dashboard_id,
                organization_id=organization_id,
            )
        else:
            return await self._find_by_column_values(
                ReportingDashboardChartAssociation,
                chart_id=chart_id,
                organization_id=organization_id,
            )

    async def find_dashboard_chart_associations_by_chart_ids(
        self,
        *,
        chart_ids: list[UUID],
        organization_id: UUID,
    ) -> dict[UUID, list[ReportingDashboardChartAssociation]]:
        db_associations = await self._find_by_column_values(
            ReportingDashboardChartAssociation,
            chart_id=chart_ids,
            organization_id=organization_id,
        )

        res: dict[UUID, list[ReportingDashboardChartAssociation]] = {}
        for db_association in db_associations:
            res.setdefault(db_association.chart_id, []).append(db_association)
        return res

    async def delete_chart(
        self, *, chart_id: UUID, user_id: UUID, organization_id: UUID
    ) -> DeleteReportingChartResult:
        await self.find_by_primary_key_or_fail(
            table_model=ReportingChart,
            id=chart_id,
        )

        deleted_dashboard_chart_associations = await self._update_by_column_values(
            table_model=ReportingDashboardChartAssociation,
            column_value_to_query={
                "chart_id": chart_id,
                "organization_id": organization_id,
            },
            column_to_update=ReportingDashboardChartAssociationUpdate(
                deleted_by_user_id=user_id,
            ),
        )

        deleted_chart = not_none(
            await self.update_by_tenanted_primary_key(
                table_model=ReportingChart,
                primary_key_to_value={"id": chart_id},
                organization_id=organization_id,
                column_to_update=ReportingChartUpdate(
                    deleted_at=zoned_utc_now(),
                    deleted_by_user_id=user_id,
                ),
            )
        )

        return DeleteReportingChartResult(
            deleted_chart=deleted_chart,
            deleted_dashboard_chart_associations=deleted_dashboard_chart_associations,
        )

    async def list_charts(self, *, organization_id: UUID) -> list[ReportingChart]:
        return await self._find_by_column_values(
            ReportingChart,
            organization_id=organization_id,
        )
