import asyncio
from abc import ABC, abstractmethod
from types import TracebackType
from typing import Self

from aiokafka import (
    AIOKafkaConsumer,
    ConsumerRebalanceListener,
    TopicPartition,
)

from salestech_be.integrations.kafka.kafka_manager import MSKConsumerFactory
from salestech_be.integrations.kafka.types import TRecord
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


class KafkaPartitionProcessor(ABC):
    @abstractmethod
    async def process(
        self, partition: TopicPartition, records: list[TRecord]
    ) -> None: ...

    async def start(self) -> None: ...  # noqa: B027

    async def stop(self) -> None: ...  # noqa: B027

    async def __aenter__(self) -> Self:
        await self.start()
        return self

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_value: BaseException | None,
        traceback: TracebackType | None,
    ) -> None:
        await self.stop()


class KafkaOffsetManager:
    def __init__(self, consumer: AIOKafkaConsumer) -> None:
        self.consumer = consumer
        self.commit_lock = asyncio.Lock()
        self.pending_offsets: dict[TopicPartition, int] = {}

    async def set_pending_offset(self, partition: TopicPartition, offset: int) -> None:
        self.pending_offsets[partition] = offset

    async def commit_pending_offsets(self) -> None:
        async with self.commit_lock:
            offsets_to_commit = self.pending_offsets.copy()
            self.pending_offsets.clear()

            if offsets_to_commit:
                logger.info("committing offsets", offsets=offsets_to_commit)
                await self.consumer.commit(offsets_to_commit)


class KafkaPartitionConsumer:
    def __init__(
        self,
        consumer: AIOKafkaConsumer,
        processor: KafkaPartitionProcessor,
        offset_manager: KafkaOffsetManager,
        partition: TopicPartition,
        batch_size: int,
    ) -> None:
        self.consumer = consumer
        self.processor = processor
        self.offset_manager = offset_manager
        self.partition = partition
        self.batch_size = batch_size
        self.record_queue = asyncio.Queue[list[TRecord] | None]()
        self.record_producer_task: asyncio.Task[None] | None = None
        self.record_consumer_task: asyncio.Task[None] | None = None

    async def consume_records(self) -> None:
        while True:
            records = await self.record_queue.get()
            if records is None:
                break

            logger.info(
                "fetched records for partition",
                partition=self.partition,
                records=len(records),
            )
            await self.processor.process(self.partition, records)
            await self.offset_manager.set_pending_offset(
                self.partition, records[-1].offset + 1
            )
            self.record_queue.task_done()

    async def produce_records(self) -> None:
        try:
            while True:
                await self.record_queue.join()
                first = await self.consumer.getone(self.partition)
                remaining = await self.consumer.getmany(
                    self.partition,
                    max_records=self.batch_size - 1,
                    timeout_ms=0,
                )
                records = [first, *remaining.get(self.partition, [])]
                await self.record_queue.put(records)
        except asyncio.CancelledError:
            logger.info("record producer cancelled")
        except Exception as e:
            logger.exception(f"record producer error - {e}")
            raise

    async def run(self) -> None:
        try:
            async with asyncio.TaskGroup() as tg:
                self.record_producer_task = tg.create_task(self.produce_records())
                self.record_consumer_task = tg.create_task(self.consume_records())
                await self.record_producer_task
                await asyncio.wait_for(self.record_consumer_task, timeout=5.0)
        finally:
            self.record_producer_task = None
            self.record_consumer_task = None

    async def stop(self) -> None:
        if self.record_producer_task:
            self.record_producer_task.cancel()
            await self.record_queue.put(None)


class KafkaConsumer(ConsumerRebalanceListener):  # type: ignore[misc]
    def __init__(
        self,
        topic: str,
        group_id: str,
        processor: KafkaPartitionProcessor,
        batch_size: int = 200,
        commit_interval: float = 5.0,
        stop_partition_timeout: float = 10.0,
    ) -> None:
        super().__init__()
        self.topic = topic
        self.consumer = MSKConsumerFactory.create_raw(
            group_id=group_id, enable_auto_commit=False
        )
        self.processor = processor
        self.offset_manager = KafkaOffsetManager(self.consumer)
        self.batch_size = batch_size
        self.commit_interval = commit_interval
        self.stop_partition_timeout = stop_partition_timeout
        self.task_group = asyncio.TaskGroup()
        self.partition_consumers: dict[TopicPartition, KafkaPartitionConsumer] = {}
        self.partition_consumer_tasks: dict[TopicPartition, asyncio.Task[None]] = {}
        self.commit_task: asyncio.Task[None] | None = None

    async def on_partitions_assigned(self, assigned: list[TopicPartition]) -> None:
        logger.info("partitions assigned", topic=self.topic, partitions=assigned)
        try:
            for partition in assigned:
                consumer = KafkaPartitionConsumer(
                    consumer=self.consumer,
                    processor=self.processor,
                    offset_manager=self.offset_manager,
                    partition=partition,
                    batch_size=self.batch_size,
                )
                task = self.task_group.create_task(consumer.run())
                self.partition_consumers[partition] = consumer
                self.partition_consumer_tasks[partition] = task
        except Exception as e:
            logger.exception(f"error on partitions assigned - {e}")
            raise

    async def on_partitions_revoked(self, revoked: list[TopicPartition]) -> None:
        logger.info("partitions revoked", topic=self.topic, partitions=revoked)
        await self.stop_partitions(revoked)
        await self.offset_manager.commit_pending_offsets()

    async def stop_partitions(self, partitions: list[TopicPartition]) -> None:
        async with asyncio.TaskGroup() as tg:
            for partition in partitions:
                tg.create_task(self.stop_partition(partition))

    async def stop_partition(self, partition: TopicPartition) -> None:
        partition_consumer = self.partition_consumers.pop(partition, None)
        partition_consumer_task = self.partition_consumer_tasks.pop(partition, None)

        if partition_consumer:
            logger.info("stopping partition consumer", partition=partition)
            await partition_consumer.stop()
        if partition_consumer_task:
            try:
                await asyncio.wait_for(
                    partition_consumer_task, timeout=self.stop_partition_timeout
                )
            except TimeoutError:
                logger.warning(
                    "partition consumer task did not stop gracefully",
                    partition=partition,
                )
                partition_consumer_task.cancel()

    async def run_commit_task(self) -> None:
        try:
            while True:
                await asyncio.sleep(self.commit_interval)
                await self.offset_manager.commit_pending_offsets()
        except asyncio.CancelledError:
            logger.info("commit task cancelled")
        except Exception as e:
            logger.exception(f"commit task error - {e}")
            raise

    async def run_until_stopped(self) -> None:
        self.task_group = asyncio.TaskGroup()
        async with self.task_group, self.processor, self.consumer:
            logger.info("kafka consumer started")
            self.consumer.subscribe(topics=[self.topic], listener=self)
            self.commit_task = self.task_group.create_task(self.run_commit_task())
            await self.commit_task

            logger.info("kafka consumer stopping")
            await self.stop_partitions(list(self.partition_consumers.keys()))
            await self.offset_manager.commit_pending_offsets()

    def stop(self) -> None:
        if self.commit_task:
            self.commit_task.cancel()
