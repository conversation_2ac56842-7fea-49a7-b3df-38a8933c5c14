import argparse
import asyncio

from temporalio import workflow
from temporalio.common import RetryPolicy

with workflow.unsafe.imports_passed_through():
    from datetime import timedelta
    from uuid import UUID

    from pydantic import BaseModel
    from sqlalchemy import text

    from salestech_be.core.ai.opportuntity_stages.activities.generate_criteria import (
        generate_criteria,
    )
    from salestech_be.core.ai.opportuntity_stages.activities.generate_sales_action_role_classification import (
        generate_contact_pipeline_role_classification,
        generate_sales_action_classification,
    )
    from salestech_be.core.ai.opportuntity_stages.criteria_configs import (
        CRITERIA_CONFIG_MAP,
    )
    from salestech_be.core.opportunity_stage_criteria.criteria_types import (
        CriteriaExtractionSourceObjectId,
        CriteriaExtractionSourceObjectType,
    )
    from salestech_be.integrations.temporal.client import get_temporal_client
    from salestech_be.integrations.temporal.config import AI_TASK_QUEUE
    from salestech_be.ree_logging import get_logger
    from salestech_be.settings import settings
    from salestech_be.temporal.database import (
        get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    )

logger = get_logger(__name__)


async def backfill_stage_criteria_ai_recs(
    organization_ids: list[UUID] | None = None,
) -> None:
    db_engine = await get_or_init_db_engine()
    client = await get_temporal_client()

    # Find all orgs if none provided
    if not organization_ids:
        # Find all internal orgs to exclude
        internal_orgs_query = text(
            """
            SELECT id, display_name
            FROM organization
            WHERE deactivated_at IS NULL
            AND display_name ILIKE '%Reevo%'
            """
        )
        internal_orgs = await db_engine.all(internal_orgs_query)
        internal_org_ids = [org.id for org in internal_orgs]

        # Get all non-internal organizations
        all_orgs_query = text(
            """
            SELECT id, display_name
            FROM organization
            WHERE deactivated_at IS NULL
            AND id != ANY(:internal_org_ids)
            ORDER BY created_at
            """
        ).bindparams(internal_org_ids=internal_org_ids)
        all_orgs = await db_engine.all(all_orgs_query)
        organization_ids = [org.id for org in all_orgs]

    for organization_id in organization_ids:
        logger.info(f"- Processing organization {organization_id}")
        # Find all pipelines that have at least one meeting
        pipelines_query = text("""
            SELECT p.id, p.display_name
            FROM pipeline p
            WHERE p.organization_id = :organization_id
            AND p.archived_at IS NULL
            AND p.closed_at IS NULL
        """).bindparams(organization_id=organization_id)
        pipelines = await db_engine.all(pipelines_query)

        for pipeline in pipelines:
            logger.info(
                f"--- Processing pipeline {pipeline.id} - {pipeline.display_name}"
            )

            # Delete all existing ai recs for this pipeline
            deletion_query = text("""
                UPDATE crm_object_ai_recs
                SET deleted_at = now(),
                    deleted_by_user_id = :user_id
                WHERE organization_id = :organization_id
                AND parent_record_ids->>'pipeline_id' = :pipeline_id
                AND deleted_at IS NULL
            """).bindparams(
                organization_id=organization_id,
                pipeline_id=str(pipeline.id),
                user_id=UUID(settings.intel_hardcoded_user_id),
            )
            await db_engine.execute(deletion_query)

            # Get all meetings for this pipeline, ordered by started_at
            meetings_query = text("""
                SELECT id, title
                FROM meeting
                WHERE pipeline_id = :pipeline_id
                AND organization_id = :organization_id
                AND deleted_at IS NULL
                AND ended_at IS NOT NULL
                AND status = 'completed'
                ORDER BY ended_at
            """).bindparams(pipeline_id=pipeline.id, organization_id=organization_id)
            meetings = await db_engine.all(meetings_query)
            meeting_ids = [meeting.id for meeting in meetings]
            if not meeting_ids:
                logger.info(
                    f"--- No meetings found for pipeline {pipeline.id} - {pipeline.display_name}"
                )
                continue

            workflow_input = StageCriteriaBackfillWorkflowInput(
                organization_id=organization_id,
                pipeline_id=pipeline.id,
                meeting_ids=meeting_ids,
            )

            # Use execute_workflow instead of start_workflow to wait for completion
            try:
                await client.execute_workflow(
                    StageCriteriaBackfillWorkflow.run,
                    id=f"stage-criteria-backfill-{organization_id}-{pipeline.id}",
                    task_queue=AI_TASK_QUEUE,
                    args=[workflow_input],
                )
                logger.info(
                    f"--- Completed pipeline {pipeline.id} - {pipeline.display_name}"
                )
            except Exception as e:
                logger.warning(
                    f"--- Failed pipeline {pipeline.id} - {pipeline.display_name}: {e!s}"
                )


class StageCriteriaBackfillWorkflowInput(BaseModel):
    organization_id: UUID
    pipeline_id: UUID
    meeting_ids: list[UUID]


@workflow.defn
class StageCriteriaBackfillWorkflow:
    @workflow.run
    async def run(
        self,
        workflow_input: StageCriteriaBackfillWorkflowInput,
    ) -> None:
        for meeting_id in workflow_input.meeting_ids:
            source_object = CriteriaExtractionSourceObjectId(
                object_type=CriteriaExtractionSourceObjectType.MEETING,
                object_id=meeting_id,
            )
            criteria_activities = []
            for config_type in CRITERIA_CONFIG_MAP:
                criteria_activity = workflow.execute_activity(
                    generate_criteria,
                    args=(
                        workflow_input.organization_id,
                        workflow_input.pipeline_id,
                        UUID(settings.intel_hardcoded_user_id),
                        source_object,
                        config_type,
                    ),
                    task_queue=AI_TASK_QUEUE,
                    start_to_close_timeout=timedelta(minutes=15),
                    schedule_to_close_timeout=timedelta(minutes=20),
                    retry_policy=RetryPolicy(
                        maximum_attempts=3,
                        initial_interval=timedelta(seconds=60),
                        backoff_coefficient=3.0,
                    ),
                )
                criteria_activities.append(criteria_activity)

            # Create separate lists for different activity types
            sales_action_activity = workflow.execute_activity(
                generate_sales_action_classification,
                args=(
                    workflow_input.organization_id,
                    workflow_input.pipeline_id,
                    source_object,
                ),
                task_queue=AI_TASK_QUEUE,
                start_to_close_timeout=timedelta(minutes=15),
                schedule_to_close_timeout=timedelta(minutes=20),
                retry_policy=RetryPolicy(
                    maximum_attempts=3,
                    initial_interval=timedelta(seconds=60),
                    backoff_coefficient=3.0,
                ),
            )

            contact_pipeline_role_activity = workflow.execute_activity(
                generate_contact_pipeline_role_classification,
                args=(
                    workflow_input.organization_id,
                    workflow_input.pipeline_id,
                    source_object,
                ),
                task_queue=AI_TASK_QUEUE,
                start_to_close_timeout=timedelta(minutes=15),
                schedule_to_close_timeout=timedelta(minutes=20),
                retry_policy=RetryPolicy(
                    maximum_attempts=3,
                    initial_interval=timedelta(seconds=60),
                    backoff_coefficient=3.0,
                ),
            )

            try:
                # Execute all activities in parallel
                await asyncio.gather(
                    *criteria_activities,
                    sales_action_activity,
                    contact_pipeline_role_activity,
                )
            except Exception as e:
                workflow.logger.exception("Error during parallel organization indexing")
                raise e


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Backfill stage criteria AI recommendations for organizations"
    )
    parser.add_argument(
        "--org-ids",
        nargs="+",
        type=str,
        help="One or more organization IDs to process. If not provided, will process all non-internal organizations.",
    )
    args = parser.parse_args()

    # Convert string UUIDs to UUID objects if provided
    org_ids = [UUID(org_id) for org_id in args.org_ids] if args.org_ids else None

    asyncio.run(backfill_stage_criteria_ai_recs(org_ids))
