[{"status": 200, "likelihood": 10, "data": {"id": "Q0xOFmXFx0yRzYBBn0v8sQ_0000", "full_name": "<PERSON>", "first_name": "<PERSON>", "middle_initial": "J", "middle_name": "<PERSON>", "last_initial": "C", "last_name": "<PERSON>ers", "sex": "Male", "birth_year": 1984, "birth_date": "1984-04-07", "linkedin_url": "linkedin.com/in/dchilders21", "linkedin_username": "dchilders21", "linkedin_id": "18416347", "facebook_url": "facebook.com/david.chi1ders", "facebook_username": "david.chi1ders", "facebook_id": "4918702", "twitter_url": null, "twitter_username": null, "github_url": "github.com/dchilders21", "github_username": "dchilders21", "work_email": "<EMAIL>", "personal_emails": ["<EMAIL>", "<EMAIL>"], "recommended_personal_email": "<EMAIL>", "mobile_phone": "+14045802632", "industry": "Marketing and Advertising", "job_title": "Senior Front End Developer", "job_title_role": "engineering", "job_title_sub_role": "web", "job_title_class": "research_and_development", "job_title_levels": ["senior"], "job_company_id": "HCOh9JQjxoqC6xhl7AY0nQn9kNvA", "job_company_name": "<PERSON><PERSON>", "job_company_website": "rapp.com", "job_company_size": "1001-5000", "job_company_founded": 1965, "job_company_industry": "Marketing and Advertising", "job_company_linkedin_url": "linkedin.com/company/rapp", "job_company_linkedin_id": "207539", "job_company_facebook_url": "facebook.com/rappworldwide", "job_company_twitter_url": "twitter.com/rapp", "job_company_location_name": "New York, New York, United States", "job_company_location_locality": "New York", "job_company_location_metro": "New York, New York", "job_company_location_region": "New York", "job_company_location_geo": "40.71,-73.99", "job_company_location_street_address": "220 East 42nd Street", "job_company_location_address_line_2": null, "job_company_location_postal_code": "10017", "job_company_location_country": "United States", "job_company_location_continent": "North America", "job_last_changed": "2019-12-02", "job_last_verified": "2025-01-07", "job_start_date": "2019-11", "location_name": "Atlanta, Georgia, United States", "location_locality": "Atlanta", "location_metro": "Atlanta, Georgia", "location_region": "Georgia", "location_country": "United States", "location_continent": "North America", "location_street_address": "1357 Lochland Road Southeast", "location_address_line_2": null, "location_postal_code": "30316", "location_geo": "33.76,-84.42", "location_last_updated": "2021-09-13", "phone_numbers": ["+14045802632", "+17709811462", "+14046267613"], "emails": [{"address": "<EMAIL>", "type": "personal"}, {"address": "<EMAIL>", "type": "current_professional"}, {"address": "<EMAIL>", "type": "professional"}, {"address": "<EMAIL>", "type": "personal"}, {"address": "<EMAIL>", "type": "professional"}], "interests": ["Automobiles", "Boating", "Cooking", "Electronics", "Exercise", "Fishing", "Fitness", "Food", "Gardening", "Golf", "Gourmet Cooking", "Health", "Home Decoration", "Home Improvement", "Investing", "Kids", "Medicine", "Movies", "Music", "Outdoors", "Reading", "Soccer", "Sports", "Watching Soccer", "Watching Sports"], "skills": ["Actionscript", "Amazon Web Services", "CSS", "Flash", "Html5", "Javascript", "Mysql", "Online Advertising", "Photoshop", "PHP", "<PERSON><PERSON>", "Python", "React", "Rich Media", "Sanity", "User Experience", "Web Design", "Woocommerce", "Wordpress"], "location_names": ["Atlanta, Georgia, United States", "San Francisco, California, United States", "Los Angeles, California, United States", "Decatur, Georgia, United States"], "regions": ["Georgia, United States", "California, United States"], "countries": ["United States"], "street_addresses": [{"name": "San Francisco, California, United States", "locality": "San Francisco", "region": "California", "metro": "San Francisco, California", "country": "United States", "continent": "North America", "street_address": "1005 Hyde Street", "address_line_2": "Apartment 3", "postal_code": "94109", "geo": "37.79,-122.4"}, {"name": "Los Angeles, California, United States", "locality": "Los Angeles", "region": "California", "metro": "Los Angeles, California", "country": "United States", "continent": "North America", "street_address": "10921 National Boulevard", "address_line_2": "Apartment 1", "postal_code": "90064", "geo": "34.03,-118.24"}, {"name": "Atlanta, Georgia, United States", "locality": "Atlanta", "region": "Georgia", "metro": "Atlanta, Georgia", "country": "United States", "continent": "North America", "street_address": "1357 Lochland Road Southeast", "address_line_2": null, "postal_code": "30316", "geo": "33.76,-84.42"}, {"name": "San Francisco, California, United States", "locality": "San Francisco", "region": "California", "metro": "San Francisco, California", "country": "United States", "continent": "North America", "street_address": "1724 Waller Street", "address_line_2": "Apartment D", "postal_code": "94117", "geo": "37.79,-122.4"}, {"name": "Los Angeles, California, United States", "locality": "Los Angeles", "region": "California", "metro": "Los Angeles, California", "country": "United States", "continent": "North America", "street_address": "1823 Holmby Avenue", "address_line_2": "Apartment 104", "postal_code": "90025", "geo": "34.03,-118.24"}, {"name": "San Francisco, California, United States", "locality": "San Francisco", "region": "California", "metro": "San Francisco, California", "country": "United States", "continent": "North America", "street_address": "1863 Lombard Street", "address_line_2": "Apartment 3", "postal_code": "94123", "geo": "37.79,-122.4"}, {"name": "Decatur, Georgia, United States", "locality": "Decatur", "region": "Georgia", "metro": "Atlanta, Georgia", "country": "United States", "continent": "North America", "street_address": "4833 Huntlea Court", "address_line_2": null, "postal_code": "30034", "geo": "33.72,-84.2"}, {"name": "San Francisco, California, United States", "locality": "San Francisco", "region": "California", "metro": "San Francisco, California", "country": "United States", "continent": "North America", "street_address": "531 Hyde Street", "address_line_2": "Apartment 5", "postal_code": "94109", "geo": "37.79,-122.4"}, {"name": "San Francisco, California, United States", "locality": "San Francisco", "region": "California", "metro": "San Francisco, California", "country": "United States", "continent": "North America", "street_address": "535 Stockton Street", "address_line_2": "Apartment 61", "postal_code": "94108", "geo": "37.79,-122.4"}, {"name": "San Francisco, California, United States", "locality": "San Francisco", "region": "California", "metro": "San Francisco, California", "country": "United States", "continent": "North America", "street_address": "Po Box 170413", "address_line_2": null, "postal_code": "94117", "geo": "37.79,-122.4"}], "experience": [{"company": {"name": "<PERSON><PERSON>", "size": "1001-5000", "id": "HCOh9JQjxoqC6xhl7AY0nQn9kNvA", "founded": 1965, "industry": "Marketing and Advertising", "location": {"name": "New York, New York, United States", "locality": "New York", "region": "New York", "metro": "New York, New York", "country": "United States", "continent": "North America", "street_address": "220 East 42nd Street", "address_line_2": null, "postal_code": "10017", "geo": "40.71,-73.99"}, "linkedin_url": "linkedin.com/company/rapp", "linkedin_id": "207539", "facebook_url": "facebook.com/rappworldwide", "twitter_url": "twitter.com/rapp", "website": "rapp.com"}, "location_names": ["San Francisco, California, United States", "New York, New York, United States"], "end_date": null, "start_date": "2019-11", "title": {"name": "Senior Front End Developer", "class": "research_and_development", "role": "engineering", "sub_role": "web", "levels": ["senior"]}, "is_primary": true}, {"company": {"name": "Organic", "size": "51-200", "id": "6ZzYtvdZS7H7XFgltQ9mNw92ciKG", "founded": 1993, "industry": "Marketing and Advertising", "location": {"name": "Cincinnati, Ohio, United States", "locality": "Cincinnati", "region": "Ohio", "metro": "Cincinnati, Ohio", "country": "United States", "continent": "North America", "street_address": "700 West Pete <PERSON>", "address_line_2": null, "postal_code": "45202", "geo": "39.13,-84.52"}, "linkedin_url": "linkedin.com/company/organic", "linkedin_id": "4259", "facebook_url": "facebook.com/organic.agency", "twitter_url": "twitter.com/organicinc", "website": "organic.com"}, "location_names": ["New York, New York, United States", "San Francisco, California, United States", "Cincinnati, Ohio, United States"], "end_date": "2022-01", "start_date": "2019-11", "title": {"name": "Senior Front End Developer", "class": "research_and_development", "role": "engineering", "sub_role": "web", "levels": ["senior"]}, "is_primary": false}, {"company": {"name": "Unitedmasters", "size": "201-500", "id": "TeFGKceB7aj4RnoCZsxsMQqy8ZMY", "founded": 2017, "industry": "Music", "location": {"name": "Brooklyn, New York, United States", "locality": "Brooklyn", "region": "New York", "metro": "New York, New York", "country": "United States", "continent": "North America", "street_address": "10 Jay Street", "address_line_2": null, "postal_code": "11201", "geo": "40.62,-74.03"}, "linkedin_url": "linkedin.com/company/unitedmasters", "linkedin_id": "11022780", "facebook_url": "facebook.com/unitedmasters", "twitter_url": "twitter.com/unitedmasters", "website": "unitedmasters.com"}, "location_names": ["San Francisco, California, United States", "Brooklyn, New York, United States"], "end_date": "2019-07", "start_date": "2019-02", "title": {"name": "Software Engineer", "class": "research_and_development", "role": "engineering", "sub_role": "software", "levels": []}, "is_primary": false}, {"company": {"name": "Red Panda Platform", "size": "1-10", "id": "O3jKPPT4KZ08V9hwTggBtgre1LCq", "founded": 2016, "industry": "Marketing and Advertising", "location": {"name": "San Francisco, California, United States", "locality": "San Francisco", "region": "California", "metro": "San Francisco, California", "country": "United States", "continent": "North America", "street_address": "633 Battery Street", "address_line_2": "Suite 118", "postal_code": "94111", "geo": "37.79,-122.4"}, "linkedin_url": "linkedin.com/company/red-panda-platform", "linkedin_id": "10476867", "facebook_url": null, "twitter_url": null, "website": "redpandaplatform.com"}, "location_names": ["San Francisco, California, United States"], "end_date": "2016-10", "start_date": "2013-10", "title": {"name": "Senior Front End Developer", "class": "research_and_development", "role": "engineering", "sub_role": "web", "levels": ["senior"]}, "is_primary": false}, {"company": {"name": "Flashtalking, Inc.", "size": "201-500", "id": "MJkFIZ1EnCywHtMK9pJm2AOBvLt9", "founded": 2001, "industry": "Online Media", "location": {"name": "New York, New York, United States", "locality": "New York", "region": "New York", "metro": "New York, New York", "country": "United States", "continent": "North America", "street_address": "120 Broadway", "address_line_2": "8th Floor", "postal_code": "10271", "geo": "40.71,-73.99"}, "linkedin_url": "linkedin.com/company/flashtalking-inc-", "linkedin_id": "817004", "facebook_url": "facebook.com/flashtalking", "twitter_url": "twitter.com/flashtalking", "website": "flashtalking.com"}, "location_names": ["New York, New York, United States"], "end_date": "2013-10", "start_date": "2012-10", "title": {"name": "Creative Developer", "class": null, "role": null, "sub_role": null, "levels": []}, "is_primary": false}, {"company": {"name": "<PERSON>wonder", "size": "51-200", "id": "h9gTFpZg6pYJS33Ss2oRGQCKhppk", "founded": 1999, "industry": "Marketing and Advertising", "location": {"name": "Atlanta, Georgia, United States", "locality": "Atlanta", "region": "Georgia", "metro": "Atlanta, Georgia", "country": "United States", "continent": "North America", "street_address": null, "address_line_2": null, "postal_code": null, "geo": "33.86,-84.47"}, "linkedin_url": "linkedin.com/company/eyewonder", "linkedin_id": "19102", "facebook_url": null, "twitter_url": "twitter.com/_ew", "website": "eyewonder.com"}, "location_names": ["Virgen De Los Remedios, Angeles, Philippines"], "end_date": "2012-06", "start_date": "2007-10", "title": {"name": "Creative Developer", "class": null, "role": null, "sub_role": null, "levels": []}, "is_primary": false}], "education": [{"school": {"name": "Uga Music Business Program", "type": "post-secondary institution", "id": "2ZLzxY3uLWog7viMhIwhAQ_0", "location": {"name": "Athens, Georgia, United States", "locality": "Athens", "region": "Georgia", "country": "United States", "continent": "North America"}, "linkedin_url": "linkedin.com/school/university-of-georgia---institute-for-leadership-advancement", "facebook_url": null, "twitter_url": null, "linkedin_id": "15100346", "website": "terry.uga.edu", "domain": "uga.edu"}, "degrees": ["Bachelors"], "start_date": "2002-01-01", "end_date": "2006", "majors": ["Management"], "minors": [], "gpa": null}, {"school": {"name": "Marist School", "type": null, "id": null, "location": null, "linkedin_url": null, "facebook_url": null, "twitter_url": null, "linkedin_id": null, "website": null, "domain": null}, "degrees": [], "start_date": null, "end_date": null, "majors": [], "minors": [], "gpa": null}, {"school": {"name": "University of Georgia", "type": "post-secondary institution", "id": "atSNCaXt6GMmL7XQFqDQXQ_0", "location": {"name": "Athens, Georgia, United States", "locality": "Athens", "region": "Georgia", "country": "United States", "continent": "North America"}, "linkedin_url": "linkedin.com/school/university-of-georgia", "facebook_url": "facebook.com/universityofga", "twitter_url": "twitter.com/universityofga", "linkedin_id": null, "website": "uga.edu", "domain": "uga.edu"}, "degrees": [], "start_date": null, "end_date": null, "majors": [], "minors": [], "gpa": null}], "profiles": [{"network": "linkedin", "id": "18416347", "url": "linkedin.com/in/dchilders21", "username": "dchilders21"}, {"network": "facebook", "id": "4918702", "url": "facebook.com/david.chi1ders", "username": "david.chi1ders"}, {"network": "github", "id": null, "url": "github.com/dchilders21", "username": "dchilders21"}, {"network": "linkedin", "id": "18416347", "url": "linkedin.com/in/david-childers-7372016", "username": "david-childers-7372016"}], "dataset_version": "30.0"}, "dataset_version": "30.0", "metadata": {"contact_id": "57966eb1-d0fb-4a38-93c4-1e3532da6767"}}]