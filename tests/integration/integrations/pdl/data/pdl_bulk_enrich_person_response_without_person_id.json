[{"status": 200, "likelihood": 8, "data": {"id": "jF2XkFMiNu3An7i-CIGRFg_0000", "full_name": "<PERSON>", "first_name": "<PERSON>", "middle_initial": "T", "middle_name": null, "last_initial": "R", "last_name": "Rum", "sex": "Male", "birth_year": 1990, "birth_date": "1990-11-09", "linkedin_url": "linkedin.com/in/matt-rum-6290a732", "linkedin_username": "matt-rum-6290a732", "linkedin_id": "113687745", "facebook_url": "facebook.com/matt.rum.1", "facebook_username": "matt.rum.1", "facebook_id": "1230870240", "twitter_url": "twitter.com/rumee4", "twitter_username": "Rumee4", "github_url": null, "github_username": null, "work_email": "<EMAIL>", "personal_emails": ["<EMAIL>", "<EMAIL>"], "recommended_personal_email": "<EMAIL>", "mobile_phone": "+19198122806", "industry": "Computer Software", "job_title": "Co-Founder", "job_title_role": "operations", "job_title_sub_role": "executive", "job_title_class": "general_and_administrative", "job_title_levels": ["owner"], "job_company_id": "m9EWGZMBRAJq8zdCNPvtKApFCt8O", "job_company_name": "Easy Money App", "job_company_website": "joinloopgolf.co", "job_company_size": "1-10", "job_company_founded": 2021, "job_company_industry": "Computer Software", "job_company_linkedin_url": "linkedin.com/company/loopgolfclub", "job_company_linkedin_id": "76246132", "job_company_facebook_url": null, "job_company_twitter_url": "mobile.twitter.com/loopgolfledger", "job_company_location_name": "San Francisco, California, United States", "job_company_location_locality": "San Francisco", "job_company_location_metro": "San Francisco, California", "job_company_location_region": "California", "job_company_location_geo": "37.79,-122.4", "job_company_location_street_address": null, "job_company_location_address_line_2": null, "job_company_location_postal_code": "94133", "job_company_location_country": "United States", "job_company_location_continent": "North America", "job_last_changed": "2021-12-10", "job_last_verified": "2024-03-21", "job_start_date": "2021-10", "location_name": "San Francisco, California, United States", "location_locality": "San Francisco", "location_metro": "San Francisco, California", "location_region": "California", "location_country": "United States", "location_continent": "North America", "location_street_address": "61 John <PERSON>", "location_address_line_2": null, "location_postal_code": "94133", "location_geo": "37.79,-122.4", "location_last_updated": "2024-03-18", "phone_numbers": ["+19198122806"], "emails": [{"address": "<EMAIL>", "type": "current_professional"}, {"address": "<EMAIL>", "type": "professional"}, {"address": "<EMAIL>", "type": "personal"}, {"address": "<EMAIL>", "type": null}, {"address": "<EMAIL>", "type": "personal"}], "interests": ["Art", "Art and Fashion", "Basketball", "Coffee and Innovative Technology", "Entrepreneurship", "Events", "Fashion", "Gardening", "Innovative Technology", "Marketing and Branding", "Surfing", "Surfing (Learning)", "Surfing and Basketball", "Technology", "Traveling"], "skills": ["Applicant Tracking Systems", "Athletics", "Blogging", "Event Management", "Event Planning", "Events", "Field Marketing", "Fundraising", "Hiring", "Hiring Practices", "Internet Recruiting", "Interviewing", "Leadership", "Management", "Marketing", "Microsoft Word", "Networking", "Public Speaking", "Recruiting", "Sales", "Sales Process", "salesforce.com", "Social Media Marketing", "Social Networking", "Sports", "Sports Marketing", "Strategic Communications", "Teamwork"], "location_names": ["San Francisco, California, United States", "Mill Valley, California, United States", "Annapolis, Maryland, United States", "Towson, Maryland, United States", "Williamsburg, Virginia, United States", "Wilmington, North Carolina, United States", "Chapel Hill, North Carolina, United States", "Ruxton, Maryland, United States"], "regions": ["California, United States", "Maryland, United States", "Virginia, United States", "North Carolina, United States"], "countries": ["United States"], "street_addresses": [{"name": "Williamsburg, Virginia, United States", "locality": "Williamsburg", "region": "Virginia", "metro": "Virginia Beach, Virginia", "country": "United States", "continent": "North America", "street_address": "115 Winston Drive", "address_line_2": null, "postal_code": "23185", "geo": "37.27,-76.7"}, {"name": "San Francisco, California, United States", "locality": "San Francisco", "region": "California", "metro": "San Francisco, California", "country": "United States", "continent": "North America", "street_address": "170 Cook Street", "address_line_2": "Apartment 1", "postal_code": "94118", "geo": "37.79,-122.4"}, {"name": "Ruxton, Maryland, United States", "locality": "<PERSON><PERSON><PERSON>", "region": "Maryland", "metro": null, "country": "United States", "continent": "North America", "street_address": "1820 Circle Road", "address_line_2": null, "postal_code": "21204", "geo": "39.40,-76.64"}, {"name": "Towson, Maryland, United States", "locality": "<PERSON><PERSON>", "region": "Maryland", "metro": "Baltimore, Maryland", "country": "United States", "continent": "North America", "street_address": "1820 Circle Road", "address_line_2": null, "postal_code": "21204", "geo": "39.40,-76.60"}, {"name": "Wilmington, North Carolina, United States", "locality": "Wilmington", "region": "North Carolina", "metro": "Wilmington, North Carolina", "country": "United States", "continent": "North America", "street_address": "1901 Wrightsville Green Avenue", "address_line_2": null, "postal_code": "28403", "geo": "34.21,-77.91"}, {"name": "San Francisco, California, United States", "locality": "San Francisco", "region": "California", "metro": "San Francisco, California", "country": "United States", "continent": "North America", "street_address": "2601 Mission Street", "address_line_2": null, "postal_code": "94110", "geo": "37.79,-122.4"}, {"name": "Mill Valley, California, United States", "locality": "Mill Valley", "region": "California", "metro": "San Francisco, California", "country": "United States", "continent": "North America", "street_address": "416 Maple Street", "address_line_2": null, "postal_code": "94941", "geo": "37.90,-122.54"}, {"name": "Annapolis, Maryland, United States", "locality": "Annapolis", "region": "Maryland", "metro": "Baltimore, Maryland", "country": "United States", "continent": "North America", "street_address": "607 Creek View Avenue", "address_line_2": null, "postal_code": "21403", "geo": "38.98,-76.47"}, {"name": "San Francisco, California, United States", "locality": "San Francisco", "region": "California", "metro": "San Francisco, California", "country": "United States", "continent": "North America", "street_address": "61 John <PERSON>", "address_line_2": "Apartment 1", "postal_code": "94133", "geo": "37.79,-122.4"}, {"name": "San Francisco, California, United States", "locality": "San Francisco", "region": "California", "metro": "San Francisco, California", "country": "United States", "continent": "North America", "street_address": "61 John <PERSON>", "address_line_2": null, "postal_code": "94133", "geo": "37.79,-122.4"}], "experience": [{"company": {"name": "Easy Money App", "size": "1-10", "id": "m9EWGZMBRAJq8zdCNPvtKApFCt8O", "founded": 2021, "industry": "Computer Software", "location": {"name": "San Francisco, California, United States", "locality": "San Francisco", "region": "California", "metro": "San Francisco, California", "country": "United States", "continent": "North America", "street_address": null, "address_line_2": null, "postal_code": "94133", "geo": "37.79,-122.4"}, "linkedin_url": "linkedin.com/company/loopgolfclub", "linkedin_id": "76246132", "facebook_url": null, "twitter_url": "mobile.twitter.com/loopgolfledger", "website": "joinloopgolf.co"}, "location_names": ["San Francisco, California, United States"], "end_date": null, "start_date": "2021-10", "title": {"name": "Co-Founder", "class": "general_and_administrative", "role": "operations", "sub_role": "executive", "levels": ["owner"]}, "is_primary": true}, {"company": {"name": "Cash App", "size": "1001-5000", "id": "5bzqIdaPuzEFnltwPCFnGAzyjCDx", "founded": null, "industry": "Internet", "location": {"name": "Atlanta, Georgia, United States", "locality": "Atlanta", "region": "Georgia", "metro": "Atlanta, Georgia", "country": "United States", "continent": "North America", "street_address": "695 North Avenue Northeast", "address_line_2": null, "postal_code": "30308", "geo": "33.76,-84.42"}, "linkedin_url": "linkedin.com/company/cash-app", "linkedin_id": "29296664", "facebook_url": "facebook.com/squarecash", "twitter_url": null, "website": "cash.app"}, "location_names": ["San Francisco, California, United States"], "end_date": "2021-10", "start_date": "2019-08", "title": {"name": "Product Operations Manager", "class": "research_and_development", "role": "product", "sub_role": "product_management", "levels": ["manager"]}, "is_primary": false}, {"company": {"name": "Square", "size": "1001-5000", "id": "i8aEevVrXCKUa9RjcBtH7Ajf1qsB", "founded": 2009, "industry": "Internet", "location": {"name": "San Francisco, California, United States", "locality": "San Francisco", "region": "California", "metro": "San Francisco, California", "country": "United States", "continent": "North America", "street_address": null, "address_line_2": null, "postal_code": "94103", "geo": "37.79,-122.4"}, "linkedin_url": "linkedin.com/company/joinsquare", "linkedin_id": "675562", "facebook_url": "facebook.com/square", "twitter_url": "twitter.com/square", "website": "squareup.com"}, "location_names": ["San Francisco, California, United States"], "end_date": "2019-06", "start_date": "2016-11", "title": {"name": "Talent Lead", "class": null, "role": null, "sub_role": null, "levels": ["manager"]}, "is_primary": false}, {"company": {"name": "Doubledutch", "size": "51-200", "id": "ByO7m97TjJzjSuHBA3poCwqTq837", "founded": 2011, "industry": "Computer Software", "location": {"name": "McLean, Virginia, United States", "locality": "<PERSON>", "region": "Virginia", "metro": null, "country": "United States", "continent": "North America", "street_address": "1765 Greensboro Station Place", "address_line_2": "7th Floor 7th Floor", "postal_code": "22102", "geo": "38.94,-77.19"}, "linkedin_url": "linkedin.com/company/doubledutch", "linkedin_id": "971844", "facebook_url": "facebook.com/playdoubledutch", "twitter_url": "twitter.com/doubledutch", "website": "doubledutch.me"}, "location_names": ["San Francisco, California, United States"], "end_date": "2016-10", "start_date": "2014-02", "title": {"name": "Sales & Marketing", "class": null, "role": null, "sub_role": null, "levels": []}, "is_primary": false}, {"company": {"name": "<PERSON><PERSON><PERSON> b.c.", "size": "1-10", "id": "t2gjjRnZitls2eBmIwlykwC0lTdN", "founded": null, "industry": null, "location": null, "linkedin_url": "linkedin.com/company/kolossos-rodou-b.c.", "linkedin_id": "27078057", "facebook_url": null, "twitter_url": null, "website": "kolossosbc.gr"}, "location_names": ["Rhodes, South Aegean, Greece"], "end_date": "2014", "start_date": "2013-08", "title": {"name": "Professional Basketball Player", "class": null, "role": null, "sub_role": null, "levels": []}, "is_primary": false}], "education": [{"school": {"name": "William & Mary", "type": "post-secondary institution", "id": "dgrsUa8KEMGvfVBKX6Be-g_0", "location": {"name": "Williamsburg, Virginia, United States", "locality": "Williamsburg", "region": "Virginia", "country": "United States", "continent": "North America"}, "linkedin_url": "linkedin.com/school/william-and-mary-public-policy", "facebook_url": "facebook.com/williamandmary", "twitter_url": "twitter.com/williamandmary", "linkedin_id": "64998234", "website": "wm.edu", "domain": "wm.edu"}, "degrees": ["Bachelors", "Bachelor of Arts"], "start_date": "2009-08", "end_date": "2013", "majors": ["Kinesiology"], "minors": [], "gpa": null}], "profiles": [{"network": "linkedin", "id": "113687745", "url": "linkedin.com/in/matt-rum-6290a732", "username": "matt-rum-6290a732"}, {"network": "facebook", "id": "1230870240", "url": "facebook.com/matt.rum.1", "username": "matt.rum.1"}, {"network": "twitter", "id": null, "url": "twitter.com/rumee4", "username": "rumee4"}], "dataset_version": "30.0"}, "dataset_version": "30.0", "metadata": {"contact_id": "cda7f137-e525-4d7e-b43a-17eb802c0c78"}}, {"status": 200, "likelihood": 9, "data": {"id": "WQSbVAfWtL6Rkxdy3zYkSA_0000", "full_name": "<PERSON>", "first_name": "<PERSON>", "middle_initial": "A", "middle_name": null, "last_initial": "R", "last_name": "<PERSON><PERSON>", "sex": "Male", "birth_year": 1993, "birth_date": "1993", "linkedin_url": "linkedin.com/in/joshua-rahm-8666b988", "linkedin_username": "joshua-rahm-8666b988", "linkedin_id": "312280206", "facebook_url": "facebook.com/joshua.rahm", "facebook_username": "joshua.rahm", "facebook_id": "100000405227961", "twitter_url": null, "twitter_username": null, "github_url": "github.com/joshuarahm", "github_username": "j<PERSON><PERSON><PERSON><PERSON>", "work_email": "<EMAIL>", "personal_emails": ["joshua<PERSON><PERSON>@gmail.com", "<EMAIL>", "<EMAIL>"], "recommended_personal_email": "joshua<PERSON><PERSON>@gmail.com", "mobile_phone": "+17202432230", "industry": "Computer Software", "job_title": "Software Engineer", "job_title_role": "engineering", "job_title_sub_role": "software", "job_title_class": "research_and_development", "job_title_levels": [], "job_company_id": "aKCIYBNF9ey6o5CjHCCO4goHYKlf", "job_company_name": "Google", "job_company_website": "google.com", "job_company_size": "10001+", "job_company_founded": 1998, "job_company_industry": "Internet", "job_company_linkedin_url": "linkedin.com/company/google", "job_company_linkedin_id": "1441", "job_company_facebook_url": "facebook.com/google", "job_company_twitter_url": "twitter.com/google", "job_company_location_name": "Mountain View, California, United States", "job_company_location_locality": "Mountain View", "job_company_location_metro": "San Jose, California", "job_company_location_region": "California", "job_company_location_geo": "37.4,-122.08", "job_company_location_street_address": "1600 Amphitheatre Parkway", "job_company_location_address_line_2": null, "job_company_location_postal_code": "94043", "job_company_location_country": "United States", "job_company_location_continent": "North America", "job_last_changed": "2017-12-04", "job_last_verified": "2025-02-07", "job_start_date": "2016-06", "location_name": "Longmont, Colorado, United States", "location_locality": "Longmont", "location_metro": "Boulder, Colorado", "location_region": "Colorado", "location_country": "United States", "location_continent": "North America", "location_street_address": "2133 Lombardy Street", "location_address_line_2": null, "location_postal_code": "80503", "location_geo": "40.17,-105.21", "location_last_updated": "2021-09-13", "phone_numbers": ["+17202432230", "+19705354139", "+19705350191", "+19705350186"], "emails": [{"address": "<EMAIL>", "type": "current_professional"}, {"address": "<EMAIL>", "type": "personal"}, {"address": "<EMAIL>", "type": "professional"}, {"address": "<EMAIL>", "type": "personal"}, {"address": "joshua<PERSON><PERSON>@gmail.com", "type": "personal"}, {"address": "<EMAIL>", "type": "professional"}, {"address": "<EMAIL>", "type": "current_professional"}], "interests": ["Golf", "New Technologies", "Programming", "Software Engineering"], "skills": ["C", "C++", "Eclipse", "Embedded Software", "Git", "<PERSON><PERSON>", "Java", "Latex", "Linux", "Mysql", "Object Oriented Design", "Oop", "Programming", "Python", "Shell Scripting", "Software Development", "Software Engineering", "Unix", "Web Development"], "location_names": ["Longmont, Colorado, United States", "Boulder, Colorado, United States", "Mead, Colorado, United States"], "regions": ["Colorado, United States"], "countries": ["United States"], "street_addresses": [{"name": "Mead, Colorado, United States", "locality": "<PERSON>", "region": "Colorado", "metro": "Greeley, Colorado", "country": "United States", "continent": "North America", "street_address": "18570 Wagon Trail", "address_line_2": null, "postal_code": "80542", "geo": "40.23,-104.99"}, {"name": "Boulder, Colorado, United States", "locality": "Boulder", "region": "Colorado", "metro": "Boulder, Colorado", "country": "United States", "continent": "North America", "street_address": "1953 Goss Street", "address_line_2": "Apartment 4", "postal_code": "80302", "geo": "40.01,-105.27"}, {"name": "Longmont, Colorado, United States", "locality": "Longmont", "region": "Colorado", "metro": "Boulder, Colorado", "country": "United States", "continent": "North America", "street_address": "2133 Lombardy Street", "address_line_2": null, "postal_code": "80503", "geo": "40.17,-105.21"}, {"name": "Boulder, Colorado, United States", "locality": "Boulder", "region": "Colorado", "metro": "Boulder, Colorado", "country": "United States", "continent": "North America", "street_address": "385 Martin Drive", "address_line_2": null, "postal_code": "80305", "geo": "40.01,-105.27"}], "experience": [{"company": {"name": "Google", "size": "10001+", "id": "aKCIYBNF9ey6o5CjHCCO4goHYKlf", "founded": 1998, "industry": "Internet", "location": {"name": "Mountain View, California, United States", "locality": "Mountain View", "region": "California", "metro": "San Jose, California", "country": "United States", "continent": "North America", "street_address": "1600 Amphitheatre Parkway", "address_line_2": null, "postal_code": "94043", "geo": "37.4,-122.08"}, "linkedin_url": "linkedin.com/company/google", "linkedin_id": "1441", "facebook_url": "facebook.com/google", "twitter_url": "twitter.com/google", "website": "google.com"}, "location_names": ["Boulder, Colorado, United States", "Mountain View, California, United States"], "end_date": null, "start_date": "2016-06", "title": {"name": "Software Engineer", "class": "research_and_development", "role": "engineering", "sub_role": "software", "levels": []}, "is_primary": true}, {"company": {"name": "Seagate Technology", "size": "10001+", "id": "gI79HhlrCCxBsVieB9lczwc5QdTM", "founded": 1979, "industry": "Computer Hardware", "location": {"name": "Fremont, California, United States", "locality": "Fremont", "region": "California", "metro": "San Francisco, California", "country": "United States", "continent": "North America", "street_address": "47488 Kato Road", "address_line_2": null, "postal_code": "94538", "geo": "37.57,-121.98"}, "linkedin_url": "linkedin.com/company/seagate-technology", "linkedin_id": "2868", "facebook_url": "facebook.com/seagate", "twitter_url": "twitter.com/seagate", "website": "seagate.com"}, "location_names": ["Cupertino, California, United States", "Longmont, Colorado, United States", "Fremont, California, United States"], "end_date": "2015-08", "start_date": "2015-05", "title": {"name": "Software Engineer Intern V", "class": "research_and_development", "role": "engineering", "sub_role": "software", "levels": ["training"]}, "is_primary": false}, {"company": {"name": "University of Colorado", "size": "10001+", "id": "KUAEnj5gZAPnddA4EXCbSQZD3EbX", "founded": 1876, "industry": "Higher Education", "location": {"name": "Denver, Colorado, United States", "locality": "Denver", "region": "Colorado", "metro": "Denver, Colorado", "country": "United States", "continent": "North America", "street_address": "1800 Grant Street", "address_line_2": null, "postal_code": "80203", "geo": "39.72,-104.98"}, "linkedin_url": "linkedin.com/company/university-of-colorado", "linkedin_id": "3770", "facebook_url": "facebook.com/cupresident", "twitter_url": "twitter.com/cusystem", "website": "cu.edu"}, "location_names": ["Denver, Colorado, United States", "Boulder, Colorado, United States"], "end_date": "2016-05", "start_date": "2014-09", "title": {"name": "Research Assistant", "class": "research_and_development", "role": "research", "sub_role": null, "levels": []}, "is_primary": false}, {"company": {"name": "LGS Innovations", "size": "1001-5000", "id": "shfCWNH4eif5YubA2TaQpAJEpxJP", "founded": 2007, "industry": "Defense & Space", "location": {"name": "Herndon, Virginia, United States", "locality": "Herndon", "region": "Virginia", "metro": "District of Columbia", "country": "United States", "continent": "North America", "street_address": "13461 Sunrise Valley Drive", "address_line_2": null, "postal_code": "20171", "geo": "38.92,-77.39"}, "linkedin_url": "linkedin.com/company/lgs-innovations", "linkedin_id": "164720", "facebook_url": "facebook.com/lgsinnovations", "twitter_url": "twitter.com/lgsinnovations", "website": "lgsinnovations.com"}, "location_names": ["Herndon, Virginia, United States"], "end_date": "2015-05", "start_date": "2012-05", "title": {"name": "Software Engineer <PERSON><PERSON>", "class": "research_and_development", "role": "engineering", "sub_role": "software", "levels": ["training"]}, "is_primary": false}, {"company": {"name": "Ridgeviewtel", "size": "51-200", "id": "2NLbAFOfvjZ7xCz9DxHqQw76ABAb", "founded": 2005, "industry": "Telecommunications", "location": {"name": "Longmont, Colorado, United States", "locality": "Longmont", "region": "Colorado", "metro": "Boulder, Colorado", "country": "United States", "continent": "North America", "street_address": "1880 Industrial Circle", "address_line_2": "Suite A", "postal_code": "80501", "geo": "40.17,-105.21"}, "linkedin_url": "linkedin.com/company/ridgeviewtel", "linkedin_id": "289731", "facebook_url": null, "twitter_url": null, "website": "ridgeviewtel.com"}, "location_names": ["Longmont, Colorado, United States"], "end_date": "2012-01", "start_date": "2010-06", "title": {"name": "Software Engineer <PERSON><PERSON>", "class": "research_and_development", "role": "engineering", "sub_role": "software", "levels": ["training"]}, "is_primary": false}], "education": [{"school": {"name": "University of Colorado Boulder", "type": "post-secondary institution", "id": "3rs1sp0sJAddUBtv2QeAXA_0", "location": {"name": "Boulder, Colorado, United States", "locality": "Boulder", "region": "Colorado", "country": "United States", "continent": "North America"}, "linkedin_url": "linkedin.com/school/cuboulder-music", "facebook_url": "facebook.com/cuboulder", "twitter_url": "twitter.com/cuboulder", "linkedin_id": "33216493", "website": "colorado.edu", "domain": "colorado.edu"}, "degrees": ["Masters", "Bachelors", "Master of Science", "Bachelor of Science"], "start_date": "2011", "end_date": "2016", "majors": ["Computer Science"], "minors": [], "gpa": null}, {"school": {"name": "University of Colorado Boulder", "type": "post-secondary institution", "id": "3rs1sp0sJAddUBtv2QeAXA_0", "location": {"name": "Boulder, Colorado, United States", "locality": "Boulder", "region": "Colorado", "country": "United States", "continent": "North America"}, "linkedin_url": "linkedin.com/school/cuboulder-music", "facebook_url": "facebook.com/cuboulder", "twitter_url": "twitter.com/cuboulder", "linkedin_id": "33216493", "website": "colorado.edu", "domain": "colorado.edu"}, "degrees": [], "start_date": null, "end_date": "2015", "majors": [], "minors": [], "gpa": null}, {"school": {"name": "Skyline High School", "type": "secondary school", "id": null, "location": null, "linkedin_url": null, "facebook_url": null, "twitter_url": null, "linkedin_id": null, "website": null, "domain": null}, "degrees": [], "start_date": null, "end_date": "2011", "majors": [], "minors": [], "gpa": null}], "profiles": [{"network": "linkedin", "id": "312280206", "url": "linkedin.com/in/joshua-rahm-8666b988", "username": "joshua-rahm-8666b988"}, {"network": "facebook", "id": "100000405227961", "url": "facebook.com/joshua.rahm", "username": "joshua.rahm"}, {"network": "github", "id": null, "url": "github.com/joshuarahm", "username": "j<PERSON><PERSON><PERSON><PERSON>"}, {"network": "gravatar", "id": null, "url": "gravatar.com/joshuarahm", "username": "j<PERSON><PERSON><PERSON><PERSON>"}], "dataset_version": "30.0"}, "dataset_version": "30.0", "metadata": {"contact_id": "f6b67c18-3552-4e99-a2f5-0ef42e0a0822"}}, {"status": 200, "likelihood": 9, "data": {"id": "NekJsRebP-0rM6A3BOlDlw_0000", "full_name": "<PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON><PERSON>", "middle_initial": null, "middle_name": null, "last_initial": "A", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "sex": "Male", "birth_year": null, "birth_date": null, "linkedin_url": "linkedin.com/in/santhosh-ayanikkat-0784b514", "linkedin_username": "santhosh-ayanikkat-0784b514", "linkedin_id": "49007900", "facebook_url": "facebook.com/santhosh.ayanikkat", "facebook_username": "santhosh.ayan<PERSON><PERSON>", "facebook_id": "100000934362670", "twitter_url": null, "twitter_username": null, "github_url": null, "github_username": null, "work_email": "<EMAIL>", "personal_emails": ["<EMAIL>", "<EMAIL>"], "recommended_personal_email": "<EMAIL>", "mobile_phone": "+18609672854", "industry": "Computer Software", "job_title": "Senior Project Manager", "job_title_role": "fulfillment", "job_title_sub_role": "project_management", "job_title_class": "services", "job_title_levels": ["senior"], "job_company_id": "2JEEUhQ7VYi8SEwitnXqQAREfk8j", "job_company_name": "Cognizant", "job_company_website": "cognizant.com", "job_company_size": "10001+", "job_company_founded": 1994, "job_company_industry": "Information Technology and Services", "job_company_linkedin_url": "linkedin.com/company/cognizant", "job_company_linkedin_id": "1680", "job_company_facebook_url": "facebook.com/cognizant", "job_company_twitter_url": "twitter.com/cognizant", "job_company_location_name": "Teaneck, New Jersey, United States", "job_company_location_locality": "Teaneck", "job_company_location_metro": "New York, New York", "job_company_location_region": "New Jersey", "job_company_location_geo": "40.89,-74.01", "job_company_location_street_address": "300 Frank West Burr Boulevard", "job_company_location_address_line_2": "Suite 600", "job_company_location_postal_code": "07666", "job_company_location_country": "United States", "job_company_location_continent": "North America", "job_last_changed": "2021-02-18", "job_last_verified": "2025-01-07", "job_start_date": "2020-01", "location_name": "Hartford, Connecticut, United States", "location_locality": "Hartford", "location_metro": "Hartford, Connecticut", "location_region": "Connecticut", "location_country": "United States", "location_continent": "North America", "location_street_address": "250 Main Street", "location_address_line_2": null, "location_postal_code": "06106", "location_geo": "41.76,-72.68", "location_last_updated": "2022-11-04", "phone_numbers": ["+18609672854"], "emails": [{"address": "<EMAIL>", "type": "personal"}, {"address": "<EMAIL>", "type": "current_professional"}, {"address": "<EMAIL>", "type": "personal"}], "interests": [], "skills": ["PMP"], "location_names": ["Hartford, Connecticut, United States", "South Windsor, Connecticut, United States", "East Windsor, Connecticut, United States"], "regions": ["Connecticut, United States"], "countries": ["United States"], "street_addresses": [{"name": "Hartford, Connecticut, United States", "locality": "Hartford", "region": "Connecticut", "metro": "Hartford, Connecticut", "country": "United States", "continent": "North America", "street_address": "250 Main Street", "address_line_2": null, "postal_code": "06106", "geo": "41.76,-72.68"}], "experience": [{"company": {"name": "Cognizant", "size": "10001+", "id": "2JEEUhQ7VYi8SEwitnXqQAREfk8j", "founded": 1994, "industry": "Information Technology and Services", "location": {"name": "Teaneck, New Jersey, United States", "locality": "Teaneck", "region": "New Jersey", "metro": "New York, New York", "country": "United States", "continent": "North America", "street_address": "300 Frank West Burr Boulevard", "address_line_2": "Suite 600", "postal_code": "07666", "geo": "40.89,-74.01"}, "linkedin_url": "linkedin.com/company/cognizant", "linkedin_id": "1680", "facebook_url": "facebook.com/cognizant", "twitter_url": "twitter.com/cognizant", "website": "cognizant.com"}, "location_names": ["Bloomfield, Connecticut, United States", "Windsor, Connecticut, United States"], "end_date": null, "start_date": "2020-01", "title": {"name": "Senior Project Manager", "class": "services", "role": "fulfillment", "sub_role": "project_management", "levels": ["senior"]}, "is_primary": true}, {"company": {"name": "Cognizant", "size": "10001+", "id": "2JEEUhQ7VYi8SEwitnXqQAREfk8j", "founded": 1994, "industry": "Information Technology and Services", "location": {"name": "Teaneck, New Jersey, United States", "locality": "Teaneck", "region": "New Jersey", "metro": "New York, New York", "country": "United States", "continent": "North America", "street_address": "300 Frank West Burr Boulevard", "address_line_2": "Suite 600", "postal_code": "07666", "geo": "40.89,-74.01"}, "linkedin_url": "linkedin.com/company/cognizant", "linkedin_id": "1680", "facebook_url": "facebook.com/cognizant", "twitter_url": "twitter.com/cognizant", "website": "cognizant.com"}, "location_names": ["Teaneck, New Jersey, United States"], "end_date": "2020-01", "start_date": "2004-10", "title": {"name": "Senior Associate", "class": null, "role": null, "sub_role": null, "levels": ["senior"]}, "is_primary": false}], "education": [{"school": {"name": "SCT College of Engineering", "type": "post-secondary institution", "id": null, "location": null, "linkedin_url": null, "facebook_url": null, "twitter_url": null, "linkedin_id": null, "website": null, "domain": null}, "degrees": [], "start_date": "2000", "end_date": "2004", "majors": ["Electronics"], "minors": [], "gpa": null}, {"school": {"name": "Sree Chithra College", "type": "post-secondary institution", "id": null, "location": null, "linkedin_url": null, "facebook_url": null, "twitter_url": null, "linkedin_id": null, "website": null, "domain": null}, "degrees": [], "start_date": null, "end_date": null, "majors": [], "minors": [], "gpa": null}], "profiles": [{"network": "linkedin", "id": "49007900", "url": "linkedin.com/in/santhosh-ayanikkat-0784b514", "username": "santhosh-ayanikkat-0784b514"}, {"network": "facebook", "id": "100000934362670", "url": "facebook.com/santhosh.ayanikkat", "username": "santhosh.ayan<PERSON><PERSON>"}], "dataset_version": "30.0"}, "dataset_version": "30.0", "metadata": {"contact_id": "ab1588a6-2618-45d0-bd03-e5c9c8519e9f"}}]