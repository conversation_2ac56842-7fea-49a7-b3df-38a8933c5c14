"""
To run this test, you need to run the following command:
    uv run pytest -v -s tests/integration/core/crm_sync/service/test_crm_sync_service.py

To run specifically TestCustomObjectImport test, you need to run the following command:
    uv run pytest -v -s tests/integration/core/crm_sync/service/test_crm_sync_service.py -k TestCustomObjectImport
"""

import random
import uuid
from collections.abc import Awaitable, Callable
from datetime import date, datetime
from decimal import Decimal
from typing import TypedDict, cast
from unittest.mock import AsyncMock, Mock, patch
from uuid import UUID, uuid4

import pytest
import pytz
from pydantic_extra_types.timezone_name import TimeZoneName

from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
    StdSelectListIdentifier,
)
from salestech_be.common.type.metadata.common import (
    CustomObjectIdentifier,
    FieldKind,
    StandardObjectIdentifier,
)
from salestech_be.common.type.metadata.field.field_indexable_config import (
    CaseAwareUniqueIndexableConfig,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    BooleanCheckboxFieldProperty,
    CurrencyFieldProperty,
    NumericFieldProperty,
    TextFieldProperty,
    TimestampFieldProperty,
)
from salestech_be.common.type.metadata.field.field_type_property_create import (
    SingleSelectFieldPropertyCreate,
)
from salestech_be.common.type.metadata.field.field_value import (
    BooleanCheckboxFieldValue,
    CurrencyFieldValue,
    NumericFieldValue,
    SingleSelectFieldValue,
    TextFieldValue,
    TimestampFieldValue,
)
from salestech_be.common.type.metadata.schema import (
    CustomObjectDescriptor,
    StandardObjectDescriptor,
)
from salestech_be.common.type.metadata.value import NativeValueType
from salestech_be.core.auth.service import UserAuthService
from salestech_be.core.custom_object.service.custom_object_service import (
    CustomObjectService,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.imports.models.import_job import (
    ColumnMapping,
    FileDupeResolution,
    FileImportType,
    FileMetadata,
    ImportConfiguration,  # Added ImportConfiguration
    ImportCsvJobStatus,
    ImportJob,
    ObjectImportMode,
    ObjectMapping,
    QualifiedImportField,
)
from salestech_be.core.imports.service.crm_sync_service import (
    CrmSyncService,
    get_crm_sync_service_from_db_engine,
)
from salestech_be.core.imports.types import (
    ImportPipelineRecord,
    ImportRecord,
)
from salestech_be.core.job.models import JobType
from salestech_be.core.metadata.dto.service_api_schema import (
    SelectListCreateRequest,
    SelectListValueCreateRequest,
    default_bootstrap_contact_stage_select_list_request,
)
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
)
from salestech_be.core.metadata.service.metadata_service import (
    MetadataService,
)
from salestech_be.core.metadata.service.pipeline_stage_service import (
    PipelineStageSelectListService,
)
from salestech_be.db.dao.oauth_repository import OauthRepository
from salestech_be.db.dao.user_integration_repository import UserIntegrationRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.user_integration_dto import UserIntegrationDto
from salestech_be.db.models.account import Account, AccountStatus
from salestech_be.db.models.contact import Contact
from salestech_be.db.models.core.types import CreatedSource
from salestech_be.db.models.custom_field import CustomField
from salestech_be.db.models.import_record import (
    ImportEntityType,
    ImportRecordStatus,
)
from salestech_be.db.models.import_record import (
    ImportRecord as DbImportRecord,
)
from salestech_be.db.models.job import Job, JobStatus
from salestech_be.db.models.pipeline import Pipeline, PipelineStatus
from salestech_be.db.models.task import Task
from salestech_be.ree_logging import get_logger
from salestech_be.util.asyncio_util.adapter import run_in_pool
from salestech_be.util.time import zoned_utc_now
from tests.integration.core.crm_sync.hubspot.test_hubspot_service import (
    prepare_hubspot_integration,
)
from tests.integration.core.crm_sync.service.test_utils import (
    _delete_import_records_by_org,
    _setup_import_repository_clean_slate_from_org_id,
)
from tests.test_util import read_response_file

logger = get_logger()


@pytest.fixture
def crm_sync_service(_engine: DatabaseEngine) -> CrmSyncService:
    return get_crm_sync_service_from_db_engine(_engine)


class TestCrmSyncService:
    raw_access_token = str(uuid.uuid4())
    raw_refresh_token = str(uuid.uuid4())

    @pytest.fixture
    async def hubspot_user_integration_dto(
        self,
        user_integration_repository: UserIntegrationRepository,
        oauth_repository: OauthRepository,
        select_list_service: InternalSelectListService,
        pipeline_stage_select_list_service: PipelineStageSelectListService,
    ) -> UserIntegrationDto:
        return await prepare_hubspot_integration(
            raw_access_token=self.raw_access_token,
            raw_refresh_token=self.raw_refresh_token,
            user_integration_repository=user_integration_repository,
            oauth_repository=oauth_repository,
            select_list_service=select_list_service,
            pipeline_stage_select_list_service=pipeline_stage_select_list_service,
        )

    @pytest.fixture
    async def setup_import_repository_clean_slate_from_hubspot_user_integration(
        self,
        crm_sync_service: CrmSyncService,
        hubspot_user_integration_dto: UserIntegrationDto,
    ) -> None:
        _ = await _delete_import_records_by_org(
            import_repository=crm_sync_service.import_repository,
            organization_id=hubspot_user_integration_dto.user_integration.organization_id,
        )

    async def sync_account(
        self,
        crm_sync_service: CrmSyncService,
        hubspot_user_integration_dto: UserIntegrationDto,
    ) -> None:
        await crm_sync_service.process_hubspot_crm_sync_job(
            job=Job(
                id=uuid.uuid4(),
                organization_id=hubspot_user_integration_dto.user_integration.organization_id,
                user_id=uuid.uuid4(),
                type=JobType.HUBSPOT_ACCOUNT_SYNC,
                metadata={},
                status=JobStatus.ENQUEUED,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
            )
        )
        inserted_accounts = await crm_sync_service.account_service.account_repository.list_all(
            organization_id=hubspot_user_integration_dto.user_integration.organization_id
        )
        assert len(inserted_accounts) == 8

        account_map: dict[str, Account] = {
            account.display_name: account for account in inserted_accounts
        }

        account_a = account_map.get("zhuventures.com")
        assert account_a is not None
        assert account_a.status == AccountStatus.TARGET
        assert account_a.official_website == "zhuventures.com"
        assert account_a.domain_name == "zhuventures.com"
        assert account_a.linkedin_url == "linkedin.com/zhuventures"

        import_records = await crm_sync_service.import_repository.list_all(
            organization_id=hubspot_user_integration_dto.user_integration.organization_id
        )

        import_map: dict[uuid.UUID, ImportRecord] = {
            record.entity_id: record for record in import_records if record.entity_id
        }

        record_a = import_map.get(account_a.id)

        assert len(import_records) == 8
        assert record_a is not None
        assert record_a.status == "SUCCESS"
        assert record_a.import_entity_type == "ACCOUNT"

    @patch("requests.get")
    async def test_sync_hubspot_contact_success(
        self,
        mocked_get: AsyncMock,
        setup_import_repository_clean_slate_from_hubspot_user_integration: None,
        crm_sync_service: CrmSyncService,
        hubspot_user_integration_dto: UserIntegrationDto,
    ) -> None:
        company_response = await run_in_pool(
            read_response_file,
            file_name="integration/integrations/hubspot/data/hubspot_list_company_response.json",
        )
        contact_response = await run_in_pool(
            read_response_file,
            file_name="integration/integrations/hubspot/data/hubspot_list_contact_response.json",
        )

        mocked_get.side_effect = [
            Mock(status_code=200, json=lambda: company_response),
            Mock(status_code=200, json=lambda: contact_response),
        ]
        await self.sync_account(
            crm_sync_service=crm_sync_service,
            hubspot_user_integration_dto=hubspot_user_integration_dto,
        )
        await crm_sync_service.process_hubspot_crm_sync_job(
            job=Job(
                id=uuid.uuid4(),
                organization_id=hubspot_user_integration_dto.user_integration.organization_id,
                user_id=uuid.uuid4(),
                type=JobType.HUBSPOT_CONTACT_SYNC,
                metadata={},
                status=JobStatus.ENQUEUED,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
            )
        )

        inserted_contacts = await crm_sync_service.contact_service.contact_repository.list_all(
            organization_id=hubspot_user_integration_dto.user_integration.organization_id
        )

        contact_map: dict[str, Contact] = {
            contact.display_name: contact for contact in inserted_contacts
        }

        assert len(inserted_contacts) == 3

        import_records_after_contact = await crm_sync_service.import_repository.list_all(
            organization_id=hubspot_user_integration_dto.user_integration.organization_id
        )
        assert len(import_records_after_contact) == 11
        import_map_after_contact: dict[uuid.UUID, ImportRecord] = {
            record.entity_id: record
            for record in import_records_after_contact
            if record.entity_id
        }

        leon_contact = contact_map.get("Test- 2 Leon")
        assert leon_contact is not None

        import_record_leon_contact = import_map_after_contact.get(leon_contact.id)
        assert import_record_leon_contact is not None
        assert import_record_leon_contact.external_id == "52320656766"

    @patch("requests.get")
    async def test_sync_hubspot_deal_success(
        self,
        mocked_get: AsyncMock,
        setup_import_repository_clean_slate_from_hubspot_user_integration: None,
        crm_sync_service: CrmSyncService,
        hubspot_user_integration_dto: UserIntegrationDto,
    ) -> None:
        company_response = await run_in_pool(
            read_response_file,
            file_name="integration/integrations/hubspot/data/hubspot_list_company_response.json",
        )
        contact_response = await run_in_pool(
            read_response_file,
            file_name="integration/integrations/hubspot/data/hubspot_list_contact_response.json",
        )
        pipeline_response = await run_in_pool(
            read_response_file,
            file_name="integration/integrations/hubspot/data/hubspot_list_pipeline_response.json",
        )
        deal_response = await run_in_pool(
            read_response_file,
            file_name="integration/integrations/hubspot/data/hubspot_list_deal_response.json",
        )

        mocked_get.side_effect = [
            Mock(status_code=200, json=lambda: company_response),
            Mock(status_code=200, json=lambda: contact_response),
            Mock(status_code=200, json=lambda: pipeline_response),
            Mock(status_code=200, json=lambda: deal_response),
        ]
        await self.sync_account(
            crm_sync_service=crm_sync_service,
            hubspot_user_integration_dto=hubspot_user_integration_dto,
        )
        await crm_sync_service.process_hubspot_crm_sync_job(
            job=Job(
                id=uuid.uuid4(),
                organization_id=hubspot_user_integration_dto.user_integration.organization_id,
                user_id=uuid.uuid4(),
                type=JobType.HUBSPOT_CONTACT_SYNC,
                metadata={},
                status=JobStatus.ENQUEUED,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
            )
        )
        await crm_sync_service.process_hubspot_crm_sync_job(
            job=Job(
                id=uuid.uuid4(),
                organization_id=hubspot_user_integration_dto.user_integration.organization_id,
                user_id=uuid.uuid4(),
                type=JobType.HUBSPOT_DEAL_SYNC,
                metadata={},
                status=JobStatus.ENQUEUED,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
            )
        )

        inserted_pipelines = await crm_sync_service.pipeline_service.pipeline_repository.list_all(
            organization_id=hubspot_user_integration_dto.user_integration.organization_id
        )
        pipeline_map: dict[str, Pipeline] = {
            pipeline.display_name: pipeline for pipeline in inserted_pipelines
        }
        assert len(inserted_pipelines) == 3
        pipeline_a = pipeline_map.get("ZhuVentureBigDeal")

        assert pipeline_a is not None
        assert pipeline_a.status == "PROSPECT"
        assert pipeline_a.amount == 200000
        assert pipeline_a.stage_id is not None

        pipeline__b = pipeline_map.get("Openstore Chance")
        assert pipeline__b is not None
        assert pipeline__b.stage_id == pipeline_a.stage_id

        import_records_after_deal = await crm_sync_service.import_repository.list_all(
            organization_id=hubspot_user_integration_dto.user_integration.organization_id
        )
        assert len(import_records_after_deal) == 14
        import_map_after_pipeline: dict[uuid.UUID, ImportRecord] = {
            record.entity_id: record
            for record in import_records_after_deal
            if record.entity_id
        }
        import_record_pipeline_a = import_map_after_pipeline.get(pipeline_a.id)
        assert import_record_pipeline_a is not None
        assert import_record_pipeline_a.external_id == "22111646985"

        await crm_sync_service.import_repository.update_import_record(
            organization_id=hubspot_user_integration_dto.user_integration.organization_id,
            record_id=import_record_pipeline_a.id,
            status=ImportRecordStatus.SUCCESS,
            status_detail="status_detail_test",
            job_id=uuid.uuid4(),
            conflicting_record=ImportPipelineRecord(
                display_name="display_name",
                amount="********",
                company_external_ids=["1234567"],
                contact_external_ids=["7654321"],
            ).model_dump(),
        )
        updated_import_record = await crm_sync_service.import_repository.get_by_id(
            organization_id=hubspot_user_integration_dto.user_integration.organization_id,
            record_id=import_record_pipeline_a.id,
        )
        assert updated_import_record is not None
        assert len(updated_import_record) == 1
        assert updated_import_record[0].status == ImportRecordStatus.SUCCESS
        assert updated_import_record[0].status_detail == "status_detail_test"
        assert updated_import_record[0].incoming_record is not None
        incoming_record = updated_import_record[0].incoming_record
        assert incoming_record.get("amount") == "********"

    @patch("requests.get")
    async def test_sync_hubspot_task_success(
        self,
        mocked_get: AsyncMock,
        setup_import_repository_clean_slate_from_hubspot_user_integration: None,
        crm_sync_service: CrmSyncService,
        hubspot_user_integration_dto: UserIntegrationDto,
    ) -> None:
        company_response = await run_in_pool(
            read_response_file,
            file_name="integration/integrations/hubspot/data/hubspot_list_company_response.json",
        )
        task_response = await run_in_pool(
            read_response_file,
            file_name="integration/integrations/hubspot/data/hubspot_list_task_response.json",
        )

        mocked_get.side_effect = [
            Mock(status_code=200, json=lambda: company_response),
            Mock(status_code=200, json=lambda: task_response),
        ]
        await self.sync_account(
            crm_sync_service=crm_sync_service,
            hubspot_user_integration_dto=hubspot_user_integration_dto,
        )
        await crm_sync_service.process_hubspot_crm_sync_job(
            job=Job(
                id=uuid.uuid4(),
                organization_id=hubspot_user_integration_dto.user_integration.organization_id,
                user_id=uuid.uuid4(),
                type=JobType.HUBSPOT_TASK_SYNC,
                metadata={},
                status=JobStatus.ENQUEUED,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
            )
        )

        inserted_tasks = await crm_sync_service.task_v2_service.task_repository.find_all_tasks_by_organization_id(
            organization_id=hubspot_user_integration_dto.user_integration.organization_id
        )

        task_map: dict[str, Task] = {task.title: task for task in inserted_tasks}

        assert len(inserted_tasks) == 6

        task_a = task_map.get("Outreach task (Email)")

        assert task_a is not None

        assert task_a.status == "OPEN"
        assert task_a.note == "foobar<br>"

        import_records_after_task = await crm_sync_service.import_repository.list_all(
            organization_id=hubspot_user_integration_dto.user_integration.organization_id
        )

        assert len(import_records_after_task) == 14
        import_map_after_task: dict[uuid.UUID, ImportRecord] = {
            record.entity_id: record
            for record in import_records_after_task
            if record.entity_id
        }

        import_record_task_a = import_map_after_task.get(task_a.id)
        assert import_record_task_a is not None
        assert import_record_task_a.external_id == "***********"

    async def test_map_type_by_reference_ids(
        self,
        setup_import_repository_clean_slate_from_hubspot_user_integration: None,
        crm_sync_service: CrmSyncService,
        hubspot_user_integration_dto: UserIntegrationDto,
    ) -> None:
        inserted_import_record = await crm_sync_service.import_repository.insert(
            DbImportRecord(
                id=uuid.uuid4(),
                import_entity_type=ImportEntityType.ACCOUNT,
                organization_id=hubspot_user_integration_dto.user_integration.organization_id,
                status=ImportRecordStatus.SUCCESS,
                status_detail=None,
                created_at=zoned_utc_now(),
                row_reference_id="jobjobjobid",
            )
        )

        import_records_after_task = await crm_sync_service.import_repository.list_all(
            organization_id=hubspot_user_integration_dto.user_integration.organization_id
        )

        assert len(import_records_after_task) == 1

        import_records_map = await crm_sync_service.import_repository.map_type_by_reference_ids(
            organization_id=hubspot_user_integration_dto.user_integration.organization_id,
            reference_id="jobjobjobid",
        )

        assert import_records_map is not None
        assert (
            import_records_map.get(ImportEntityType.ACCOUNT)
            == import_records_after_task
        )
        record = import_records_map.get(ImportEntityType.ACCOUNT)
        assert record is not None
        assert record[0] == inserted_import_record

    async def test_import_pipeline_record_serialization(self) -> None:
        record = ImportPipelineRecord(
            display_name="display_name",
            amount="********",
            company_external_ids=["1234567"],
            contact_external_ids=["7654321"],
        )
        record_dict = record.model_dump()
        assert record_dict["display_name"] == "display_name"
        assert record_dict["amount"] == "********"

    class TestAccountImport:
        async def test_import_account_record_happy_path(
            self,
            crm_sync_service: CrmSyncService,
            user_auth_service: UserAuthService,
            make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
        ) -> None:
            """Test importing an account record via CSV."""
            user_id, organization_id = await make_user_org()
            logger.info(f"user_id: {user_id}, organization_id: {organization_id}")
            await _setup_import_repository_clean_slate_from_org_id(
                crm_sync_service=crm_sync_service,
                organization_id=organization_id,
            )

            user = await user_auth_service.user_repository.get_by_id(user_id=user_id)
            logger.info(f"user: {user}")
            assert user is not None, "User should not be None"

            # Create a job for account import - use created_user.id instead of a new UUID
            job_id = uuid4()
            job = ImportJob(
                id=job_id,
                organization_id=organization_id,
                created_by_user_id=user_id,
                display_name="Test Account CSV Import Happy Path",
                status=ImportCsvJobStatus.STARTED,  # Changed from ENQUEUED
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=FileMetadata(
                    file_id=uuid4(),  # Dummy file_id
                    original_filename="happy_path_accounts.csv",
                    object_mappings=[
                        ObjectMapping(
                            object_identifier=StandardObjectIdentifier(
                                object_name=StdObjectIdentifiers.account
                            ),
                            column_mappings=[
                                ColumnMapping(
                                    column_name="display_name",
                                    qualified_field=QualifiedImportField(
                                        path=["company_display_name"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="domain_name",
                                    qualified_field=QualifiedImportField(
                                        path=["company_domain_name"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="official_website_url",
                                    qualified_field=QualifiedImportField(
                                        path=["company_official_website"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="company_description",
                                    qualified_field=QualifiedImportField(
                                        path=["company_description"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="company_industry",
                                    qualified_field=QualifiedImportField(
                                        path=["company_industry"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="company_technologies",
                                    qualified_field=QualifiedImportField(
                                        path=["company_technologies"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="estimated_annual_revenue",
                                    qualified_field=QualifiedImportField(
                                        path=["company_estimated_annual_revenue"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="estimated_employee_count",
                                    qualified_field=QualifiedImportField(
                                        path=["company_estimated_employee_count"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="linkedin_url",
                                    qualified_field=QualifiedImportField(
                                        path=["company_linkedin_url"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="facebook_url",
                                    qualified_field=QualifiedImportField(
                                        path=["company_facebook_url"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="x_url",
                                    qualified_field=QualifiedImportField(
                                        path=["company_x_url"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="street_one",
                                    qualified_field=QualifiedImportField(
                                        path=["company_address_street_one"]
                                    ),
                                ),  # Path for model_extra
                                ColumnMapping(
                                    column_name="street_two",
                                    qualified_field=QualifiedImportField(
                                        path=["company_address_street_two"]
                                    ),
                                ),  # Path for model_extra
                                ColumnMapping(
                                    column_name="zip_code",
                                    qualified_field=QualifiedImportField(
                                        path=["company_address_zip_code"]
                                    ),
                                ),  # Path for model_extra
                                ColumnMapping(
                                    column_name="city",
                                    qualified_field=QualifiedImportField(
                                        path=["company_address_city"]
                                    ),
                                ),  # Path for model_extra
                                ColumnMapping(
                                    column_name="state",
                                    qualified_field=QualifiedImportField(
                                        path=["company_address_state"]
                                    ),
                                ),  # Path for model_extra
                                ColumnMapping(
                                    column_name="country",
                                    qualified_field=QualifiedImportField(
                                        path=["company_address_country"]
                                    ),
                                ),  # Path for model_extra
                                ColumnMapping(
                                    column_name="status",
                                    qualified_field=QualifiedImportField(
                                        path=["company_status"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="owner_email",
                                    qualified_field=QualifiedImportField(
                                        path=["company_owner_email"]
                                    ),
                                ),
                            ],
                        )
                    ],
                    association_label_mapping=[],
                ),
                configuration=ImportConfiguration(
                    object_identifiers=[
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account
                        )
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.UPSERT,
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                ),  # Changed from None
                workflow_id=None,
                completed_at=None,
                result_metadata=None,
                object_identifier=None,  # Set as in example
            )

            prefix = random.choice(["Enterprise", "Strategic", "Global", "Premium"])  # noqa: S311
            suffix = random.choice(["Solutions", "Partnership", "Expansion", "Deal"])  # noqa: S311
            number = random.randint(1, 999)  # noqa: S311
            random_display_name = f"{prefix} {suffix} {number}"

            account_status = "TARGET"  # Could be TARGET, SELLING, or CUSTOMER
            # Prepare CSV content with account fields
            csv_content = (
                "display_name,domain_name,official_website_url,company_description,company_industry,company_technologies,"
                "estimated_annual_revenue,estimated_employee_count,linkedin_url,facebook_url,x_url,"
                "street_one,street_two,zip_code,city,state,country,status,owner_email\n"
                f"{random_display_name},testcompany.com,https://testcompany.com,A test company,Tech;Software,"
                "Python;AWS,1000000,100,https://linkedin.com/company/test,https://facebook.com/test,"
                "https://x.com/test,123 Test St,Suite 100,12345,Test City,Test State,Test Country,"
                f"{account_status},{user.email}"
            )

            # Process the import using internal methods similar to test_csv_import_creates_objects.py
            row_num_to_csv_row_map = await crm_sync_service._process_csv_data_v2(
                file_binary=csv_content.encode(),
                csv_import_job=job,
                heartbeat_resume=None,
            )
            assert row_num_to_csv_row_map is not None, (
                "CSV data processing failed to produce row map"
            )

            processed_records = await crm_sync_service._process_import_rows_v2(
                csv_import_job=job,
                row_num_to_csv_row_map=row_num_to_csv_row_map,
                heartbeat_resume=None,
            )

            # Verify results - processed_records is a list of ImportRecord
            # The old test checked result["error"], this is no longer applicable.
            assert len(processed_records) > 0, (
                "No records were processed by _process_import_rows_v2"
            )

            # Verify the record was created by fetching from DB (as in the original test)
            # This part remains mostly the same, just ensure job.id is correctly used.
            db_records = await crm_sync_service.import_repository.list_by_job_id(
                organization_id=organization_id,
                job_id=job.id,
            )

            assert len(db_records) == 1
            record = db_records[0]

            # Verify import record details
            assert record.status == ImportRecordStatus.SUCCESS
            assert record.import_entity_type == ImportEntityType.ACCOUNT
            assert record.entity_id is not None

            # Verify the account was created with correct data
            account = await crm_sync_service.account_service.get_account_v2(
                account_id=record.entity_id,
                organization_id=organization_id,
            )
            logger.info(f"account: {account}")

            assert account.display_name == random_display_name
            assert account.domain_name == "testcompany.com"
            assert account.official_website == "https://testcompany.com"
            assert account.description == "A test company"
            assert account.estimated_annual_revenue == 1000000
            assert account.estimated_employee_count == 100
            assert account.linkedin_url == "https://linkedin.com/company/test"
            assert account.facebook_url == "https://facebook.com/test"
            assert account.x_url == "https://x.com/test"
            assert account.status == AccountStatus.TARGET
            assert account.owner_user_id == user_id  # Verify owner was set correctly
            assert account.street_one == "123 Test St"
            assert account.street_two == "Suite 100"
            assert account.zip_code == "12345"
            assert account.city == "Test City"
            assert account.state == "Test State"
            assert account.country == "Test Country"

        async def test_import_account_happy_path_but_no_street_one(
            self,
            crm_sync_service: CrmSyncService,
            user_auth_service: UserAuthService,
            make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
        ) -> None:
            """Test importing an account record via CSV."""
            user_id, organization_id = await make_user_org()
            logger.info(f"user_id: {user_id}, organization_id: {organization_id}")
            await _setup_import_repository_clean_slate_from_org_id(
                crm_sync_service=crm_sync_service,
                organization_id=organization_id,
            )

            user = await user_auth_service.user_repository.get_by_id(user_id=user_id)
            logger.info(f"user: {user}")
            assert user is not None, "User should not be None"

            # Create a job for account import - use created_user.id instead of a new UUID
            job_id = uuid4()
            job = ImportJob(
                id=job_id,
                organization_id=organization_id,
                created_by_user_id=user_id,
                display_name="Test Account CSV Import Happy Path",
                status=ImportCsvJobStatus.STARTED,  # Changed from ENQUEUED
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=FileMetadata(
                    file_id=uuid4(),  # Dummy file_id
                    original_filename="happy_path_accounts.csv",
                    object_mappings=[
                        ObjectMapping(
                            object_identifier=StandardObjectIdentifier(
                                object_name=StdObjectIdentifiers.account
                            ),
                            column_mappings=[
                                ColumnMapping(
                                    column_name="display_name",
                                    qualified_field=QualifiedImportField(
                                        path=["company_display_name"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="domain_name",
                                    qualified_field=QualifiedImportField(
                                        path=["company_domain_name"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="official_website_url",
                                    qualified_field=QualifiedImportField(
                                        path=["company_official_website"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="company_description",
                                    qualified_field=QualifiedImportField(
                                        path=["company_description"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="company_industry",
                                    qualified_field=QualifiedImportField(
                                        path=["company_industry"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="company_technologies",
                                    qualified_field=QualifiedImportField(
                                        path=["company_technologies"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="estimated_annual_revenue",
                                    qualified_field=QualifiedImportField(
                                        path=["company_estimated_annual_revenue"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="estimated_employee_count",
                                    qualified_field=QualifiedImportField(
                                        path=["company_estimated_employee_count"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="linkedin_url",
                                    qualified_field=QualifiedImportField(
                                        path=["company_linkedin_url"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="facebook_url",
                                    qualified_field=QualifiedImportField(
                                        path=["company_facebook_url"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="x_url",
                                    qualified_field=QualifiedImportField(
                                        path=["company_x_url"]
                                    ),
                                ),
                                # ColumnMapping(
                                #     column_name="street_one",
                                #     qualified_field=QualifiedImportField(
                                #         path=["company_address_street_one"]
                                #     ),
                                # ),  # Testing no street_one
                                ColumnMapping(
                                    column_name="street_two",
                                    qualified_field=QualifiedImportField(
                                        path=["company_address_street_two"]
                                    ),
                                ),  # Path for model_extra
                                ColumnMapping(
                                    column_name="zip_code",
                                    qualified_field=QualifiedImportField(
                                        path=["company_address_zip_code"]
                                    ),
                                ),  # Path for model_extra
                                ColumnMapping(
                                    column_name="city",
                                    qualified_field=QualifiedImportField(
                                        path=["company_address_city"]
                                    ),
                                ),  # Path for model_extra
                                ColumnMapping(
                                    column_name="state",
                                    qualified_field=QualifiedImportField(
                                        path=["company_address_state"]
                                    ),
                                ),  # Path for model_extra
                                ColumnMapping(
                                    column_name="country",
                                    qualified_field=QualifiedImportField(
                                        path=["company_address_country"]
                                    ),
                                ),  # Path for model_extra
                                ColumnMapping(
                                    column_name="status",
                                    qualified_field=QualifiedImportField(
                                        path=["company_status"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="owner_email",
                                    qualified_field=QualifiedImportField(
                                        path=["company_owner_email"]
                                    ),
                                ),
                            ],
                        )
                    ],
                    association_label_mapping=[],
                ),
                configuration=ImportConfiguration(
                    object_identifiers=[
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account
                        )
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.UPSERT,
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                ),  # Changed from None
                workflow_id=None,
                completed_at=None,
                result_metadata=None,
                object_identifier=None,  # Set as in example
            )

            prefix = random.choice(["Enterprise", "Strategic", "Global", "Premium"])  # noqa: S311
            suffix = random.choice(["Solutions", "Partnership", "Expansion", "Deal"])  # noqa: S311
            number = random.randint(1, 999)  # noqa: S311
            random_display_name = f"{prefix} {suffix} {number}"

            account_status = "TARGET"  # Could be TARGET, SELLING, or CUSTOMER
            # Prepare CSV content with account fields
            csv_content = (
                "display_name,domain_name,official_website_url,company_description,company_industry,company_technologies,"
                "estimated_annual_revenue,estimated_employee_count,linkedin_url,facebook_url,x_url,"
                "street_two,zip_code,city,state,country,status,owner_email\n"
                f"{random_display_name},testcompany.com,https://testcompany.com,A test company,Tech;Software,"
                "Python;AWS,500-1000777++++,98-298+,https://linkedin.com/company/test,https://facebook.com/test,"
                "https://x.com/test,Suite 100,12345,Test City,Test State,Test Country,"
                f"{account_status},{user.email}"
            )

            # Process the import using internal methods similar to test_csv_import_creates_objects.py
            row_num_to_csv_row_map = await crm_sync_service._process_csv_data_v2(
                file_binary=csv_content.encode(),
                csv_import_job=job,
                heartbeat_resume=None,
            )
            assert row_num_to_csv_row_map is not None, (
                "CSV data processing failed to produce row map"
            )

            processed_records = await crm_sync_service._process_import_rows_v2(
                csv_import_job=job,
                row_num_to_csv_row_map=row_num_to_csv_row_map,
                heartbeat_resume=None,
            )

            # Verify results - processed_records is a list of ImportRecord
            # The old test checked result["error"], this is no longer applicable.
            assert len(processed_records) > 0, (
                "No records were processed by _process_import_rows_v2"
            )

            # Verify the record was created by fetching from DB (as in the original test)
            # This part remains mostly the same, just ensure job.id is correctly used.
            db_records = await crm_sync_service.import_repository.list_by_job_id(
                organization_id=organization_id,
                job_id=job.id,
            )

            assert len(db_records) == 1
            record = db_records[0]

            # Verify import record details
            assert record.status == ImportRecordStatus.SUCCESS
            assert record.import_entity_type == ImportEntityType.ACCOUNT
            assert record.entity_id is not None

            # Verify the account was created with correct data
            account = await crm_sync_service.account_service.get_account_v2(
                account_id=record.entity_id,
                organization_id=organization_id,
            )
            logger.info(f"account: {account}")

            assert account.display_name == random_display_name
            assert account.domain_name == "testcompany.com"
            assert account.official_website == "https://testcompany.com"
            assert account.description == "A test company"
            assert account.estimated_annual_revenue == 1000777
            assert account.estimated_employee_count == 298
            assert account.linkedin_url == "https://linkedin.com/company/test"
            assert account.facebook_url == "https://facebook.com/test"
            assert account.x_url == "https://x.com/test"
            assert account.status == AccountStatus.TARGET
            assert account.owner_user_id == user_id  # Verify owner was set correctly
            # Address should still work without street_one
            assert account.street_one is None
            assert account.street_two == "Suite 100"
            assert account.zip_code == "12345"
            assert account.city == "Test City"
            assert account.state == "Test State"
            assert account.country == "Test Country"

        # Example of creating an account record with custom fields by hand. Doesn't actually test imports
        async def test_create_account_record_with_custom_fields(
            self,
            custom_object_service: CustomObjectService,
            metadata_service: MetadataService,
        ) -> None:
            """Test creating an account record with custom fields."""

            user_id = uuid.uuid4()
            organization_id = uuid.uuid4()

            # Create extension custom object
            account_object_with_custom_fields = (
                await custom_object_service.enable_extension_custom_object(
                    user_id=user_id,
                    organization_id=organization_id,
                    objects_to_enable=[ExtendableStandardObject.account],
                )
            )
            logger.info(
                f"account_object_with_custom_fields: {account_object_with_custom_fields}"
            )

            # Add null check for account object
            account_object = account_object_with_custom_fields.get(
                ExtendableStandardObject.account
            )
            if not account_object:
                raise ValueError("Failed to create account custom object")

            """ Create custom fields """
            # Text (single line)
            account_custom_text_field = (
                await custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=account_object.id,
                    custom_field_type_property_create=TextFieldProperty(
                        field_display_name="Account Custom Field Test 1",
                        is_required=False,
                        index_config=CaseAwareUniqueIndexableConfig(is_indexed=True),
                    ),
                )
            )
            logger.info(f"account_custom_text_field: {account_custom_text_field}")

            """ Select List Custom Field """
            # Create select list for music genres
            select_list = (
                await custom_object_service.select_list_service.create_select_list(
                    select_list_create_request=SelectListCreateRequest(
                        display_name="Select List Test",
                        application_code_name=None,
                        description="Testing select lists on account",
                        data_type=NativeValueType.STRING,
                        actioning_user_id=user_id,
                        organization_id=organization_id,
                    )
                )
            )

            # Add the select list value options and store their IDs
            select_list_values = ["1", "2", "3", "abc", "xyz"]
            for display_value in select_list_values:
                await custom_object_service.select_list_service.add_select_list_value(
                    select_list_id=select_list.id,
                    actioning_user_id=user_id,
                    organization_id=organization_id,
                    slvcr=SelectListValueCreateRequest(
                        display_value=display_value,
                        is_default=False,
                    ),
                )

            select_list_custom_field = (
                await custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=account_object.id,  # Now safe to access .id
                    custom_field_type_property_create=TextFieldProperty(
                        field_display_name="Select List Account custom field",
                        is_required=False,
                        index_config=CaseAwareUniqueIndexableConfig(is_indexed=True),
                    ),
                )
            )
            logger.info(f"select_list_custom_field: {select_list_custom_field}")
            """ End of Select List Custom Field """

            # Create extension custom object record
            account_extension_record = (
                await custom_object_service.create_custom_object_data_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_dto_or_id=account_object.id,  # Now safe to access .id
                    custom_field_data_by_field_id={
                        account_custom_text_field.id: "test_value",
                        select_list_custom_field.id: random.choice(select_list_values),  # noqa: S311
                    },
                    display_name="Source Record",
                    extension_id=uuid.uuid4(),
                )
            )
            logger.info(f"account_extension_record: {account_extension_record}")

            org_schema = await metadata_service.get_organization_domain_object_schema(
                organization_id=organization_id,
            )
            # logger.info(f"org_schema: {org_schema}")

            # Define a TypedDict for the account object dictionary
            class AccountObjectDict(TypedDict):
                name: str
                standard_fields: list[str]
                custom_fields: list[str]

            # Use the TypedDict for type safety
            account_object_dict: AccountObjectDict = {
                "name": "",
                "standard_fields": [],
                "custom_fields": [],
            }

            for org_schema_object in org_schema.objects:
                # Handle both StandardObjectIdentifier and CustomObjectIdentifier
                # Check if it's a standard object first
                # if hasattr(org_schema_object.object_identifier, "object_name"):
                if isinstance(org_schema_object, StandardObjectDescriptor):
                    object_name = org_schema_object.object_identifier.object_name
                elif isinstance(org_schema_object, CustomObjectDescriptor):
                    # For custom objects, use the display name instead
                    object_name = org_schema_object.display_name
                else:
                    raise ValueError(f"Unknown object type: {type(org_schema_object)}")

                if object_name == "account":
                    logger.info(f"object: {org_schema_object}\n\n")
                    account_object_dict["name"] = object_name
                    for field in org_schema_object.fields:
                        logger.info(f"field: {field}\n\n")
                        if field.field_kind == FieldKind.STANDARD:
                            # Lists are guaranteed to exist now due to TypedDict
                            account_object_dict["standard_fields"].append(
                                field.field_identifier.field_name
                            )
                        elif field.field_kind == FieldKind.CUSTOM:
                            # Lists are guaranteed to exist now due to TypedDict
                            account_object_dict["custom_fields"].append(
                                field.field_type_property.field_display_name
                            )
                        else:
                            raise ValueError(f"Unknown field kind: {field.field_kind}")

            logger.info(f"account_object_dict: {account_object_dict}\n\n")

        async def test_import_account_record_with_custom_fields_happy_path(
            self,
            crm_sync_service: CrmSyncService,
            custom_object_service: CustomObjectService,
            user_auth_service: UserAuthService,
            make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
        ) -> None:
            """Test importing an account record with custom fields."""

            user_id, organization_id = await make_user_org()
            logger.info(f"user_id: {user_id}, organization_id: {organization_id}")
            await _setup_import_repository_clean_slate_from_org_id(
                crm_sync_service=crm_sync_service,
                organization_id=organization_id,
            )

            user = await user_auth_service.user_repository.get_by_id(user_id=user_id)
            logger.info(f"user: {user}")
            assert user is not None, "User should not be None"

            # Create extension custom object
            account_object_with_custom_fields = (
                await custom_object_service.enable_extension_custom_object(
                    user_id=user_id,
                    organization_id=organization_id,
                    objects_to_enable=[ExtendableStandardObject.account],
                )
            )
            logger.info(
                f"account_object_with_custom_fields: {account_object_with_custom_fields}"
            )

            # Add null check for account object
            account_object = account_object_with_custom_fields.get(
                ExtendableStandardObject.account
            )
            if not account_object:
                raise ValueError("Failed to create account custom object")

            # Create custom fields
            # Text (single line)
            account_custom_text_field = (
                await custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=account_object.id,  # Now safe to access .id
                    custom_field_type_property_create=TextFieldProperty(
                        field_display_name="Account Custom Field Test 1",
                        is_required=False,
                    ),
                )
            )
            logger.info(f"account_custom_text_field: {account_custom_text_field}")

            # Amount/Currency
            # Note: This doesn't take decimals in the Web UI
            account_custom_amount_field = (
                await custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=account_object.id,
                    custom_field_type_property_create=CurrencyFieldProperty(
                        field_display_name="Currency Field",
                        is_required=False,
                        decimal_precision=5,
                        total_precision=10,
                    ),
                )
            )
            logger.info(f"account_custom_amount_field: {account_custom_amount_field}")

            # Number
            # Note: This doesn't take decimals in the Web UI
            account_custom_number_field = (
                await custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=account_object.id,
                    custom_field_type_property_create=NumericFieldProperty(
                        field_display_name="Number Field",
                        is_required=False,
                        decimal_precision=5,
                        total_precision=10,
                    ),
                )
            )
            logger.info(f"account_custom_number_field: {account_custom_number_field}")

            # Date
            # Note: Can take in whatever date format, must cast to <YYYY-MM-DD> format on backend
            # Returns <MM/DD/YYYY> format on Web UI
            # Examples: 3/20/2025, 1/1/2025, 12/31/2024
            account_custom_date_field = (
                await custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=account_object.id,
                    custom_field_type_property_create=TimestampFieldProperty(
                        field_display_name="Date Field",
                        is_required=False,
                    ),
                )
            )
            logger.info(f"account_custom_date_field: {account_custom_date_field}")

            # Boolean/Checkbox
            account_custom_boolean_field = (
                await custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=account_object.id,
                    custom_field_type_property_create=BooleanCheckboxFieldProperty(
                        field_display_name="Boolean Field",
                        is_required=False,
                    ),
                )
            )
            logger.info(f"account_custom_boolean_field: {account_custom_boolean_field}")

            """ Select List Custom Field """
            # Create select list for music genres
            select_list = (
                await custom_object_service.select_list_service.create_select_list(
                    select_list_create_request=SelectListCreateRequest(
                        display_name="Select List Test",
                        application_code_name=None,
                        description="Testing select lists on account",
                        data_type=NativeValueType.STRING,
                        actioning_user_id=user_id,
                        organization_id=organization_id,
                    )
                )
            )

            # Add the select list value options and store their IDs
            select_list_values = ["1", "2", "3", "abc", "xyz"]
            select_list_value_dtos = []
            for display_value in select_list_values:
                dto = await custom_object_service.select_list_service.add_select_list_value(
                    select_list_id=select_list.id,
                    actioning_user_id=user_id,
                    organization_id=organization_id,
                    slvcr=SelectListValueCreateRequest(
                        display_value=display_value,
                        is_default=False,
                    ),
                )
                select_list_value_dtos.append(dto)

            # Create a map for easy lookup
            select_list_display_to_id_map = {
                dto.primary_value.display_value: dto.primary_value.id
                for dto in select_list_value_dtos
            }

            select_list_custom_field = (
                await custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=account_object.id,  # Now safe to access .id
                    custom_field_type_property_create=SingleSelectFieldPropertyCreate(
                        field_display_name="Select List Account custom field",
                        is_required=False,
                        select_list_id=select_list.id,
                    ),
                )
            )
            logger.info(f"select_list_custom_field: {select_list_custom_field}")
            """ End of Select List Custom Field """

            prefix_1 = random.choice(["Enterprise", "Strategic", "Global", "Premium"])  # noqa: S311
            suffix_1 = random.choice(["Solutions", "Partnership", "Expansion", "Deal"])  # noqa: S311
            number_1 = random.randint(1, 999)  # noqa: S311
            random_display_name_1 = f"{prefix_1} {suffix_1} {number_1}"

            prefix_2 = random.choice(["Enterprise", "Strategic", "Global", "Premium"])  # noqa: S311
            suffix_2 = random.choice(["Solutions", "Partnership", "Expansion", "Deal"])  # noqa: S311
            number_2 = random.randint(1, 999)  # noqa: S311
            random_display_name_2 = f"{prefix_2} {suffix_2} {number_2}"

            account_status = "TARGET"  # Could be TARGET, SELLING, or CUSTOMER

            # Prepare CSV content with account fields
            # Note: If you misspell the custom field name, it will not be imported

            # Note: Test omitting some of the defined custom fields
            custom_fields_column_headers = "Account Custom Field Test 1,Select List Account custom field,Currency Field,Number Field,Boolean Field,Date Field"
            # custom_fields_column_headers = "Account Custom Field Test 1,Select List Account custom field,Currency Field,Number Field"
            # custom_fields_column_headers = "Account Custom Field Test 1,Select List Account custom field,Currency Field"
            # custom_fields_column_headers = "Account Custom Field Test 1"
            custom_field_values_row_1 = "custom_value_1,abc,123,69,true,March 26 1995"
            # custom_field_values_row_1 = "custom_value_1,abc,123,69,true"
            # custom_field_values_row_1 = "YOLO"
            custom_field_values_row_2 = "custom_value_2,xyz,999,420,false,90-6-17"
            # custom_field_values_row_2 = "custom_value_2,xyz,999,420,false"
            # custom_field_values_row_2 = "BUDDIES"

            csv_content = (
                "display_name,domain_name,official_website_url,company_description,company_industry,company_technologies,"
                "estimated_annual_revenue,estimated_employee_count,linkedin_url,facebook_url,x_url,"
                "street_one,street_two,zip_code,city,state,country,status,owner_email,"
                f"{custom_fields_column_headers}\n"  # Note: Do NOT misspell these custom field names!
                # Data record 1 is below
                f"{random_display_name_1},testcompany.com,https://testcompany.com,A test company,Tech;Software,"
                "Python;AWS,1000000,100,https://linkedin.com/company/test,https://facebook.com/test,"
                f"https://x.com/test,123 Test St,Suite 100,12345,Test City,Test State,Test Country,{account_status},{user.email},"
                f"{custom_field_values_row_1}\n"
                # Data record 2 is below
                f"{random_display_name_2},foobar.com,https://foobar.com,A foobar company,Testing Tech;Hardware,"
                "Java;Google,999999,99,https://linkedin.com/company/foobar,https://facebook.com/foobar,"
                f"https://x.com/foobar,123 foobar St,Suite 100,12345,foobar City,foobar State,foobar Country,{account_status},{user.email},"
                f"{custom_field_values_row_2}\n"
            )

            custom_fields = [account_custom_text_field, select_list_custom_field]
            logger.info(f"custom_fields: {custom_fields}")
            logger.info(
                f"type(account_custom_text_field): {type(account_custom_text_field)}"
            )

            # Create a job for account import - use created_user.id instead of a new UUID
            job_id = uuid4()
            job = ImportJob(
                id=job_id,
                organization_id=organization_id,
                created_by_user_id=user_id,
                display_name="Test Account CSV Import With Custom Fields",
                status=ImportCsvJobStatus.STARTED,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=FileMetadata(
                    file_id=uuid4(),
                    original_filename="custom_fields_accounts.csv",
                    object_mappings=[
                        ObjectMapping(
                            object_identifier=StandardObjectIdentifier(
                                object_name=StdObjectIdentifiers.account
                            ),
                            column_mappings=[
                                # Standard Field Mappings (copy from your random_csv_content setup)
                                ColumnMapping(
                                    column_name="display_name",
                                    qualified_field=QualifiedImportField(
                                        path=["company_display_name"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="domain_name",
                                    qualified_field=QualifiedImportField(
                                        path=["company_domain_name"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="official_website_url",
                                    qualified_field=QualifiedImportField(
                                        path=["company_official_website"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="company_description",
                                    qualified_field=QualifiedImportField(
                                        path=["company_description"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="company_industry",
                                    qualified_field=QualifiedImportField(
                                        path=["company_industry"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="company_technologies",
                                    qualified_field=QualifiedImportField(
                                        path=["company_technologies"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="estimated_annual_revenue",
                                    qualified_field=QualifiedImportField(
                                        path=["company_estimated_annual_revenue"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="estimated_employee_count",
                                    qualified_field=QualifiedImportField(
                                        path=["company_estimated_employee_count"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="linkedin_url",
                                    qualified_field=QualifiedImportField(
                                        path=["company_linkedin_url"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="facebook_url",
                                    qualified_field=QualifiedImportField(
                                        path=["company_facebook_url"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="x_url",
                                    qualified_field=QualifiedImportField(
                                        path=["company_x_url"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="status",
                                    qualified_field=QualifiedImportField(
                                        path=["company_status"]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="owner_email",
                                    qualified_field=QualifiedImportField(
                                        path=["company_owner_email"]
                                    ),
                                ),
                                # Custom Field Mappings
                                ColumnMapping(
                                    column_name="Account Custom Field Test 1",  # Variable from your test
                                    qualified_field=QualifiedImportField(
                                        path=[str(account_custom_text_field.id)]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="Select List Account custom field",  # Variable from your test
                                    qualified_field=QualifiedImportField(
                                        path=[str(select_list_custom_field.id)]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="Currency Field",  # Variable from your test
                                    qualified_field=QualifiedImportField(
                                        path=[str(account_custom_amount_field.id)]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="Number Field",  # Variable from your test
                                    qualified_field=QualifiedImportField(
                                        path=[str(account_custom_number_field.id)]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="Boolean Field",  # Variable from your test
                                    qualified_field=QualifiedImportField(
                                        path=[str(account_custom_boolean_field.id)]
                                    ),
                                ),
                                ColumnMapping(
                                    column_name="Date Field",  # Variable from your test
                                    qualified_field=QualifiedImportField(
                                        path=[str(account_custom_date_field.id)]
                                    ),
                                ),
                            ],
                        )
                    ],
                    association_label_mapping=[],
                ),
                configuration=ImportConfiguration(
                    object_identifiers=[
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account
                        )
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.UPSERT,
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                ),
                workflow_id=None,
                completed_at=None,
                result_metadata=None,
                object_identifier=None,
            )

            # Process the import using internal methods
            row_num_to_csv_row_map = await crm_sync_service._process_csv_data_v2(
                file_binary=csv_content.encode(),
                csv_import_job=job,
                heartbeat_resume=None,
            )
            assert row_num_to_csv_row_map is not None, (
                "CSV data processing failed to produce row map"
            )
            # Expect 2 data rows from the test's CSV content (header is processed by DictReader and not included in this map)
            assert len(row_num_to_csv_row_map) == 2, (
                f"Expected 2 data rows in map, got {len(row_num_to_csv_row_map)}"
            )

            processed_records = await crm_sync_service._process_import_rows_v2(
                csv_import_job=job,
                row_num_to_csv_row_map=row_num_to_csv_row_map,
                heartbeat_resume=None,
            )
            logger.info(f"processed_records: {processed_records}")

            assert len(processed_records) == 2, "Expected 2 records to be processed"

            account_1_record = None
            account_2_record = None

            # Identify records based on a unique property from the CSV (e.g., display_name)
            for record in processed_records:
                assert record.status == ImportRecordStatus.SUCCESS, (
                    f"Record {record.id} ({record.incoming_record.get('display_name') if record.incoming_record else 'N/A'}) failed with status {record.status}: {record.status_detail}"
                )
                assert record.import_entity_type == ImportEntityType.ACCOUNT
                assert record.entity_id is not None
                if (
                    record.incoming_record
                    and record.incoming_record.get("display_name")
                    == random_display_name_1
                ):
                    account_1_record = record
                elif (
                    record.incoming_record
                    and record.incoming_record.get("display_name")
                    == random_display_name_2
                ):
                    account_2_record = record

            assert account_1_record is not None, (
                "Failed to find account_1_record in processed_records by display_name_1"
            )
            assert account_2_record is not None, (
                "Failed to find account_2_record in processed_records by display_name_2"
            )

            # Verify the record was created
            records = await crm_sync_service.import_repository.list_by_job_id(
                organization_id=organization_id,
                job_id=job.id,
            )
            logger.info(f"records: {records}")
            assert len(records) == 2

            # Sort records by created_at to ensure deterministic order
            sorted_records = sorted(records, key=lambda x: x.created_at)

            # Verify the records were created with the correct data
            record_1 = sorted_records[0]
            record_2 = sorted_records[1]

            assert record_1.status == ImportRecordStatus.SUCCESS
            assert record_2.status == ImportRecordStatus.SUCCESS

            account_1 = None
            if isinstance(record_1.entity_id, UUID):
                account_1 = await crm_sync_service.account_service.get_account_v2(
                    account_id=record_1.entity_id,
                    organization_id=organization_id,
                )
            logger.info(f"account_1: {account_1}")

            assert account_1 is not None
            assert account_1.display_name == random_display_name_1
            assert account_1.domain_name == "testcompany.com"
            assert account_1.official_website == "https://testcompany.com"
            assert account_1.description == "A test company"
            assert account_1.technology_list == ["Python", "AWS"]

            account_extension_record_1 = None
            custom_object_data_1 = None
            if isinstance(record_1.entity_id, UUID):
                account_extension_record_1 = (
                    await custom_object_service.get_custom_object_data_by_extension_id(
                        organization_id=organization_id,
                        parent_object_name=ExtendableStandardObject.account,
                        custom_object_data_extension_id=record_1.entity_id,
                    )
                )
            logger.info(f"account_extension_record_1: {account_extension_record_1}")
            assert account_extension_record_1 is not None
            custom_object_data_1 = account_extension_record_1.custom_object_data
            assert custom_object_data_1 is not None

            # Verify the custom fields were created with the correct data
            # Text Field (slot 1)
            custom_field_data_slot_1 = custom_object_data_1.value_1
            assert custom_field_data_slot_1 is not None
            text_field_val_1_raw = custom_field_data_slot_1.value_by_field_id.get(
                account_custom_text_field.id
            )
            assert isinstance(text_field_val_1_raw, TextFieldValue), (
                f"Expected TextFieldValue for slot 1, got {type(text_field_val_1_raw)}"
            )
            assert text_field_val_1_raw.text == "custom_value_1"

            # Select List Field (slot 6)
            custom_field_data_slot_6 = custom_object_data_1.value_6
            assert custom_field_data_slot_6 is not None
            select_field_val_1_raw = custom_field_data_slot_6.value_by_field_id.get(
                select_list_custom_field.id
            )
            assert isinstance(select_field_val_1_raw, SingleSelectFieldValue), (
                f"Expected SingleSelectFieldValue for {select_list_custom_field.field_display_name}"
            )
            expected_uuid_for_abc = select_list_display_to_id_map.get("abc")
            assert select_field_val_1_raw.value_id == expected_uuid_for_abc, (
                "Select list value for 'abc' does not match"
            )

            # Currency Field (slot 2)
            custom_field_data_slot_2 = custom_object_data_1.value_2
            assert custom_field_data_slot_2 is not None
            currency_field_val_1_raw = custom_field_data_slot_2.value_by_field_id.get(
                account_custom_amount_field.id
            )
            assert isinstance(currency_field_val_1_raw, CurrencyFieldValue), (
                f"Expected CurrencyFieldValue for {account_custom_amount_field.field_display_name}"
            )
            assert currency_field_val_1_raw.currency == Decimal("123")

            # Numeric Field (slot 3)
            custom_field_data_slot_3 = custom_object_data_1.value_3
            assert custom_field_data_slot_3 is not None
            numeric_field_val_1_raw = custom_field_data_slot_3.value_by_field_id.get(
                account_custom_number_field.id
            )
            assert isinstance(numeric_field_val_1_raw, NumericFieldValue), (
                f"Expected NumericFieldValue for {account_custom_number_field.field_display_name}"
            )
            assert numeric_field_val_1_raw.number == Decimal("69")

            # Boolean Field (slot 5)
            custom_field_data_slot_5 = custom_object_data_1.value_5
            assert custom_field_data_slot_5 is not None
            boolean_field_val_1_raw = custom_field_data_slot_5.value_by_field_id.get(
                account_custom_boolean_field.id
            )
            assert isinstance(boolean_field_val_1_raw, BooleanCheckboxFieldValue), (
                f"Expected BooleanCheckboxFieldValue for {account_custom_boolean_field.field_display_name}"
            )
            assert boolean_field_val_1_raw.checked is True

            # Date Field (slot 4)
            custom_field_data_slot_4 = custom_object_data_1.value_4
            assert custom_field_data_slot_4 is not None
            date_field_val_1_raw = custom_field_data_slot_4.value_by_field_id.get(
                account_custom_date_field.id
            )
            assert isinstance(date_field_val_1_raw, TimestampFieldValue), (
                f"Expected TimestampFieldValue for {account_custom_date_field.field_display_name}"
            )
            assert date_field_val_1_raw.timestamp is not None
            assert date_field_val_1_raw.timestamp.date() == date(1995, 3, 26)

            # Verify the account was created with the correct data
            account_2 = None
            if isinstance(record_2.entity_id, UUID):
                account_2 = await crm_sync_service.account_service.get_account_v2(
                    account_id=record_2.entity_id,
                    organization_id=organization_id,
                )
            logger.info(f"account_2: {account_2}")

            assert account_2 is not None
            assert account_2.display_name == random_display_name_2
            assert account_2.domain_name == "foobar.com"
            assert account_2.official_website == "https://foobar.com"
            assert account_2.description == "A foobar company"
            assert account_2.technology_list == ["Java", "Google"]

            account_extension_record_2 = None
            custom_object_data_2 = None
            if isinstance(record_2.entity_id, UUID):
                account_extension_record_2 = (
                    await custom_object_service.get_custom_object_data_by_extension_id(
                        organization_id=organization_id,
                        parent_object_name=ExtendableStandardObject.account,
                        custom_object_data_extension_id=record_2.entity_id,
                    )
                )
            logger.info(f"account_extension_record_2: {account_extension_record_2}")
            assert account_extension_record_2 is not None
            custom_object_data_2 = account_extension_record_2.custom_object_data
            assert custom_object_data_2 is not None

            # Verify the custom fields were created with the correct data
            # Text Field (slot 1)
            custom_field_data_2_slot_1 = custom_object_data_2.value_1
            assert custom_field_data_2_slot_1 is not None
            text_field_val_2_raw = custom_field_data_2_slot_1.value_by_field_id.get(
                account_custom_text_field.id
            )
            assert isinstance(text_field_val_2_raw, TextFieldValue), (
                f"Expected TextFieldValue for {account_custom_text_field.field_display_name}"
            )
            assert text_field_val_2_raw.text == "custom_value_2"

            # Select List Field (slot 6)
            custom_field_data_2_slot_6 = custom_object_data_2.value_6
            assert custom_field_data_2_slot_6 is not None
            select_field_val_2_raw = custom_field_data_2_slot_6.value_by_field_id.get(
                select_list_custom_field.id
            )
            assert isinstance(select_field_val_2_raw, SingleSelectFieldValue), (
                f"Expected SingleSelectFieldValue for {select_list_custom_field.field_display_name}"
            )
            expected_uuid_for_xyz = select_list_display_to_id_map.get("xyz")
            assert select_field_val_2_raw.value_id == expected_uuid_for_xyz, (
                "Select list value for 'xyz' does not match"
            )

            # Currency Field (slot 2)
            custom_field_data_2_slot_2 = custom_object_data_2.value_2
            assert custom_field_data_2_slot_2 is not None
            currency_field_val_2_raw = custom_field_data_2_slot_2.value_by_field_id.get(
                account_custom_amount_field.id
            )
            assert isinstance(currency_field_val_2_raw, CurrencyFieldValue), (
                f"Expected CurrencyFieldValue for {account_custom_amount_field.field_display_name}"
            )
            assert currency_field_val_2_raw.currency == Decimal("999")

            # Numeric Field (slot 3)
            custom_field_data_2_slot_3 = custom_object_data_2.value_3
            assert custom_field_data_2_slot_3 is not None
            numeric_field_val_2_raw = custom_field_data_2_slot_3.value_by_field_id.get(
                account_custom_number_field.id
            )
            assert isinstance(numeric_field_val_2_raw, NumericFieldValue), (
                f"Expected NumericFieldValue for {account_custom_number_field.field_display_name}"
            )
            assert numeric_field_val_2_raw.number == Decimal("420")

            # Boolean Field (slot 5)
            custom_field_data_2_slot_5 = custom_object_data_2.value_5
            assert custom_field_data_2_slot_5 is not None
            boolean_field_val_2_raw = custom_field_data_2_slot_5.value_by_field_id.get(
                account_custom_boolean_field.id
            )
            assert isinstance(boolean_field_val_2_raw, BooleanCheckboxFieldValue), (
                f"Expected BooleanCheckboxFieldValue for {account_custom_boolean_field.field_display_name}"
            )
            assert boolean_field_val_2_raw.checked is False

            # Date Field (slot 4)
            # CSV value: "90-6-17" (implies YYYY-MM-DD with 2-digit year)
            # dateutil.parser should handle this as 1990-06-17 if year > 68 else 20xx
            # For "90", it should be 1990.
            custom_field_data_2_slot_4 = custom_object_data_2.value_4
            assert custom_field_data_2_slot_4 is not None
            date_field_val_2_raw = custom_field_data_2_slot_4.value_by_field_id.get(
                account_custom_date_field.id
            )
            assert isinstance(date_field_val_2_raw, TimestampFieldValue), (
                f"Expected TimestampFieldValue for {account_custom_date_field.field_display_name}"
            )
            assert date_field_val_2_raw.timestamp is not None
            assert date_field_val_2_raw.timestamp.date() == date(1990, 6, 17)

    class TestContactImport:
        async def test_import_contact_record_happy_path(
            self,
            crm_sync_service: CrmSyncService,
            user_auth_service: UserAuthService,
            make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
        ) -> None:
            """Test importing a contact record via CSV."""

            user_id, organization_id = await make_user_org()
            logger.info(f"user_id: {user_id}, organization_id: {organization_id}")
            await _setup_import_repository_clean_slate_from_org_id(
                crm_sync_service=crm_sync_service,
                organization_id=organization_id,
            )

            user = await user_auth_service.user_repository.get_by_id(user_id=user_id)
            logger.info(f"user: {user}")

            # Find existing contact stage list
            contact_stage_list = await crm_sync_service.contact_service.select_list_service.find_default_select_list_dto_by_application_code_name(
                organization_id=organization_id,
                application_code_name=StdSelectListIdentifier.contact_stage,
            )
            logger.info(f"contact_stage_list: {contact_stage_list}")

            # Get stage values to verify
            if contact_stage_list is not None:
                stage_values = await crm_sync_service.contact_service.select_list_service.list_select_list_value(
                    select_list_id=contact_stage_list.select_list_id,
                    organization_id=organization_id,
                )
            else:
                stage_values = []
            logger.info(f"stage_values: {stage_values}")

            # Create a job for contact import
            # Ensure we are using "Reached Out" as the stage for the CSV and for assertions
            stage_for_csv = (
                default_bootstrap_contact_stage_select_list_request()
                .select_list_value_req[1]  # This corresponds to "Reached Out"
                .display_value
            )
            assert stage_for_csv == "Reached Out", (
                "The intended stage for CSV should be 'Reached Out'"
            )
            assert user is not None, "User should not be None"

            # Hardcoded values for test reproducibility
            hc_first_name = "Testy"
            hc_last_name = "McTestFace"
            hc_display_name = f"{hc_first_name} {hc_last_name}"
            hc_email = "<EMAIL>"
            hc_phone_number = "+14085550000"  # E.164 format
            hc_department = "QA Engineering"
            hc_linkedin_url = f"https://linkedin.com/in/{hc_first_name}{hc_last_name}"
            hc_facebook_url = f"https://facebook.com/{hc_first_name}{hc_last_name}"
            hc_x_url = f"https://x.com/{hc_first_name}{hc_last_name}"
            # Use the original user's email for owner_email to ensure the owner exists in the system for the test.
            owner_email_for_csv = user.email

            csv_content = (
                "display_name,first_name,last_name,primary_contact_email,primary_phone_number,"
                "department,linkedin_url,facebook_url,x_url,owner_email,stage\n"
                f"{hc_display_name},{hc_first_name},{hc_last_name},{hc_email},{hc_phone_number},"
                f"{hc_department},{hc_linkedin_url},"
                f"{hc_facebook_url},{hc_x_url},{owner_email_for_csv},{stage_for_csv}"
            )

            file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="display_name",
                                qualified_field=QualifiedImportField(
                                    path=["contact_display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="first_name",
                                qualified_field=QualifiedImportField(
                                    path=["contact_first_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="last_name",
                                qualified_field=QualifiedImportField(
                                    path=["contact_last_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="primary_contact_email",
                                qualified_field=QualifiedImportField(
                                    path=["contact_primary_email"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="primary_phone_number",
                                qualified_field=QualifiedImportField(
                                    path=["contact_primary_phone_number"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="department",
                                qualified_field=QualifiedImportField(
                                    path=["contact_department"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="linkedin_url",
                                qualified_field=QualifiedImportField(
                                    path=["contact_linkedin_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="facebook_url",
                                qualified_field=QualifiedImportField(
                                    path=["contact_facebook_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="x_url",
                                qualified_field=QualifiedImportField(
                                    path=["contact_x_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="owner_email",
                                qualified_field=QualifiedImportField(
                                    path=["contact_owner_email"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="stage",
                                qualified_field=QualifiedImportField(
                                    path=["contact_stage_value"]
                                ),
                            ),
                        ],
                    )
                ],
                association_label_mapping=[],
            )

            import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                display_name="Test Contact CSV Import",
                status=ImportCsvJobStatus.STARTED,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact
                        )
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.CREATE_ONLY,  # Or UPSERT depending on desired behavior
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                ),
                workflow_id=str(uuid4()),  # Example workflow ID
            )

            # Process the import
            processed_records = await crm_sync_service._process_import_job_file_v2(
                file_binary=csv_content.encode(),
                csv_import_job=import_job,
                heartbeat_resume=None,
            )
            logger.info(f"processed_records: {processed_records}")

            # Verify results
            assert len(processed_records) == 1
            import_record = processed_records[0]

            # Occasionally, the phone number will be invalid, so we need to handle that
            if (
                import_record.status == ImportRecordStatus.FAILED
                and import_record.status_detail
                and "Invalid phone number" in import_record.status_detail
            ):
                logger.warning(
                    f"Import failed due to invalid phone number: {import_record.status_detail}"
                )
                return  # Test is considered passed if invalid phone number is the reason for failure as per original logic

            assert import_record.status == ImportRecordStatus.SUCCESS, (
                f"Import record failed: {import_record.status_detail}"
            )

            # Verify the record was created
            assert import_record.status == ImportRecordStatus.SUCCESS
            assert import_record.import_entity_type == ImportEntityType.CONTACT
            assert import_record.entity_id is not None

            # Verify the contact was created with correct data
            contact = await crm_sync_service.contact_service.get_contact_v2(
                contact_id=import_record.entity_id,
                organization_id=organization_id,
            )
            logger.info(f"contact: {contact}")
            assert contact.display_name == hc_display_name
            assert contact.first_name == hc_first_name
            assert contact.last_name == hc_last_name
            assert contact.department == hc_department
            assert contact.linkedin_url == hc_linkedin_url
            assert contact.facebook_url == hc_facebook_url
            assert contact.x_url == hc_x_url
            assert contact.stage.display_value == stage_for_csv  # Expect "Reached Out"
            assert contact.owner_user_id == user_id  # Verify owner was set correctly

        async def test_import_contact_record_with_custom_fields_happy_path(
            self,
            custom_object_service: CustomObjectService,
            crm_sync_service: CrmSyncService,
            user_auth_service: UserAuthService,
            make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
        ) -> None:
            """Test importing a contact record with custom fields via CSV."""

            user_id, organization_id = await make_user_org()
            logger.info(f"user_id: {user_id}, organization_id: {organization_id}")
            await _setup_import_repository_clean_slate_from_org_id(
                crm_sync_service=crm_sync_service,
                organization_id=organization_id,
            )

            user = await user_auth_service.user_repository.get_by_id(user_id=user_id)
            logger.info(f"user: {user}")

            # Create extension custom object
            contact_object_with_custom_fields = (
                await custom_object_service.enable_extension_custom_object(
                    user_id=user_id,
                    organization_id=organization_id,
                    objects_to_enable=[ExtendableStandardObject.contact],
                )
            )
            logger.info(
                f"contact_object_with_custom_fields: {contact_object_with_custom_fields}"
            )

            # Add null check for contact object
            contact_object = contact_object_with_custom_fields.get(
                ExtendableStandardObject.contact
            )
            if not contact_object:
                raise ValueError("Failed to create contact custom object")

            # Create custom fields
            """ Select List Custom Field """
            select_list = (
                await custom_object_service.select_list_service.create_select_list(
                    select_list_create_request=SelectListCreateRequest(
                        display_name="Select List Test",
                        application_code_name=None,
                        description="Testing select lists on contact",
                        data_type=NativeValueType.STRING,
                        actioning_user_id=user_id,
                        organization_id=organization_id,
                    )
                )
            )

            # Add the select list value options and store their IDs
            select_list_values = ["Matt", "Francis", "Raj", "Sa", "Robert"]
            for display_value in select_list_values:
                await custom_object_service.select_list_service.add_select_list_value(
                    select_list_id=select_list.id,
                    actioning_user_id=user_id,
                    organization_id=organization_id,
                    slvcr=SelectListValueCreateRequest(
                        display_value=display_value,
                        is_default=False,
                    ),
                )

            select_list_custom_field = (
                await custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=contact_object.id,
                    custom_field_type_property_create=SingleSelectFieldPropertyCreate(
                        field_display_name="Select List Contact custom field",
                        is_required=False,
                        select_list_id=select_list.id,
                    ),
                )
            )
            logger.info(f"select_list_custom_field: {select_list_custom_field}")
            """ End of Select List Custom Field """

            # Create a text field custom field
            contact_custom_text_field = (
                await custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=contact_object.id,  # Now safe to access .id
                    custom_field_type_property_create=TextFieldProperty(
                        field_display_name="Contact Custom Field Test 1",
                        is_required=False,
                    ),
                )
            )
            logger.info(f"contact_custom_text_field: {contact_custom_text_field}")

            # Find existing contact stage list
            contact_stage_list = await crm_sync_service.contact_service.select_list_service.find_default_select_list_dto_by_application_code_name(
                organization_id=organization_id,
                application_code_name=StdSelectListIdentifier.contact_stage,
            )
            logger.info(f"contact_stage_list: {contact_stage_list}")

            # Get stage values to verify
            if contact_stage_list is not None:
                stage_values = await crm_sync_service.contact_service.select_list_service.list_select_list_value(
                    select_list_id=contact_stage_list.select_list_id,
                    organization_id=organization_id,
                )
            else:
                stage_values = []
            logger.info(f"stage_values: {stage_values}")

            # Hardcoded values for test reproducibility for standard fields
            hc_first_name = "Testy"
            hc_last_name = "McTestFace"
            hc_display_name = f"{hc_first_name} {hc_last_name}"
            hc_email = "<EMAIL>"  # Ensure unique email
            hc_phone_number = "+14085550001"  # E.164 format, ensure unique
            hc_department = "Custom QA"
            hc_linkedin_url = (
                f"https://linkedin.com/in/{hc_first_name}{hc_last_name}Custom"
            )
            hc_facebook_url = (
                f"https://facebook.com/{hc_first_name}{hc_last_name}Custom"
            )
            hc_x_url = f"https://x.com/{hc_first_name}{hc_last_name}Custom"

            # Use the original user's email for owner_email to ensure the owner exists.
            # If user.email is None, this will cause issues, ensure user fixture provides an email.
            assert user is not None and user.email is not None, (
                "Test user must have an email for owner_email CSV field"
            )
            owner_email_for_csv = user.email

            # Stage for CSV (using "Reached Out" for this custom field test)
            # Ensure this stage is actually available/bootstrapped for the org.
            # default_bootstrap_contact_stage_select_list_request().select_list_value_req[1] is 'Reached Out'
            stage_for_csv = (
                default_bootstrap_contact_stage_select_list_request()
                .select_list_value_req[1]
                .display_value
            )
            assert stage_for_csv == "Reached Out"

            random_custom_object_team_member = random.choice(select_list_values)  # noqa: S311
            custom_text_field_value = "contact custom text value"

            csv_content = (
                "display_name,first_name,last_name,primary_contact_email,primary_phone_number,"
                "department,linkedin_url,facebook_url,x_url,owner_email,stage,"
                "Select List Contact custom field,Contact Custom Field Test 1\n"
                f"{hc_display_name},{hc_first_name},{hc_last_name},{hc_email},{hc_phone_number},"
                f"{hc_department},{hc_linkedin_url},{hc_facebook_url},{hc_x_url},{owner_email_for_csv},{stage_for_csv},"
                f"{random_custom_object_team_member},{custom_text_field_value}\n"
            )

            logger.info(f"CSV Content:\\n{csv_content}")
            logger.info(
                f"Select List Custom Field ID: {select_list_custom_field.id}, Name: {select_list_custom_field.field_display_name}"
            )
            logger.info(
                f"Text Custom Field ID: {contact_custom_text_field.id}, Name: {contact_custom_text_field.field_display_name}"
            )

            file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="display_name",
                                qualified_field=QualifiedImportField(
                                    path=["contact_display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="first_name",
                                qualified_field=QualifiedImportField(
                                    path=["contact_first_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="last_name",
                                qualified_field=QualifiedImportField(
                                    path=["contact_last_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="primary_contact_email",
                                qualified_field=QualifiedImportField(
                                    path=["contact_primary_email"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="primary_phone_number",
                                qualified_field=QualifiedImportField(
                                    path=["contact_primary_phone_number"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="department",
                                qualified_field=QualifiedImportField(
                                    path=["contact_department"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="linkedin_url",
                                qualified_field=QualifiedImportField(
                                    path=["contact_linkedin_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="facebook_url",
                                qualified_field=QualifiedImportField(
                                    path=["contact_facebook_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="x_url",
                                qualified_field=QualifiedImportField(
                                    path=["contact_x_url"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="owner_email",
                                qualified_field=QualifiedImportField(
                                    path=["contact_owner_email"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="stage",
                                qualified_field=QualifiedImportField(
                                    path=["contact_stage_value"]
                                ),
                            ),
                            # Custom Field Mappings
                            ColumnMapping(
                                column_name="Select List Contact custom field",  # Must match CSV header
                                qualified_field=QualifiedImportField(
                                    path=[str(select_list_custom_field.id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="Contact Custom Field Test 1",  # Must match CSV header
                                qualified_field=QualifiedImportField(
                                    path=[str(contact_custom_text_field.id)]
                                ),
                            ),
                        ],
                    )
                ],
                association_label_mapping=[],
            )

            import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                display_name="Test Contact CSV Import with Custom Fields",
                status=ImportCsvJobStatus.STARTED,  # Use ImportCsvJobStatus
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact
                        )
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.CREATE_ONLY,
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                ),
                workflow_id=str(uuid4()),
            )

            # Process the import using _process_import_job_file_v2
            processed_records = await crm_sync_service._process_import_job_file_v2(
                file_binary=csv_content.encode(),
                csv_import_job=import_job,
                heartbeat_resume=None,
            )
            logger.info(f"processed_records: {processed_records}")

            # Verify results
            assert len(processed_records) == 1
            record = processed_records[0]

            # Handle potential phone number validation failures (though less likely with hardcoded valid number)
            if (
                record.status == ImportRecordStatus.FAILED
                and record.status_detail
                and "Invalid phone number" in record.status_detail
            ):
                logger.warning(
                    f"Import failed due to invalid phone number: {record.status_detail}"
                )
                # Depending on strictness, you might want to fail here or allow if other fields are primary focus
                pytest.fail(
                    f"Import failed due to invalid phone number: {record.status_detail}"
                )

            assert record.status == ImportRecordStatus.SUCCESS, (
                f"Import record failed: {record.status_detail}"
            )
            assert record.import_entity_type == ImportEntityType.CONTACT
            assert record.entity_id is not None

            # Verify the contact was created with correct data
            contact = await crm_sync_service.contact_service.get_contact_v2(
                contact_id=record.entity_id,
                organization_id=organization_id,
            )
            logger.info(f"contact: {contact}")

            # Verify contact metadata using hardcoded values
            assert contact.display_name == hc_display_name
            assert contact.first_name == hc_first_name
            assert contact.last_name == hc_last_name
            assert contact.primary_email == hc_email
            assert contact.primary_phone_number == hc_phone_number
            assert contact.department == hc_department
            assert contact.linkedin_url == hc_linkedin_url
            assert contact.facebook_url == hc_facebook_url
            assert contact.x_url == hc_x_url
            assert contact.owner_user_id == user_id
            assert (
                contact.stage.display_value == stage_for_csv
            )  # Should be "Reached Out"

            # Verify custom fields
            contact_extension_record = await custom_object_service.get_custom_object_data_by_extension_id(
                organization_id=organization_id,
                parent_object_name=ExtendableStandardObject.contact,
                custom_object_data_extension_id=record.entity_id,  # record.entity_id is the contact_id
            )
            assert contact_extension_record is not None, (
                "Contact extension record not found"
            )

            # Determine which slot (value_1, value_2, etc.) holds which custom field
            # This depends on the slot_number assigned to the custom fields upon creation.
            # We need to check contact_custom_text_field.slot_number and select_list_custom_field.slot_number

            found_text_field = False
            found_select_list_field = False

            for i in range(
                1, 6
            ):  # Check slots 1 through 5, common max for basic custom fields
                value_slot_attr = f"value_{i}"
                value_slot = getattr(
                    contact_extension_record.custom_object_data, value_slot_attr, None
                )

                if value_slot:
                    # Check for Text Custom Field
                    if contact_custom_text_field.id in value_slot.value_by_field_id:
                        field_value_data = value_slot.value_by_field_id[
                            contact_custom_text_field.id
                        ]
                        assert isinstance(field_value_data, TextFieldValue), (
                            f"Expected TextFieldValue for text custom field, got {type(field_value_data)} in slot {i}"
                        )
                        assert field_value_data.text == custom_text_field_value
                        found_text_field = True
                        logger.info(
                            f"Found text custom field in slot {i} with value: {field_value_data.text}"
                        )

                    # Check for Select List Custom Field
                    if select_list_custom_field.id in value_slot.value_by_field_id:
                        field_value_data = value_slot.value_by_field_id[
                            select_list_custom_field.id
                        ]
                        assert isinstance(field_value_data, SingleSelectFieldValue), (
                            f"Expected SingleSelectFieldValue for select list custom field, got {type(field_value_data)} in slot {i}"
                        )
                        assert field_value_data.value_id is not None
                        selected_option = await custom_object_service.select_list_service.get_select_list_value(
                            organization_id=organization_id,
                            select_list_value_id=field_value_data.value_id,
                        )
                        assert (
                            selected_option.display_value
                            == random_custom_object_team_member
                        )
                        found_select_list_field = True
                        logger.info(
                            f"Found select list custom field in slot {i} with selected option: {selected_option.display_value}"
                        )

            assert found_text_field, "Text custom field not found or value mismatch"
            assert found_select_list_field, (
                "Select list custom field not found or value mismatch"
            )

    class TestPipelineImport:
        async def test_import_pipeline_record_happy_path(
            self,
            crm_sync_service: CrmSyncService,
            user_auth_service: UserAuthService,
            make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
        ) -> None:
            """Test importing a pipeline record via CSV."""

            user_id, organization_id = await make_user_org()
            logger.info(f"user_id: {user_id}, organization_id: {organization_id}")
            await _setup_import_repository_clean_slate_from_org_id(
                crm_sync_service=crm_sync_service,
                organization_id=organization_id,
            )

            user = await user_auth_service.user_repository.get_by_id(user_id=user_id)
            logger.info(f"user: {user}")

            # Find existing pipeline stage list
            pipeline_stage_list = await crm_sync_service.pipeline_service.select_list_service.find_default_select_list_dto_by_application_code_name(
                organization_id=organization_id,
                application_code_name=StdSelectListIdentifier.pipeline_stage,
            )
            logger.info(f"pipeline_stage_list: {pipeline_stage_list}")

            # Get stage values to verify
            if pipeline_stage_list is not None:
                stage_values = await crm_sync_service.pipeline_service.select_list_service.list_select_list_value(
                    select_list_id=pipeline_stage_list.select_list_id,
                    organization_id=organization_id,
                )
            else:
                stage_values = []
            logger.info(f"stage_values: {stage_values}")

            # Hardcoded values for test reproducibility
            pipeline_display_name = "Project Titan - Q4 Deal"
            pipeline_stage = "Qualify"  # Ensure this is a valid stage display name
            deal_amount_str = "3500000"
            account_display_name_csv = "Wayne Enterprises"
            account_domain_csv = "wayne.example.com"
            contact_email_csv = (
                f"lucius.fox@{account_domain_csv}"  # Primary contact for the deal
            )

            # Next step details and dates
            next_step_details_csv = "Schedule technical demo with Bruce W."
            next_step_due_at_str = (
                "2024-09-15 10:00"  # Local time, ensure timezone in config
            )
            anticipated_closing_at_str = "2024-12-15 17:00"
            expires_at_str = "2024-12-31 23:59"  # Assuming pipeline doesn't expire or this is a custom field

            assert user is not None and user.email is not None, (
                "Test user must have an email for owner_email for related objects"
            )
            owner_email_for_csv = (
                user.email
            )  # Used for account/contact ownership if created

            csv_content = (
                "opportunity_display_name,opportunity_stage_value,opportunity_deal_amount,"
                "account_display_name,account_domain_name,contact_primary_email,"
                "opportunity_next_step_details,opportunity_next_step_due_at,opportunity_anticipated_closing_at,owner_email,expires_at\n"
                f"{pipeline_display_name},{pipeline_stage},{deal_amount_str},"
                f"{account_display_name_csv},{account_domain_csv},{contact_email_csv},"
                f"{next_step_details_csv},{next_step_due_at_str},{anticipated_closing_at_str},{owner_email_for_csv},{expires_at_str}\n"
            )
            logger.info(f"CSV Content:\\n{csv_content}")

            file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.pipeline
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="opportunity_display_name",
                                qualified_field=QualifiedImportField(
                                    path=["opportunity_display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="opportunity_stage_value",
                                qualified_field=QualifiedImportField(
                                    path=["opportunity_stage_value"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="opportunity_deal_amount",
                                qualified_field=QualifiedImportField(
                                    path=["opportunity_deal_amount"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="opportunity_next_step_details",
                                qualified_field=QualifiedImportField(
                                    path=["opportunity_next_step_details"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="opportunity_next_step_due_at",
                                qualified_field=QualifiedImportField(
                                    path=["opportunity_next_step_due_at"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="opportunity_anticipated_closing_at",
                                qualified_field=QualifiedImportField(
                                    path=["opportunity_anticipated_closing_at"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="expires_at",
                                qualified_field=QualifiedImportField(
                                    path=["expires_at"]
                                ),
                            ),
                        ],
                    ),
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="account_display_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="account_domain_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_domain_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="owner_email",
                                qualified_field=QualifiedImportField(
                                    path=["company_owner_email"]
                                ),
                            ),  # For account owner
                        ],
                    ),
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="contact_primary_email",
                                qualified_field=QualifiedImportField(
                                    path=["contact_primary_email"]
                                ),
                            ),
                            # To create a contact meaningfully, more fields like first/last name would be ideal.
                            # The system might create a placeholder contact based on email.
                            ColumnMapping(
                                column_name="owner_email",
                                qualified_field=QualifiedImportField(
                                    path=["contact_owner_email"]
                                ),
                            ),  # For contact owner
                            # Associate contact with account via company_domain_name from CSV
                            ColumnMapping(
                                column_name="account_domain_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_domain_name"]
                                ),
                            ),
                        ],
                    ),
                    # Potentially ContactAccountRole mapping if roles are involved and created via CSV
                ],
                association_label_mapping=[],  # Define if CSV creates explicit associations beyond standard linking
            )

            import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                display_name="Test Pipeline CSV Import",
                status=ImportCsvJobStatus.STARTED,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[  # Order can matter; typically Account, then Contact, then Pipeline
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account
                        ),
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact
                        ),
                        StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.pipeline
                        ),
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.UPSERT,  # Or CREATE_ONLY / UPDATE_ONLY
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,  # Define how duplicates in the file are handled
                    timezone=TimeZoneName(
                        "America/Los_Angeles"
                    ),  # Example timezone for parsing dates in CSV if not UTC
                ),
                workflow_id=str(uuid4()),
            )

            # Process the import
            processed_records = await crm_sync_service._process_import_job_file_v2(
                file_binary=csv_content.encode(),
                csv_import_job=import_job,
                heartbeat_resume=None,
            )
            logger.info(f"Processed records: {processed_records}")

            # Verify results
            # Expecting 3 records: 1 Account, 1 Contact, 1 Pipeline due to UPSERT mode and new data
            # If CREATE_ONLY and objects exist, some might be SKIPPED or CONFLICT.

            pipeline_record = None
            account_record = None
            _contact_record = None

            for record in processed_records:
                assert record.status == ImportRecordStatus.SUCCESS, (
                    f"Import record failed: {record.status_detail} for type {record.import_entity_type}"
                )
                if record.import_entity_type == ImportEntityType.PIPELINE:
                    pipeline_record = record
                elif record.import_entity_type == ImportEntityType.ACCOUNT:
                    account_record = record
                elif record.import_entity_type == ImportEntityType.CONTACT:
                    _contact_record = record

            assert pipeline_record is not None, (
                "Pipeline record not found in processed records"
            )
            assert account_record is not None, (
                "Account record not found in processed records"
            )
            # Contact creation might be basic if only email is provided, or might be skipped if not enough info.
            # assert contact_record is not None, "Contact record not found"

            assert pipeline_record.entity_id is not None
            # Verify the pipeline was created with correct data
            pipeline_obj = await crm_sync_service.pipeline_service.get_pipeline_by_id(
                pipeline_id=pipeline_record.entity_id,
                organization_id=organization_id,
            )
            logger.info(f"Pipeline object: {pipeline_obj}")

            # Basic fields
            assert pipeline_obj.display_name == pipeline_display_name
            assert pipeline_obj.amount == Decimal(deal_amount_str)
            # pipeline.status is an enum, check its value if needed, e.g. PipelineStatus.OPEN or .DEAL
            # For CSV import, it usually becomes DEAL if not specified otherwise or closed.
            assert pipeline_obj.status == PipelineStatus.DEAL
            assert pipeline_obj.next_step_details == next_step_details_csv
            assert pipeline_obj.created_source == CreatedSource.CSV_IMPORT

            # Stage info
            assert pipeline_obj.stage.display_value == pipeline_stage

            # Dates - ensure correct timezone handling during assertion
            # The import process should convert local times from CSV to UTC based on job config timezone
            expected_next_step_due_at_utc = (
                pytz.timezone("America/Los_Angeles")
                .localize(datetime.strptime(next_step_due_at_str, "%Y-%m-%d %H:%M"))  # noqa: DTZ007
                .astimezone(pytz.UTC)
            )
            expected_anticipated_closing_at_utc = (
                pytz.timezone("America/Los_Angeles")
                .localize(
                    datetime.strptime(anticipated_closing_at_str, "%Y-%m-%d %H:%M")  # noqa: DTZ007
                )
                .astimezone(pytz.UTC)
            )
            expected_expires_at_utc = (
                pytz.timezone("America/Los_Angeles")
                .localize(datetime.strptime(expires_at_str, "%Y-%m-%d %H:%M"))  # noqa: DTZ007
                .astimezone(pytz.UTC)
            )

            assert pipeline_obj.next_step_due_at == expected_next_step_due_at_utc
            assert (
                pipeline_obj.anticipated_closing_at
                == expected_anticipated_closing_at_utc
            )
            assert pipeline_obj.expires_at == expected_expires_at_utc

            # Verify account association
            assert account_record.entity_id is not None
            assert pipeline_obj.account_id == account_record.entity_id

            # Verify account details
            account_obj = await crm_sync_service.account_service.get_account_v2(
                account_id=account_record.entity_id, organization_id=organization_id
            )
            assert account_obj.display_name == account_display_name_csv
            assert account_obj.domain_name == account_domain_csv
            # Could assert owner if owner_email_for_csv was for the account and it's mapped.

            # Verify no closed info since pipeline is open
            assert pipeline_obj.closed_at is None
            assert pipeline_obj.closed_by_user_id is None
            assert pipeline_obj.closed_reason_select_list_values is None
            assert pipeline_obj.closed_reason_custom_detail is None

        def _get_utc_from_local_with_fallback(
            self, dt: datetime, tz_name: str | None
        ) -> datetime:
            """
            Converts a naive datetime object, assumed to be in the specified timezone (or UTC if None),
            to a UTC datetime.
            """
            if tz_name:
                try:
                    local_tz = pytz.timezone(tz_name)
                    localized_dt = local_tz.localize(dt)
                except pytz.exceptions.UnknownTimeZoneError:
                    # Fallback to UTC if timezone name is invalid
                    logger.warning(
                        f"Unknown timezone '{tz_name}', falling back to UTC for localization."
                    )
                    localized_dt = pytz.utc.localize(dt)
            else:
                # If no timezone is specified, assume the naive datetime is already UTC
                localized_dt = pytz.utc.localize(dt)

            return localized_dt.astimezone(pytz.utc)

        async def test_import_pipeline_record_with_custom_fields_happy_path(
            self,
            custom_object_service: CustomObjectService,
            crm_sync_service: CrmSyncService,
            user_auth_service: UserAuthService,
            make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
        ) -> None:
            user_id, organization_id = await make_user_org()
            await _setup_import_repository_clean_slate_from_org_id(
                crm_sync_service=crm_sync_service,
                organization_id=organization_id,
            )

            user = await user_auth_service.user_repository.get_by_id(user_id=user_id)
            assert user is not None and user.email is not None, (
                "Test user must exist and have an email"
            )
            owner_email_for_csv = user.email

            # Create extension custom object for Pipeline
            pipeline_cobj_map = (
                await custom_object_service.enable_extension_custom_object(
                    user_id=user_id,
                    organization_id=organization_id,
                    objects_to_enable=[ExtendableStandardObject.pipeline],
                )
            )
            pipeline_cobj_dto = pipeline_cobj_map.get(ExtendableStandardObject.pipeline)
            if not pipeline_cobj_dto:
                raise ValueError("Failed to create pipeline custom object DTO")

            # Create Text Custom Field for Pipeline
            pipeline_custom_text_field = (
                await custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=pipeline_cobj_dto.id,
                    custom_field_type_property_create=TextFieldProperty(
                        field_display_name="Pipeline Custom Text CSV", is_required=False
                    ),
                )
            )

            # Create Select List Custom Field for Pipeline
            select_list = (
                await custom_object_service.select_list_service.create_select_list(
                    select_list_create_request=SelectListCreateRequest(
                        display_name="Pipeline Test Status Select List",
                        data_type=NativeValueType.STRING,
                        actioning_user_id=user_id,
                        organization_id=organization_id,
                    )
                )
            )
            select_list_options = ["Option Alpha", "Option Beta", "Option Gamma"]
            for option_val in select_list_options:
                await custom_object_service.select_list_service.add_select_list_value(
                    select_list_id=select_list.id,
                    actioning_user_id=user_id,
                    organization_id=organization_id,
                    slvcr=SelectListValueCreateRequest(
                        display_value=option_val, is_default=False
                    ),
                )
            pipeline_select_list_custom_field = (
                await custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=pipeline_cobj_dto.id,
                    custom_field_type_property_create=SingleSelectFieldPropertyCreate(
                        field_display_name="Pipeline Custom Select CSV",
                        is_required=False,
                        select_list_id=select_list.id,
                    ),
                )
            )

            # --- CSV Data ---
            pipeline_display_name_csv = f"Dyno Pipeline {random.randint(1000, 9999)}"  # noqa: S311
            pipeline_stage_csv = (
                "Qualify"  # Ensure this is a valid stage from default pipeline stages
            )
            deal_amount_csv = "750000"

            account_display_name_csv = "CSV Test Account Inc."
            account_domain_csv = f"csvtest{random.randint(1000, 9999)}.com"  # noqa: S311

            contact_email_csv = (
                f"contact.person{random.randint(1000, 9999)}@{account_domain_csv}"  # noqa: S311
            )

            next_step_details_csv = "Finalize proposal details with client"
            next_step_due_at_csv = "2024-10-20 16:00"
            anticipated_closing_at_csv = "2024-12-10 17:00"
            # expires_at_csv = "2025-01-15 23:59" # Example, if using expires_at

            custom_text_field_value_csv = "Alpha Team Custom Data"
            custom_select_list_value_csv = random.choice(select_list_options)  # noqa: S311

            csv_header = (
                "opportunity_display_name,opportunity_stage_value,opportunity_deal_amount,"
                "account_display_name,account_domain_name,contact_primary_email,"
                "opportunity_next_step_details,opportunity_next_step_due_at,opportunity_anticipated_closing_date,"  # Use anticipated_closing_date for CSV
                "owner_email,"  # For Account/Contact owners primarily
                f"{pipeline_custom_text_field.field_display_name.replace(' ', '_')}_CSV,"  # CSV safe name
                f"{pipeline_select_list_custom_field.field_display_name.replace(' ', '_')}_CSV"  # CSV safe name
            )
            csv_data_row = (
                f"{pipeline_display_name_csv},{pipeline_stage_csv},{deal_amount_csv},"
                f"{account_display_name_csv},{account_domain_csv},{contact_email_csv},"
                f"{next_step_details_csv},{next_step_due_at_csv},{anticipated_closing_at_csv},"
                f"{owner_email_for_csv},"
                f"{custom_text_field_value_csv},{custom_select_list_value_csv}"
            )
            csv_content = f"{csv_header}\n{csv_data_row}\n"
            logger.info(f"CSV Content:\n{csv_content}")

            # --- ImportJob Configuration ---
            file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.pipeline
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="opportunity_display_name",
                                qualified_field=QualifiedImportField(
                                    path=["opportunity_display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="opportunity_stage_value",
                                qualified_field=QualifiedImportField(
                                    path=["opportunity_stage_value"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="opportunity_deal_amount",
                                qualified_field=QualifiedImportField(
                                    path=["opportunity_deal_amount"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="opportunity_next_step_details",
                                qualified_field=QualifiedImportField(
                                    path=["opportunity_next_step_details"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="opportunity_next_step_due_at",
                                qualified_field=QualifiedImportField(
                                    path=["opportunity_next_step_due_at"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="opportunity_anticipated_closing_date",
                                qualified_field=QualifiedImportField(
                                    path=["opportunity_anticipated_closing_at"]
                                ),
                            ),  # map to model field
                            ColumnMapping(
                                column_name="owner_email",
                                qualified_field=QualifiedImportField(
                                    path=["opportunity_owner_email"]
                                ),
                            ),  # Pipeline owner
                            # Custom Fields
                            ColumnMapping(
                                column_name=f"{pipeline_custom_text_field.field_display_name.replace(' ', '_')}_CSV",
                                qualified_field=QualifiedImportField(
                                    path=[str(pipeline_custom_text_field.id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name=f"{pipeline_select_list_custom_field.field_display_name.replace(' ', '_')}_CSV",
                                qualified_field=QualifiedImportField(
                                    path=[str(pipeline_select_list_custom_field.id)]
                                ),
                            ),
                        ],
                    ),
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.account
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="account_display_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="account_domain_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_domain_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="owner_email",
                                qualified_field=QualifiedImportField(
                                    path=["company_owner_email"]
                                ),
                            ),
                        ],
                    ),
                    ObjectMapping(
                        object_identifier=StandardObjectIdentifier(
                            object_name=StdObjectIdentifiers.contact
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="contact_primary_email",
                                qualified_field=QualifiedImportField(
                                    path=["contact_primary_email"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="owner_email",
                                qualified_field=QualifiedImportField(
                                    path=["contact_owner_email"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="account_domain_name",
                                qualified_field=QualifiedImportField(
                                    path=["company_domain_name"]
                                ),
                            ),  # For linking contact to account
                        ],
                    ),
                ],
                association_label_mapping=[],
            )

            import_job_config = ImportConfiguration(
                object_identifiers=[
                    StandardObjectIdentifier(
                        object_name=StdObjectIdentifiers.account
                    ),  # Accounts first
                    StandardObjectIdentifier(
                        object_name=StdObjectIdentifiers.contact
                    ),  # Then Contacts
                    StandardObjectIdentifier(
                        object_name=StdObjectIdentifiers.pipeline
                    ),  # Then Pipelines
                ],
                file_import_type=FileImportType.SINGLE_FILE,
                object_import_mode=ObjectImportMode.UPSERT,
                file_dupe_resolution=FileDupeResolution.USE_FIRST,
                timezone=TimeZoneName("America/Los_Angeles"),
            )

            import_job = ImportJob(
                id=uuid4(),
                workflow_id=str(uuid4()),  # Required for V2 job
                organization_id=organization_id,
                created_by_user_id=user_id,
                display_name="Test Pipeline CSV Import with Custom Fields",
                status=ImportCsvJobStatus.STARTED,
                metadata=file_metadata,
                configuration=import_job_config,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
            )

            # Process the import
            processed_records = await crm_sync_service._process_import_job_file_v2(
                file_binary=csv_content.encode(),
                csv_import_job=import_job,
                heartbeat_resume=None,
            )
            logger.info(f"Processed records: {processed_records}")

            # --- Assertions ---
            pipeline_record: ImportRecord | None = None
            account_record: ImportRecord | None = None
            _contact_record: ImportRecord | None = None

            for record_item in processed_records:
                # Allow SKIPPED for Account/Contact if they were already created by a prior object mapping in the same row.
                # Pipeline itself should be SUCCESS
                if record_item.import_entity_type == ImportEntityType.PIPELINE:
                    assert record_item.status == ImportRecordStatus.SUCCESS, (
                        f"Pipeline import failed: {record_item.status_detail}"
                    )
                    pipeline_record = record_item
                elif record_item.import_entity_type == ImportEntityType.ACCOUNT:
                    assert record_item.status in [
                        ImportRecordStatus.SUCCESS,
                        ImportRecordStatus.SKIPPED,
                        ImportRecordStatus.CONFLICT,
                    ], f"Account import/link failed: {record_item.status_detail}"
                    account_record = record_item
                elif record_item.import_entity_type == ImportEntityType.CONTACT:
                    assert record_item.status in [
                        ImportRecordStatus.SUCCESS,
                        ImportRecordStatus.SKIPPED,
                        ImportRecordStatus.CONFLICT,
                    ], f"Contact import/link failed: {record_item.status_detail}"
                    _contact_record = record_item

            assert (
                pipeline_record is not None and pipeline_record.entity_id is not None
            ), "Pipeline record not found or entity_id missing"
            assert (
                account_record is not None and account_record.entity_id is not None
            ), "Account record not found or entity_id missing"
            # Contact can be optional if only email is provided and no other identifying info
            # assert contact_record is not None and contact_record.entity_id is not None, "Contact record not found or entity_id missing"

            # Verify PipelineV2 object
            pipeline_obj = await crm_sync_service.pipeline_service.get_pipeline_by_id(
                pipeline_id=pipeline_record.entity_id, organization_id=organization_id
            )
            assert pipeline_obj.display_name == pipeline_display_name_csv
            assert pipeline_obj.amount == Decimal(deal_amount_csv)
            assert (
                pipeline_obj.status == PipelineStatus.DEAL
            )  # Default for new non-closed
            assert pipeline_obj.stage.display_value == pipeline_stage_csv
            assert pipeline_obj.next_step_details == next_step_details_csv

            # Verify dates
            cfg_timezone = (
                import_job.configuration.timezone
                if import_job.configuration
                else TimeZoneName("UTC")
            )
            expected_next_step_due_at_utc = self._get_utc_from_local_with_fallback(
                datetime.strptime(next_step_due_at_csv, "%Y-%m-%d %H:%M"),  # noqa: DTZ007
                cfg_timezone,
            )
            assert pipeline_obj.next_step_due_at == expected_next_step_due_at_utc
            expected_anticipated_closing_at_utc = (
                self._get_utc_from_local_with_fallback(
                    datetime.strptime(anticipated_closing_at_csv, "%Y-%m-%d %H:%M"),  # noqa: DTZ007
                    cfg_timezone,
                )
            )
            assert (
                pipeline_obj.anticipated_closing_at
                == expected_anticipated_closing_at_utc
            )

            # Verify Account
            assert pipeline_obj.account_id == account_record.entity_id
            account_obj = await crm_sync_service.account_service.get_account_v2(
                account_id=account_record.entity_id, organization_id=organization_id
            )
            assert account_obj.display_name == account_display_name_csv
            assert account_obj.domain_name == account_domain_csv
            assert account_obj.owner_user_id == user_id

            # Verify Custom Fields
            pipeline_extension_data = (
                await custom_object_service.get_custom_object_data_by_extension_id(
                    organization_id=organization_id,
                    parent_object_name=ExtendableStandardObject.pipeline,
                    custom_object_data_extension_id=pipeline_obj.id,
                )
            )
            assert pipeline_extension_data is not None

            # Text Custom Field
            text_field_id = pipeline_custom_text_field.id
            text_field_slot = pipeline_custom_text_field.slot_number
            text_field_value_container = getattr(
                pipeline_extension_data.custom_object_data, f"value_{text_field_slot}"
            )
            assert text_field_value_container is not None, (
                f"Custom field slot {text_field_slot} not found for text field"
            )
            text_field_value_obj = text_field_value_container.value_by_field_id.get(
                text_field_id
            )
            assert isinstance(text_field_value_obj, TextFieldValue), (
                f"Expected TextFieldValue for {text_field_id}, got {type(text_field_value_obj)}"
            )
            assert text_field_value_obj.text == custom_text_field_value_csv

            # Select List Custom Field
            select_field_id = pipeline_select_list_custom_field.id
            select_field_slot = pipeline_select_list_custom_field.slot_number
            select_field_value_container = getattr(
                pipeline_extension_data.custom_object_data, f"value_{select_field_slot}"
            )
            assert select_field_value_container is not None, (
                f"Custom field slot {select_field_slot} not found for select list"
            )
            select_field_value_obj = select_field_value_container.value_by_field_id.get(
                select_field_id
            )
            assert isinstance(select_field_value_obj, SingleSelectFieldValue), (
                f"Expected SingleSelectFieldValue for {select_field_id}, got {type(select_field_value_obj)}"
            )
            assert select_field_value_obj.value_id is not None

            retrieved_select_list_value = (
                await custom_object_service.select_list_service.get_select_list_value(
                    organization_id=organization_id,
                    select_list_value_id=select_field_value_obj.value_id,
                )
            )
            assert (
                retrieved_select_list_value.display_value
                == custom_select_list_value_csv
            )

    class TestCustomObjectImport:
        @pytest.fixture
        async def example_csv_content(self) -> str:
            return (
                "custom_object_display_name,col1,col2,col3,col4\n"  # Important: "custom_object_display_name" must be exact!
                "Test Record 1,x,1,2,xyz\n"
                "Test Record 2,5,4,8,abc\n"
            )

        @pytest.fixture
        async def example_csv_content_display_name_not_in_column1(self) -> str:
            return (
                "col1,custom_object_display_name,col2,col3,col4\n"  # Important: "custom_object_display_name" must be exact!
                "x,Test Record 1,1,2,xyz\n"
                "5,Test Record 2,4,8,abc\n"
            )

        @pytest.fixture
        async def example_csv_content_with_conflicts(self) -> str:
            return (
                "col1,custom_object_display_name,col2,col3,col4\n"  # Important: "custom_object_display_name" must be exact!
                "x,Test Record 1,1,2,xyz\n"
                "5,Test Record 2,4,8,abc\n"
                "y,Test Record 1,3,6,def\n"  # Conflict with Test Record 1
            )

        @pytest.fixture
        async def example_csv_content_with_select_list(self) -> str:
            return (
                "custom_object_display_name,col1,col2,col3,select1\n"  # select1 is a select list
                "Test Record 1,x,1,2,xyz\n"
                "Test Record 2,5,4,8,abc\n"
            )

        @pytest.fixture
        async def example_csv_content_with_select_list_and_conflicts(self) -> str:
            return (
                "custom_object_display_name,col1,col2,col3,select1\n"  # select1 is a select list
                "Test Record 1,x,1,2,xyz\n"
                "Test Record 2,5,4,8,abc\n"
                "Test Record 1,y,3,6,def\n"  # Conflict with Test Record 1
            )

        async def test_import_custom_object_record_happy_path(  # noqa: C901
            self,
            crm_sync_service: CrmSyncService,
            custom_object_service: CustomObjectService,
            example_csv_content: str,
        ) -> None:
            """Test importing a custom object record via CSV."""

            user_id = uuid.uuid4()
            organization_id = uuid.uuid4()
            await _setup_import_repository_clean_slate_from_org_id(
                crm_sync_service=crm_sync_service,
                organization_id=organization_id,
            )

            # Create a test custom object first
            custom_object = await custom_object_service.create_standalone_custom_object(
                user_id=user_id,
                organization_id=organization_id,
                object_display_name="Test Import Object",
            )

            number_of_custom_fields = (
                len(example_csv_content.split("\n")[0].split(",")) - 1
            )  # -1 for display name
            custom_fields_map: dict[uuid.UUID, str] = {}
            custom_fields: list[CustomField] = []
            for i in range(number_of_custom_fields):
                custom_field = await custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    custom_field_type_property_create=TextFieldProperty(
                        field_display_name=f"col{i + 1}",
                        is_required=False,
                        index_config=CaseAwareUniqueIndexableConfig(is_indexed=True),
                    ),
                )
                custom_fields_map[custom_field.id] = custom_field.field_display_name
                custom_fields.append(custom_field)
            logger.info(f"custom_fields_map: {custom_fields_map}")

            file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=CustomObjectIdentifier(
                            organization_id=organization_id,
                            object_id=custom_object.id,
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="custom_object_display_name",
                                qualified_field=QualifiedImportField(
                                    path=["display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col1",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[0].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col2",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[1].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col3",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[2].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col4",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[3].id)]
                                ),
                            ),
                        ],
                    )
                ],
                association_label_mapping=[],
            )

            # Create a job for custom object import
            import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        CustomObjectIdentifier(
                            organization_id=organization_id,
                            object_id=custom_object.id,
                        ),
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.CREATE_ONLY,
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                ),
                status=ImportCsvJobStatus.STARTED,
            )

            logger.info(f"example_csv_content: {example_csv_content}")

            # Process the import
            processed_records = await crm_sync_service._process_import_job_file_v2(
                file_binary=example_csv_content.encode(),
                csv_import_job=import_job,
                heartbeat_resume=None,
            )

            # Verify results
            assert len(processed_records) == 2
            successful_records = [
                r for r in processed_records if r.status == ImportRecordStatus.SUCCESS
            ]
            assert len(successful_records) == 2

            # Verify records were created successfully and get their entity_ids
            entity_ids_map: dict[str, uuid.UUID] = {}

            for record in successful_records:
                assert record.entity_id is not None
                custom_obj_data = await custom_object_service.get_custom_object_data(
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    custom_object_data_id=record.entity_id,
                )
                assert custom_obj_data.custom_object_data.display_name is not None
                entity_ids_map[custom_obj_data.custom_object_data.display_name] = (
                    record.entity_id
                )

            # Get the custom object data using display names for verification (as in reference)
            custom_object_data_row_1 = (
                await custom_object_service.get_custom_object_data_by_display_name(
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    display_name="Test Record 1",
                )
            )
            custom_object_data_row_2 = (
                await custom_object_service.get_custom_object_data_by_display_name(
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    display_name="Test Record 2",
                )
            )

            assert custom_object_data_row_1 is not None
            assert custom_object_data_row_2 is not None

            assert (
                custom_object_data_row_1.custom_object_data.display_name
                == "Test Record 1"
            )

            # Get the TextFieldValue objects and verify their text values
            for field_id, field_name in custom_fields_map.items():
                match field_name:
                    case "col1":
                        assert (
                            custom_object_data_row_1.custom_object_data.value_1
                            is not None
                        )
                        value = custom_object_data_row_1.custom_object_data.value_1.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "x"
                    case "col2":
                        assert (
                            custom_object_data_row_1.custom_object_data.value_2
                            is not None
                        )
                        value = custom_object_data_row_1.custom_object_data.value_2.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "1"
                    case "col3":
                        assert (
                            custom_object_data_row_1.custom_object_data.value_3
                            is not None
                        )
                        value = custom_object_data_row_1.custom_object_data.value_3.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "2"
                    case "col4":
                        assert (
                            custom_object_data_row_1.custom_object_data.value_4
                            is not None
                        )
                        value = custom_object_data_row_1.custom_object_data.value_4.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "xyz"

            assert (
                custom_object_data_row_2.custom_object_data.display_name
                == "Test Record 2"
            )

            # Get the TextFieldValue objects and verify their text values
            for field_id, field_name in custom_fields_map.items():
                match field_name:
                    case "col1":
                        assert (
                            custom_object_data_row_2.custom_object_data.value_1
                            is not None
                        )
                        value = custom_object_data_row_2.custom_object_data.value_1.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "5"
                    case "col2":
                        assert (
                            custom_object_data_row_2.custom_object_data.value_2
                            is not None
                        )
                        value = custom_object_data_row_2.custom_object_data.value_2.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "4"
                    case "col3":
                        assert (
                            custom_object_data_row_2.custom_object_data.value_3
                            is not None
                        )
                        value = custom_object_data_row_2.custom_object_data.value_3.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "8"
                    case "col4":
                        assert (
                            custom_object_data_row_2.custom_object_data.value_4
                            is not None
                        )
                        value = custom_object_data_row_2.custom_object_data.value_4.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "abc"

        async def test_import_custom_object_record_with_conflicts(  # noqa: C901, PLR0912
            self,
            crm_sync_service: CrmSyncService,
            custom_object_service: CustomObjectService,
            example_csv_content_with_conflicts: str,
        ) -> None:
            """Test importing a custom object record with conflicts at custom object level."""

            user_id = uuid.uuid4()
            organization_id = uuid.uuid4()
            await _setup_import_repository_clean_slate_from_org_id(
                crm_sync_service=crm_sync_service,
                organization_id=organization_id,
            )

            # Create a test custom object first
            custom_object = await custom_object_service.create_standalone_custom_object(
                user_id=user_id,
                organization_id=organization_id,
                object_display_name="Test Import Object",
            )

            number_of_custom_fields = (
                len(example_csv_content_with_conflicts.split("\n")[0].split(",")) - 1
            )  # -1 for display name
            custom_fields_map: dict[uuid.UUID, str] = {}
            custom_fields: list[CustomField] = []
            for i in range(number_of_custom_fields):
                custom_field = await custom_object_service.create_custom_field_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    custom_field_type_property_create=TextFieldProperty(
                        field_display_name=f"col{i + 1}",
                        is_required=False,
                        index_config=CaseAwareUniqueIndexableConfig(is_indexed=True),
                    ),
                )
                custom_fields_map[custom_field.id] = custom_field.field_display_name
                custom_fields.append(custom_field)
            logger.info(f"custom_fields_map: {custom_fields_map}")

            file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=CustomObjectIdentifier(
                            organization_id=organization_id,
                            object_id=custom_object.id,
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="custom_object_display_name",
                                qualified_field=QualifiedImportField(
                                    path=["display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col1",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[0].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col2",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[1].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col3",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[2].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col4",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[3].id)]
                                ),
                            ),
                        ],
                    )
                ],
                association_label_mapping=[],
            )

            # Create a job for custom object import
            import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        CustomObjectIdentifier(
                            organization_id=organization_id,
                            object_id=custom_object.id,
                        ),
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.CREATE_ONLY,
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                ),
                status=ImportCsvJobStatus.STARTED,
            )

            logger.info(
                f"example_csv_content_with_conflicts: {example_csv_content_with_conflicts}"
            )

            # Process the import
            processed_records = await crm_sync_service._process_import_job_file_v2(
                file_binary=example_csv_content_with_conflicts.encode(),
                csv_import_job=import_job,
                heartbeat_resume=None,
            )
            logger.info(f"processed_records: {processed_records}")

            # Verify record counts and statuses
            assert len(processed_records) == 3

            successful_records: list[ImportRecord] = []
            failed_records: list[ImportRecord] = []
            for record in processed_records:
                if record.status == ImportRecordStatus.SUCCESS:
                    successful_records.append(record)
                elif record.status in [
                    ImportRecordStatus.FAILED,
                    ImportRecordStatus.CONFLICT,
                ]:
                    failed_records.append(record)

            assert len(successful_records) == 2
            assert len(failed_records) == 1

            # Verify the failed record
            failed_record = failed_records[0]
            assert failed_record.status_detail is not None
            assert "Test Record 1" in failed_record.status_detail
            assert "Test Import Object" in failed_record.status_detail
            assert (
                "already exists" in failed_record.status_detail.lower()
                or "conflict" in failed_record.status_detail.lower()
            )
            assert failed_record.entity_id is None

            # Verify the first 2 records were created successfully
            records_by_name: dict[str, ImportRecord] = {}
            for record in successful_records:
                assert record.entity_id is not None
                custom_obj_data = await custom_object_service.get_custom_object_data(
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    custom_object_data_id=record.entity_id,
                )
                assert custom_obj_data.custom_object_data.display_name is not None
                records_by_name[custom_obj_data.custom_object_data.display_name] = (
                    record
                )

            logger.info(f"records_by_name (successful): {records_by_name}")

            record_1 = records_by_name["Test Record 1"]
            record_2 = records_by_name["Test Record 2"]

            custom_object_data_row_1 = (
                await custom_object_service.get_custom_object_data(
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    custom_object_data_id=cast(uuid.UUID, record_1.entity_id),
                )
            )
            assert custom_object_data_row_1 is not None

            custom_object_data_row_2 = (
                await custom_object_service.get_custom_object_data(
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    custom_object_data_id=cast(uuid.UUID, record_2.entity_id),
                )
            )
            assert custom_object_data_row_2 is not None

            logger.info(
                f"custom_object_data row 1.custom_object_data.value_1: {custom_object_data_row_1.custom_object_data.value_1}"
            )

            assert (
                custom_object_data_row_1.custom_object_data.display_name
                == "Test Record 1"
            )

            for field_id, field_name in custom_fields_map.items():
                if field_name == "col1":
                    assert (
                        custom_object_data_row_1.custom_object_data.value_1 is not None
                    )
                    value = custom_object_data_row_1.custom_object_data.value_1.value_by_field_id[
                        field_id
                    ]
                    assert isinstance(value, TextFieldValue) and value.text == "x"
                elif field_name == "col2":
                    assert (
                        custom_object_data_row_1.custom_object_data.value_2 is not None
                    )
                match field_name:
                    case "col1":
                        assert (
                            custom_object_data_row_1.custom_object_data.value_1
                            is not None
                        )
                        value = custom_object_data_row_1.custom_object_data.value_1.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "x"
                    case "col2":
                        assert (
                            custom_object_data_row_1.custom_object_data.value_2
                            is not None
                        )
                        value = custom_object_data_row_1.custom_object_data.value_2.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "1"
                    case "col3":
                        assert (
                            custom_object_data_row_1.custom_object_data.value_3
                            is not None
                        )
                        value = custom_object_data_row_1.custom_object_data.value_3.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "2"
                    case "col4":
                        assert (
                            custom_object_data_row_1.custom_object_data.value_4
                            is not None
                        )
                        value = custom_object_data_row_1.custom_object_data.value_4.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "xyz"

            assert (
                custom_object_data_row_2.custom_object_data.display_name
                == "Test Record 2"
            )

            # Get the TextFieldValue objects and verify their text values
            for field_id, field_name in custom_fields_map.items():
                match field_name:
                    case "col1":
                        assert (
                            custom_object_data_row_2.custom_object_data.value_1
                            is not None
                        )
                        value = custom_object_data_row_2.custom_object_data.value_1.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "5"
                    case "col2":
                        assert (
                            custom_object_data_row_2.custom_object_data.value_2
                            is not None
                        )
                        value = custom_object_data_row_2.custom_object_data.value_2.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "4"
                    case "col3":
                        assert (
                            custom_object_data_row_2.custom_object_data.value_3
                            is not None
                        )
                        value = custom_object_data_row_2.custom_object_data.value_3.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "8"
                    case "col4":
                        assert (
                            custom_object_data_row_2.custom_object_data.value_4
                            is not None
                        )
                        value = custom_object_data_row_2.custom_object_data.value_4.value_by_field_id[
                            field_id
                        ]
                        assert isinstance(value, TextFieldValue) and value.text == "abc"

        async def test_import_custom_object_record_with_select_list_happy_path(  # noqa: C901, PLR0912
            self,
            crm_sync_service: CrmSyncService,
            custom_object_service: CustomObjectService,
            example_csv_content_with_select_list: str,
        ) -> None:
            """Test importing a custom object record with a select list."""

            user_id = uuid4()
            organization_id = uuid4()
            await _setup_import_repository_clean_slate_from_org_id(
                crm_sync_service=crm_sync_service,
                organization_id=organization_id,
            )

            # Create a test custom object first
            custom_object = await custom_object_service.create_standalone_custom_object(
                user_id=user_id,
                organization_id=organization_id,
                object_display_name="Test Import Object With Select List",
            )

            # Create select list for music genres
            select_list = (
                await custom_object_service.select_list_service.create_select_list(
                    select_list_create_request=SelectListCreateRequest(
                        display_name="Import Select List Test",
                        application_code_name=None,
                        description="Testing importing select lists",
                        data_type=NativeValueType.STRING,
                        actioning_user_id=user_id,
                        organization_id=organization_id,
                    )
                )
            )

            # Add the select list value options and store their IDs
            select_list_value_ids = {}
            values = ["2", "8", "xyz", "abc"]
            for display_value in values:
                value_result = await custom_object_service.select_list_service.add_select_list_value(
                    select_list_id=select_list.id,
                    actioning_user_id=user_id,
                    organization_id=organization_id,
                    slvcr=SelectListValueCreateRequest(
                        display_value=display_value,
                        is_default=False,
                    ),
                )
                logger.info(f"add_select_list_value_result: {value_result}")
                select_list_value_ids[display_value] = value_result.primary_value.id
            logger.info(f"select_list_value_ids: {select_list_value_ids}")

            number_of_custom_fields = (
                len(example_csv_content_with_select_list.split("\n")[0].split(",")) - 1
            )  # -1 for display name
            custom_fields_map: dict[uuid.UUID, str] = {}
            custom_fields: list[CustomField] = []
            for i in range(number_of_custom_fields):
                if i != number_of_custom_fields - 1:  # Last field is the select list
                    custom_field = await custom_object_service.create_custom_field_v2(
                        user_id=user_id,
                        organization_id=organization_id,
                        custom_object_id=custom_object.id,
                        custom_field_type_property_create=TextFieldProperty(
                            field_display_name=f"col{i + 1}",
                            is_required=False,
                            index_config=CaseAwareUniqueIndexableConfig(
                                is_indexed=True
                            ),
                        ),
                    )
                else:
                    custom_field = await custom_object_service.create_custom_field_v2(
                        user_id=user_id,
                        organization_id=organization_id,
                        custom_object_id=custom_object.id,
                        custom_field_type_property_create=SingleSelectFieldPropertyCreate(
                            field_display_name="select1",
                            is_required=False,
                            select_list_id=select_list.id,
                        ),
                    )
                logger.info(f"custom_field: {custom_field}")
                custom_fields_map[custom_field.id] = custom_field.field_display_name
                custom_fields.append(custom_field)
            logger.info(f"custom_fields_map: {custom_fields_map}")

            file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=CustomObjectIdentifier(
                            organization_id=organization_id,
                            object_id=custom_object.id,
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="custom_object_display_name",
                                qualified_field=QualifiedImportField(
                                    path=["display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col1",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[0].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col2",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[1].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col3",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[2].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="select1",  # This is the select list column
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[3].id)]
                                ),
                            ),
                        ],
                    )
                ],
                association_label_mapping=[],
            )

            # Create a job for custom object import
            import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        CustomObjectIdentifier(
                            organization_id=organization_id,
                            object_id=custom_object.id,
                        ),
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.CREATE_ONLY,
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                ),
                status=ImportCsvJobStatus.STARTED,
            )

            # Process the import
            processed_records = await crm_sync_service._process_import_job_file_v2(
                file_binary=example_csv_content_with_select_list.encode(),
                csv_import_job=import_job,
                heartbeat_resume=None,
            )
            logger.info(f"processed_records: {processed_records}")

            # Verify results
            assert len(processed_records) == 2  # Two rows in the CSV
            for record in processed_records:
                assert record.status == ImportRecordStatus.SUCCESS
                assert record.entity_id is not None

            # Map successful records by their display names
            records_by_name: dict[str, ImportRecord] = {}
            for record in processed_records:
                assert record.entity_id is not None
                custom_obj_data = await custom_object_service.get_custom_object_data(
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    custom_object_data_id=record.entity_id,
                )
                assert custom_obj_data.custom_object_data.display_name is not None
                records_by_name[custom_obj_data.custom_object_data.display_name] = (
                    record
                )

            logger.info(f"records_by_name: {records_by_name}")

            # Get the records by their display names
            record_1 = records_by_name["Test Record 1"]
            record_2 = records_by_name["Test Record 2"]

            # Get the custom object data
            custom_object_data_row_1 = (
                await custom_object_service.get_custom_object_data(
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    custom_object_data_id=cast(uuid.UUID, record_1.entity_id),
                )
            )
            assert custom_object_data_row_1 is not None

            custom_object_data_row_2 = (
                await custom_object_service.get_custom_object_data(
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    custom_object_data_id=cast(uuid.UUID, record_2.entity_id),
                )
            )
            assert custom_object_data_row_2 is not None
            assert (
                custom_object_data_row_1.custom_object_data.display_name
                == "Test Record 1"
            )

            # Get the objects and verify their text values
            # CSV header: custom_object_display_name,col1,col2,col3,select1
            # CSV row 1: Test Record 1,x,1,2,xyz
            for field_id, field_name in custom_fields_map.items():
                if field_name == "col1":
                    assert (
                        custom_object_data_row_1.custom_object_data.value_1 is not None
                    )
                    value = custom_object_data_row_1.custom_object_data.value_1.value_by_field_id[
                        field_id
                    ]
                    assert isinstance(value, TextFieldValue) and value.text == "x"
                elif field_name == "col2":
                    assert (
                        custom_object_data_row_1.custom_object_data.value_2 is not None
                    )
                    value = custom_object_data_row_1.custom_object_data.value_2.value_by_field_id[
                        field_id
                    ]
                    assert isinstance(value, TextFieldValue) and value.text == "1"
                elif field_name == "col3":
                    assert (
                        custom_object_data_row_1.custom_object_data.value_3 is not None
                    )
                    value = custom_object_data_row_1.custom_object_data.value_3.value_by_field_id[
                        field_id
                    ]
                    assert isinstance(value, TextFieldValue) and value.text == "2"
                elif field_name == "select1":  # Mapped to custom_fields[3]
                    assert (
                        custom_object_data_row_1.custom_object_data.value_4 is not None
                    )
                    value = custom_object_data_row_1.custom_object_data.value_4.value_by_field_id[
                        field_id
                    ]
                    logger.info(f"value (select1 for row 1): {value}")
                    assert isinstance(value, SingleSelectFieldValue)
                    assert value.value_id == select_list_value_ids["xyz"]

            assert (
                custom_object_data_row_2.custom_object_data.display_name
                == "Test Record 2"
            )

            # Get the TextFieldValue objects and verify their text values
            # CSV row 2: Test Record 2,5,4,8,abc
            for field_id, field_name in custom_fields_map.items():
                if field_name == "col1":
                    assert (
                        custom_object_data_row_2.custom_object_data.value_1 is not None
                    )
                    value = custom_object_data_row_2.custom_object_data.value_1.value_by_field_id[
                        field_id
                    ]
                    assert isinstance(value, TextFieldValue) and value.text == "5"
                elif field_name == "col2":
                    assert (
                        custom_object_data_row_2.custom_object_data.value_2 is not None
                    )
                    value = custom_object_data_row_2.custom_object_data.value_2.value_by_field_id[
                        field_id
                    ]
                    assert isinstance(value, TextFieldValue) and value.text == "4"
                elif field_name == "col3":
                    assert (
                        custom_object_data_row_2.custom_object_data.value_3 is not None
                    )
                    value = custom_object_data_row_2.custom_object_data.value_3.value_by_field_id[
                        field_id
                    ]
                    assert isinstance(value, TextFieldValue) and value.text == "8"
                elif field_name == "select1":
                    assert (
                        custom_object_data_row_2.custom_object_data.value_4 is not None
                    )
                    value = custom_object_data_row_2.custom_object_data.value_4.value_by_field_id[
                        field_id
                    ]
                    logger.info(f"value (select1 for row 2): {value}")
                    assert isinstance(value, SingleSelectFieldValue)
                    assert value.value_id == select_list_value_ids["abc"]

        async def test_import_custom_object_record_with_select_list_and_duplicate_record_conflict(  # noqa: C901, PLR0912
            self,
            crm_sync_service: CrmSyncService,
            custom_object_service: CustomObjectService,
            example_csv_content_with_select_list_and_conflicts: str,
        ) -> None:
            """Test importing a custom object record with a select list and conflicts."""

            user_id = uuid4()
            organization_id = uuid4()
            await _setup_import_repository_clean_slate_from_org_id(
                crm_sync_service=crm_sync_service,
                organization_id=organization_id,
            )

            # Create a test custom object first
            custom_object = await custom_object_service.create_standalone_custom_object(
                user_id=user_id,
                organization_id=organization_id,
                object_display_name="Test Import Object With Select List",
            )

            # Create select list for music genres
            select_list = (
                await custom_object_service.select_list_service.create_select_list(
                    select_list_create_request=SelectListCreateRequest(
                        display_name="Import Select List Test Conflict",
                        application_code_name=None,
                        description="Testing importing select lists with conflicts",
                        data_type=NativeValueType.STRING,
                        actioning_user_id=user_id,
                        organization_id=organization_id,
                    )
                )
            )

            # Add the select list value options and store their IDs
            select_list_value_ids = {}
            values = ["2", "8", "xyz", "abc", "def"]
            for display_value in values:
                value_result = await custom_object_service.select_list_service.add_select_list_value(
                    select_list_id=select_list.id,
                    actioning_user_id=user_id,
                    organization_id=organization_id,
                    slvcr=SelectListValueCreateRequest(
                        display_value=display_value,
                        is_default=False,
                    ),
                )
                logger.info(f"add_select_list_value_result: {value_result}")
                select_list_value_ids[display_value] = value_result.primary_value.id
            logger.info(f"select_list_value_ids: {select_list_value_ids}")

            number_of_custom_fields = (
                len(
                    example_csv_content_with_select_list_and_conflicts.split("\n")[
                        0
                    ].split(",")
                )
                - 1
            )  # -1 for display name
            custom_fields_map: dict[uuid.UUID, str] = {}
            custom_fields: list[CustomField] = []
            for i in range(number_of_custom_fields):
                if i != number_of_custom_fields - 1:  # Last field is the select list
                    custom_field = await custom_object_service.create_custom_field_v2(
                        user_id=user_id,
                        organization_id=organization_id,
                        custom_object_id=custom_object.id,
                        custom_field_type_property_create=TextFieldProperty(
                            field_display_name=f"col{i + 1}",
                            is_required=False,
                            index_config=CaseAwareUniqueIndexableConfig(
                                is_indexed=True
                            ),
                        ),
                    )
                else:
                    custom_field = await custom_object_service.create_custom_field_v2(
                        user_id=user_id,
                        organization_id=organization_id,
                        custom_object_id=custom_object.id,
                        custom_field_type_property_create=SingleSelectFieldPropertyCreate(
                            field_display_name="select1",
                            is_required=False,
                            select_list_id=select_list.id,
                        ),
                    )
                logger.info(f"custom_field: {custom_field}")
                custom_fields_map[custom_field.id] = custom_field.field_display_name
                custom_fields.append(custom_field)
            logger.info(f"custom_fields_map: {custom_fields_map}")

            # Define FileMetadata for the V2 import job
            file_metadata = FileMetadata(
                file_id=uuid4(),
                object_mappings=[
                    ObjectMapping(
                        object_identifier=CustomObjectIdentifier(
                            organization_id=organization_id,
                            object_id=custom_object.id,
                        ),
                        column_mappings=[
                            ColumnMapping(
                                column_name="custom_object_display_name",
                                qualified_field=QualifiedImportField(
                                    path=["display_name"]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col1",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[0].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col2",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[1].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="col3",
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[2].id)]
                                ),
                            ),
                            ColumnMapping(
                                column_name="select1",  # This is the select list column
                                qualified_field=QualifiedImportField(
                                    path=[str(custom_fields[3].id)]
                                ),
                            ),
                        ],
                    )
                ],
                association_label_mapping=[],
            )

            # Create a V2 ImportJob
            import_job = ImportJob(
                id=uuid4(),
                organization_id=organization_id,
                created_by_user_id=user_id,
                created_at=datetime.now(pytz.UTC),
                updated_at=datetime.now(pytz.UTC),
                metadata=file_metadata,
                configuration=ImportConfiguration(
                    object_identifiers=[
                        CustomObjectIdentifier(
                            organization_id=organization_id,
                            object_id=custom_object.id,
                        ),
                    ],
                    file_import_type=FileImportType.SINGLE_FILE,
                    object_import_mode=ObjectImportMode.CREATE_ONLY,  # To trigger conflict on duplicate
                    file_dupe_resolution=FileDupeResolution.USE_FIRST,
                ),
                status=ImportCsvJobStatus.STARTED,
            )

            # Process the import using _process_import_job_file_v2
            processed_records = await crm_sync_service._process_import_job_file_v2(
                file_binary=example_csv_content_with_select_list_and_conflicts.encode(),
                csv_import_job=import_job,
                heartbeat_resume=None,  # Not testing heartbeat resume here
            )
            logger.info(f"processed_records: {processed_records}")

            # Verify import records results
            assert len(processed_records) == 3  # Should have 3 records from the CSV

            # Count successes and failures
            success_records = [
                r for r in processed_records if r.status == ImportRecordStatus.SUCCESS
            ]
            failed_records = [
                r for r in processed_records if r.status == ImportRecordStatus.FAILED
            ]

            assert len(success_records) == 2, "Expected 2 successful imports"
            assert len(failed_records) == 1, (
                "Expected 1 failed import due to duplicate display_name with CREATE_ONLY mode"
            )

            for success_record in success_records:
                assert success_record.entity_id is not None

            failed_record_instance = failed_records[0]
            assert (
                failed_record_instance.entity_id is None
            )  # Failed record should not have an entity_id
            assert failed_record_instance.status_detail is not None
            # Check for mention of the conflicting display name and reason for failure
            assert "Test Record 1" in failed_record_instance.status_detail
            assert (
                "already exists" in failed_record_instance.status_detail.lower()
                or "duplicate" in failed_record_instance.status_detail.lower()
            )

            # Map successful records by their display names for easier assertion
            records_by_name: dict[str, ImportRecord] = {}
            for record in success_records:
                custom_obj_data = await custom_object_service.get_custom_object_data(
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    custom_object_data_id=cast(UUID, record.entity_id),
                )
                assert custom_obj_data.custom_object_data.display_name is not None
                records_by_name[custom_obj_data.custom_object_data.display_name] = (
                    record
                )

            logger.info(f"Successfully imported records_by_name: {records_by_name}")

            # Get the successful records by their display names
            # CSV content for conflicts:
            # custom_object_display_name,col1,col2,col3,select1
            # Test Record 1,x,1,2,xyz  -- This should be SUCCESSFUL (first instance)
            # Test Record 2,5,4,8,abc  -- This should be SUCCESSFUL
            # Test Record 1,k,7,m,def  -- This should FAIL (duplicate display_name with CREATE_ONLY)

            record_1_successful = records_by_name[
                "Test Record 1"
            ]  # The first "Test Record 1"
            record_2_successful = records_by_name["Test Record 2"]

            # Get and verify the custom object data for the first successful record ("Test Record 1")
            custom_object_data_row_1 = (
                await custom_object_service.get_custom_object_data(
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    custom_object_data_id=cast(UUID, record_1_successful.entity_id),
                )
            )
            assert custom_object_data_row_1 is not None
            assert (
                custom_object_data_row_1.custom_object_data.display_name
                == "Test Record 1"
            )

            # Verify fields for the first successful "Test Record 1" (x,1,2,xyz)
            for field_id, field_name in custom_fields_map.items():
                if field_name == "col1":
                    assert (
                        custom_object_data_row_1.custom_object_data.value_1 is not None
                    )
                    value = custom_object_data_row_1.custom_object_data.value_1.value_by_field_id[
                        field_id
                    ]
                    assert isinstance(value, TextFieldValue) and value.text == "x"
                elif field_name == "col2":
                    assert (
                        custom_object_data_row_1.custom_object_data.value_2 is not None
                    )
                    value = custom_object_data_row_1.custom_object_data.value_2.value_by_field_id[
                        field_id
                    ]
                    assert isinstance(value, TextFieldValue) and value.text == "1"
                elif field_name == "col3":
                    assert (
                        custom_object_data_row_1.custom_object_data.value_3 is not None
                    )
                    value = custom_object_data_row_1.custom_object_data.value_3.value_by_field_id[
                        field_id
                    ]
                    assert isinstance(value, TextFieldValue) and value.text == "2"
                elif field_name == "select1":
                    assert (
                        custom_object_data_row_1.custom_object_data.value_4 is not None
                    )
                    value = custom_object_data_row_1.custom_object_data.value_4.value_by_field_id[
                        field_id
                    ]
                    assert isinstance(value, SingleSelectFieldValue)
                    assert value.value_id == select_list_value_ids["xyz"]

            # Get and verify the custom object data for the second successful record ("Test Record 2")
            custom_object_data_row_2 = (
                await custom_object_service.get_custom_object_data(
                    organization_id=organization_id,
                    custom_object_id=custom_object.id,
                    custom_object_data_id=cast(UUID, record_2_successful.entity_id),
                )
            )
            assert custom_object_data_row_2 is not None
            assert (
                custom_object_data_row_2.custom_object_data.display_name
                == "Test Record 2"
            )

            # Verify fields for "Test Record 2" (5,4,8,abc)
            for field_id, field_name in custom_fields_map.items():
                if field_name == "col1":
                    assert (
                        custom_object_data_row_2.custom_object_data.value_1 is not None
                    )
                    value = custom_object_data_row_2.custom_object_data.value_1.value_by_field_id[
                        field_id
                    ]
                    assert isinstance(value, TextFieldValue) and value.text == "5"
                elif field_name == "col2":
                    assert (
                        custom_object_data_row_2.custom_object_data.value_2 is not None
                    )
                    value = custom_object_data_row_2.custom_object_data.value_2.value_by_field_id[
                        field_id
                    ]
                    assert isinstance(value, TextFieldValue) and value.text == "4"
                elif field_name == "col3":
                    assert (
                        custom_object_data_row_2.custom_object_data.value_3 is not None
                    )
                    value = custom_object_data_row_2.custom_object_data.value_3.value_by_field_id[
                        field_id
                    ]
                    assert isinstance(value, TextFieldValue) and value.text == "8"
                elif field_name == "select1":
                    assert (
                        custom_object_data_row_2.custom_object_data.value_4 is not None
                    )
                    value = custom_object_data_row_2.custom_object_data.value_4.value_by_field_id[
                        field_id
                    ]
                    assert isinstance(value, SingleSelectFieldValue)
                    assert value.value_id == select_list_value_ids["abc"]
