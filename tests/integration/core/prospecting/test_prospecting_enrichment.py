from unittest.mock import AsyncMock, patch
from uuid import UUID, uuid4

from salestech_be.core.prospecting.prospecting_enrichment_service import (
    ProspectingEnrichmentService,
)
from salestech_be.core.prospecting.type.person_type_v2 import BulkEnrichPersonRequest
from salestech_be.core.quota.service.quota_policy_service import QuotaPolicyService
from salestech_be.db.dao.pdl_person_repository import PDLPersonRepository
from salestech_be.db.dao.prospecting_run_repository import ProspectingRunRepository
from salestech_be.db.dao.quota_repository import QuotaUsageRepository
from salestech_be.db.dto.person_dto import PersonDto
from salestech_be.db.dto.prospecting_dto import (
    EMAIL_ENRICH_CREDITS_PER_ENRICHMENT,
    MOBILE_ENRICH_CREDITS_PER_ENRICHMENT,
)
from salestech_be.db.models.pdl_person import <PERSON><PERSON><PERSON>
from salestech_be.db.models.person import Person
from salestech_be.db.models.prospecting_run import ProspectingRunStatus
from salestech_be.db.models.quota import (
    QuotaConsumerEntityType,
    QuotaConsumingResource,
    QuotaPeriod,
)
from salestech_be.integrations.pdl.model import (
    PeopleDataLabsBulkEnrichResponse,
)
from salestech_be.util.asyncio_util.adapter import run_in_pool
from salestech_be.util.time import zoned_utc_now
from tests.test_util import read_response_file_list


async def test_bulk_enrich_contact_without_person_id(
    prospecting_enrichment_service: ProspectingEnrichmentService,
    quota_usage_repo: QuotaUsageRepository,
    quota_policy_service: QuotaPolicyService,
    pdl_person_repository: PDLPersonRepository,
    prospecting_run_repository: ProspectingRunRepository,
) -> None:
    # ----- 1. Set up test environment -----
    organization_id = uuid4()
    user_id = uuid4()

    # Set up quota policy
    await quota_policy_service.upsert_quota_policy(
        quota_limit=1000,
        period=QuotaPeriod.MONTHLY,
        resource=QuotaConsumingResource.PROSPECTING_ENRICHMENT,
        entity_type=QuotaConsumerEntityType.ORGANIZATION,
        entity_id=organization_id,
        organization_id=organization_id,
        user_id=user_id,
        applied_sub_entity_types=None,
    )

    # Load mock PDL response data
    json_data_without_person_id = await run_in_pool(
        read_response_file_list,
        file_name="integration/integrations/pdl/data/pdl_bulk_enrich_person_response_without_person_id.json",
    )

    # Calculate expected enrichment credits: 3 requests succeed, each successful enrichment needs email and phone credits
    expected_enrichment_credits = (
        EMAIL_ENRICH_CREDITS_PER_ENRICHMENT + MOBILE_ENRICH_CREDITS_PER_ENRICHMENT
    ) * 3

    # ----- 2. Record quota usage before test -----
    period_start = zoned_utc_now().replace(hour=0, minute=0, second=0, microsecond=0)
    period_end = zoned_utc_now().replace(
        hour=23, minute=59, second=59, microsecond=999999
    )

    before_usage = await quota_usage_repo.get_aggregate_user_usage_in_period(
        organization_id=organization_id,
        entity_id=organization_id,
        entity_type=QuotaConsumerEntityType.ORGANIZATION,
        resource=QuotaConsumingResource.PROSPECTING_ENRICHMENT,
        period_start=period_start,
        period_end=period_end,
    )

    # ----- 3. Prepare test data: requests with different identification methods -----
    # Create fixed contact IDs for easier tracking
    contact_id_1 = UUID("cda7f137-e525-4d7e-b43a-17eb802c0c78")  # Using name+company
    contact_id_2 = UUID("f6b67c18-3552-4e99-a2f5-0ef42e0a0822")  # Using LinkedIn URL
    contact_id_3 = UUID("ab1588a6-2618-45d0-bd03-e5c9c8519e9f")  # Using email

    enrich_requests_with_person_id = [
        # Request 1: Using name and company
        BulkEnrichPersonRequest(
            contact_id=contact_id_1,
            first_name="Matt",
            last_name="Rum",
            display_name="Matt Rum",
            company_name="Easy Money App",
        ),
        # Request 2: Using LinkedIn URL
        BulkEnrichPersonRequest(
            contact_id=contact_id_2,
            linkedin_url="linkedin.com/in/joshua-rahm-8666b988",
        ),
        # Request 3: Using email
        BulkEnrichPersonRequest(
            contact_id=contact_id_3,
            email="<EMAIL>",
        ),
    ]

    # ----- 4. Execute test: mock PDL API and call enrichment service(without person_id) -----
    with patch(
        "salestech_be.integrations.pdl.pdl_client.PdlClient.bulk_enrich_person"
    ) as mock_bulk_enrich_person:
        mock_bulk_enrich_person.return_value = (
            PeopleDataLabsBulkEnrichResponse.from_response_list(
                json_data_without_person_id
            )
        )

        # Call the method under test
        results = await prospecting_enrichment_service.bulk_enrich_contact(
            organization_id=organization_id,
            user_id=user_id,
            enrich_requests=enrich_requests_with_person_id,
            enrich_phone_numbers=True,
        )

        # ----- 5. Verify results -----
        # 5.1 Check response structure
        assert results is not None, "Should return non-null results"
        assert len(results) == len(enrich_requests_with_person_id), (
            "Result count should match request count"
        )
        # Request 1 (Name+company): Expected to succeed, verify name match
        person_dto_1 = results[contact_id_1]
        assert person_dto_1 is not None, (
            "Name+company request should find matching person"
        )
        assert person_dto_1.db_person.full_name == "Matt Rum", (
            "Name should match request data"
        )

        # Request 2 (LinkedIn URL): Expected to succeed, verify LinkedIn URL match
        person_dto_2 = results[contact_id_2]
        assert person_dto_2 is not None, (
            "LinkedIn URL request should find matching person"
        )
        assert person_dto_2.db_person.linkedin_url is not None, (
            "LinkedIn URL should be found"
        )
        assert (
            "linkedin.com/in/joshua-rahm-8666b988"
            in person_dto_2.db_person.linkedin_url
        ), "LinkedIn URL should match request data"

        # Request 4 (Email): Expected to succeed, verify email match
        person_dto_3 = results[contact_id_3]
        assert person_dto_3 is not None, "Email request should find matching person"
        assert person_dto_3.db_person.work_email is not None, "Email should be found"
        assert (
            person_dto_3.db_person.work_email == "<EMAIL>"
        ), "Email should match request data"

    # ----- 6. Verify quota usage -----
    # Get quota usage after test
    after_usage = await quota_usage_repo.get_aggregate_user_usage_in_period(
        organization_id=organization_id,
        entity_id=organization_id,
        entity_type=QuotaConsumerEntityType.ORGANIZATION,
        resource=QuotaConsumingResource.PROSPECTING_ENRICHMENT,
        period_start=period_start,
        period_end=period_end,
    )

    # Verify quota usage increment matches expectation
    usage_difference = after_usage - before_usage
    assert usage_difference == expected_enrichment_credits, (
        f"Quota usage increment should be {expected_enrichment_credits}, "
        f"but was {usage_difference}"
    )

    # ----- 7. Verify Prospecting Run and Prospecting Run results -----
    prospecting_runs = await prospecting_run_repository.list_by_organization_id(
        organization_id
    )
    assert len(prospecting_runs) == 1, "Should have created exactly one prospecting run"

    # Find the most recent run
    prospecting_run = prospecting_runs[0]
    assert prospecting_run.status == ProspectingRunStatus.COMPLETED, (
        "Prospecting run should be completed"
    )

    # Verify Prospecting Run results
    prospecting_run_results = await prospecting_run_repository.list_results_by_run_id(
        prospecting_run.id
    )
    assert len(prospecting_run_results) == len(enrich_requests_with_person_id), (
        "Prospecting run results count should match request count"
    )

    # Verify each result has correct data
    for run_result in prospecting_run_results:
        if run_result.contact_id == contact_id_1:
            assert run_result.person_id == person_dto_1.db_person.id, (
                "First request should have person_id 1"
            )
        elif run_result.contact_id == contact_id_2:
            assert run_result.person_id == person_dto_2.db_person.id, (
                "Second request should have person_id 2"
            )
        elif run_result.contact_id == contact_id_3:
            assert run_result.person_id == person_dto_3.db_person.id, (
                "Third request should have person_id 3"
            )


async def test_bulk_enrich_contact_with_person_id(
    prospecting_enrichment_service: ProspectingEnrichmentService,
    quota_usage_repo: QuotaUsageRepository,
    quota_policy_service: QuotaPolicyService,
    pdl_person_repository: PDLPersonRepository,
    prospecting_run_repository: ProspectingRunRepository,
) -> None:
    # ----- 1. Set up test environment -----
    organization_id = uuid4()
    user_id = uuid4()

    # Set up quota policy
    await quota_policy_service.upsert_quota_policy(
        quota_limit=1000,
        period=QuotaPeriod.MONTHLY,
        resource=QuotaConsumingResource.PROSPECTING_ENRICHMENT,
        entity_type=QuotaConsumerEntityType.ORGANIZATION,
        entity_id=organization_id,
        organization_id=organization_id,
        user_id=user_id,
        applied_sub_entity_types=None,
    )

    json_data_with_person_id = await run_in_pool(
        read_response_file_list,
        file_name="integration/integrations/pdl/data/pdl_bulk_enrich_person_response_with_person_id.json",
    )

    # Calculate expected enrichment credits: 4 requests succeed, each successful enrichment needs email and phone credits
    expected_enrichment_credits = (
        EMAIL_ENRICH_CREDITS_PER_ENRICHMENT + MOBILE_ENRICH_CREDITS_PER_ENRICHMENT
    ) * 1

    # ----- 2. Record quota usage before test -----
    period_start = zoned_utc_now().replace(hour=0, minute=0, second=0, microsecond=0)
    period_end = zoned_utc_now().replace(
        hour=23, minute=59, second=59, microsecond=999999
    )

    before_usage = await quota_usage_repo.get_aggregate_user_usage_in_period(
        organization_id=organization_id,
        entity_id=organization_id,
        entity_type=QuotaConsumerEntityType.ORGANIZATION,
        resource=QuotaConsumingResource.PROSPECTING_ENRICHMENT,
        period_start=period_start,
        period_end=period_end,
    )

    # ----- 3. Prepare test data: requests with different identification methods -----
    # Create fixed contact IDs for easier tracking
    contact_id = UUID("57966eb1-d0fb-4a38-93c4-1e3532da6767")  # Using person_id

    person_id = uuid4()
    await pdl_person_repository.insert(
        PDLPerson(
            id=uuid4(),
            organization_id=organization_id,
            person_id=person_id,
            ext_id="Q0xOFmXFx0yRzYBBn0v8sQ_0000",
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            data={"test": "data1"},
        )
    )

    enrich_requests_with_person_id = [
        # Request 4: Using person_id
        BulkEnrichPersonRequest(
            contact_id=contact_id,
            person_id=person_id,
        ),
    ]

    # ----- 4. Execute test: mock PDL API and call enrichment service(without person_id) -----
    with (
        patch.object(
            prospecting_enrichment_service,
            "_find_person_dto_list_by_person_ids",
            new_callable=AsyncMock,
        ) as mock_find_persons,
        patch(
            "salestech_be.integrations.pdl.pdl_client.PdlClient.bulk_enrich_person"
        ) as mock_bulk_enrich_person,
    ):
        # Configure mock response data
        mock_find_persons.return_value = [
            PersonDto(
                db_person=Person(
                    id=person_id,
                    user_id=user_id,
                    organization_id=organization_id,
                    created_at=zoned_utc_now(),
                    updated_at=zoned_utc_now(),
                ),
                db_company=None,
            )
        ]
        mock_bulk_enrich_person.return_value = (
            PeopleDataLabsBulkEnrichResponse.from_response_list(
                json_data_with_person_id
            )
        )

        # Call the method under test
        results = await prospecting_enrichment_service.bulk_enrich_contact(
            organization_id=organization_id,
            user_id=user_id,
            enrich_requests=enrich_requests_with_person_id,
            enrich_phone_numbers=True,
        )

        # ----- 5. Verify results -----
        # 5.1 Check response structure
        assert results is not None, "Should return non-null results"
        assert len(results) == len(enrich_requests_with_person_id), (
            "Result count should match request count"
        )

        # Request (Person ID): Expected to succeed, verify person_id match
        person_dto = results[contact_id]
        assert person_dto is not None, "Person ID request should find matching person"
        assert person_dto.db_person.id == person_id, (
            "Person ID should match request data"
        )

    # ----- 6. Verify quota usage -----
    # Get quota usage after test
    after_usage = await quota_usage_repo.get_aggregate_user_usage_in_period(
        organization_id=organization_id,
        entity_id=organization_id,
        entity_type=QuotaConsumerEntityType.ORGANIZATION,
        resource=QuotaConsumingResource.PROSPECTING_ENRICHMENT,
        period_start=period_start,
        period_end=period_end,
    )

    # Verify quota usage increment matches expectation
    usage_difference = after_usage - before_usage
    assert usage_difference == expected_enrichment_credits, (
        f"Quota usage increment should be {expected_enrichment_credits}, "
        f"but was {usage_difference}"
    )

    # ----- 7. Verify Prospecting Run and Prospecting Run results -----
    prospecting_runs = await prospecting_run_repository.list_by_organization_id(
        organization_id
    )
    assert len(prospecting_runs) == 1, "Should have created exactly one prospecting run"

    # Find the most recent run
    prospecting_run = prospecting_runs[0]
    assert prospecting_run.status == ProspectingRunStatus.COMPLETED, (
        "Prospecting run should be completed"
    )

    # Verify Prospecting Run results
    prospecting_run_results = await prospecting_run_repository.list_results_by_run_id(
        prospecting_run.id
    )
    assert len(prospecting_run_results) == len(enrich_requests_with_person_id), (
        "Prospecting run results count should match request count"
    )

    # Verify each result has correct data
    run_result = prospecting_run_results[0]
    assert run_result.contact_id == contact_id
    assert run_result.person_id == person_dto.db_person.id, (
        "Fourth request should have person_id 1"
    )


async def test_preprocess_enrich_requests(
    prospecting_enrichment_service: ProspectingEnrichmentService,
) -> None:
    # Setup test data
    organization_id = uuid4()
    user_id = uuid4()

    # Create test requests with different scenarios
    person_id_enriched = uuid4()
    person_id_not_enriched = uuid4()

    # Create mock person DTOs
    person_dto_enriched = PersonDto(
        db_person=Person(
            id=person_id_enriched,
            user_id=user_id,
            organization_id=organization_id,
            last_enriched_at=zoned_utc_now(),
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        ),
        db_company=None,
    )

    person_dto_not_enriched = PersonDto(
        db_person=Person(
            id=person_id_not_enriched,
            user_id=user_id,
            organization_id=organization_id,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        ),
        db_company=None,
    )

    # Mock person_repository.list_by_ids to return our test data
    with patch.object(
        prospecting_enrichment_service,
        "_find_person_dto_list_by_person_ids",
        new_callable=AsyncMock,
    ) as mock_find_persons:
        mock_find_persons.return_value = [person_dto_enriched, person_dto_not_enriched]

        # Create test requests
        enrich_requests = [
            # With person_id, already enriched
            BulkEnrichPersonRequest(
                contact_id=uuid4(),
                person_id=person_id_enriched,
            ),
            # With person_id, not enriched
            BulkEnrichPersonRequest(
                contact_id=uuid4(),
                person_id=person_id_not_enriched,
            ),
            # Without person_id
            BulkEnrichPersonRequest(
                contact_id=uuid4(),
                email="<EMAIL>",
            ),
        ]

        # Call the method
        (
            requests_needing_enrichment,
            existing_enriched_contacts,
        ) = await prospecting_enrichment_service._preprocess_enrich_requests(
            organization_id=organization_id,
            enrich_requests=enrich_requests,
        )

        # Assertions
        assert (
            len(requests_needing_enrichment) == 2
        )  # The non-enriched person and the one without person_id
        assert len(existing_enriched_contacts) == 1  # The already enriched person

        # Check that the enriched person was identified correctly
        assert person_id_enriched in [
            person_dto.db_person.id
            for person_dto in existing_enriched_contacts.values()
            if person_dto is not None
        ]

        # Verify the requests that need enrichment
        person_ids_needing_enrichment = [
            req.person_id for req in requests_needing_enrichment if req.person_id
        ]
        assert person_id_not_enriched in person_ids_needing_enrichment
