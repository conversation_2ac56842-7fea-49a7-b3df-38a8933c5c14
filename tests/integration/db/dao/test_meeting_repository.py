from datetime import timedel<PERSON>
from uuid import UUID, uuid4

from salestech_be.db.dao.contact_repository import ContactRepository
from salestech_be.db.dao.meeting_repository import MeetingRepository
from salestech_be.db.dto.research_dto import (
    ContactResearchDto,
    ContactSnippet,
    MeetingResearchDto,
)
from salestech_be.db.models.meeting import (
    BotProvider,
    BotStatusEvent,
    BotStatusHistory,
    LiveTranscriptSession,
    Meeting,
    MeetingAttendeeMonologue,
    MeetingAttendeeTalkRatio,
    MeetingBot,
    MeetingBotStatus,
    MeetingBotUpdate,
    MeetingInvitee,
    MeetingProvider,
    MeetingReferenceIdType,
    MeetingStats,
    MeetingStatus,
    RecallRecording,
    RecallRecordingData,
    ScreenShareRange,
    TranscriptProvider,
)
from salestech_be.db.models.transcript import Transcript, TranscriptReferenceIdType
from salestech_be.db.models.user_calendar_event import (
    CalendarEventParticipant,
    UserCalendarEvent,
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none


class TestMeetingRepository:
    async def test_get_meeting_by_reference_id_and_type(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository, organization_id=organization_id
        )

        result = await meeting_repository.get_meeting_by_reference_id_and_type(
            organization_id=organization_id,
            reference_id=meeting.reference_id,
            reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
        )
        assert result == meeting

    async def test_get_meeting_by_reference_id_and_type_not_found(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository, organization_id=organization_id
        )

        result = await meeting_repository.get_meeting_by_reference_id_and_type(
            organization_id=organization_id,
            reference_id=str(uuid4()),  # Does not exist
            reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
        )
        assert result is None

        result = await meeting_repository.get_meeting_by_reference_id_and_type(
            organization_id=uuid4(),  # Different org
            reference_id=meeting.reference_id,
            reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
        )
        assert result is None

    async def test_get_meeting_by_consent_id(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        consent_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
            consent_id=consent_id,
        )

        result = await meeting_repository.get_meeting_by_consent_id(
            consent_id=consent_id
        )
        assert result == [meeting]

    async def test_get_meeting_by_consent_id_not_found(
        self, meeting_repository: MeetingRepository
    ) -> None:
        result = await meeting_repository.get_meeting_by_consent_id(consent_id=uuid4())
        assert result == []

    async def test_find_future_scheduled_meeting_bots_no_match(
        self, meeting_repository: MeetingRepository
    ) -> None:
        result = await meeting_repository.find_future_scheduled_meeting_bots(
            organization_id=uuid4()
        )
        assert result == []

    async def test_find_future_scheduled_meeting_bots(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        meeting_bot = await self._persist_meeting_bot(
            meeting_repository=meeting_repository,
            meeting=meeting,
            external_meeting_bot_id=str(uuid4()),
        )
        meeting_bot = not_none(
            await meeting_repository.update_by_tenanted_primary_key(
                table_model=MeetingBot,
                organization_id=organization_id,
                primary_key_to_value={
                    "id": meeting_bot.id,
                },
                column_to_update={
                    "scheduled_at": not_none(meeting_bot.scheduled_at)
                    + timedelta(days=1)
                },
            )
        )
        result = await meeting_repository.find_future_scheduled_meeting_bots(
            organization_id=organization_id
        )
        assert result == [meeting_bot]

    async def test_find_future_meeting_bots_by_meeting_user_id_no_match(
        self, meeting_repository: MeetingRepository
    ) -> None:
        result = await meeting_repository.find_future_meeting_bots_by_meeting_user_id(
            organization_id=uuid4(),
            user_id=uuid4(),
        )
        assert result == []

    async def test_find_future_meeting_bots_by_meeting_user_id(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        external_bot_id = str(uuid4())
        meeting_bot = await self._persist_meeting_bot(
            meeting_repository=meeting_repository,
            meeting=meeting,
            external_meeting_bot_id=external_bot_id,
        )
        meeting_bot = not_none(
            await meeting_repository.update_by_tenanted_primary_key(
                table_model=MeetingBot,
                organization_id=organization_id,
                primary_key_to_value={
                    "id": meeting_bot.id,
                },
                column_to_update={
                    "scheduled_at": not_none(meeting_bot.scheduled_at)
                    + timedelta(days=1)
                },
            )
        )

        result = await meeting_repository.find_future_meeting_bots_by_meeting_user_id(
            organization_id=organization_id,
            user_id=not_none(meeting.created_by_user_id),
        )
        assert result == [meeting_bot]

    async def test_get_meeting_for_external_bot_id(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        external_bot_id = str(uuid4())
        await self._persist_meeting_bot(
            meeting_repository=meeting_repository,
            meeting=meeting,
            external_meeting_bot_id=external_bot_id,
        )

        result = await meeting_repository.get_meeting_for_external_bot_id(
            external_meeting_bot_id=external_bot_id, bot_provider=BotProvider.RECALLAI
        )
        assert result == meeting

    async def test_get_meeting_for_external_bot_id_not_found(
        self, meeting_repository: MeetingRepository
    ) -> None:
        result = await meeting_repository.get_meeting_for_external_bot_id(
            external_meeting_bot_id=str(uuid4()), bot_provider=BotProvider.RECALLAI
        )
        assert result is None

    async def test_list_meetings_by_organization_id_no_match(
        self, meeting_repository: MeetingRepository
    ) -> None:
        result = await meeting_repository.list_meetings_by_organization_id(
            organization_id=uuid4(), meeting_ids=None
        )
        assert result == []

    async def test_list_meetings_by_organization_id_match_by_organization_id(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        result = await meeting_repository.list_meetings_by_organization_id(
            organization_id=organization_id, meeting_ids=None
        )
        assert result == [meeting]

    async def test_list_meetings_by_organization_id_match_by_meeting_id(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        # Second one that will be filtered out by meeting id
        await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )

        result = await meeting_repository.list_meetings_by_organization_id(
            organization_id=organization_id, meeting_ids=[meeting.id]
        )
        assert result == [meeting]

    async def test_list_meetings_with_calendar_time_mismatch(
        self, meeting_repository: MeetingRepository
    ) -> None:
        # Validate query can execute
        await meeting_repository.list_meetings_with_calendar_time_mismatch(
            minutes_window=30
        )

    async def test_list_meeting_bots_with_meeting_time_mismatch(
        self, meeting_repository: MeetingRepository
    ) -> None:
        # Validate query can execute
        await meeting_repository.list_meeting_bots_with_meeting_time_mismatch(
            days_window=1, join_offset_seconds=60
        )

    async def test_list_meeting_urls_with_extra_scheduled_bots(
        self, meeting_repository: MeetingRepository
    ) -> None:
        # Validate query can execute
        await meeting_repository.list_meeting_urls_with_extra_scheduled_bots()

    async def test_list_overdue_analyzing_meetings(
        self, meeting_repository: MeetingRepository
    ) -> None:
        # Validate query can execute
        await meeting_repository.list_overdue_analyzing_meetings(minutes_window=1)

    async def test_list_overdue_active_meetings(
        self, meeting_repository: MeetingRepository
    ) -> None:
        # Validate query can execute
        await meeting_repository.list_overdue_active_meetings(minutes_window=1)

    async def test_get_meeting_bot_by_realtime_event_token(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        external_bot_id = str(uuid4())
        meeting_bot = await self._persist_meeting_bot(
            meeting_repository=meeting_repository,
            meeting=meeting,
            external_meeting_bot_id=external_bot_id,
        )

        result = await meeting_repository.get_meeting_bot_by_realtime_event_token(
            realtime_event_token=not_none(meeting_bot.realtime_event_token)
        )
        assert result == meeting_bot

    async def test_find_meeting_bots_by_organization_id(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        meeting_bot = await self._persist_meeting_bot(
            meeting_repository=meeting_repository,
            meeting=meeting,
            external_meeting_bot_id=str(uuid4()),
        )
        second_meeting_bot = await self._persist_meeting_bot(
            meeting_repository=meeting_repository,
            meeting=meeting,
            external_meeting_bot_id=str(uuid4()),
        )

        result = await meeting_repository.find_meeting_bots_by_organization_id(
            organization_id=organization_id,
            meeting_ids=[meeting.id],
        )
        assert len(result) == 2
        assert meeting_bot in result
        assert second_meeting_bot in result

    async def test_find_meeting_bots_by_organization_id_not_found(
        self, meeting_repository: MeetingRepository
    ) -> None:
        result = await meeting_repository.find_meeting_bots_by_organization_id(
            organization_id=uuid4(),
            meeting_ids=[],
        )
        assert result == []

    async def test_get_meeting_bot_by_external_id(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        external_bot_id = str(uuid4())
        meeting_bot = await self._persist_meeting_bot(
            meeting_repository=meeting_repository,
            meeting=meeting,
            external_meeting_bot_id=external_bot_id,
        )

        result = await meeting_repository.get_meeting_bot_by_external_id(
            external_meeting_bot_id=external_bot_id, provider=BotProvider.RECALLAI
        )
        assert result == meeting_bot

    async def test_get_meeting_bot_by_external_id_not_found(
        self, meeting_repository: MeetingRepository
    ) -> None:
        result = await meeting_repository.get_meeting_bot_by_external_id(
            external_meeting_bot_id=str(uuid4()), provider=BotProvider.RECALLAI
        )
        assert result is None

    async def test_get_meeting_bot_by_external_id_and_meeting_id(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        external_bot_id = str(uuid4())
        meeting_bot = await self._persist_meeting_bot(
            meeting_repository=meeting_repository,
            meeting=meeting,
            external_meeting_bot_id=external_bot_id,
        )
        result = await meeting_repository.get_meeting_bot_by_external_id_and_meeting_id(
            external_meeting_bot_id=external_bot_id, meeting_id=meeting.id
        )
        assert result == meeting_bot

    async def test_get_meeting_bot_by_external_id_and_meeting_id_not_found(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        result = await meeting_repository.get_meeting_bot_by_external_id_and_meeting_id(
            external_meeting_bot_id=str(uuid4()), meeting_id=meeting.id
        )
        assert result is None

    async def test_find_meeting_bots_by_meeting(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        meeting_bot = await self._persist_meeting_bot(
            meeting_repository=meeting_repository,
            meeting=meeting,
            external_meeting_bot_id=str(uuid4()),
        )
        result = await meeting_repository.find_meeting_bots_by_meeting(
            organization_id=organization_id, meeting_id=meeting.id
        )
        assert result == [meeting_bot]

    async def test_find_meeting_bots_by_meeting_not_found(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        result = await meeting_repository.find_meeting_bots_by_meeting(
            organization_id=organization_id, meeting_id=meeting.id
        )
        assert result == []

    async def test_find_meeting_bots_by_meeting_url_and_time(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        meeting_bot = await self._persist_meeting_bot(
            meeting_repository=meeting_repository,
            meeting=meeting,
            external_meeting_bot_id=str(uuid4()),
        )
        result = await meeting_repository.find_meeting_bots_by_meeting_url_and_time(
            organization_id=organization_id,
            meeting_id=meeting.id,
            meeting_url=not_none(meeting.meeting_url),
            scheduled_at=not_none(meeting_bot.scheduled_at),
        )
        assert result == [meeting_bot]

    async def test_get_live_transcript_session(
        self, meeting_repository: MeetingRepository
    ) -> None:
        meeting_id = uuid4()
        organization_id = uuid4()
        user_id = uuid4()
        time_now = zoned_utc_now()
        record = await meeting_repository.insert(
            LiveTranscriptSession(
                id=uuid4(),
                organization_id=organization_id,
                user_id=user_id,
                meeting_id=meeting_id,
                created_at=time_now,
                updated_at=time_now,
                last_connected_at=time_now,
            )
        )
        result = await meeting_repository.get_live_transcript_session(
            organization_id=organization_id, meeting_id=meeting_id, user_id=user_id
        )
        assert result == record

    async def test_get_live_transcript_session_not_found(
        self, meeting_repository: MeetingRepository
    ) -> None:
        meeting_id = uuid4()
        organization_id = uuid4()
        user_id = uuid4()
        time_now = zoned_utc_now()
        await meeting_repository.insert(
            LiveTranscriptSession(
                id=uuid4(),
                organization_id=organization_id,
                user_id=user_id,
                meeting_id=meeting_id,
                created_at=time_now,
                updated_at=time_now,
                last_connected_at=time_now,
            )
        )

        result = await meeting_repository.get_live_transcript_session(
            organization_id=uuid4(),  # Does not match
            meeting_id=meeting_id,
            user_id=user_id,
        )
        assert result is None

        result = await meeting_repository.get_live_transcript_session(
            organization_id=organization_id,
            meeting_id=uuid4(),  # Does not match
            user_id=user_id,
        )
        assert result is None

        result = await meeting_repository.get_live_transcript_session(
            organization_id=organization_id,
            meeting_id=meeting_id,
            user_id=uuid4(),  # Does not match
        )
        assert result is None

    async def test_update_recording_id_by_bot_id(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        external_bot_id = str(uuid4())
        meeting_bot = await self._persist_meeting_bot(
            meeting_repository=meeting_repository,
            meeting=meeting,
            external_meeting_bot_id=external_bot_id,
        )

        recording_id = str(uuid4())
        result = await meeting_repository.update_recording_id_by_bot_id(
            external_meeting_bot_id=external_bot_id, recording_id=recording_id
        )
        assert result

        expected_meeting_bot = meeting_bot.copy(
            update={
                "external_recording_id": recording_id,
                "updated_at": result.updated_at,
            }
        )
        assert result == expected_meeting_bot

    async def test_append_bot_status_history_and_update_status(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        external_bot_id = str(uuid4())
        meeting_bot = await self._persist_meeting_bot(
            meeting_repository=meeting_repository,
            meeting=meeting,
            external_meeting_bot_id=external_bot_id,
        )

        status_event = BotStatusEvent(
            status="exited", sub_code=None, status_at=zoned_utc_now()
        )
        result = await meeting_repository.append_bot_status_history_and_update_status(
            external_meeting_bot_id=external_bot_id,
            bot_provider=BotProvider.RECALLAI,
            status_event=status_event,
            status=MeetingBotStatus.EXITED,
        )
        assert result

        expected_meeting_bot = meeting_bot.copy(
            update={
                "status": MeetingBotStatus.EXITED,
                "status_history": BotStatusHistory(status_history=[status_event]),
                "updated_at": result.updated_at,
            }
        )
        assert result == expected_meeting_bot

    async def test_append_bot_status_history(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        external_bot_id = str(uuid4())
        meeting_bot = await self._persist_meeting_bot(
            meeting_repository=meeting_repository,
            meeting=meeting,
            external_meeting_bot_id=external_bot_id,
        )

        status_event = BotStatusEvent(
            status="exited", sub_code=None, status_at=zoned_utc_now()
        )
        result = await meeting_repository.append_bot_status_history(
            external_meeting_bot_id=external_bot_id,
            bot_provider=BotProvider.RECALLAI,
            status_event=status_event,
        )
        assert result

        expected_meeting_bot = meeting_bot.copy(
            update={
                "status_history": BotStatusHistory(status_history=[status_event]),
                "updated_at": result.updated_at,
            }
        )
        assert result == expected_meeting_bot

    async def test_update_meeting_bot_media_url(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        external_bot_id = str(uuid4())
        meeting_bot = await self._persist_meeting_bot(
            meeting_repository=meeting_repository,
            meeting=meeting,
            external_meeting_bot_id=external_bot_id,
        )

        updated_at = zoned_utc_now()
        result = await meeting_repository.update_meeting_bot_by_external_id(
            organization_id=organization_id,
            external_meeting_bot_id=external_bot_id,
            meeting_bot_update=MeetingBotUpdate(
                media_url="https://media.localhost",
                media_vtt_url="https://media-vtt.localhost",
                media_sprite_url="https://media-sprite.localhost",
                updated_at=updated_at,
            ),
        )
        expected = meeting_bot.copy(
            update={
                "updated_at": updated_at,
                "media_url": "https://media.localhost",
                "media_vtt_url": "https://media-vtt.localhost",
                "media_sprite_url": "https://media-sprite.localhost",
            }
        )
        assert result == expected

    async def test_update_meeting_bot_recording_data(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        external_bot_id = str(uuid4())
        meeting_bot = await self._persist_meeting_bot(
            meeting_repository=meeting_repository,
            meeting=meeting,
            external_meeting_bot_id=external_bot_id,
        )

        retention = zoned_utc_now() + timedelta(days=1)
        updated_at = zoned_utc_now()
        completed_at = zoned_utc_now() - timedelta(minutes=1)
        recording_metadata = RecallRecordingData(
            recordings=[
                RecallRecording(
                    id=str(uuid4()),
                    started_at=zoned_utc_now(),
                    completed_at=zoned_utc_now(),
                )
            ],
        )

        screen_share_time_ranges = [
            ScreenShareRange(start_offset=0, end_offset=10),
            ScreenShareRange(start_offset=20, end_offset=30),
        ]
        result = await meeting_repository.update_meeting_bot_recording_data(
            organization_id=organization_id,
            external_meeting_bot_id=external_bot_id,
            external_media_url="https://external.media.localhost",
            external_media_retention_ended_at=retention,
            recording_metadata=recording_metadata,
            screen_share_ranges=screen_share_time_ranges,
            updated_at=updated_at,
            completed_at=completed_at,
        )
        expected = meeting_bot.copy(
            update={
                "external_media_url": "https://external.media.localhost",
                "external_media_retention_ended_at": retention,
                "recording_metadata": recording_metadata,
                "screen_share_ranges": [
                    ScreenShareRange(start_offset=0, end_offset=10),
                    ScreenShareRange(start_offset=20, end_offset=30),
                ],
                "updated_at": updated_at,
                "completed_at": completed_at,
            }
        )
        assert result == expected

    async def test_create_meeting_stats(
        self, meeting_repository: MeetingRepository
    ) -> None:
        meeting_stats = await self._persist_meeting_stats(
            meeting_repository=meeting_repository
        )
        retrieved_meeting_stats = await meeting_repository.find_by_primary_key(
            MeetingStats, id=meeting_stats.id
        )
        assert retrieved_meeting_stats is not None
        assert meeting_stats == retrieved_meeting_stats

    async def test_list_meetings_by_user_id(
        self, meeting_repository: MeetingRepository
    ) -> None:
        # Arrange
        organization_id = uuid4()
        user_id = uuid4()
        other_user_id = uuid4()

        # Create a meeting linked to our test user
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )

        # Create a user calendar event linking the meeting to our user
        time_now = zoned_utc_now()
        await meeting_repository.insert(
            UserCalendarEvent(
                id=uuid4(),
                title="Test-001",
                user_id=user_id,
                organization_id=organization_id,
                user_calendar_id=uuid4(),
                is_busy=True,
                external_id=str(uuid4()),
                group_key=meeting.reference_id,
                html_link="https://calendar.google.com/event2",
                status="confirmed",
                organizer_email="<EMAIL>",
                participants=[
                    CalendarEventParticipant(
                        email="<EMAIL>",
                        status="yes",
                    )
                ],
                meeting_id=meeting.id,
                starts_at=meeting.starts_at,
                ends_at=meeting.ends_at,
                external_created_at=time_now,
                external_updated_at=time_now,
                created_at=time_now,
                updated_at=time_now,
            )
        )

        # Create another meeting linked to a different user
        other_meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        await meeting_repository.insert(
            UserCalendarEvent(
                id=uuid4(),
                title="Test-002",
                user_id=other_user_id,
                organization_id=organization_id,
                user_calendar_id=uuid4(),
                is_busy=True,
                external_id=str(uuid4()),
                group_key=other_meeting.reference_id,
                html_link="https://calendar.google.com/event2",
                status="confirmed",
                organizer_email="<EMAIL>",
                participants=[
                    CalendarEventParticipant(
                        email="<EMAIL>",
                        status="yes",
                    )
                ],
                meeting_id=other_meeting.id,
                starts_at=other_meeting.starts_at,
                ends_at=other_meeting.ends_at,
                external_created_at=time_now,
                external_updated_at=time_now,
                created_at=time_now,
                updated_at=time_now,
            )
        )

        # Create a dialer meeting
        dialer_meeting = await self._persist_dialer_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
            user_id=user_id,
        )

        # Act
        result = await meeting_repository.list_meetings_by_user_id(
            organization_id=organization_id, user_id=user_id
        )

        # Assert
        assert len(result) == 2
        assert {str(result[0].id), str(result[1].id)} == {
            str(meeting.id),
            str(dialer_meeting.id),
        }

        # Test no results for different organization
        result = await meeting_repository.list_meetings_by_user_id(
            organization_id=uuid4(),  # Different org
            user_id=user_id,
        )
        assert len(result) == 0

        # Test no results for different user
        result = await meeting_repository.list_meetings_by_user_id(
            organization_id=organization_id,
            user_id=uuid4(),  # Different user
        )
        assert len(result) == 0

    async def test_find_transcript_for_meeting_bot(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        meeting_bot_id = uuid4()
        persisted_transcript = await meeting_repository.insert(
            Transcript(
                id=uuid4(),
                reference_id=str(meeting_bot_id),
                reference_id_type=TranscriptReferenceIdType.MEETING_BOT,
                organization_id=organization_id,
                provider=TranscriptProvider.ASSEMBLYAI,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            )
        )
        result = await meeting_repository.find_transcript_for_meeting_bot(
            organization_id=organization_id, meeting_bot_id=meeting_bot_id
        )
        assert result == persisted_transcript

    async def test_create_meeting_bot_transcript_initial_rows(
        self, meeting_repository: MeetingRepository
    ) -> None:
        meeting_id = uuid4()
        external_bot_id = str(uuid4())
        bot_provider = BotProvider.RECALLAI
        transcript_provider = TranscriptProvider.ASSEMBLYAI
        organization_id = uuid4()
        scheduled_at = zoned_utc_now()
        realtime_event_token = uuid4()
        meeting_bot_name = str(uuid4())
        meeting_url = "https://meet.localhost"

        (
            result_meeting_bot,
            result_transcript,
        ) = await meeting_repository.create_meeting_bot_transcript_initial_rows(
            meeting_id=meeting_id,
            external_bot_id=external_bot_id,
            bot_provider=bot_provider,
            meeting_bot_name=meeting_bot_name,
            transcript_provider=transcript_provider,
            organization_id=organization_id,
            scheduled_at=scheduled_at,
            realtime_event_token=realtime_event_token,
            meeting_url=meeting_url,
        )

        expected_meeting_bot = MeetingBot(
            id=result_meeting_bot.id,  # generated
            meeting_id=meeting_id,
            external_meeting_bot_id=external_bot_id,
            created_at=result_meeting_bot.created_at,  # generated
            updated_at=result_meeting_bot.updated_at,  # generated
            provider=bot_provider.value,
            organization_id=organization_id,
            scheduled_at=scheduled_at,
            status=MeetingBotStatus.PENDING,
            realtime_event_token=realtime_event_token,
            name=meeting_bot_name,
            meeting_url=meeting_url,
        )
        assert result_meeting_bot == expected_meeting_bot

        expected_transcript = Transcript(
            id=result_transcript.id,
            reference_id=str(result_meeting_bot.id),
            reference_id_type=TranscriptReferenceIdType.MEETING_BOT,
            organization_id=organization_id,
            provider=transcript_provider.value,
            created_at=result_transcript.created_at,
            updated_at=result_transcript.updated_at,
        )
        assert result_transcript == expected_transcript

    async def _persist_dialer_meeting(
        self,
        meeting_repository: MeetingRepository,
        organization_id: UUID,
        consent_id: UUID | None = None,
        user_id: UUID | None = None,
    ) -> Meeting:
        time_now = zoned_utc_now()
        starts_at = zoned_utc_now() + timedelta(hours=1)
        ends_at = starts_at + timedelta(hours=1)
        research_content = MeetingResearchDto(
            contact_researches=[
                ContactResearchDto(
                    id=uuid4(),
                    organization_id=organization_id,
                    intel_person_id=uuid4(),
                    snippet=ContactSnippet(
                        first_name="first",
                        last_name="last",
                        full_name="first last",
                        photo_url=None,
                        title="CEO",
                        current_company_name="AI",
                        current_company_linkedin_url=None,
                        linkedin_url=None,
                        experience=None,
                    ),
                    socials=None,
                )
            ],
            account_researches=[],
        )
        return await meeting_repository.insert(
            Meeting(
                id=uuid4(),
                reference_id=str(uuid4()),
                reference_id_type=MeetingReferenceIdType.VOICE_V2,
                meeting_url="https://meet.localhost",
                meeting_platform=MeetingProvider.VOICE,
                conferencing_details=None,
                created_at=time_now,
                updated_at=time_now,
                starts_at=starts_at,
                ends_at=ends_at,
                title=str(uuid4()),
                description="Description",
                invitees=[MeetingInvitee(user_id=user_id, is_organizer=True)],
                organization_id=organization_id,
                status=MeetingStatus.COMPLETED,
                created_by_user_id=user_id,
                organizer_user_id=user_id,
                consent_id=consent_id,
                research_content=research_content,
            )
        )

    async def _persist_meeting(
        self,
        meeting_repository: MeetingRepository,
        organization_id: UUID,
        consent_id: UUID | None = None,
    ) -> Meeting:
        time_now = zoned_utc_now()
        starts_at = zoned_utc_now() + timedelta(hours=1)
        ends_at = starts_at + timedelta(hours=1)
        research_content = MeetingResearchDto(
            contact_researches=[
                ContactResearchDto(
                    id=uuid4(),
                    organization_id=organization_id,
                    intel_person_id=uuid4(),
                    snippet=ContactSnippet(
                        first_name="first",
                        last_name="last",
                        full_name="first last",
                        photo_url=None,
                        title="CEO",
                        current_company_name="AI",
                        current_company_linkedin_url=None,
                        linkedin_url=None,
                        experience=None,
                    ),
                    socials=None,
                )
            ],
            account_researches=[],
        )
        user_id = uuid4()
        return await meeting_repository.insert(
            Meeting(
                id=uuid4(),
                reference_id=str(uuid4()),
                reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
                meeting_url="https://meet.localhost",
                meeting_platform=MeetingProvider.GMEET,
                conferencing_details=None,
                created_at=time_now,
                updated_at=time_now,
                starts_at=starts_at,
                ends_at=ends_at,
                title=str(uuid4()),
                description="Description",
                invitees=[MeetingInvitee(user_id=user_id, is_organizer=True)],
                organization_id=organization_id,
                status=MeetingStatus.SCHEDULED,
                created_by_user_id=user_id,
                organizer_user_id=user_id,
                consent_id=consent_id,
                research_content=research_content,
                owner_user_id=user_id,
            )
        )

    async def _persist_meeting_bot(
        self,
        meeting_repository: MeetingRepository,
        meeting: Meeting,
        external_meeting_bot_id: str,
    ) -> MeetingBot:
        time_now = zoned_utc_now()
        meeting_bot = MeetingBot(
            id=uuid4(),
            meeting_id=meeting.id,
            external_meeting_bot_id=external_meeting_bot_id,
            created_at=time_now,
            updated_at=time_now,
            provider=BotProvider.RECALLAI,
            organization_id=meeting.organization_id,
            scheduled_at=time_now,
            status=MeetingBotStatus.SCHEDULED,
            realtime_event_token=uuid4(),
            meeting_url=meeting.meeting_url,
        )
        return await meeting_repository.insert(meeting_bot)

    async def _persist_meeting_stats(
        self,
        meeting_repository: MeetingRepository,
    ) -> MeetingStats:
        time_now = zoned_utc_now()
        organization_id = uuid4()
        talk_rations = [
            MeetingAttendeeTalkRatio(
                user_id=uuid4(), is_organizer=True, total_time=38, ratio=0.34
            ),
            MeetingAttendeeTalkRatio(
                contact_id=uuid4(), is_organizer=True, total_time=68, ratio=0.66
            ),
        ]

        longest_user_monologue = MeetingAttendeeMonologue(
            user_id=uuid4(), is_organizer=True, total_time=11, start_offset=2
        )
        meeting_stats = await meeting_repository.insert(
            MeetingStats(
                id=uuid4(),
                organization_id=organization_id,
                talk_ratios=talk_rations,
                longest_user_monologue=longest_user_monologue,
                longest_customer_story=None,
                interactivity=1.0,
                patience=2.1,
                created_at=time_now,
                updated_at=time_now,
            )
        )
        assert meeting_stats.organization_id == organization_id
        assert meeting_stats.talk_ratios == talk_rations
        assert meeting_stats.longest_user_monologue == longest_user_monologue
        assert meeting_stats.longest_customer_story is None
        assert meeting_stats.interactivity == 1.0
        assert meeting_stats.patience == 2.1
        assert meeting_stats.created_at == time_now
        assert meeting_stats.updated_at == time_now

        return meeting_stats

    async def test_list_earliest_meeting_id_by_pipeline(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        first_meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        pipeline_id = uuid4()
        now = zoned_utc_now()
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=organization_id,
            primary_key_to_value={"id": first_meeting.id},
            column_to_update={"pipeline_id": pipeline_id, "starts_at": now},
        )

        second_meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=organization_id,
            primary_key_to_value={"id": second_meeting.id},
            column_to_update={
                "pipeline_id": pipeline_id,
                "starts_at": now + timedelta(hours=1),
            },
        )

        # Other meeting, no pipeline set
        third_meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )

        result = await meeting_repository.list_earliest_meeting_id_by_pipeline(
            organization_id=organization_id, meeting_ids=None
        )
        assert result == {pipeline_id: first_meeting.id}

        result = await meeting_repository.list_earliest_meeting_id_by_pipeline(
            organization_id=organization_id,
            meeting_ids=[first_meeting.id, second_meeting.id, third_meeting.id],
        )
        assert result == {pipeline_id: first_meeting.id}

    async def test_list_meetings_by_contact_id(
        self, meeting_repository: MeetingRepository
    ) -> None:
        # Arrange
        organization_id = uuid4()
        contact_id = uuid4()
        other_contact_id = uuid4()

        # Create a meeting with our test contact in invitees
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=organization_id,
            primary_key_to_value={"id": meeting.id},
            column_to_update={
                "invitees": [{"contact_id": str(contact_id), "is_organizer": False}]
            },
        )

        # Create another meeting with a different contact
        other_meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=organization_id,
            primary_key_to_value={"id": other_meeting.id},
            column_to_update={
                "invitees": [
                    {"contact_id": str(other_contact_id), "is_organizer": False}
                ]
            },
        )

        # Create a deleted meeting with our test contact
        deleted_meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=organization_id,
            primary_key_to_value={"id": deleted_meeting.id},
            column_to_update={
                "invitees": [{"contact_id": str(contact_id), "is_organizer": False}],
                "deleted_at": zoned_utc_now(),
            },
        )

        # Create a meeting in a different organization
        other_org_meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=uuid4(),  # Different org
        )
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=other_org_meeting.organization_id,
            primary_key_to_value={"id": other_org_meeting.id},
            column_to_update={
                "invitees": [{"contact_id": str(contact_id), "is_organizer": False}]
            },
        )

        # Act
        result = await meeting_repository.list_meetings_by_contact_id(
            organization_id=organization_id,
            contact_id=contact_id,
        )

        # Assert
        assert len(result) == 1
        assert result[0].id == meeting.id

        # Test no results for different organization
        result = await meeting_repository.list_meetings_by_contact_id(
            organization_id=uuid4(),  # Different org
            contact_id=contact_id,
        )
        assert len(result) == 0

        # Test no results for different contact
        result = await meeting_repository.list_meetings_by_contact_id(
            organization_id=organization_id,
            contact_id=uuid4(),  # Different contact
        )
        assert len(result) == 0

    async def test_list_meetings_by_contact_ids(
        self, meeting_repository: MeetingRepository
    ) -> None:
        organization_id = uuid4()
        contact_id = uuid4()
        other_contact_id = uuid4()

        # Create a meeting with our test contact in invitees
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=organization_id,
            primary_key_to_value={"id": meeting.id},
            column_to_update={
                "invitees": [{"contact_id": str(contact_id), "is_organizer": False}]
            },
        )

        # Create another meeting with a different contact
        other_meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=organization_id,
            primary_key_to_value={"id": other_meeting.id},
            column_to_update={
                "invitees": [
                    {"contact_id": str(other_contact_id), "is_organizer": False}
                ]
            },
        )

        # Create a meeting in a different organization
        other_org_meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=uuid4(),  # Different org
        )
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=other_org_meeting.organization_id,
            primary_key_to_value={"id": other_org_meeting.id},
            column_to_update={
                "invitees": [{"contact_id": str(contact_id), "is_organizer": False}]
            },
        )

        result = await meeting_repository.list_meetings_by_contact_ids(
            organization_id=organization_id,
            contact_ids=[contact_id, other_contact_id],
        )

        # Assert
        assert len(result) == 2
        result_ids = {meeting.id for meeting in result}
        expected_ids = {meeting.id, other_meeting.id}
        assert result_ids == expected_ids

        result_excluding_other_meeting = (
            await meeting_repository.list_meetings_by_contact_ids(
                organization_id=organization_id,
                contact_ids=[contact_id],
                exclude_meeting_ids={other_meeting.id},
            )
        )
        assert len(result_excluding_other_meeting) == 1
        assert result_excluding_other_meeting[0].id == meeting.id

    async def test_list_meetings_by_contact_ids_single_contact(
        self, meeting_repository: MeetingRepository
    ) -> None:
        # Arrange
        organization_id = uuid4()
        contact_id = uuid4()

        # Create a meeting with our test contact in invitees
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=organization_id,
            primary_key_to_value={"id": meeting.id},
            column_to_update={
                "invitees": [{"contact_id": str(contact_id), "is_organizer": False}]
            },
        )

        # Act
        result = await meeting_repository.list_meetings_by_contact_ids(
            organization_id=organization_id,
            contact_ids=[contact_id],
        )

        # Assert
        assert len(result) == 1
        assert result[0].id == meeting.id

    async def test_list_meetings_by_contact_ids_multiple_contacts(
        self, meeting_repository: MeetingRepository
    ) -> None:
        # Arrange
        organization_id = uuid4()
        contact_id_1 = uuid4()
        contact_id_2 = uuid4()

        # Create first meeting with contact 1
        meeting_1 = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=organization_id,
            primary_key_to_value={"id": meeting_1.id},
            column_to_update={
                "invitees": [{"contact_id": str(contact_id_1), "is_organizer": False}]
            },
        )

        # Create second meeting with contact 2
        meeting_2 = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=organization_id,
            primary_key_to_value={"id": meeting_2.id},
            column_to_update={
                "invitees": [{"contact_id": str(contact_id_2), "is_organizer": False}]
            },
        )

        # Create third meeting with both contacts
        meeting_3 = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=organization_id,
            primary_key_to_value={"id": meeting_3.id},
            column_to_update={
                "invitees": [
                    {"contact_id": str(contact_id_1), "is_organizer": False},
                    {"contact_id": str(contact_id_2), "is_organizer": False},
                ]
            },
        )

        # Act
        result = await meeting_repository.list_meetings_by_contact_ids(
            organization_id=organization_id,
            contact_ids=[contact_id_1, contact_id_2],
        )

        # Assert
        assert len(result) == 3
        result_ids = {meeting.id for meeting in result}
        expected_ids = {meeting_1.id, meeting_2.id, meeting_3.id}
        assert result_ids == expected_ids

    async def test_list_meetings_by_contact_ids_with_exclusions(
        self, meeting_repository: MeetingRepository
    ) -> None:
        # Arrange
        organization_id = uuid4()
        contact_id = uuid4()

        # Create first meeting with contact
        meeting_1 = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=organization_id,
            primary_key_to_value={"id": meeting_1.id},
            column_to_update={
                "invitees": [{"contact_id": str(contact_id), "is_organizer": False}]
            },
        )

        # Create second meeting with same contact
        meeting_2 = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=organization_id,
            primary_key_to_value={"id": meeting_2.id},
            column_to_update={
                "invitees": [{"contact_id": str(contact_id), "is_organizer": False}]
            },
        )

        # Act
        result = await meeting_repository.list_meetings_by_contact_ids(
            organization_id=organization_id,
            contact_ids=[contact_id],
            exclude_meeting_ids={meeting_2.id},
        )

        # Assert
        assert len(result) == 1
        assert result[0].id == meeting_1.id

    async def test_list_meetings_by_contact_ids_empty_list(
        self,
        meeting_repository: MeetingRepository,
    ) -> None:
        # Arrange
        organization_id = uuid4()

        # Create a meeting
        await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )

        # Act
        result = await meeting_repository.list_meetings_by_contact_ids(
            organization_id=organization_id,
            contact_ids=[],
        )

        # Assert
        assert result == []

    async def test_list_meetings_by_contact_ids_non_existent_contact(
        self, meeting_repository: MeetingRepository
    ) -> None:
        # Arrange
        organization_id = uuid4()
        non_existent_contact_id = uuid4()

        # Create a meeting without the contact
        await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )

        # Act
        result = await meeting_repository.list_meetings_by_contact_ids(
            organization_id=organization_id,
            contact_ids=[non_existent_contact_id],
        )

        # Assert
        assert result == []

    async def test_list_meetings_by_primary_account_ids_direct_meeting(
        self, meeting_repository: MeetingRepository
    ) -> None:
        # Arrange
        organization_id = uuid4()
        account_id = uuid4()

        # Create a meeting directly linked to the account
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=organization_id,
            primary_key_to_value={"id": meeting.id},
            column_to_update={"account_id": account_id},
        )

        # Act
        result = await meeting_repository.list_meetings_by_primary_account_ids(
            organization_id=organization_id,
            primary_account_ids=[account_id],
        )

        # Assert
        assert len(result) == 1
        assert result[0].id == meeting.id

    async def test_list_meetings_by_primary_account_ids_with_contacts(
        self,
        meeting_repository: MeetingRepository,
        contact_repository: ContactRepository,
    ) -> None:
        # Arrange
        organization_id = uuid4()
        account_id = uuid4()
        contact_id = uuid4()

        # Create a meeting with a contact associated with the account
        meeting_with_contact = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=organization_id,
            primary_key_to_value={"id": meeting_with_contact.id},
            column_to_update={
                "invitees": [{"contact_id": contact_id, "is_organizer": False}]
            },
        )

        # Create a direct meeting with the account
        meeting_with_account = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=organization_id,
            primary_key_to_value={"id": meeting_with_account.id},
            column_to_update={"account_id": account_id},
        )

        # Create the contact and contact-account association using repository
        from salestech_be.db.models.contact import Contact, ContactAccountAssociation

        time_now = zoned_utc_now()
        user_id = uuid4()
        contact = Contact(
            id=contact_id,
            organization_id=organization_id,
            created_at=time_now,
            updated_at=time_now,
            display_name="Test Contact",
            created_by_user_id=user_id,
            owner_user_id=user_id,
            stage_id=uuid4(),
        )
        await contact_repository.insert(contact)

        association = ContactAccountAssociation(
            id=uuid4(),
            organization_id=organization_id,
            contact_id=contact_id,
            account_id=account_id,
            is_primary=True,
            created_at=time_now,
            updated_at=time_now,
            created_by_user_id=user_id,
            updated_by_user_id=user_id,
        )
        await contact_repository.insert(association)

        # Act
        result = await meeting_repository.list_meetings_by_primary_account_ids(
            organization_id=organization_id,
            primary_account_ids=[account_id],
        )

        # Assert
        assert len(result) == 2
        result_ids = {meeting.id for meeting in result}
        expected_ids = {meeting_with_contact.id, meeting_with_account.id}
        assert result_ids == expected_ids

    async def test_list_meetings_by_primary_account_ids_empty_list(
        self, meeting_repository: MeetingRepository
    ) -> None:
        # Arrange
        organization_id = uuid4()

        # Create a meeting
        await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )

        # Act
        result = await meeting_repository.list_meetings_by_primary_account_ids(
            organization_id=organization_id,
            primary_account_ids=[],
        )

        # Assert
        assert result == []

    async def test_list_meetings_by_primary_account_ids_archived_contacts(
        self,
        meeting_repository: MeetingRepository,
        contact_repository: ContactRepository,
    ) -> None:
        # Arrange
        organization_id = uuid4()
        account_id = uuid4()
        contact_id = uuid4()

        # Create a meeting with a contact that will be archived
        meeting = await self._persist_meeting(
            meeting_repository=meeting_repository,
            organization_id=organization_id,
        )
        await meeting_repository.update_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=organization_id,
            primary_key_to_value={"id": meeting.id},
            column_to_update={
                "invitees": [{"contact_id": str(contact_id), "is_organizer": False}]
            },
        )

        # Create the contact and contact-account association with archived contact
        from salestech_be.db.models.contact import Contact, ContactAccountAssociation

        time_now = zoned_utc_now()
        user_id = uuid4()
        contact = Contact(
            id=contact_id,
            organization_id=organization_id,
            created_at=time_now,
            updated_at=time_now,
            archived_at=time_now,  # Contact is archived
            display_name="Test Contact",
            created_by_user_id=user_id,
            owner_user_id=user_id,
            stage_id=uuid4(),
        )
        await contact_repository.insert(contact)

        association = ContactAccountAssociation(
            id=uuid4(),
            organization_id=organization_id,
            contact_id=contact_id,
            account_id=account_id,
            is_primary=True,
            created_at=time_now,
            updated_at=time_now,
            created_by_user_id=user_id,
            updated_by_user_id=user_id,
        )
        await contact_repository.insert(association)

        # Act
        result = await meeting_repository.list_meetings_by_primary_account_ids(
            organization_id=organization_id,
            primary_account_ids=[account_id],
        )

        # Assert - should not return meetings with archived contacts
        assert result == []
