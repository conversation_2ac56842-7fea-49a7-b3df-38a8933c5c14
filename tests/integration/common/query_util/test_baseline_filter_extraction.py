from uuid import UUID, uuid4

import pytest

from salestech_be.common.query_util.baseline_filter_extraction import (
    BaselineFilterExtractor,
)
from salestech_be.common.query_util.filter_schema import (
    CompositeFilter,
    FilterSpec,
    QualifiedField,
    ValueFilter,
)
from salestech_be.common.query_util.operator import MatchOperator
from salestech_be.common.schema_manager.std_object_field_identifier import (
    AccountField,
    StdObjectIdentifiers,
)
from salestech_be.common.type.metadata.schema import (
    OrganizationSchemaDescriptor,
)
from salestech_be.core.metadata.service.metadata_service import MetadataService


@pytest.fixture
def organization_id() -> UUID:
    return uuid4()


@pytest.fixture
async def organization_schema_descriptor(
    metadata_service: MetadataService,
    organization_id: UUID,
) -> OrganizationSchemaDescriptor:
    return await metadata_service.get_organization_domain_object_schema(
        organization_id=organization_id,
    )


@pytest.fixture
def filter_extractor() -> BaselineFilterExtractor:
    return BaselineFilterExtractor()


async def test_extract_baseline_filter_group_simple_value_filter(
    organization_schema_descriptor: OrganizationSchemaDescriptor,
    filter_extractor: BaselineFilterExtractor,
) -> None:
    """Test extracting a simple value filter."""
    account_object_identifier = StdObjectIdentifiers.account.identifier
    filter_spec = FilterSpec(
        primary_object_identifier=account_object_identifier,
        filter=ValueFilter(
            field=QualifiedField(path=(AccountField.display_name,)),
            value="test",
            operator=MatchOperator.EQ,
        ),
    )

    result = filter_extractor.extract_baseline_filter_group(
        filter_spec=filter_spec,
        # organization_schema_descriptor=organization_schema_descriptor,
    )

    assert result is not None
    assert len(result.baseline_filters_by_object_identifier) == 1
    assert account_object_identifier in result.baseline_filters_by_object_identifier
    account_filters = result.baseline_filters_by_object_identifier[
        account_object_identifier
    ]
    assert len(account_filters.must_filters) == 1

    assert account_filters.must_filters[0] == filter_spec.filter


async def test_extract_baseline_filter_group_composite_and_filter(
    organization_schema_descriptor: OrganizationSchemaDescriptor,
    filter_extractor: BaselineFilterExtractor,
) -> None:
    """Test extracting filters from a composite AND filter."""
    account_object_identifier = StdObjectIdentifiers.account.identifier
    filter_spec = FilterSpec(
        primary_object_identifier=account_object_identifier,
        filter=CompositeFilter(
            all_of=[
                ValueFilter(
                    field=QualifiedField(path=(AccountField.display_name,)),
                    value="test",
                    operator=MatchOperator.EQ,
                ),
                ValueFilter(
                    field=QualifiedField(path=(AccountField.description,)),
                    value="description",
                    operator=MatchOperator.CONTAINS,
                ),
            ],
            any_of=[],
            none_of=[],
        ),
    )

    result = filter_extractor.extract_baseline_filter_group(
        filter_spec=filter_spec,
        # organization_schema_descriptor=organization_schema_descriptor,
    )

    assert result is not None
    assert len(result.baseline_filters_by_object_identifier) == 1
    assert account_object_identifier in result.baseline_filters_by_object_identifier
    account_filters = result.baseline_filters_by_object_identifier[
        account_object_identifier
    ]
    assert len(account_filters.must_filters) == 2
    assert len(account_filters.must_not_filters) == 0
    assert len(account_filters.at_least_one_filters) == 0


async def test_extract_baseline_filter_group_composite_or_filter(
    organization_schema_descriptor: OrganizationSchemaDescriptor,
    filter_extractor: BaselineFilterExtractor,
) -> None:
    """Test extracting filters from a composite OR filter."""
    account_object_identifier = StdObjectIdentifiers.account.identifier
    filter_spec = FilterSpec(
        primary_object_identifier=account_object_identifier,
        filter=CompositeFilter(
            all_of=[],
            any_of=[
                ValueFilter(
                    field=QualifiedField(path=(AccountField.display_name,)),
                    value="test1",
                    operator=MatchOperator.EQ,
                ),
                ValueFilter(
                    field=QualifiedField(path=(AccountField.display_name,)),
                    value="test2",
                    operator=MatchOperator.EQ,
                ),
            ],
            none_of=[],
        ),
    )

    result = filter_extractor.extract_baseline_filter_group(
        filter_spec=filter_spec,
        # organization_schema_descriptor=organization_schema_descriptor,
    )

    assert result is not None
    assert len(result.baseline_filters_by_object_identifier) == 1
    assert account_object_identifier in result.baseline_filters_by_object_identifier
    account_filters = result.baseline_filters_by_object_identifier[
        account_object_identifier
    ]
    assert len(account_filters.must_filters) == 0
    assert len(account_filters.must_not_filters) == 0
    assert len(account_filters.at_least_one_filters) == 2


async def test_extract_baseline_filter_group_composite_not_filter(
    organization_schema_descriptor: OrganizationSchemaDescriptor,
    filter_extractor: BaselineFilterExtractor,
) -> None:
    """Test extracting filters from a composite NOT filter."""
    account_object_identifier = StdObjectIdentifiers.account.identifier
    filter_spec = FilterSpec(
        primary_object_identifier=account_object_identifier,
        filter=CompositeFilter(
            all_of=[],
            any_of=[],
            none_of=[
                ValueFilter(
                    field=QualifiedField(path=(AccountField.display_name,)),
                    value="test",
                    operator=MatchOperator.EQ,
                ),
            ],
        ),
    )

    result = filter_extractor.extract_baseline_filter_group(
        filter_spec=filter_spec,
        # organization_schema_descriptor=organization_schema_descriptor,
    )

    assert result is not None
    assert len(result.baseline_filters_by_object_identifier) == 1
    assert account_object_identifier in result.baseline_filters_by_object_identifier
    account_filters = result.baseline_filters_by_object_identifier[
        account_object_identifier
    ]
    assert len(account_filters.must_filters) == 0
    assert len(account_filters.must_not_filters) == 1
    assert len(account_filters.at_least_one_filters) == 0


async def test_extract_baseline_filter_group_nested_composite_filter(
    organization_schema_descriptor: OrganizationSchemaDescriptor,
    filter_extractor: BaselineFilterExtractor,
) -> None:
    """Test extracting filters from nested composite filters."""
    account_object_identifier = StdObjectIdentifiers.account.identifier
    filter_spec = FilterSpec(
        primary_object_identifier=account_object_identifier,
        filter=CompositeFilter(
            all_of=[
                CompositeFilter(
                    any_of=[
                        ValueFilter(
                            field=QualifiedField(path=(AccountField.display_name,)),
                            value="test",
                            operator=MatchOperator.EQ,
                        ),
                        ValueFilter(
                            field=QualifiedField(path=(AccountField.display_name,)),
                            value="test",
                            operator=MatchOperator.EQ,
                        ),
                    ],
                    all_of=[
                        ValueFilter(
                            field=QualifiedField(path=(AccountField.display_name,)),
                            value="test",
                            operator=MatchOperator.EQ,
                        ),
                    ],
                    none_of=[],
                ),
            ],
            any_of=[],
            none_of=[],
        ),
    )

    result = filter_extractor.extract_baseline_filter_group(
        filter_spec=filter_spec,
        # organization_schema_descriptor=organization_schema_descriptor,
    )

    assert result is not None
    assert len(result.baseline_filters_by_object_identifier) == 1
    assert account_object_identifier in result.baseline_filters_by_object_identifier
    account_filters = result.baseline_filters_by_object_identifier[
        account_object_identifier
    ]
    assert len(account_filters.must_filters) == 1
    assert len(account_filters.must_not_filters) == 0
    assert len(account_filters.at_least_one_filters) == 0, (
        "child any_of should be ignored in parent layer"
    )


# async def test_extract_baseline_filter_group_field_reference(
#     organization_schema_descriptor: OrganizationSchemaDescriptor,
#     filter_extractor: BaselineFilterExtractor,
# ) -> None:
#     """Test extracting filters with field references."""
#     account_object_identifier = StdObjectIdentifiers.account.identifier
#     contact_object_identifier = StdObjectIdentifiers.contact.identifier
#     filter_spec = FilterSpec(
#         primary_object_identifier=account_object_identifier,
#         filter=ValueFilter(
#             field=FieldReference(
#                 relationship_id=AccountRelationship.primary_account__from__contact,
#                 field=QualifiedField(path=(ContactField.display_name,)),
#             ),
#             value="test",
#             operator=MatchOperator.EQ,
#         ),
#     )

#     result = filter_extractor.extract_baseline_filter_group(
#         filter_spec=filter_spec,
#         organization_schema_descriptor=organization_schema_descriptor,
#     )

#     assert result is not None
#     assert len(result.baseline_filters_by_object_identifier) == 1
#     assert contact_object_identifier in result.baseline_filters_by_object_identifier


async def test_extract_baseline_filter_group_mixed_composite_filter(
    organization_schema_descriptor: OrganizationSchemaDescriptor,
    filter_extractor: BaselineFilterExtractor,
) -> None:
    """Test extracting filters from a composite filter with mixed conditions."""
    account_object_identifier = StdObjectIdentifiers.account.identifier
    filter_spec = FilterSpec(
        primary_object_identifier=account_object_identifier,
        filter=CompositeFilter(
            all_of=[
                ValueFilter(
                    field=QualifiedField(path=(AccountField.display_name,)),
                    value="test",
                    operator=MatchOperator.EQ,
                ),
            ],
            any_of=[
                ValueFilter(
                    field=QualifiedField(path=(AccountField.description,)),
                    value="desc1",
                    operator=MatchOperator.CONTAINS,
                ),
                ValueFilter(
                    field=QualifiedField(path=(AccountField.description,)),
                    value="desc2",
                    operator=MatchOperator.CONTAINS,
                ),
            ],
            none_of=[
                ValueFilter(
                    field=QualifiedField(path=(AccountField.display_name,)),
                    value="blocked",
                    operator=MatchOperator.EQ,
                ),
            ],
        ),
    )

    result = filter_extractor.extract_baseline_filter_group(
        filter_spec=filter_spec,
        # organization_schema_descriptor=organization_schema_descriptor,
    )

    assert result is not None
    assert len(result.baseline_filters_by_object_identifier) == 1
    assert account_object_identifier in result.baseline_filters_by_object_identifier
    account_filters = result.baseline_filters_by_object_identifier[
        account_object_identifier
    ]
    assert len(account_filters.must_filters) == 1
    assert len(account_filters.must_not_filters) == 1
    assert len(account_filters.at_least_one_filters) == 2


# async def test_extract_baseline_filter_group_invalid_field_reference(
#     organization_schema_descriptor: OrganizationSchemaDescriptor,
#     filter_extractor: BaselineFilterExtractor,
# ) -> None:
#     """Test extracting filters with invalid field reference."""
#     account_object_identifier = StdObjectIdentifiers.account.identifier
#     filter_spec = FilterSpec(
#         primary_object_identifier=account_object_identifier,
#         filter=ValueFilter(
#             field=FieldReference(
#                 relationship_id=uuid4(),  # Invalid relationship ID
#                 field=QualifiedField(path=("nonexistent_field",)),
#             ),
#             value="test",
#             operator=MatchOperator.EQ,
#         ),
#     )

#     result = filter_extractor.extract_baseline_filter_group(
#         filter_spec=filter_spec,
#         organization_schema_descriptor=organization_schema_descriptor,
#     )

#     assert result is not None
#     assert len(result.baseline_filters_by_object_identifier) == 0


# async def test_extract_baseline_filter_group_any_of_with_field_reference(
#     organization_schema_descriptor: OrganizationSchemaDescriptor,
#     filter_extractor: BaselineFilterExtractor,
# ) -> None:
#     """Test that any_of filters containing field references are ignored."""
#     account_object_identifier = StdObjectIdentifiers.account.identifier
#     filter_spec = FilterSpec(
#         primary_object_identifier=account_object_identifier,
#         filter=CompositeFilter(
#             all_of=[],
#             any_of=[
#                 ValueFilter(
#                     field=FieldReference(
#                         relationship_id=uuid4(),
#                         field=QualifiedField(path=("contact_name",)),
#                     ),
#                     value="test",
#                     operator=MatchOperator.EQ,
#                 ),
#             ],
#             none_of=[],
#         ),
#     )

#     result = filter_extractor.extract_baseline_filter_group(
#         filter_spec=filter_spec,
#         organization_schema_descriptor=organization_schema_descriptor,
#     )

#     assert result is not None
#     assert len(result.baseline_filters_by_object_identifier) == 0
