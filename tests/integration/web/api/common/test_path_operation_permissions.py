import re
from typing import Any, assert_never
from unittest.mock import Mock, patch
from uuid import uuid4

from fastapi import FastAP<PERSON>
from fastapi.routing import APIRoute

from salestech_be.common.exception.exception import ForbiddenError
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.types import ReevoJWTClaims

PERMISSION_PATTERN = re.compile(r"^require_(?P<action>.*?)_(?P<resource>.*?)_access$")

logger = get_logger(__name__)


async def _test_decorator(dependencies: list[Any], user_permissions: list[str]) -> bool:
    # Create JWT claims with the test permissions
    jwt_claims = ReevoJWTClaims(
        sub=uuid4(),
        org=uuid4(),
        perm=user_permissions,
    )

    for dep in dependencies:
        await dep(jwt_claims)
    return True


@patch("salestech_be.services.auth.perm_predicates.settings")
async def test_permission_decorators(mock_settings: Mock, fastapi_app: FastAPI) -> None:  # noqa: C901, PLR0912
    """
    Test permission decorators for specific route groups.
    """
    mock_settings.enable_mailbox_perms = True
    mock_settings.enable_prospecting_perms = True
    mock_settings.enable_sequence_perms = True

    def unwrap_dependency(dependency: Any) -> Any:
        if hasattr(dependency, "dependency"):
            return dependency.dependency
        return dependency

    def _get_required_permission(dependency: Any) -> str | None:
        """
        Extract the required permission from a permission dependency function.

        Args:
            dependency: The permission function (e.g., require_read_account_access)

        Returns:
            str: The permission string (e.g., "read:account")
        """
        # FastAPI wraps our dependency, so get the original function
        dependency = unwrap_dependency(dependency)

        # Check if dependency has __name__ attribute and extract permission
        if hasattr(dependency, "__name__"):
            m = PERMISSION_PATTERN.search(dependency.__name__)
            if m:
                if m.group("action") == "super" and m.group("resource") == "admin":
                    return "super_admin:*"  # TODO: super_admin:* vs super:admin, for Colin to decide
                return f"{m.group('action')}:{m.group('resource')}"

            # admin access is a special case.
            match dependency.__name__:
                case "require_admin_access":
                    return "admin:*"
                case _ as never:
                    assert_never(never)
        return None

    # Routes that require permissions
    included_paths = {
        # All Accounts routes.
        "/api/v1/accounts",
        # All Contacts routes.
        "/api/v1/contacts",
        # All Domains, Email Accounts, Email Account Pools, Signature routes.
        "/api/v1/emails/accounts",
        "/api/v1/emails/outbound/domains",
        "/api/v1/emails/pools",
        "/api/v2/emails/accounts",
        "/api/v1/users/signatures",
        # All Giant Tasks routes.
        "/api/v1/giant_tasks",
        # imports
        "/api/v1/imports/csv/job_reviews",
        # Prospecting routes.
        "/api/v1/prospecting/company",
        "/api/v1/prospecting/filter_fields",
        "/api/v1/prospecting/filter_field_options",
        "/api/v1/prospecting/people",
        "/api/v1/prospecting/queries",
        "/api/v1/prospecting/run",
        # All Sequence routes.
        "/api/v1/sequences",
        "/api/v1/sequence_actions",
        "/api/v1/sequence_enrollments",
        "/api/v1/sequence_steps",
        # All Tasks routes.
        "/api/v1/tasks",
        # All Pipeline routes.
        "/api/v1/pipelines",
    }

    excluded_paths = {
        # GET paths
        "/api/echo/test",
        "/api/v1/activities/calendars",
        "/api/v1/activities/calendars/schedule/scan_calendar_event",
        "/api/v1/activities/calendars/{calendar_id}",
        "/api/v1/activities/{activity_id}",
        "/api/v1/analytics/dashboards",
        "/api/v1/api_key/{scope}",
        "/api/v1/approval_request/{approval_request_id}",
        "/api/v1/audience/list/{audience_list_id}",
        "/api/v1/auth/admin/me",
        "/api/v1/auth/callback",
        "/api/v1/auth/echo/deal",
        "/api/v1/business_process/sales_methodologies_templates/_list",
        "/api/v1/business_process/organization_sales_methodology",
        "/api/v1/voice/call",  # temp disabled
        "/api/v1/voice/call/{call_id}/disposition",
        "/api/v1/auth/echo_header",
        "/api/v1/auth/me",
        "/api/v1/auth/signon",
        "/api/v1/auth/signout",
        "/api/v1/citations/_list_by_ids",
        "/api/v1/chat",
        "/api/v1/chat/{chat_id}",
        "/api/v1/chat/{chat_id}/messages/_list",
        "/api/v1/cloud_files/{attachment_id}",
        "/api/v1/connect/callback/google",
        "/api/v1/connect/callback/hubspot",
        "/api/v1/connect/callback/microsoft",
        "/api/v1/connect/callback/recall/zoom",
        "/api/v1/connect/callback/zoom",
        "/api/v1/connect/google",
        "/api/v1/connect/hubspot",
        "/api/v1/connect/integration",
        "/api/v1/connect/microsoft",
        "/api/v1/connect/organization/integration",
        "/api/v1/connect/organization/list_integration",
        "/api/v1/connect/sync_task",
        "/api/v1/connect/test_only/auth/google",
        "/api/v1/connect/test_only/auth/microsoft",
        "/api/v1/connect/test_only/home",
        "/api/v1/connect/test_only/login/google",
        "/api/v1/connect/test_only/login/microsoft",
        "/api/v1/connect/test_only/logout/google",
        "/api/v1/connect/test_only/logout/microsoft",
        "/api/v1/connect/test_only/zoom/create_meeting",
        "/api/v1/connect/test_only/zoom/home",
        "/api/v1/connect/test_only/zoom/login",
        "/api/v1/connect/test_only/zoom/logout",
        "/api/v1/connect/test_only/zoom/update_meeting",
        "/api/v1/connect/zoom",
        "/api/v1/crm_sync/sync_instance",
        "/api/v1/crm_sync/records",
        "/api/v1/custom_object/data/{custom_object_id}",
        "/api/v1/custom_object/data/{custom_object_id}/{custom_object_data_id}",
        "/api/v1/emails/insights/{insight_id}",
        "/api/v1/emails/unsubscription_groups/{unsubscription_group_id}",
        "/api/v1/event_schedule/booking/{event_schedule_booking_id}",
        "/api/v1/event_schedule/{event_schedule_id}",
        "/api/v1/event_schedule/variables/_list",
        "/api/v1/event_schedule/{user_org_identifier}/{event_title_slug}/id",
        "/api/v1/extraction_section/{extraction_section_id}",
        "/api/v1/forms/{form_id}",
        "/api/v1/goals/templates",
        "/api/v1/goals/{user_goal_id}",
        "/api/v1/imports/csv/jobs/{job_id}",
        "/api/v1/imports/csv/jobs/{job_id}/details",
        "/api/v1/imports/csv/jobs/{job_id}/summary",
        "/api/v1/imports/csv/samples",
        "/api/v1/jobs/filter",
        "/api/v1/imports/csv/jobs",
        "/api/v1/jobs/{job_id}",
        "/api/v1/llm/stream",
        "/api/v1/llm_config/config/extraction/templates",
        "/api/v1/llm_config/config/extraction/{extraction_config_section_id}",
        "/api/v1/meetings/external/{external_event_id}",
        "/api/v1/meetings/shares/{share_id}",
        "/api/v1/meetings/shares/{share_id}/meeting_info",
        "/api/v1/meetings/shares/{share_id}/meeting_info/preview",
        "/api/v1/meetings/{meeting_id}/clips/{clip_id}",
        "/api/v1/meetings/{meeting_id}/research",
        "/api/v1/meetings/{meeting_id}/stats",
        "/api/v1/meetings/{meeting_id}/transcripts",
        "/api/v1/metadata/custom/standard_object/enablement",
        "/api/v1/metadata/field_type_description",
        "/api/v1/metadata/organization_schema",
        "/api/v1/metadata/tenant_schema",
        "/api/v1/monitoring/health",
        "/api/v1/notes",
        "/api/v1/stage_criteria/list_value/{stage_list_value_id}",
        "/api/v1/notes/{note_id}",
        "/api/v1/onboarding/state/{onboarding_type}/org/{of_organization_id}",
        "/api/v1/onboarding/state/{onboarding_type}/org/{of_organization_id}/user/{of_user_id}",
        "/api/v1/organization/{organization_id}",
        "/api/v1/organization/organization_preference/{key}",
        "/api/v1/permission/permission_claims/user/{target_user_id}",
        "/api/v1/permission/permission_set/{permission_set_id}",
        "/api/v1/permission/permission_set_group/{permission_set_group_id}",
        "/api/v1/pipeline_stage_select_lists/{pipeline_stage_select_list_id}",
        "/api/v1/pipeline_intel/{pipeline_id}",
        "/api/v1/prompts/context/{context_id}",
        "/api/v1/prompts/prompt/generate_email_template_customized_opener/theme",
        "/api/v1/prompts/prompt/{prompt_id}",
        "/api/v1/prompts/template/{template_id}",
        "/api/v1/propagation_rule/{rule_id}",
        "/api/v1/prospecting/credits",
        "/api/v1/prospecting/credits/estimate",
        "/api/v1/quotas/policies/{policy_id}",
        "/api/v1/quotas/usages/summary",
        "/api/v1/quotas/check_quota",
        "/api/v1/select_lists/{select_list_id}",
        "/api/v1/users",
        "/api/v1/users/v2",
        "/api/v1/users/{target_user_id}",
        "/api/v1/users/user_preference/{key}",
        "/api/v1/user_invite/verify",
        "/api/v1/user_invite/done",
        "/api/v1/user_invite/is_invited/{email}",
        "/api/v1/variables/_list",
        "/api/v1/view_management/object_list_view_schema/{view_id}",
        "/api/v1/view_management/org_view_schema_preference_config",
        "/api/v1/view_management/user_view_schema_preference_config",
        "/api/v1/voice/admin",
        "/api/v1/voice/admin/provider-accounts",
        "/api/v1/voice/admin/provider-accounts/{provider_account_id}",
        "/api/v1/voice/admin/verified_phone_numbers/{phone_number}",
        "/api/v1/voice/phone-numbers",
        "/api/v1/voice/usage",
        "/api/v1/voice/voicemail/upload",
        "/api/v1/webhook/nylas/v3",
        "/api/v1/webhook/nylas/v3/calendar",
        "/api/v1/webhook/nylas/v3/grant",
        "/api/v1/webhook/recall_zoom",
        "/api/v1/webhook/account_and_contact_creation",
        "/api/v1/workflow_nodes/{node_id}",
        "/api/v1/workflow_runs/{run_id}",
        "/api/v1/workflow_snapshots/{snapshot_id}",
        "/api/v1/workflows/{workflow_id}",
        "/api/v1/crm_integrity/{job_id}",
        "/api/v1/trackers/{tracker_id}",
        "/api/v1/metadata/association_records",
        "/api/v1/sequence_step_variants/{variant_id}",
        "/api/v1/sequence_step_variants/{variant_id}/_clone",
        "/api/v1/sequence_step_variants/{variant_id}/_activate",
        "/api/v1/sequence_step_variants/{variant_id}/_deactivate",
        "/api/v1/stage_criteria_v2/stage_exit_criteria/{stage_value_id}",
        "/api/v1/stage_criteria_v2/stage_list_entrance_criteria/{stage_list_id}",
        "/e/{encrypted_str}",
        # POST paths
        "/api/echo",
        "/api/v1/stage_criteria/list",
        "/api/echo/openai",
        "/api/v1/activities/_list",
        "/api/v1/activities/_list_records",
        "/api/v1/activities/calendars/_get_event_by_meeting",
        "/api/v1/activities/calendars/_list_events",
        "/api/v1/activities/calendars/events",
        "/api/v1/activities/calendars/events/{calendar_event_id}/_rsvp",
        "/api/v1/activities/calendars/events/test/create_sync_calendar_schedule",
        "/api/v1/analytics/guest_token",
        "/api/v1/approval_request/_list",
        "/api/v1/approval_request/{approval_request_id}/_approve",
        "/api/v1/approval_request/{approval_request_id}/_deny",
        "/api/v1/audience/list",
        "/api/v1/audience/list/_list",
        "/api/v1/audience/list/{audience_list_id}/_clone",
        "/api/v1/audience/list_membership/_add",
        "/api/v1/audience/list_membership/_list",
        "/api/v1/audience/list_membership/_remove",
        "/api/v1/auth/refresh",
        "/api/v1/auth/token",
        "/api/v1/chat/_list",
        "/api/v1/cloud_files/_upload",
        "/api/v1/cloud_files/multipart_uploads/_complete",
        "/api/v1/cloud_files/multipart_uploads",
        "/api/v1/cloud_files/presigned_urls",
        "/api/v1/comments",
        "/api/v1/comments/_list",
        "/api/v1/conversations/_list",
        "/api/v1/crm_sync/update_instance",
        "/api/v1/custom_object/data/_query_indexed_v2",
        "/api/v1/custom_object/data_v2/{custom_object_id}",
        "/api/v1/custom_object/data/{custom_object_id}/_list_records/",
        "/api/v1/domain_object_lists",
        "/api/v1/domain_object_lists/_list",
        "/api/v1/domain_object_lists/{list_id}/_add_and_remove_items",
        "/api/v1/domain_object_lists/{list_id}/items/_list",
        "/api/v1/emails/insights",
        "/api/v1/emails/insights/_generate",
        "/api/v1/emails/insights/_list",
        "/api/v1/emails/messages",
        "/api/v1/emails/templates",
        "/api/v1/emails/templates/_list",
        "/api/v1/emails/templates/_preview",
        "/api/v1/emails/templates/{template_id}/_preview",
        "/api/v1/emails/templates/_list_history",
        "/api/v1/emails/unsubscription_groups",
        "/api/v1/emails/unsubscription_groups/_list",
        "/api/v1/emails/unsubscription_groups/_unsubscribe",
        "/api/v1/emails/global/threads/_list",
        "/api/v1/emails/global/threads/_list_for_sales_action_tagging",
        "/api/v1/emails/global/messages/{global_message_id}",
        "/api/v1/emails/global/messages/{global_message_id}/cancel",
        "/api/v1/emails/global/messages/{global_message_id}/_temp_ai_rec",
        "/api/v1/emails/global/messages/{global_message_id}/_by_source",
        "/api/v1/emails/global/threads/{global_thread_id}",
        "/api/v1/event_schedule",
        "/api/v1/event_schedule/_list",
        "/api/v1/event_schedule/_list_v2",
        "/api/v1/event_schedule/_list_shared_schedules",
        "/api/v1/event_schedule/{event_schedule_id}/availability",
        "/api/v1/event_schedule/{event_schedule_id}/book",
        "/api/v1/event_schedule/{user_org_identifier}/{event_title_slug}/availability",
        "/api/v1/extraction_section",
        "/api/v1/extraction_section/_list",
        "/api/v1/extraction_section/{extraction_section_id}/_remove",
        "/api/v1/extraction_section/{extraction_section_id}/field",
        "/api/v1/extraction_section/{extraction_section_id}/field/{extraction_field_id}/_remove",
        "/api/v1/user_feedback",
        "/api/v1/forms/_list",
        "/api/v1/goals",
        "/api/v1/goals/_list",
        "/api/v1/imports/csv/jobs/{job_id}/signal",
        "/api/v1/imports/",
        "/api/v1/jobs",
        "/api/v1/jobs/_list",
        "/api/v1/llm_config/config/extraction",
        "/api/v1/llm_config/config/extraction/_list",
        "/api/v1/llm_config/config/extraction/_onboard",
        "/api/v1/llm_config/config/extraction/{extraction_config_section_id}/_remove",
        "/api/v1/llm_config/config/extraction/{extraction_config_section_id}/field",
        "/api/v1/llm_config/config/extraction/{extraction_config_section_id}/field/{extraction_field_id}/_remove",
        "/api/v1/meetings/_list",
        "/api/v1/meetings/_list_history",
        "/api/v1/meetings/bots/_clear_future",
        "/api/v1/meetings/bots",
        "/api/v1/meetings/bots/_import",
        "/api/v1/meetings/bots/{meeting_bot_id}/sync_external_settings",
        "/api/v1/meetings/consent",
        "/api/v1/meetings/insights/_list",
        "/api/v1/meetings/insights/fields",
        "/api/v1/meetings/insights/{insight_id}/_re_rank",
        "/api/v1/meetings/live_recordings",
        "/api/v1/meetings/live_transcript_sessions",
        "/api/v1/meetings/meeting_annotations",
        "/api/v1/meetings/meeting_annotations/_list",
        "/api/v1/meetings/shares",
        "/api/v1/meetings/shares/_list",
        "/api/v1/meetings/{meeting_id}/_temp_ai_rec",
        "/api/v1/meetings/{meeting_id}/_by_source",
        "/api/v1/meetings/{meeting_id}/_analyze",
        "/api/v1/meetings/{meeting_id}/_end",
        "/api/v1/meetings/{meeting_id}/chat",
        "/api/v1/meetings/{meeting_id}/chat/messages/_list",
        "/api/v1/meetings/{meeting_id}/clips",
        "/api/v1/meetings/{meeting_id}/clips/_list",
        "/api/v1/meetings/{meeting_id}/transcripts/_process",
        "/api/v1/metadata/association",
        "/api/v1/metadata/custom/object",
        "/api/v1/notes/_list",
        "/api/v1/object_metadata/ai_creation_recs/_reject",
        "/api/v1/object_metadata/ai_creation_recs/_list",
        "/api/v1/object_metadata/ai_recs/_list_active_by_source",
        "/api/v1/organization/_list",
        "/api/v1/organization/organization_preference",
        "/api/v1/organization/backfill_permissions_for_all",
        "/api/v1/organization/{organization_id}/backfill_permissions",
        "/api/v1/organization/{organization_id}/patch_public_domain_accounts",
        "/api/v1/permission/permission_set",
        "/api/v1/permission/permission_set/user/_assign",
        "/api/v1/permission/permission_set_group",
        "/api/v1/permission/permission_set_group/permission_set/_assign",
        "/api/v1/permission/permission_set_group/user/_assign",
        "/api/v1/pipeline_stage_select_lists",
        "/api/v1/pipeline_stage_select_lists/_list_summary",
        "/api/v1/pipeline_stage_select_lists/{pipeline_stage_select_list_id}/_activate",
        "/api/v1/pipeline_stage_select_lists/{pipeline_stage_select_list_id}/_deactivate",
        "/api/v1/pipeline_stage_select_lists/{pipeline_stage_select_list_id}/values/{pipeline_stage_select_list_value_id}/_activate",
        "/api/v1/pipeline_stage_select_lists/{pipeline_stage_select_list_id}/values/{pipeline_stage_select_list_value_id}/_deactivate",
        "/api/v1/pipeline_stage_select_lists/{pipeline_stage_select_list_id}/values/{pipeline_stage_select_list_value_id}/_remove_and_remap",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition/items",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition/items/{item_id}",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria/items",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria/items/{item_id}",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process/items",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process/items/{item_id}",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/identified_pain",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/identified_pain/items",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/identified_pain/items/{item_id}",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric/items",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric/items/{item_id}",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/paper_process",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/paper_process/items",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/paper_process/items/{item_id}",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition/_by_source",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition/_temp_ai_rec",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition/items/{item_id}/_by_source",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition/items/{item_id}/_temp_ai_rec",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition/items/_temp_ai_object_rec",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/competition/items/_by_source",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria/_by_source",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria/_temp_ai_rec",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria/items/{item_id}/_by_source",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria/items/{item_id}/_temp_ai_rec",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria/items/_temp_ai_object_rec",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_criteria/items/_by_source",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process/_by_source",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process/_temp_ai_rec",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process/items/{item_id}/_by_source",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process/items/{item_id}/_temp_ai_rec",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process/items/_temp_ai_object_rec",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/decision_process/items/_by_source",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/identified_pain/items/{item_id}/_by_source",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/identified_pain/items/{item_id}/_temp_ai_rec",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/identified_pain/items/_temp_ai_object_rec",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/identified_pain/items/_by_source",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric/_by_source",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric/_temp_ai_rec",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric/items/{item_id}/_by_source",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric/items/{item_id}/_temp_ai_rec",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric/items/_temp_ai_object_rec",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/metric/items/_by_source",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/paper_process/items/{item_id}/_by_source",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/paper_process/items/{item_id}/_temp_ai_rec",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/paper_process/items/_temp_ai_object_rec",
        "/api/v1/pipeline_qualification_properties/{pipeline_id}/paper_process/items/_by_source",
        "/api/v1/prompts/context",
        "/api/v1/prompts/context/_list",
        "/api/v1/prompts/prompt/_list",
        "/api/v1/prompts/prompt/evaluate_email_template_content",
        "/api/v1/prompts/prompt/generate_email_template",
        "/api/v1/prompts/prompt/generate_email_template_customized_opener",
        "/api/v1/prompts/prompt/rephrase_email_template_body",
        "/api/v1/prompts/prompt/rephrase_email_template_subject",
        "/api/v1/prompts/template",
        "/api/v1/prompts/template/_list",
        "/api/v1/propagation_rule",
        "/api/v1/propagation_rule/_list",
        "/api/v1/prospecting/credits/usage/_list",
        "/api/v1/quotas/policies/_list",
        "/api/v1/quotas/policies/{policy_id}/_reset",
        "/api/v1/search/_search",
        "/api/v1/select_lists",
        "/api/v1/select_lists/_list_details",
        "/api/v1/select_lists/{select_list_id}/_activate",
        "/api/v1/select_lists/{select_list_id}/_deactivate",
        "/api/v1/select_lists/{select_list_id}/list_value/{select_list_value_id}/_activate",
        "/api/v1/select_lists/{select_list_id}/list_value/{select_list_value_id}/_deactivate",
        "/api/v1/select_lists/{select_list_id}/list_value/{select_list_value_id}/_remove_and_remap",
        "/api/v1/stage_criteria_v2/_list_stage_criteria",
        "/api/v1/stage_criteria_v2/_list_stage_criteria_item_template",
        "/api/v1/sse/chat",
        "/api/v1/users/_invite",
        "/api/v1/users/_list_legacy",
        "/api/v1/users/_list",
        "/api/v1/users/user_preference",
        "/api/v1/users/_reinvite",
        "/api/v1/users/organization_user/_deactivate",
        "/api/v1/users/platform_credentials",
        "/api/v1/users/platform_credentials/_list",
        "/api/v1/users/test/authed_echo",
        "/api/v1/user_invite",
        "/api/v1/user_invite/all",
        "/api/v1/view_management/object_list_view_schema",
        "/api/v1/view_management/object_list_view_schema/_list",
        "/api/v1/view_management/object_list_view_schema/{view_id}/_clone",
        "/api/v1/view_management/object_list_view_schema/{view_id}/_promote",
        "/api/v1/voice/admin/phone_numbers",
        "/api/v1/voice/admin/phone_numbers/_list",
        "/api/v1/voice/admin/process_transcript/{call_sid}",
        "/api/v1/voice/admin/start_transcription/{call_sid}",
        "/api/v1/voice/admin/verified_phone_numbers",
        "/api/v1/voice/phone-numbers/purchase",
        "/api/v1/voice/phone-numbers/verify",
        "/api/v1/voice/available-numbers/search",
        "/api/v1/voice/twilio_token",
        "/api/v1/voice/twilio_update_recording_status/{call_sid}",
        "/api/v1/voice/token",
        "/api/v1/voice/call/initiate",
        "/api/v1/voice/call/recording/update",
        "/api/v1/webhook/assembly_ai_transcript",
        "/api/v1/webhook/brightdata/scraper/notify",
        "/api/v1/webhook/recallai/bot/event",
        "/api/v1/webhook/twilio/amd",
        "/api/v1/webhook/twilio/call-status",
        "/api/v1/webhook/twilio/log",
        "/api/v1/webhook/twilio/recording-status",
        "/api/v1/webhook/twilio/voice",
        "/api/v1/webhook/twilio/call-redirect",
        "/api/v1/webhook/twilio/onboarding-verification-callback",
        "/api/v1/webhook/zoom/deauth",
        "/api/v1/workflow_blocks",
        "/api/v1/workflow_blocks/_list",
        "/api/v1/workflow_edges",
        "/api/v1/workflow_edges/_list",
        "/api/v1/workflow_nodes",
        "/api/v1/workflow_nodes/_list",
        "/api/v1/workflow_run_nodes/_list",
        "/api/v1/workflow_runs/_list",
        "/api/v1/workflow_snapshots/_list",
        "/api/v1/workflow_snapshots/_list_templates",
        "/api/v1/workflow_snapshots/{snapshot_id}/_create_workflow",
        "/api/v1/workflow_snapshots/{snapshot_id}/_manually_run",
        "/api/v1/workflow_snapshots/{snapshot_id}/_publish",
        "/api/v1/workflow_snapshots/{snapshot_id}/_validate",
        "/api/v1/workflows",
        "/api/v1/workflows/_list",
        "/api/v1/workflows/test/produce_resource_change_trigger_event",
        "/api/v1/workflows/test/produce_trigger_event/{form_id}",
        "/api/v1/workflows/{workflow_id}/webhook",
        "/api/v1/crm_integrity",
        "/api/v1/crm_integrity/_preview",
        "/api/v1/crm_integrity/{job_id}/_retry",
        "/api/v1/crm_integrity/_list",
        "/api/v1/crm_integrity/user_options/_list",
        "/api/v1/crm_integrity/{job_id}/_start",
        "/api/v1/imports/import_meeting",
        "/api/v1/imports/jobs",
        "/api/v1/trackers",
        "/api/v1/trackers/_list",
        "/api/v1/meetings/{meeting_id}/tracker_stats/_list",
        "/api/v1/meetings/{meeting_id}/tracker_details/_list",
        "/api/v1/meetings/{meeting_id}/refresh_info",
        "/api/v1/superadmin/organizations/login",
        "/api/v1/superadmin/organizations/_list",
        "/api/v1/superadmin/user_invite",
        "/api/v1/superadmin/user_invite/_list",
        "/api/v1/ai/email/reply",
        "/api/v1/ai/email/rephrase",
        "/api/v1/ai/email/compose",
        "/api/v1/ai/tracker/generate_phrases",
        "/api/v1/ai/meeting/generate",
        "/api/v1/ai/meeting/chat",
        "/api/v1/ai/meeting/chat/messages/_list",
        "/api/v1/emails/templates/categories",
        "/api/v1/metadata/custom/object/{custom_object_id}/field_v2",
        "/api/v1/emails/templates/categories/_list",
        "/api/v1/metadata/association/get",
        "/api/v1/metadata/association/object/get",
        "/api/v1/metadata/association/delete",
        "/api/v1/sequence_step_variants",
        "/api/v1/reporting/engine/_preview",
        "/api/v1/reporting/engine/_query",
        # PATCH paths
        "/api/v1/activities/calendars/events/{calendar_event_id}",
        "/api/v1/comments/{comment_id}",
        "/api/v1/custom_object/data_v2/{custom_object_id}/{custom_object_data_id}",
        "/api/v1/domain_object_lists/{list_id}",
        "/api/v1/emails/templates/{template_id}",
        "/api/v1/emails/templates/categories/{category_id}",
        "/api/v1/extraction_section/{extraction_section_id}/field/{extraction_field_id}",
        "/api/v1/llm_config/config/extraction/{extraction_config_section_id}/field/{extraction_field_id}",
        "/api/v1/meetings/insights/{insight_id}",
        "/api/v1/meetings/meeting_annotations/{meeting_annotation_id}",
        "/api/v1/meetings/{meeting_id}",
        "/api/v1/metadata/custom/object/{custom_object_id}",
        "/api/v1/metadata/custom/object/{custom_object_id}/field_v2/{custom_field_id}",
        "/api/v1/users/{target_user_id}/v2",
        "/api/v1/workflow_blocks/{block_id}",
        "/api/v1/workflow_edges/{edge_id}",
        # DELETE paths
        "/api/v1/ai/meeting/{meeting_id}/chat",
        "/api/v1/connect/google/integration",
        "/api/v1/connect/integration/{user_integration_id}",
        "/api/v1/meetings/{meeting_id}/bots",
        "/api/v1/meetings/insight_sections/{insight_section_id}",
        "/api/v1/voice/admin/phone_numbers/{phone_number}",
        "/api/v1/stage_criteria_v2/stage_exit_criteria/{stage_exit_criteria_id}",
        "/api/v1/stage_criteria_v2/stage_list_entrance_criteria/{stage_list_entrance_criteria_id}",
        # PUT paths
        "/api/v1/metadata/associations_records",
        "/api/v1/organization/{organization_id}/reset_extraction_configs",
        "/api/v1/pipeline_stage_select_lists/{pipeline_stage_select_list_id}/values",
        "/api/v1/select_lists/{select_list_id}/value",
    }

    # Verify no included endpoint is a prefix of an excluded endpoint
    for included_path in included_paths:
        for excluded_path in excluded_paths:
            if excluded_path.startswith(included_path):
                raise AssertionError(
                    f"Included endpoint '{included_path}' is a prefix of excluded endpoint '{excluded_path}'. "
                    "This would cause ambiguity in endpoint matching. "
                    "Each endpoint must be either included or excluded, but not both."
                )

    uncategorized_paths = set()

    for route in fastapi_app.routes:
        if not isinstance(route, APIRoute):
            continue

        # Check if route path starts with any of our included paths
        if any(route.path.startswith(path) for path in included_paths):
            # Test included route
            if route.dependencies:
                perm_deps = []
                required_permissions = []

                for dep in route.dependencies:
                    perm = _get_required_permission(dep)
                    if perm:
                        required_permissions.append(perm)
                        perm_deps.append(unwrap_dependency(dep))

                if not required_permissions:
                    logger.warning(f"No permission found for dependency: {route.path}")
                    continue

                # Test with required permissions - should succeed.
                result = await _test_decorator(
                    dependencies=perm_deps, user_permissions=required_permissions
                )
                assert result is True  # Verify successful execution

                # Test with missing permissions - should fail.
                # require_read_placeholder_access() will not raise ForbiddenError (on purpose)since it does not check any claims.
                try:
                    await _test_decorator(dependencies=perm_deps, user_permissions=[])
                    # If you see this error, it means you did not add a proper permission check to the route.
                    raise AssertionError(
                        f"ForbiddenError expected for included path {route.path}, but was not raised. You likely used require_read_placeholder_access() which is not allowed for an included path."
                    )
                except ForbiddenError:
                    pass

                if "super_admin:*" in required_permissions:
                    # Test with super_admin permissions
                    result = await _test_decorator(
                        dependencies=perm_deps, user_permissions=["super_admin:*"]
                    )
                    assert result is True
                else:
                    # Test with admin permissions
                    result = await _test_decorator(
                        dependencies=perm_deps, user_permissions=["admin:*"]
                    )
                    assert result is True

        elif route.path not in excluded_paths:
            # Track uncategorized routes
            uncategorized_paths.add(route.path)

    # After checking all routes, raise error if any were uncategorized
    if uncategorized_paths:
        raise AssertionError(
            f"Found {len(uncategorized_paths)} routes that are neither included nor excluded:\n"
            f"{sorted(uncategorized_paths)}\n"
            "All routes must be explicitly categorized."
        )
