import uuid
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import UUID, uuid4

import pytest
from httpx import AsyncClient

from salestech_be.common.exception.exception import ForbiddenError
from salestech_be.common.schema_manager.std_object_relationship import (
    EmailAccountRelationship,
)
from salestech_be.core.common.accounts_receivable import AccountsReceivable
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.email.account.schema import (
    ArchiveEmailAccountRequest,
    CreateEmailAccountRequest,
    EmailAccountType,
    MockEmailAccountConfigParams,
    PatchEmailAccountRequestV2,
)
from salestech_be.core.email.account.service_v2 import EmailAccountServiceV2
from salestech_be.core.email.outbound_domain.schema import (
    EmailAccountArchiveSequenceHandling,
)
from salestech_be.core.quota.service.quota_policy_service import QuotaPolicyService
from salestech_be.core.quota.service.quota_service import QuotaService
from salestech_be.db.dao.email_account import (
    EmailAccountPoolRepository,
    EmailAccountRepository,
)
from salestech_be.db.dao.email_account_warm_up_campaign_repository import (
    EmailAccountWarmUpCampaignRepository,
)
from salestech_be.db.dao.outbound_repository import (
    OutboundDomainRepository,
    OutboundWorkspaceRepository,
)
from salestech_be.db.dao.sequence_enrollment_repo import SequenceEnrollmentRepository
from salestech_be.db.dao.sequence_repository import SequenceRepository
from salestech_be.db.dao.signature_repository import SignatureRepository
from salestech_be.db.models.email_account import (
    EmailAccount,
    EmailAccountPool,
    EmailAccountPoolMembership,
    EmailAccountUseOverride,
    EmailProvider,
)
from salestech_be.db.models.email_account_warm_up import (
    MailboxWarmUpService,
)
from salestech_be.db.models.outbound import (
    OutboundDomain,
    OutboundDomainStatus,
    OutboundVendor,
    OutboundWorkspace,
)
from salestech_be.db.models.quota import (
    QuotaConsumerEntityType,
    QuotaConsumingResource,
    QuotaPeriod,
)
from salestech_be.db.models.sequence import (
    SequenceEnrollment,
    SequenceEnrollmentStatus,
    SequenceStatus,
    SequenceV2,
    SequenceV2Schedule,
    SequenceVisibility,
)
from salestech_be.integrations.infraforge.type import OneTimeDomainPurchaseInvoice
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from tests.integration.web.api.util.common_api_client import CommonAPIClient


def create_email_account_pool(
    organization_id: UUID, owner_user_id: UUID
) -> EmailAccountPool:
    return EmailAccountPool(
        id=uuid4(),
        organization_id=organization_id,
        owner_user_id=owner_user_id,
        name="Test Email Account Pool",
        is_default=True,
        created_at=zoned_utc_now(),
        created_by_user_id=owner_user_id,
    )


async def create_outbound_workspace(
    outbound_workspace_repository: OutboundWorkspaceRepository,
    organization_id: UUID,
) -> OutboundWorkspace:
    return await outbound_workspace_repository.insert_outbound_workspace(
        name=f"Test Outbound Workspace for {organization_id}",
        organization_id=organization_id,
        vendor=OutboundVendor.INFRAFORGE,
        external_id=f"test_{uuid4()}",
    )


async def insert_outbound_domain(
    outbound_domain_repository: OutboundDomainRepository,
    organization_id: UUID,
    user_id: UUID,
    workspace_id: UUID,
) -> OutboundDomain:
    return not_none(
        await outbound_domain_repository.insert_outbound_domain(
            organization_id=organization_id,
            workspace_id=workspace_id,
            created_by_user_id=user_id,
            domain=f"test-{organization_id}.com",
            external_id=f"ext_id_{organization_id}",
            status=OutboundDomainStatus.ACTIVE,
            forward_to_domain=None,
            invoice=OneTimeDomainPurchaseInvoice(
                amountPaid=1400,
                creditsApplied=0,
                id=f"ext_id_{organization_id}",
                paidAt="2024-04-17T00:00:00Z",
                subTotal=1400,
                total=1400,
            ),
        )
    )


async def create_domain_purchase_quota_policy(
    quota_policy_service: QuotaPolicyService,
    organization_id: UUID,
    user_id: UUID,
) -> None:
    await quota_policy_service.upsert_quota_policy(
        organization_id=organization_id,
        entity_id=organization_id,
        entity_type=QuotaConsumerEntityType.ORGANIZATION,
        resource=QuotaConsumingResource.DOMAIN,
        quota_limit=1,
        period=QuotaPeriod.ANNUAL,
        user_id=user_id,
        applied_sub_entity_types=None,
    )


async def create_email_account(
    email_account_repository: EmailAccountRepository,
    organization_id: UUID,
    user_id: UUID,
    domain: OutboundDomain,
) -> EmailAccount:
    return await email_account_repository.create_email_account(
        EmailAccount(
            id=uuid4(),
            organization_id=organization_id,
            owner_user_id=user_id,
            email=f"test{organization_id}@test{organization_id}.com",
            type=EmailAccountType.REGULAR,
            outbound_domain_id=domain.id,
            active=True,
            external_id=f"ext_id_{organization_id}",
            vendor=EmailProvider.INFRAFORGE,
            first_name=f"Test-{organization_id}",
            last_name="User",
            is_default=False,
            seconds_delay_between_emails=600,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
        )
    )


async def create_mailbox_purchase_quota_policy(
    quota_policy_service: QuotaPolicyService,
    organization_id: UUID,
    user_id: UUID,
) -> None:
    await quota_policy_service.upsert_quota_policy(
        organization_id=organization_id,
        entity_id=organization_id,
        entity_type=QuotaConsumerEntityType.ORGANIZATION,
        resource=QuotaConsumingResource.MAILBOX,
        applied_sub_entity_types=None,
        quota_limit=5,
        period=QuotaPeriod.ANNUAL,
        user_id=user_id,
    )


async def create_user_mailbox_quota_policy(
    quota_policy_service: QuotaPolicyService,
    organization_id: UUID,
    user_id: UUID,
    quota_limit: int = 3,
) -> None:
    await quota_policy_service.upsert_quota_policy(
        organization_id=organization_id,
        entity_id=user_id,
        entity_type=QuotaConsumerEntityType.USER,
        resource=QuotaConsumingResource.MAILBOX,
        applied_sub_entity_types=None,
        quota_limit=quota_limit,
        period=QuotaPeriod.ANNUAL,
        user_id=user_id,
    )


async def create_outbound_email_send_quota_policy(
    quota_policy_service: QuotaPolicyService,
    organization_id: UUID,
    user_id: UUID,
    email_account: EmailAccount,
) -> None:
    await quota_policy_service.upsert_quota_policy(
        organization_id=organization_id,
        entity_id=email_account.id,
        entity_type=QuotaConsumerEntityType.EMAIL_ACCOUNT,
        resource=QuotaConsumingResource.EMAIL,
        applied_sub_entity_types=None,
        quota_limit=5,
        period=QuotaPeriod.DAILY,
        user_id=user_id,
    )


async def test_list_email_account_v2_use_override(
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    outbound_workspace_repository: OutboundWorkspaceRepository,
    outbound_domain_repository: OutboundDomainRepository,
    quota_policy_service: QuotaPolicyService,
    email_account_repository: EmailAccountRepository,
) -> None:
    admin_api_client = multi_api_client["admin"]
    organization_id = admin_api_client.default_organization_id
    user_id = admin_api_client.default_user_id

    # For Infraforge, you need to create a workspace first.
    outbound_workspace = await create_outbound_workspace(
        outbound_workspace_repository=outbound_workspace_repository,
        organization_id=organization_id,
    )

    # Once the workspace is created, you can create a domain.
    domain = await insert_outbound_domain(
        outbound_domain_repository=outbound_domain_repository,
        organization_id=organization_id,
        workspace_id=outbound_workspace.id,
        user_id=user_id,
    )

    # We need an org-level quota policy for number of domain purchases allowed.
    # This is NOT currently required for the _list endpoint, but can be used to test other scenarios.
    await create_domain_purchase_quota_policy(
        quota_policy_service=quota_policy_service,
        organization_id=organization_id,
        user_id=user_id,
    )

    # Once the domain is created, we can create an email account.
    email_account = await create_email_account(
        email_account_repository=email_account_repository,
        organization_id=organization_id,
        user_id=user_id,
        domain=domain,
    )

    # We need an org-level quota policy for number of mailbox purchases allowed.
    # This is NOT currently required for the _list endpoint, but can be used to test other scenarios.
    await create_mailbox_purchase_quota_policy(
        quota_policy_service=quota_policy_service,
        organization_id=organization_id,
        user_id=user_id,
    )

    # This is REQUIRED for the _list endpoint.
    # We need an email-account-level quota policy for number of outbound (cold-outreach) emails allowed per day.
    await create_outbound_email_send_quota_policy(
        quota_policy_service=quota_policy_service,
        organization_id=organization_id,
        user_id=user_id,
        email_account=email_account,
    )

    # -----------------------------------------------------------------------
    # Test that the email account is returned in the _list request, with the use_override attribute.
    response = await admin_api_client.api_test_client.client.post(
        "/api/v2/emails/accounts/_list",
        json={},
    )
    assert response.status_code == 200

    # We are expecting (the default behavior of) use_override to be None on creation.
    response_data = response.json()
    assert len(response_data["list_data"]) == 1
    email_account_data = response_data["list_data"][0]["data"]
    assert email_account_data["email"] == email_account.email
    assert email_account_data["use_override"] is None

    # -----------------------------------------------------------------------
    # Update the email account with a use_override value.
    update_request = PatchEmailAccountRequestV2(
        use_override=EmailAccountUseOverride.USE_DESPITE_WARMUP_STATUS,
    )
    update_response = await admin_api_client.api_test_client.client.patch(
        f"/api/v2/emails/accounts/{email_account.id}",
        json=update_request.model_dump(mode="json"),
    )
    assert update_response.status_code == 200

    # -----------------------------------------------------------------------
    # Check that _list now returns the use_override value.
    response = await admin_api_client.api_test_client.client.post(
        "/api/v2/emails/accounts/_list",
        json={},
    )
    assert response.status_code == 200
    response_data = response.json()
    email_account_data = response_data["list_data"][0]["data"]
    assert email_account_data["email"] == email_account.email
    assert (
        email_account_data["use_override"]
        == EmailAccountUseOverride.USE_DESPITE_WARMUP_STATUS
    )

    # -----------------------------------------------------------------------
    # Update the email account to remove the use_override value.
    update_request = PatchEmailAccountRequestV2(
        use_override=None,
    )
    update_response = await admin_api_client.api_test_client.client.patch(
        f"/api/v2/emails/accounts/{email_account.id}",
        json=update_request.model_dump(mode="json"),
    )
    assert update_response.status_code == 200

    # -----------------------------------------------------------------------
    # Check that _list endpoint returns the use_override value as None.
    response = await admin_api_client.api_test_client.client.post(
        "/api/v2/emails/accounts/_list",
        json={},
    )
    assert response.status_code == 200
    response_data = response.json()
    email_account_data = response_data["list_data"][0]["data"]
    assert email_account_data["email"] == email_account.email
    assert email_account_data["use_override"] is None


# tests list and get email account v2
async def test_list_email_account_v2(
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    email_account_repository: EmailAccountRepository,
    email_account_pool_repository: EmailAccountPoolRepository,
    sequence_repository: SequenceRepository,
    sequence_enrollment_repository: SequenceEnrollmentRepository,
    outbound_workspace_repository: OutboundWorkspaceRepository,
    quota_policy_service: QuotaPolicyService,
    quota_service: QuotaService,
    outbound_domain_repository: OutboundDomainRepository,
) -> None:
    admin_api_client = multi_api_client["admin"]
    organization_id = admin_api_client.default_organization_id
    user_id = admin_api_client.default_user_id

    # Create a mock workspace
    await create_outbound_workspace(
        outbound_workspace_repository=outbound_workspace_repository,
        organization_id=organization_id,
    )

    # Create a mailbox purchase quota policy
    await create_mailbox_purchase_quota_policy(
        quota_policy_service=quota_policy_service,
        organization_id=organization_id,
        user_id=user_id,
    )

    # Create a user mailbox quota policy
    await create_user_mailbox_quota_policy(
        quota_policy_service=quota_policy_service,
        organization_id=organization_id,
        user_id=user_id,
        quota_limit=5,
    )

    # Create an email account pool
    create_email_account_pool_req = create_email_account_pool(
        organization_id=organization_id,
        owner_user_id=user_id,
    )
    email_account_pool = await email_account_pool_repository.insert(
        create_email_account_pool_req
    )

    # Create a sequence
    my_sequence_id = uuid4()
    sequence = SequenceV2(
        id=my_sequence_id,
        organization_id=organization_id,
        owner_user_id=user_id,
        status=SequenceStatus.ACTIVE,
        name="Test Sequence",
        visibility=SequenceVisibility.TEAM_EDITABLE,
        schedule=SequenceV2Schedule(
            timezone="America/New_York",
            skip_holidays=False,
            schedule_times=[],
        ),
        created_at=zoned_utc_now(),
        created_by_user_id=user_id,
    )
    sequence = await sequence_repository.insert(sequence)

    # First, create a mock domain using the buy domains endpoint
    email_domain = str(uuid.uuid4()) + ".com"
    buy_domain_request = {
        "domains": [email_domain],
        "forward_to_domain": None,
        "is_mock_record": True,
    }

    # Call the buy domains endpoint
    buy_domains_response = await admin_api_client.api_test_client.client.post(
        "/api/v1/emails/outbound/domains/_buy",
        json=buy_domain_request,
    )

    # Verify the domain was created
    assert buy_domains_response.status_code == 200
    domains_data = buy_domains_response.json()
    assert len(domains_data) > 0
    domain_id = UUID(domains_data[0]["id"])

    # We are now pending on purchase until domain health is properly configured which won't happen in this test because it's in the workflow which doesn't properly run in tests, force active for testing
    await outbound_domain_repository.update_outbound_domain(
        domain_id, {"status": OutboundDomainStatus.ACTIVE}
    )

    # Create test email accounts using the API endpoint
    test_accounts = []

    for i in range(3):
        # Create the email account request with the mock domain
        create_request = CreateEmailAccountRequest(
            owner_user_id=user_id,
            email=f"test{i}@{email_domain}",
            first_name=f"Test{i}",
            last_name="User",
            reply_to_email=f"reply{i}@{email_domain}",
            warmup_limit=10,
            daily_quota=40,
            email_account_pool_ids=[email_account_pool.id],
            is_in_default_pool=False,
            mock_config_params=MockEmailAccountConfigParams(
                mock_purchase=True
            ),  # No need to specify domain name
            outbound_domain_id=domain_id,  # Use the created domain
        )

        # Call the creation endpoint
        create_response = await admin_api_client.api_test_client.client.post(
            "/api/v2/emails/accounts",
            json=create_request.model_dump(mode="json"),
        )

        # Verify the creation response
        assert create_response.status_code == 200
        created_data = create_response.json()

        email_account_id = UUID(created_data["id"])

        # Get the created email account
        email_account = await email_account_repository.find_by_tenanted_primary_key(
            EmailAccount,
            organization_id=organization_id,
            id=email_account_id,
        )
        assert email_account is not None

        # Create sequence enrollment
        sequence_enrollment = SequenceEnrollment(
            id=uuid4(),
            organization_id=organization_id,
            email_account_id=email_account.id,
            sequence_id=sequence.id,
            contact_id=uuid4(),
            status=SequenceEnrollmentStatus.ACTIVE,
            enrolled_at=zoned_utc_now(),
            enrolled_by_user_id=user_id,
            updated_at=zoned_utc_now(),
            updated_by_user_id=user_id,
        )
        await sequence_enrollment_repository.insert(sequence_enrollment)
        test_accounts.append(email_account)

    # Verify that the quota usage for the plan included mailbox for the organization was not increased by 1
    included_mailbox_quota_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )
    assert (
        included_mailbox_quota_summary.total_remaining == 2
    )  # This value is 2 because the mailbox is a mock record, so it's not included in the plan-included-cost-mailbox quota
    assert included_mailbox_quota_summary.total_used == 0
    assert included_mailbox_quota_summary.total_limit == 2

    # Verify that the quota usage for the mailbox for the user was increased by 1
    mailbox_quota_summary_per_user = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.MAILBOX,
        period=QuotaPeriod.ANNUAL,
        user_id=user_id,
    )
    assert mailbox_quota_summary_per_user.total_remaining == 2
    assert mailbox_quota_summary_per_user.total_used == 3
    assert mailbox_quota_summary_per_user.total_limit == 5

    mailbox_quota_summary_per_organization = (
        await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )
    )
    assert mailbox_quota_summary_per_organization.total_remaining == 2
    assert mailbox_quota_summary_per_organization.total_used == 3
    assert mailbox_quota_summary_per_organization.total_limit == 5

    # Call the list endpoint
    response = await admin_api_client.api_test_client.client.post(
        "/api/v2/emails/accounts/_list",
        json={
            "filter": None,
            "sort": None,
            "page_size": 10,
            "page_number": 1,
            "ordered_object_fields": [
                {
                    "relationship_id": EmailAccountRelationship.email_account__to__sequence.value,
                    "field": {"path": ["id"]},
                }
            ],
            "user_id": str(user_id),
        },
    )

    # Verify response
    assert response.status_code == 200
    response_data = response.json()

    # Check records
    records = response_data["list_data"]
    assert len(records) >= 3  # At least our 3 test accounts

    # Verify our test accounts are in the response
    test_emails = [account.email for account in test_accounts]
    found_test_emails = []

    for record in records:
        email = record["data"]["email"]
        if email in test_emails:
            found_test_emails.append(email)

            # Verify the record structure and data
            assert "data" in record
            assert "id" in record["data"]
            assert record["data"]["email"] == email
            assert record["data"]["owner_user_id"] == str(user_id)
            assert record["data"]["created_by_user_id"] == str(user_id)
            assert record["data"]["organization_id"] == str(organization_id)
            assert record["data"]["created_at"] is not None

            # Find the matching test account
            test_account = next(acc for acc in test_accounts if acc.email == email)
            assert record["data"]["first_name"] == test_account.first_name
            assert record["data"]["last_name"] == test_account.last_name

            assert (
                record["related_records"][
                    EmailAccountRelationship.email_account__to__sequence.value
                ]
                is not None
            )

            # Verify the sequence relationship
            sequence_record = record["related_records"][
                EmailAccountRelationship.email_account__to__sequence.value
            ][0]

            assert sequence_record is not None
            assert sequence_record["data"]["id"] == str(sequence.id)
            assert sequence_record["data"]["name"] == sequence.name
            assert sequence_record["data"]["status"] == sequence.status
            assert sequence_record["data"]["organization_id"] == str(organization_id)
            assert sequence_record["data"]["created_at"] is not None

    # Ensure all our test accounts were found
    assert sorted(found_test_emails) == sorted(test_emails)

    # Test filtering by record_id
    specific_account = test_accounts[0]
    filter_response = await admin_api_client.api_test_client.client.post(
        "/api/v2/emails/accounts/_list",
        json={
            "record_id": str(specific_account.id),
            "cursor": None,
            "filter_spec": None,
            "sorting_spec": None,
            "ordered_object_fields": None,
        },
    )

    assert filter_response.status_code == 200
    filter_data = filter_response.json()

    # Should only return one record
    assert len(filter_data["list_data"]) == 1
    assert filter_data["list_data"][0]["data"]["email"] == specific_account.email
    assert (
        filter_data["list_data"][0]["data"]["first_name"] == specific_account.first_name
    )
    assert filter_data["list_data"][0]["data"]["id"] == str(specific_account.id)

    get_response = await admin_api_client.api_test_client.client.get(
        f"/api/v2/emails/accounts/{specific_account.id}",
    )
    assert get_response.status_code == 200
    get_data = get_response.json()
    assert get_data["email"] == specific_account.email
    assert get_data["first_name"] == specific_account.first_name
    assert get_data["last_name"] == specific_account.last_name
    assert get_data["id"] == str(specific_account.id)
    assert get_data["organization_id"] == str(organization_id)
    assert get_data["created_at"] is not None
    assert get_data["created_by_user_id"] == str(user_id)


async def test_update_email_account_v2(
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    email_account_repository: EmailAccountRepository,
    email_account_pool_repository: EmailAccountPoolRepository,
    email_account_warm_up_campaign_repository: EmailAccountWarmUpCampaignRepository,
    quota_policy_service: QuotaPolicyService,
    quota_service: QuotaService,
    outbound_domain_repository: OutboundDomainRepository,
) -> None:
    admin_api_client = multi_api_client["admin"]
    user1_api_client = multi_api_client["user1"]
    organization_id = admin_api_client.default_organization_id
    admin_user_id = admin_api_client.default_user_id
    user1_id = user1_api_client.default_user_id
    # First, create a mock domain using the buy domains endpoint
    email_domain = str(uuid.uuid4()) + ".com"
    await create_user_mailbox_quota_policy(
        quota_policy_service=quota_policy_service,
        organization_id=organization_id,
        user_id=admin_user_id,
        quota_limit=1,
    )
    buy_domain_request = {
        "domains": [email_domain],
        "forward_to_domain": None,
        "is_mock_record": True,
    }

    # Call the buy domains endpoint
    buy_domains_response = await admin_api_client.api_test_client.client.post(
        "/api/v1/emails/outbound/domains/_buy",
        json=buy_domain_request,
    )

    # Verify the domain was created
    assert buy_domains_response.status_code == 200
    domains_data = buy_domains_response.json()
    assert len(domains_data) > 0
    domain_id = UUID(domains_data[0]["id"])

    # We are now pending on purchase until domain health is properly configured which won't happen in this test because it's in the workflow which doesn't properly run in tests, force active for testing
    await outbound_domain_repository.update_outbound_domain(
        domain_id, {"status": OutboundDomainStatus.ACTIVE}
    )

    # Create the email account request with the mock domain
    create_request = CreateEmailAccountRequest(
        owner_user_id=admin_user_id,
        email=f"updatetest@{email_domain}",
        first_name="Original",
        last_name="Name",
        reply_to_email=f"reply@{email_domain}",
        warmup_limit=10,
        daily_quota=40,
        seconds_delay_between_emails=300,  # Custom delay of 5 minutes
        email_account_pool_ids=None,
        is_in_default_pool=False,
        mock_config_params=MockEmailAccountConfigParams(
            mock_purchase=True
        ),  # No need to specify domain name
        outbound_domain_id=domain_id,  # Use the created domain
    )

    # Call the creation endpoint
    create_response = await admin_api_client.api_test_client.client.post(
        "/api/v2/emails/accounts",
        json=create_request.model_dump(mode="json"),
    )

    # Verify the creation response
    assert create_response.status_code == 200
    created_data = create_response.json()
    email_account_id = UUID(created_data["id"])

    # Verify that the quota usage for the plan included mailbox for the organization was not increased by 1
    included_mailbox_quota_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )
    assert (
        included_mailbox_quota_summary.total_remaining == 2
    )  # This value is 2 because the mailbox is a mock record, so it's not included in the plan-included-cost-mailbox quota
    assert included_mailbox_quota_summary.total_used == 0
    assert included_mailbox_quota_summary.total_limit == 2

    # Verify that the quota usage for the mailbox for the user was increased by 1
    mailbox_quota_summary_per_user = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.MAILBOX,
        period=QuotaPeriod.ANNUAL,
        user_id=create_request.owner_user_id,
    )
    assert mailbox_quota_summary_per_user.total_remaining == 0
    assert mailbox_quota_summary_per_user.total_used == 1
    assert mailbox_quota_summary_per_user.total_limit == 1

    # Verify that the quota usage for the organization's mailbox quota
    organization_mailbox_quota_summary = (
        await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )
    )
    assert organization_mailbox_quota_summary.total_remaining == 4
    assert organization_mailbox_quota_summary.total_used == 1
    assert organization_mailbox_quota_summary.total_limit == 5

    # Get the created email account
    email_account = await email_account_repository.find_by_tenanted_primary_key(
        EmailAccount,
        organization_id=organization_id,
        id=email_account_id,
    )
    assert email_account is not None

    # Verify the custom seconds_delay_between_emails was set correctly
    assert email_account.seconds_delay_between_emails == 300
    # Create email account pools
    pools = []
    for i in range(2):
        pool = EmailAccountPool(
            id=uuid4(),
            organization_id=organization_id,
            owner_user_id=admin_user_id,
            name=f"Test Pool {i}",
            is_default=False,
            created_at=zoned_utc_now(),
            created_by_user_id=admin_user_id,
        )
        pool = await email_account_pool_repository.insert(pool)
        pools.append(pool)

    # Verify the warmup campaign was automatically created
    found_campaign = await email_account_warm_up_campaign_repository.find_campaign_by_email_account_id_organization_id_and_warm_up_service(
        email_account_id=email_account.id,
        organization_id=organization_id,
        warm_up_service=MailboxWarmUpService.MAILIVERY,
    )
    assert found_campaign is not None
    assert found_campaign.is_mock_record is True

    # Perform the initial get to verify starting state
    initial_get_response = await admin_api_client.api_test_client.client.get(
        f"/api/v2/emails/accounts/{email_account.id}",
    )
    assert initial_get_response.status_code == 200
    initial_data = initial_get_response.json()
    assert initial_data["first_name"] == "Original"
    assert initial_data["last_name"] == "Name"
    assert initial_data["active"]

    # Verify no initial pool memberships
    initial_memberships = await email_account_pool_repository.get_active_email_account_pool_memberships_by_email_account_id(
        organization_id=organization_id,
        email_account_id=email_account.id,
    )
    assert len(initial_memberships) == 0

    # Update the email account with fields from the V2 schema including pool memberships
    update_payload = {
        "first_name": "Updated",
        "last_name": "User",
        "reply_to_email": "<EMAIL>",
        "warmup_limit": 10,
        "seconds_delay_between_emails": 180,  # Update to 3 minutes
        "type": EmailAccountType.OUTBOUND,
        "email_account_pool_ids": [str(pools[0].id), str(pools[1].id)],
    }

    # Make the update request
    update_response = await admin_api_client.api_test_client.client.patch(
        f"/api/v2/emails/accounts/{email_account.id}",
        json=update_payload,
    )

    assert update_response.status_code == 200
    update_data = update_response.json()

    # Verify the updated fields in the response
    assert update_data["first_name"] == "Updated"
    assert update_data["last_name"] == "User"
    assert update_data["reply_to_email"] == "<EMAIL>"
    assert update_data["type"] == EmailAccountType.OUTBOUND

    # Verify pool memberships were created
    updated_memberships = await email_account_pool_repository.get_active_email_account_pool_memberships_by_email_account_id(
        organization_id=organization_id,
        email_account_id=email_account.id,
    )
    # Since the repository only returns active memberships
    assert len(updated_memberships) == 2
    pool_ids = {membership.email_account_pool_id for membership in updated_memberships}
    assert pools[0].id in pool_ids
    assert pools[1].id in pool_ids

    # Verify the email account can be found by ID
    updated_email_account = await email_account_repository.find_by_tenanted_primary_key(
        EmailAccount,
        organization_id=organization_id,
        id=email_account.id,
        exclude_deleted_or_archived=False,
    )
    assert updated_email_account is not None

    # Verify the seconds_delay_between_emails was updated
    assert updated_email_account.seconds_delay_between_emails == 180
    assert updated_email_account.first_name == "Updated"
    assert updated_email_account.last_name == "User"
    assert updated_email_account.reply_to_email == "<EMAIL>"
    assert (
        updated_email_account.transaction_type
        == AccountsReceivable.INVOICED_TO_CUSTOMER
    )

    # Verify the email account can be found by owner user ID
    owner_accounts = await email_account_repository.find_accounts_by_owner_user_id(
        organization_id=organization_id,
        owner_user_id=admin_user_id,
    )
    assert email_account.id in [acc.id for acc in owner_accounts]

    # Get the email account again to verify persistence
    get_response = await admin_api_client.api_test_client.client.get(
        f"/api/v2/emails/accounts/{email_account.id}",
    )
    assert get_response.status_code == 200
    get_data = get_response.json()

    # Verify all fields are correctly populated in the returned data
    assert get_data["id"] == str(email_account.id)
    assert get_data["organization_id"] == str(organization_id)
    assert get_data["owner_user_id"] == str(admin_user_id)
    assert get_data["created_by_user_id"] == str(admin_user_id)
    assert get_data["email"] == f"updatetest@{email_domain}"

    # Verify updated fields
    assert get_data["first_name"] == "Updated"
    assert get_data["last_name"] == "User"
    assert get_data["reply_to_email"] == "<EMAIL>"
    assert get_data["type"] == EmailAccountType.OUTBOUND

    # Verify fields that should remain unchanged
    assert get_data["active"]
    # Verify metadata fields
    assert get_data["created_at"] is not None
    assert get_data["updated_at"] is not None
    assert get_data["updated_by_user_id"] == str(admin_user_id)

    # Set signature_id to null
    remove_signature_payload = {"signature_id": None}

    # Make the update request to remove signature
    remove_signature_response = await admin_api_client.api_test_client.client.patch(
        f"/api/v2/emails/accounts/{email_account.id}",
        json=remove_signature_payload,
    )

    assert remove_signature_response.status_code == 200
    remove_signature_data = remove_signature_response.json()
    assert remove_signature_data["signature_id"] is None

    # Verify the email account in the database has null signature_id
    final_email_account = await email_account_repository.find_by_tenanted_primary_key(
        EmailAccount,
        organization_id=organization_id,
        id=email_account.id,
        exclude_deleted_or_archived=False,
    )
    assert final_email_account is not None
    assert final_email_account.signature_id is None

    # Final get to verify persistence
    final_get_response = await admin_api_client.api_test_client.client.get(
        f"/api/v2/emails/accounts/{email_account.id}",
    )
    assert final_get_response.status_code == 200
    final_get_data = final_get_response.json()
    assert final_get_data["signature_id"] is None

    # Buy a mailbox for user_id_2
    create_request2 = CreateEmailAccountRequest(
        owner_user_id=user1_id,
        email=f"testuser1@{email_domain}",
        first_name="Test",
        last_name="User2",
        reply_to_email=f"reply@{email_domain}",
        warmup_limit=10,
        daily_quota=40,
        seconds_delay_between_emails=300,  # Custom delay of 5 minutes
        email_account_pool_ids=None,
        is_in_default_pool=False,
        mock_config_params=MockEmailAccountConfigParams(
            mock_purchase=True
        ),  # No need to specify domain name
        outbound_domain_id=domain_id,  # Use the created domain
    )

    create_response2 = await admin_api_client.api_test_client.client.post(
        "/api/v2/emails/accounts",
        json=create_request2.model_dump(mode="json"),
    )

    assert create_response2.status_code == 200
    create_data2 = create_response2.json()
    assert create_data2["email"] == f"testuser1@{email_domain}"
    email_account_id2 = UUID(create_data2["id"])

    # Update the email account to transfer to user_id_1
    update_payload2 = {"owner_user_id": str(admin_user_id)}
    update_response2 = await admin_api_client.api_test_client.client.patch(
        f"/api/v2/emails/accounts/{email_account_id2}",
        json=update_payload2,
    )
    assert update_response2.status_code == 402
    assert update_response2.json().get("error") == "PaymentError"


async def test_update_email_account_pools_v2(
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    email_account_repository: EmailAccountRepository,
    email_account_pool_repository: EmailAccountPoolRepository,
    quota_service: QuotaService,
    outbound_domain_repository: OutboundDomainRepository,
) -> None:
    admin_api_client = multi_api_client["admin"]
    organization_id = admin_api_client.default_organization_id
    user_id = admin_api_client.default_user_id

    # First, create a mock domain using the buy domains endpoint
    email_domain = str(uuid.uuid4()) + ".com"
    buy_domain_request = {
        "domains": [email_domain],
        "forward_to_domain": None,
        "is_mock_record": True,
    }

    # Call the buy domains endpoint
    buy_domains_response = await admin_api_client.api_test_client.client.post(
        "/api/v1/emails/outbound/domains/_buy",
        json=buy_domain_request,
    )

    # Verify the domain was created
    assert buy_domains_response.status_code == 200
    domains_data = buy_domains_response.json()
    assert len(domains_data) > 0
    domain_id = UUID(domains_data[0]["id"])

    # We are now pending on purchase until domain health is properly configured which won't happen in this test because it's in the workflow which doesn't properly run in tests, force active for testing
    await outbound_domain_repository.update_outbound_domain(
        domain_id, {"status": OutboundDomainStatus.ACTIVE}
    )

    # Create the email account request with the mock domain
    create_request = CreateEmailAccountRequest(
        owner_user_id=user_id,
        email=f"pooltest@{email_domain}",
        first_name="Pool",
        last_name="Test",
        reply_to_email=f"reply@{email_domain}",
        warmup_limit=10,
        daily_quota=40,
        email_account_pool_ids=None,
        is_in_default_pool=False,
        mock_config_params=MockEmailAccountConfigParams(
            mock_purchase=True
        ),  # No need to specify domain name
        outbound_domain_id=domain_id,  # Use the created domain
    )

    # Call the creation endpoint
    create_response = await admin_api_client.api_test_client.client.post(
        "/api/v2/emails/accounts",
        json=create_request.model_dump(mode="json"),
    )

    # Verify the creation response
    assert create_response.status_code == 200
    created_data = create_response.json()
    email_account_id = UUID(created_data["id"])

    # Verify that the quota usage for the organization's mailbox quota
    mailbox_quota_summary_per_organization = (
        await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )
    )

    assert mailbox_quota_summary_per_organization.total_remaining == 4
    assert mailbox_quota_summary_per_organization.total_used == 1
    assert mailbox_quota_summary_per_organization.total_limit == 5

    # Verify that the quota usage for the user's mailbox quota
    mailbox_quota_summary_per_user = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.MAILBOX,
        period=QuotaPeriod.ANNUAL,
        user_id=user_id,
    )
    assert mailbox_quota_summary_per_user.total_remaining == 1
    assert mailbox_quota_summary_per_user.total_used == 1
    assert mailbox_quota_summary_per_user.total_limit == 2

    # Verify that the quota usage for the plan included mailbox for the organization was not increased by 1
    included_mailbox_quota_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )
    assert (
        included_mailbox_quota_summary.total_remaining == 2
    )  # This value is 2 because the mailbox is a mock record, so it's not included in the plan-included-cost-mailbox quota
    assert included_mailbox_quota_summary.total_used == 0
    assert included_mailbox_quota_summary.total_limit == 2

    # Get the created email account
    email_account = await email_account_repository.find_by_tenanted_primary_key(
        EmailAccount,
        organization_id=organization_id,
        id=email_account_id,
    )
    assert email_account is not None

    # Create multiple email account pools
    pools = []
    for i in range(3):
        pool = EmailAccountPool(
            id=uuid4(),
            organization_id=organization_id,
            owner_user_id=user_id,
            name=f"Test Pool {i}",
            is_default=False,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
        )
        pool = await email_account_pool_repository.insert(pool)
        pools.append(pool)

    # Initially associate the email account with the first pool only
    initial_membership = EmailAccountPoolMembership(
        id=uuid4(),
        organization_id=organization_id,
        email_account_pool_id=pools[0].id,
        email_account_id=email_account.id,
        created_at=zoned_utc_now(),
        created_by_user_id=user_id,
    )
    await email_account_pool_repository.insert(initial_membership)

    # Verify the initial pool membership
    initial_memberships = await email_account_pool_repository.get_active_email_account_pool_memberships_by_email_account_id(
        organization_id=organization_id,
        email_account_id=email_account.id,
    )
    assert len(initial_memberships) == 1
    assert initial_memberships[0].email_account_pool_id == pools[0].id

    # Update the email account with a new set of pools (remove pool[0], add pool[1] and pool[2])
    update_payload = {"email_account_pool_ids": [str(pools[1].id), str(pools[2].id)]}

    # Make the update request
    update_response = await admin_api_client.api_test_client.client.patch(
        f"/api/v2/emails/accounts/{email_account.id}",
        json=update_payload,
    )

    assert update_response.status_code == 200

    # Verify the updated pool memberships
    updated_memberships = await email_account_pool_repository.get_active_email_account_pool_memberships_by_email_account_id(
        organization_id=organization_id,
        email_account_id=email_account.id,
    )

    # Since the repository function only returns active memberships,
    # we should only have memberships for pool[1] and pool[2]
    assert len(updated_memberships) == 2

    updated_pool_ids = {m.email_account_pool_id for m in updated_memberships}
    assert pools[1].id in updated_pool_ids
    assert pools[2].id in updated_pool_ids
    assert (
        pools[0].id not in updated_pool_ids
    )  # Original pool should not be in active memberships

    # Test adding back the first pool and removing one of the others
    new_owner = uuid4()
    second_update_payload = {
        "email_account_pool_ids": [str(pools[0].id), str(pools[1].id)],
        "owner_user_id": str(new_owner),
    }

    second_update_response = await admin_api_client.api_test_client.client.patch(
        f"/api/v2/emails/accounts/{email_account.id}",
        json=second_update_payload,
    )

    assert second_update_response.status_code == 200

    assert second_update_response.json()["owner_user_id"] == str(new_owner)

    # Verify that the quota usage for the organization's mailbox quota
    mailbox_quota_summary_per_organization = (
        await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )
    )

    assert mailbox_quota_summary_per_organization.total_remaining == 4
    assert mailbox_quota_summary_per_organization.total_used == 1
    assert mailbox_quota_summary_per_organization.total_limit == 5

    # Verify that the quota usage for the user's mailbox quota is now 0 since we transferred the email account to the new owner
    mailbox_quota_summary_for_user1 = (
        await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.MAILBOX,
            period=QuotaPeriod.ANNUAL,
            user_id=user_id,
        )
    )
    assert mailbox_quota_summary_for_user1.total_remaining == 2
    assert mailbox_quota_summary_for_user1.total_used == 0
    assert mailbox_quota_summary_for_user1.total_limit == 2

    # Verify that the quota usage for the second user's mailbox quota, which should be 1 now that we transferred the email account to them
    mailbox_quota_summary_for_user2 = (
        await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.MAILBOX,
            period=QuotaPeriod.ANNUAL,
            user_id=new_owner,
        )
    )
    assert mailbox_quota_summary_for_user2.total_remaining == 1
    assert mailbox_quota_summary_for_user2.total_used == 1
    assert mailbox_quota_summary_for_user2.total_limit == 2

    # Verify that the quota usage for the plan included mailbox for the organization was not increased by 1
    included_mailbox_quota_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )
    assert (
        included_mailbox_quota_summary.total_remaining == 2
    )  # This value is 2 because the mailbox is a mock record, so it's not included in the plan-included-cost-mailbox quota
    assert included_mailbox_quota_summary.total_used == 0
    assert included_mailbox_quota_summary.total_limit == 2

    updated_email_account = await email_account_repository.find_by_tenanted_primary_key(
        EmailAccount,
        organization_id=organization_id,
        id=email_account.id,
    )
    assert updated_email_account is not None
    assert updated_email_account.owner_user_id == new_owner

    # Verify the second update memberships
    final_memberships = await email_account_pool_repository.get_active_email_account_pool_memberships_by_email_account_id(
        organization_id=organization_id,
        email_account_id=email_account.id,
    )

    # Should be 2 active memberships (pools[0] and pools[1])
    assert len(final_memberships) == 2

    final_pool_ids = {m.email_account_pool_id for m in final_memberships}
    assert pools[0].id in final_pool_ids
    assert pools[1].id in final_pool_ids
    assert pools[2].id not in final_pool_ids  # Should be deleted and not returned


async def test_create_and_update_email_account_with_mock_config(
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    email_account_repository: EmailAccountRepository,
    email_account_warm_up_campaign_repo: EmailAccountWarmUpCampaignRepository,
    email_account_pool_repository: EmailAccountPoolRepository,
    quota_policy_service: QuotaPolicyService,
    quota_service: QuotaService,
) -> None:
    admin_api_client = multi_api_client["admin"]
    organization_id = admin_api_client.default_organization_id
    user_id = admin_api_client.default_user_id

    # Create email account pools for testing
    pools = []
    for i in range(2):
        pool = EmailAccountPool(
            id=uuid4(),
            organization_id=organization_id,
            owner_user_id=user_id,
            name=f"Test Pool {i} for Mock Account",
            is_default=False,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
        )
        pool = await email_account_pool_repository.insert(pool)
        pools.append(pool)

    # STEP 1: Create a mock email account

    # Create mock email account config params
    mock_config = MockEmailAccountConfigParams(
        mock_purchase=True, mock_domain_name="mockdomain.com"
    )

    # Create the email account request using Pydantic model
    create_request = CreateEmailAccountRequest(
        owner_user_id=user_id,
        email="<EMAIL>",
        first_name="Original",
        last_name="Name",
        reply_to_email="<EMAIL>",
        warmup_limit=10,
        daily_quota=40,
        email_account_pool_ids=[pools[0].id],
        is_in_default_pool=False,
        mock_config_params=mock_config,
        outbound_domain_id=uuid4(),  # Will be mocked
    )

    # Call the creation endpoint with the Pydantic model
    create_response = await admin_api_client.api_test_client.client.post(
        "/api/v2/emails/accounts",
        json=create_request.model_dump(mode="json"),
    )

    # Verify the creation response
    assert create_response.status_code == 200
    created_data = create_response.json()
    email_account_id = UUID(created_data["id"])

    # Verify that the quota usage for the organization's mailbox quota
    mailbox_quota_summary_per_organization = (
        await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )
    )

    assert mailbox_quota_summary_per_organization.total_remaining == 4
    assert mailbox_quota_summary_per_organization.total_used == 1
    assert mailbox_quota_summary_per_organization.total_limit == 5

    # Verify that the quota usage for the user's mailbox quota
    mailbox_quota_summary_per_user = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.MAILBOX,
        period=QuotaPeriod.ANNUAL,
        user_id=user_id,
    )
    assert mailbox_quota_summary_per_user.total_remaining == 1
    assert mailbox_quota_summary_per_user.total_used == 1
    assert mailbox_quota_summary_per_user.total_limit == 2

    # Verify that the quota usage for the plan included mailbox for the organization was not increased by 1
    included_mailbox_quota_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )
    assert (
        included_mailbox_quota_summary.total_remaining == 2
    )  # This value is 1 because the mailbox is a mock record, so it's not included in the plan-included-cost-mailbox quota
    assert included_mailbox_quota_summary.total_used == 0
    assert included_mailbox_quota_summary.total_limit == 2

    # STEP 2: Get the created email account
    get_response = await admin_api_client.api_test_client.client.get(
        f"/api/v2/emails/accounts/{email_account_id}",
    )
    assert get_response.status_code == 200
    get_data = get_response.json()

    # Verify initial data
    assert get_data["email"] == create_request.email
    assert get_data["first_name"] == "Original"
    assert get_data["last_name"] == "Name"
    assert get_data["reply_to_email"] == "<EMAIL>"

    # Verify email account exists in DB with is_mock_record flag
    email_account = await email_account_repository.find_by_tenanted_primary_key(
        EmailAccount,
        organization_id=organization_id,
        id=email_account_id,
    )
    assert email_account is not None
    assert email_account.is_mock_record is True

    # Verify initial warmup campaign
    initial_warmup_campaign = await email_account_warm_up_campaign_repo.find_campaign_by_email_account_id_organization_id_and_warm_up_service(
        email_account_id=email_account_id,
        organization_id=organization_id,
        warm_up_service=MailboxWarmUpService.MAILIVERY,
    )
    assert initial_warmup_campaign is not None
    assert initial_warmup_campaign.is_mock_record is True
    assert initial_warmup_campaign.email_per_day == 10

    # Verify initial pool memberships
    initial_memberships = await email_account_pool_repository.get_active_email_account_pool_memberships_by_email_account_id(
        organization_id=organization_id,
        email_account_id=email_account_id,
    )
    assert len(initial_memberships) == 1
    assert initial_memberships[0].email_account_pool_id == pools[0].id

    # STEP 3: Update the email account
    update_request = PatchEmailAccountRequestV2(
        first_name="Updated",
        last_name="User",
        reply_to_email="<EMAIL>",
        warmup_limit=10,
        email_account_pool_ids=[pools[1].id],  # Change to different pool
    )

    # Call the update endpoint
    update_response = await admin_api_client.api_test_client.client.patch(
        f"/api/v2/emails/accounts/{email_account_id}",
        json=update_request.model_dump(exclude_unset=True, mode="json"),
    )

    assert update_response.status_code == 200
    update_data = update_response.json()

    # Verify update response data
    assert update_data["first_name"] == "Updated"
    assert update_data["last_name"] == "User"
    assert update_data["reply_to_email"] == "<EMAIL>"

    # STEP 4: Get the updated email account
    final_get_response = await admin_api_client.api_test_client.client.get(
        f"/api/v2/emails/accounts/{email_account_id}",
    )
    assert final_get_response.status_code == 200
    final_data = final_get_response.json()

    # Verify final data after update
    assert final_data["email"] == create_request.email  # Email shouldn't change
    assert final_data["first_name"] == "Updated"
    assert final_data["last_name"] == "User"
    assert final_data["reply_to_email"] == "<EMAIL>"
    assert final_data["organization_id"] == str(organization_id)
    assert final_data["updated_at"] is not None
    assert final_data["updated_by_user_id"] == str(user_id)

    # Verify updated warmup campaign
    updated_warmup_campaign = await email_account_warm_up_campaign_repo.find_campaign_by_email_account_id_organization_id_and_warm_up_service(
        email_account_id=email_account_id,
        organization_id=organization_id,
        warm_up_service=MailboxWarmUpService.MAILIVERY,
    )
    assert updated_warmup_campaign is not None
    assert updated_warmup_campaign.is_mock_record is True

    # Verify updated pool memberships
    updated_memberships = await email_account_pool_repository.get_active_email_account_pool_memberships_by_email_account_id(
        organization_id=organization_id,
        email_account_id=email_account_id,
    )
    assert len(updated_memberships) == 1
    assert (
        updated_memberships[0].email_account_pool_id == pools[1].id
    )  # Changed to pool[1]


async def test_archive_email_account_v2(
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    email_account_repository: EmailAccountRepository,
    email_account_pool_repository: EmailAccountPoolRepository,
    email_account_warm_up_campaign_repository: EmailAccountWarmUpCampaignRepository,
    quota_service: QuotaService,
    outbound_domain_repository: OutboundDomainRepository,
) -> None:
    admin_api_client = multi_api_client["admin"]
    organization_id = admin_api_client.default_organization_id
    user_id = admin_api_client.default_user_id

    # First, create a mock domain using the buy domains endpoint
    email_domain = str(uuid.uuid4()) + ".com"
    buy_domain_request = {
        "domains": [email_domain],
        "forward_to_domain": None,
        "is_mock_record": True,
    }

    # Call the buy domains endpoint
    buy_domains_response = await admin_api_client.api_test_client.client.post(
        "/api/v1/emails/outbound/domains/_buy",
        json=buy_domain_request,
    )

    # Verify the domain was created
    assert buy_domains_response.status_code == 200
    domains_data = buy_domains_response.json()
    assert len(domains_data) > 0
    domain_id = UUID(domains_data[0]["id"])

    # We are now pending on purchase until domain health is properly configured which won't happen in this test because it's in the workflow which doesn't properly run in tests, force active for testing
    await outbound_domain_repository.update_outbound_domain(
        domain_id, {"status": OutboundDomainStatus.ACTIVE}
    )

    # Create the email account request with the mock domain
    create_request = CreateEmailAccountRequest(
        owner_user_id=user_id,
        email=f"archivetest@{email_domain}",
        first_name="Archive",
        last_name="Test",
        reply_to_email=f"reply@{email_domain}",
        warmup_limit=10,
        daily_quota=40,
        email_account_pool_ids=None,
        is_in_default_pool=False,
        mock_config_params=MockEmailAccountConfigParams(
            mock_purchase=True
        ),  # No need to specify domain name
        outbound_domain_id=domain_id,  # Use the created domain
    )

    # Call the creation endpoint
    create_response = await admin_api_client.api_test_client.client.post(
        "/api/v2/emails/accounts",
        json=create_request.model_dump(mode="json"),
    )

    # Verify the creation response
    assert create_response.status_code == 200
    created_data = create_response.json()
    email_account_id = UUID(created_data["id"])

    # Verify that the quota usage for the organization's mailbox quota
    mailbox_quota_summary_per_organization = (
        await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )
    )

    assert mailbox_quota_summary_per_organization.total_remaining == 4
    assert mailbox_quota_summary_per_organization.total_used == 1
    assert mailbox_quota_summary_per_organization.total_limit == 5

    # Verify that the quota usage for the user's mailbox quota
    mailbox_quota_summary_per_user = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.MAILBOX,
        period=QuotaPeriod.ANNUAL,
        user_id=user_id,
    )
    assert mailbox_quota_summary_per_user.total_remaining == 1
    assert mailbox_quota_summary_per_user.total_used == 1
    assert mailbox_quota_summary_per_user.total_limit == 2

    # Verify that the quota usage for the plan included mailbox for the organization was not increased by 1
    included_mailbox_quota_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )
    assert (
        included_mailbox_quota_summary.total_remaining == 2
    )  # This value is 1 because the mailbox is a mock record, so it's not included in the plan-included-cost-mailbox quota
    assert included_mailbox_quota_summary.total_used == 0
    assert included_mailbox_quota_summary.total_limit == 2

    # Get the created email account
    email_account = await email_account_repository.find_by_tenanted_primary_key(
        EmailAccount,
        organization_id=organization_id,
        id=email_account_id,
    )
    assert email_account is not None
    # Create email account pool and add the email account to it
    pool = EmailAccountPool(
        id=uuid4(),
        organization_id=organization_id,
        owner_user_id=user_id,
        name="Test Pool",
        is_default=False,
        created_at=zoned_utc_now(),
        created_by_user_id=user_id,
    )
    pool = await email_account_pool_repository.insert(pool)

    # Add the email account to the pool
    membership = EmailAccountPoolMembership(
        id=uuid4(),
        organization_id=organization_id,
        email_account_pool_id=pool.id,
        email_account_id=email_account.id,
        created_at=zoned_utc_now(),
        created_by_user_id=user_id,
    )
    await email_account_pool_repository.insert(membership)

    # Verify the email account exists and is active
    initial_get_response = await admin_api_client.api_test_client.client.get(
        f"/api/v2/emails/accounts/{email_account.id}",
    )
    assert initial_get_response.status_code == 200
    initial_data = initial_get_response.json()
    assert initial_data["active"] is True

    # Verify the warmup campaign was automatically created
    found_campaign = await email_account_warm_up_campaign_repository.find_campaign_by_email_account_id_organization_id_and_warm_up_service(
        email_account_id=email_account.id,
        organization_id=organization_id,
        warm_up_service=MailboxWarmUpService.MAILIVERY,
    )
    assert found_campaign is not None
    assert found_campaign.is_mock_record is True
    assert found_campaign.email_account_id == email_account.id

    # Verify the initial pool membership exists
    initial_memberships = await email_account_pool_repository.get_active_email_account_pool_memberships_by_email_account_id(
        organization_id=organization_id,
        email_account_id=email_account.id,
    )
    assert len(initial_memberships) == 1
    assert initial_memberships[0].email_account_pool_id == pool.id

    # Archive the email account instead of deleting it
    archive_payload = {"sequence_handling": EmailAccountArchiveSequenceHandling.STOP}
    archive_response = await admin_api_client.api_test_client.client.post(
        f"/api/v2/emails/accounts/{email_account.id}/_archive",
        json=archive_payload,
    )

    assert archive_response.status_code == 200

    # Verify the email account is now marked as inactive using find_by_tenanted_primary_key
    updated_email_account = await email_account_repository.find_by_tenanted_primary_key(
        EmailAccount,
        organization_id=organization_id,
        id=email_account.id,
        exclude_deleted_or_archived=False,
    )
    assert updated_email_account is not None
    assert updated_email_account.active is False
    assert updated_email_account.archived_at is not None
    assert updated_email_account.archived_by_user_id == user_id
    assert (
        updated_email_account.transaction_type
        == AccountsReceivable.INVOICED_TO_CUSTOMER
    )
    # Verify email accounts by organization id no longer includes the deleted account
    all_active_accounts = (
        await email_account_repository.find_accounts_by_organization_id(
            organization_id=organization_id,
        )
    )
    assert email_account.id not in [acc.id for acc in all_active_accounts]

    # Verify all pool memberships are marked as deleted
    post_delete_memberships = await email_account_pool_repository.get_active_email_account_pool_memberships_by_email_account_id(
        organization_id=organization_id,
        email_account_id=email_account.id,
    )
    assert len(post_delete_memberships) == 0

    # Verify the email account no longer appears in GET requests (or appears as inactive)
    get_response = await admin_api_client.api_test_client.client.get(
        f"/api/v2/emails/accounts/{email_account.id}",
    )

    # Either the account shouldn't be returned at all (404) or should be inactive
    if get_response.status_code == 200:
        get_data = get_response.json()
        assert get_data["active"] is False
    else:
        assert get_response.status_code == 404

    # check quotas again (nothing should have changed since we still "own" the mailbox)
    # Verify that the quota usage for the organization's mailbox quota
    mailbox_quota_summary_per_organization = (
        await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )
    )

    assert mailbox_quota_summary_per_organization.total_remaining == 4
    assert mailbox_quota_summary_per_organization.total_used == 1
    assert mailbox_quota_summary_per_organization.total_limit == 5

    # Verify that the quota usage for the user's mailbox quota
    mailbox_quota_summary_per_user = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.MAILBOX,
        period=QuotaPeriod.ANNUAL,
        user_id=user_id,
    )
    assert mailbox_quota_summary_per_user.total_remaining == 1
    assert mailbox_quota_summary_per_user.total_used == 1
    assert mailbox_quota_summary_per_user.total_limit == 2

    # Verify that the quota usage for the plan included mailbox for the organization was not increased by 1
    included_mailbox_quota_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )
    assert (
        included_mailbox_quota_summary.total_remaining == 2
    )  # This value is 2 because the mailbox is a mock record, so it's not included in the plan-included-cost-mailbox quota
    assert included_mailbox_quota_summary.total_used == 0
    assert included_mailbox_quota_summary.total_limit == 2

    # Try to use the archive endpoint for a non-existent email account ID
    non_existent_id = uuid4()
    mock_temporal_client = AsyncMock()
    with (
        patch(
            "salestech_be.web.api.email.common.imap_sync_schedule_service.get_temporal_client",
            return_value=mock_temporal_client,
        ),
        patch(
            "salestech_be.core.email.account.service_v2.get_temporal_client",
            return_value=mock_temporal_client,
        ),
    ):
        wrong_id_archive_response = await admin_api_client.api_test_client.client.post(
            f"/api/v2/emails/accounts/{non_existent_id}/_archive",
            json=archive_payload,
        )
    # Should return 404 or appropriate error code
    assert wrong_id_archive_response.status_code > 399


async def test_email_account_signature_relationship(
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    signature_repository: SignatureRepository,
    email_account_repository: EmailAccountRepository,
    quota_service: QuotaService,
    outbound_domain_repository: OutboundDomainRepository,
) -> None:
    # Setup test data
    admin_api_client = multi_api_client["admin"]
    organization_id = admin_api_client.default_organization_id
    user_id = admin_api_client.default_user_id

    # Create a signature
    signature = await signature_repository.create_signature(
        signature_name="Test Signature",
        signature_html="<p>This is a test signature</p>",
        organization_id=organization_id,
        user_id=user_id,
    )

    # First, create a mock domain using the buy domains endpoint
    email_domain = str(uuid.uuid4()) + ".com"
    buy_domain_request = {
        "domains": [email_domain],
        "forward_to_domain": None,
        "is_mock_record": True,
    }

    # Call the buy domains endpoint
    buy_domains_response = await admin_api_client.api_test_client.client.post(
        "/api/v1/emails/outbound/domains/_buy",
        json=buy_domain_request,
    )

    # Verify the domain was created
    assert buy_domains_response.status_code == 200
    domains_data = buy_domains_response.json()
    assert len(domains_data) > 0
    domain_id = UUID(domains_data[0]["id"])

    # We are now pending on purchase until domain health is properly configured which won't happen in this test because it's in the workflow which doesn't properly run in tests, force active for testing
    await outbound_domain_repository.update_outbound_domain(
        domain_id, {"status": OutboundDomainStatus.ACTIVE}
    )

    # Create the email account request with the mock domain
    create_request = CreateEmailAccountRequest(
        owner_user_id=user_id,
        email=f"signaturetest@{email_domain}",
        first_name="Signature",
        last_name="Test",
        reply_to_email=f"reply@{email_domain}",
        signature_id=signature.id,  # Link to the signature
        warmup_limit=10,
        daily_quota=40,
        email_account_pool_ids=None,
        is_in_default_pool=False,
        mock_config_params=MockEmailAccountConfigParams(
            mock_purchase=True
        ),  # No need to specify domain name
        outbound_domain_id=domain_id,  # Use the created domain
    )

    # Call the creation endpoint
    create_response = await admin_api_client.api_test_client.client.post(
        "/api/v2/emails/accounts",
        json=create_request.model_dump(mode="json"),
    )

    # Verify the creation response
    assert create_response.status_code == 200
    created_data = create_response.json()

    # Verify that the quota usage for the organization's mailbox quota
    mailbox_quota_summary_per_organization = (
        await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )
    )

    assert mailbox_quota_summary_per_organization.total_remaining == 4
    assert mailbox_quota_summary_per_organization.total_used == 1
    assert mailbox_quota_summary_per_organization.total_limit == 5

    # Verify that the quota usage for the user's mailbox quota
    mailbox_quota_summary_per_user = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.MAILBOX,
        period=QuotaPeriod.ANNUAL,
        user_id=user_id,
    )
    assert mailbox_quota_summary_per_user.total_remaining == 1
    assert mailbox_quota_summary_per_user.total_used == 1
    assert mailbox_quota_summary_per_user.total_limit == 2

    # Verify that the quota usage for the plan included mailbox for the organization was not increased by 1
    included_mailbox_quota_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )
    assert (
        included_mailbox_quota_summary.total_remaining == 2
    )  # This value is 2 because the mailbox is a mock record, so it's not included in the plan-included-cost-mailbox quota
    assert included_mailbox_quota_summary.total_used == 0
    assert included_mailbox_quota_summary.total_limit == 2

    email_account_id = UUID(created_data["id"])

    # Verify the email account was created with the signature_id
    email_account = await email_account_repository.find_by_tenanted_primary_key(
        EmailAccount,
        organization_id=organization_id,
        id=email_account_id,
    )
    assert email_account is not None
    assert email_account.signature_id == signature.id

    # Call the list endpoint with the signature relationship specified
    response = await admin_api_client.api_test_client.client.post(
        "/api/v2/emails/accounts/_list",
        json={
            "filter": None,
            "sort": None,
            "page_size": 10,
            "page_number": 1,
            "ordered_object_fields": [
                {
                    "relationship_id": EmailAccountRelationship.email_account__to__signature.value,
                    "field": {"path": ["id"]},
                }
            ],
        },
    )

    # Verify response
    assert response.status_code == 200
    response_data = response.json()
    # Find our test account in the response
    test_account_record = None
    for record in response_data["list_data"]:
        if record["data"]["id"] == str(email_account_id):
            test_account_record = record
            break

    assert test_account_record is not None, "Test email account not found in response"

    # Verify the signature relationship
    assert (
        EmailAccountRelationship.email_account__to__signature.value
        in test_account_record["related_records"]
    )
    assert (
        len(
            test_account_record["related_records"][
                EmailAccountRelationship.email_account__to__signature.value
            ]
        )
        == 1
    )
    signature_record = test_account_record["related_records"][
        EmailAccountRelationship.email_account__to__signature.value
    ][0]

    # The signature should be returned as a single record, not a list
    assert signature_record is not None
    assert signature_record["data"]["id"] == str(signature.id)
    assert signature_record["data"]["name"] == signature.name
    assert signature_record["data"]["signature_html"] == signature.signature_html
    assert signature_record["data"]["organization_id"] == str(organization_id)
    assert signature_record["data"]["created_at"] is not None


async def test_archive_unarchive_email_account_v2(
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    email_account_repository: EmailAccountRepository,
    email_account_pool_repository: EmailAccountPoolRepository,
    email_account_warm_up_campaign_repository: EmailAccountWarmUpCampaignRepository,
    quota_service: QuotaService,
    outbound_domain_repository: OutboundDomainRepository,
) -> None:
    admin_api_client = multi_api_client["admin"]
    organization_id = admin_api_client.default_organization_id
    user_id = admin_api_client.default_user_id

    # First, create a mock domain using the buy domains endpoint
    email_domain = str(uuid.uuid4()) + ".com"
    buy_domain_request = {
        "domains": [email_domain],
        "forward_to_domain": None,
        "is_mock_record": True,
    }

    # Call the buy domains endpoint
    buy_domains_response = await admin_api_client.api_test_client.client.post(
        "/api/v1/emails/outbound/domains/_buy",
        json=buy_domain_request,
    )

    # Verify the domain was created
    assert buy_domains_response.status_code == 200
    domains_data = buy_domains_response.json()
    assert len(domains_data) > 0
    domain_id = UUID(domains_data[0]["id"])

    # We are now pending on purchase until domain health is properly configured which won't happen in this test because it's in the workflow which doesn't properly run in tests, force active for testing
    await outbound_domain_repository.update_outbound_domain(
        domain_id, {"status": OutboundDomainStatus.ACTIVE}
    )

    # Create the email account request with the mock domain
    create_request = CreateEmailAccountRequest(
        owner_user_id=user_id,
        email=f"archive-unarchive@{email_domain}",
        first_name="Archive",
        last_name="Unarchive",
        reply_to_email=f"reply@{email_domain}",
        warmup_limit=10,
        daily_quota=40,
        email_account_pool_ids=None,
        is_in_default_pool=False,
        mock_config_params=MockEmailAccountConfigParams(mock_purchase=True),
        outbound_domain_id=domain_id,
    )

    # Call the creation endpoint
    create_response = await admin_api_client.api_test_client.client.post(
        "/api/v2/emails/accounts",
        json=create_request.model_dump(mode="json"),
    )

    # Verify the creation response
    assert create_response.status_code == 200
    created_data = create_response.json()

    # Verify that the quota usage for the organization's mailbox quota
    mailbox_quota_summary_per_organization = (
        await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )
    )

    assert mailbox_quota_summary_per_organization.total_remaining == 4
    assert mailbox_quota_summary_per_organization.total_used == 1
    assert mailbox_quota_summary_per_organization.total_limit == 5

    # Verify that the quota usage for the user's mailbox quota
    mailbox_quota_summary_per_user = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.MAILBOX,
        period=QuotaPeriod.ANNUAL,
        user_id=user_id,
    )
    assert mailbox_quota_summary_per_user.total_remaining == 1
    assert mailbox_quota_summary_per_user.total_used == 1
    assert mailbox_quota_summary_per_user.total_limit == 2

    # Verify that the quota usage for the plan included mailbox for the organization was not increased by 1
    included_mailbox_quota_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )
    assert (
        included_mailbox_quota_summary.total_remaining == 2
    )  # This value is 2 because the mailbox is a mock record, so it's not included in the plan-included-cost-mailbox quota
    assert included_mailbox_quota_summary.total_used == 0
    assert included_mailbox_quota_summary.total_limit == 2

    email_account_id = UUID(created_data["id"])

    # Get the created email account
    email_account = await email_account_repository.find_by_tenanted_primary_key(
        EmailAccount,
        organization_id=organization_id,
        id=email_account_id,
    )
    assert email_account is not None

    # Verify the email account exists and is active
    initial_get_response = await admin_api_client.api_test_client.client.get(
        f"/api/v2/emails/accounts/{email_account.id}",
    )
    assert initial_get_response.status_code == 200
    initial_data = initial_get_response.json()
    assert initial_data["active"] is True

    # Check that the account appears in _list endpoint before archiving
    pre_archive_list_response = await admin_api_client.api_test_client.client.post(
        "/api/v2/emails/accounts/_list",
        json={
            "filter": None,
            "sort": None,
            "page_size": 10,
            "page_number": 1,
        },
    )
    assert pre_archive_list_response.status_code == 200
    pre_archive_list_data = pre_archive_list_response.json()

    # Find our account in the list results
    pre_archive_found = False
    for record in pre_archive_list_data["list_data"]:
        if record["data"]["id"] == str(email_account_id):
            pre_archive_found = True
            assert record["data"]["active"] is True
            break

    assert pre_archive_found, "Email account not found in list results before archiving"

    # Step 1: Archive the email account
    archive_payload = {"sequence_handling": EmailAccountArchiveSequenceHandling.STOP}

    mock_temporal_client = AsyncMock()
    with patch(
        "salestech_be.core.email.account.service_v2.get_temporal_client",
        return_value=mock_temporal_client,
    ):
        archive_response = await admin_api_client.api_test_client.client.post(
            f"/api/v2/emails/accounts/{email_account.id}/_archive",
            json=archive_payload,
        )

        assert archive_response.status_code == 200

        # Verify the email account is now marked as inactive
        archived_email_account = (
            await email_account_repository.find_by_tenanted_primary_key(
                EmailAccount,
                organization_id=organization_id,
                id=email_account.id,
                exclude_deleted_or_archived=False,
            )
        )
        assert archived_email_account is not None
        assert archived_email_account.active is False
        assert archived_email_account.archived_at is not None
        assert archived_email_account.archived_by_user_id == user_id

    # Verify the account does not appear in active accounts list
    all_active_accounts = (
        await email_account_repository.find_accounts_by_organization_id(
            organization_id=organization_id,
        )
    )
    assert email_account.id not in [acc.id for acc in all_active_accounts]

    # Check that the archived account still appears in _list endpoint
    archived_list_response = await admin_api_client.api_test_client.client.post(
        "/api/v2/emails/accounts/_list",
        json={
            "filter": None,
            "sort": None,
            "page_size": 10,
            "page_number": 1,
        },
    )
    assert archived_list_response.status_code == 200
    archived_list_data = archived_list_response.json()

    # Find our archived account in the list results
    archived_found = False
    for record in archived_list_data["list_data"]:
        if record["data"]["id"] == str(email_account_id):
            archived_found = True
            assert record["data"]["active"] is False
            assert record["data"]["archived_at"] is not None
            break

    assert archived_found, "Archived email account not found in list results"

    mock_temporal_client = AsyncMock()
    with patch(
        "salestech_be.core.email.account.service_v2.get_temporal_client",
        return_value=mock_temporal_client,
    ):
        # Step 2: Unarchive the email account
        unarchive_response = await admin_api_client.api_test_client.client.post(
            f"/api/v2/emails/accounts/{email_account.id}/_unarchive",
            json={},
        )

        assert unarchive_response.status_code == 200

        # Verify the email account is active again
        unarchived_email_account = (
            await email_account_repository.find_by_tenanted_primary_key(
                table_model=EmailAccount,
                organization_id=organization_id,
                id=email_account.id,
            )
        )

        assert unarchived_email_account is not None
        assert unarchived_email_account.active is True
        assert unarchived_email_account.archived_at is None
    assert unarchived_email_account.archived_by_user_id is None

    # Verify the account appears in active accounts list again
    updated_active_accounts = (
        await email_account_repository.find_accounts_by_organization_id(
            organization_id=organization_id,
        )
    )
    assert email_account.id in [acc.id for acc in updated_active_accounts]

    # Verify the email account appears in GET requests as active
    final_get_response = await admin_api_client.api_test_client.client.get(
        f"/api/v2/emails/accounts/{email_account.id}",
    )
    assert final_get_response.status_code == 200
    final_data = final_get_response.json()
    assert final_data["active"] is True

    # Check that the unarchived account still appears in _list endpoint
    unarchived_list_response = await admin_api_client.api_test_client.client.post(
        "/api/v2/emails/accounts/_list",
        json={
            "filter": None,
            "sort": None,
            "page_size": 10,
            "page_number": 1,
        },
    )
    assert unarchived_list_response.status_code == 200
    unarchived_list_data = unarchived_list_response.json()

    # Find our unarchived account in the list results
    unarchived_found = False
    for record in unarchived_list_data["list_data"]:
        if record["data"]["id"] == str(email_account_id):
            unarchived_found = True
            assert record["data"]["active"] is True
            assert record["data"]["archived_at"] is None
            break

    assert unarchived_found, "Unarchived email account not found in list results"

    # Verify that quotas stay the same after unarchiving
    # Verify that the quota usage for the organization's mailbox quota
    mailbox_quota_summary_per_organization = (
        await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )
    )

    assert mailbox_quota_summary_per_organization.total_remaining == 4
    assert mailbox_quota_summary_per_organization.total_used == 1
    assert mailbox_quota_summary_per_organization.total_limit == 5

    # Verify that the quota usage for the user's mailbox quota
    mailbox_quota_summary_per_user = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.MAILBOX,
        period=QuotaPeriod.ANNUAL,
        user_id=user_id,
    )
    assert mailbox_quota_summary_per_user.total_remaining == 1
    assert mailbox_quota_summary_per_user.total_used == 1
    assert mailbox_quota_summary_per_user.total_limit == 2

    # Verify that the quota usage for the plan included mailbox for the organization was not increased by 1
    included_mailbox_quota_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )
    assert (
        included_mailbox_quota_summary.total_remaining == 2
    )  # This value is 2 because the mailbox is a mock record, so it's not included in the plan-included-cost-mailbox quota
    assert included_mailbox_quota_summary.total_used == 0
    assert included_mailbox_quota_summary.total_limit == 2


async def test_custom_seconds_delay_between_emails(
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    email_account_repository: EmailAccountRepository,
    quota_service: QuotaService,
    outbound_domain_repository: OutboundDomainRepository,
) -> None:
    """Test that the custom seconds_delay_between_emails parameter is honored when creating email accounts."""
    admin_api_client = multi_api_client["admin"]
    organization_id = admin_api_client.default_organization_id
    user_id = admin_api_client.default_user_id

    # First, create a mock domain using the buy domains endpoint
    email_domain = str(uuid.uuid4()) + ".com"
    buy_domain_request = {
        "domains": [email_domain],
        "forward_to_domain": None,
        "is_mock_record": True,
    }

    # Call the buy domains endpoint
    buy_domains_response = await admin_api_client.api_test_client.client.post(
        "/api/v1/emails/outbound/domains/_buy",
        json=buy_domain_request,
    )

    # Verify the domain was created
    assert buy_domains_response.status_code == 200
    domains_data = buy_domains_response.json()
    assert len(domains_data) > 0
    domain_id = UUID(domains_data[0]["id"])

    # We are now pending on purchase until domain health is properly configured, force active for testing
    await outbound_domain_repository.update_outbound_domain(
        domain_id, {"status": OutboundDomainStatus.ACTIVE}
    )

    # Create an email account with a custom seconds_delay_between_emails value
    custom_delay = 120  # 2 minutes
    create_request = CreateEmailAccountRequest(
        owner_user_id=user_id,
        email=f"custom_delay@{email_domain}",
        first_name="Custom",
        last_name="Delay",
        reply_to_email=f"reply@{email_domain}",
        warmup_limit=10,
        daily_quota=40,
        seconds_delay_between_emails=custom_delay,  # Set custom delay
        email_account_pool_ids=None,
        is_in_default_pool=False,
        mock_config_params=MockEmailAccountConfigParams(mock_purchase=True),
        outbound_domain_id=domain_id,
    )

    # Call the creation endpoint
    create_response = await admin_api_client.api_test_client.client.post(
        "/api/v2/emails/accounts",
        json=create_request.model_dump(mode="json"),
    )

    # Verify the creation response
    assert create_response.status_code == 200
    created_data = create_response.json()

    # Verify that the quota usage for the organization's mailbox quota
    mailbox_quota_summary_per_organization = (
        await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )
    )

    assert mailbox_quota_summary_per_organization.total_remaining == 4
    assert mailbox_quota_summary_per_organization.total_used == 1
    assert mailbox_quota_summary_per_organization.total_limit == 5

    # Verify that the quota usage for the user's mailbox quota
    mailbox_quota_summary_per_user = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.MAILBOX,
        period=QuotaPeriod.ANNUAL,
        user_id=user_id,
    )
    assert mailbox_quota_summary_per_user.total_remaining == 1
    assert mailbox_quota_summary_per_user.total_used == 1
    assert mailbox_quota_summary_per_user.total_limit == 2

    # Verify that the quota usage for the plan included mailbox for the organization was not increased by 1
    included_mailbox_quota_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )
    assert (
        included_mailbox_quota_summary.total_remaining == 2
    )  # This value is 2 because the mailbox is a mock record, so it's not included in the plan-included-cost-mailbox quota
    assert included_mailbox_quota_summary.total_used == 0
    assert included_mailbox_quota_summary.total_limit == 2

    email_account_id = UUID(created_data["id"])

    # Retrieve the email account from the database to verify the seconds_delay_between_emails value
    db_email_account = await email_account_repository._find_unique_by_column_values(
        EmailAccount,
        id=email_account_id,
        organization_id=organization_id,
    )

    # Verify the custom delay was saved correctly
    assert db_email_account is not None
    assert db_email_account.seconds_delay_between_emails == custom_delay
    assert db_email_account.transaction_type == AccountsReceivable.INVOICED_TO_CUSTOMER

    # Create another email account with the default seconds_delay_between_emails value
    default_create_request = CreateEmailAccountRequest(
        owner_user_id=user_id,
        email=f"default_delay@{email_domain}",
        first_name="Default",
        last_name="Delay",
        reply_to_email=f"reply2@{email_domain}",
        warmup_limit=10,
        daily_quota=40,
        # seconds_delay_between_emails not specified, should use default 600
        email_account_pool_ids=None,
        is_in_default_pool=False,
        mock_config_params=MockEmailAccountConfigParams(mock_purchase=True),
        outbound_domain_id=domain_id,
    )

    # Call the creation endpoint
    default_create_response = await admin_api_client.api_test_client.client.post(
        "/api/v2/emails/accounts",
        json=default_create_request.model_dump(mode="json"),
    )

    # Verify the creation response
    assert default_create_response.status_code == 200
    default_created_data = default_create_response.json()
    default_email_account_id = UUID(default_created_data["id"])

    # Retrieve the email account from the database
    default_db_email_account = (
        await email_account_repository._find_unique_by_column_values(
            EmailAccount,
            id=default_email_account_id,
            organization_id=organization_id,
        )
    )

    # Verify the default delay was used
    assert default_db_email_account is not None
    assert default_db_email_account.seconds_delay_between_emails == 600  # Default value


async def test_email_account_permissions(
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    outbound_workspace_repository: OutboundWorkspaceRepository,
    quota_policy_service: QuotaPolicyService,
    email_account_service_v2: EmailAccountServiceV2,
    quota_service: QuotaService,
    outbound_domain_repository: OutboundDomainRepository,
) -> None:
    """
    Test that the DomainService permission checks work correctly for email accounts.

    This test verifies:
    1. Admin users can access and update any email account
    2. Regular users can access and update their own email accounts
    3. Regular users cannot read other regular users' email accounts (read restrictions)
    4. Regular users cannot update other regular users' email accounts
    5. Regular users cannot update admin-owned email accounts
    """
    # Patch settings to enable mailbox permissions and add required permissions to users
    with patch("salestech_be.settings.settings.enable_mailbox_perms", True):
        # Setup test data with three users
        admin_api_client = multi_api_client["admin"]
        user1_api_client = multi_api_client["user1"]
        user2_api_client = multi_api_client["user2"]

        # Add email account permissions to user1
        await user1_api_client.set_permissions(
            [
                "read:email_account",
                "update:email_account",
                "delete:email_account",
            ]
        )

        # Add email account permissions to user2
        await user2_api_client.set_permissions(
            [
                "read:email_account",
                "update:email_account",
                "delete:email_account",
            ]
        )

        admin_organization_id = admin_api_client.default_organization_id
        admin_user_id = admin_api_client.default_user_id
        user1_id = user1_api_client.default_user_id
        user2_id = user2_api_client.default_user_id

        # Create a mock workspace
        await create_outbound_workspace(
            outbound_workspace_repository=outbound_workspace_repository,
            organization_id=admin_organization_id,
        )

        # Create a mailbox purchase quota policy
        await create_mailbox_purchase_quota_policy(
            quota_policy_service=quota_policy_service,
            organization_id=admin_organization_id,
            user_id=admin_user_id,
        )

        # First, create a mock domain using the buy domains endpoint
        email_domain = str(uuid.uuid4()) + ".com"
        buy_domain_request = {
            "domains": [email_domain],
            "forward_to_domain": None,
            "is_mock_record": True,
        }

        # Call the buy domains endpoint
        buy_domains_response = await admin_api_client.api_test_client.client.post(
            "/api/v1/emails/outbound/domains/_buy",
            json=buy_domain_request,
        )

        # Verify the domain was created
        assert buy_domains_response.status_code == 200
        domains_data = buy_domains_response.json()
        assert len(domains_data) > 0
        domain_id = UUID(domains_data[0]["id"])

        # We are now pending on purchase until domain health is properly configured which won't happen in this test because it's in the workflow which doesn't properly run in tests, force active for testing
        await outbound_domain_repository.update_outbound_domain(
            domain_id, {"status": OutboundDomainStatus.ACTIVE}
        )

        # Create an email account owned by user1
        user1_create_request = CreateEmailAccountRequest(
            owner_user_id=user1_id,
            email=f"user1@{email_domain}",
            first_name="User1",
            last_name="Test",
            reply_to_email=f"reply1@{email_domain}",
            warmup_limit=10,
            daily_quota=40,
            email_account_pool_ids=None,
            is_in_default_pool=False,
            mock_config_params=MockEmailAccountConfigParams(mock_purchase=True),
            outbound_domain_id=domain_id,
        )

        # Call the creation endpoint as user1 to create user1's email account, should fail since only admin can create email accounts
        user1_create_response = await user1_api_client.api_test_client.client.post(
            "/api/v2/emails/accounts",
            json=user1_create_request.model_dump(mode="json"),
        )
        assert user1_create_response.status_code == 403
        # Note no quota is made since this request fails before the quota is checked

        # Call the creation endpoint as admin to create user1's email account
        user1_create_response = await admin_api_client.api_test_client.client.post(
            "/api/v2/emails/accounts",
            json=user1_create_request.model_dump(mode="json"),
        )

        # Verify the creation response
        assert user1_create_response.status_code == 200
        user1_created_data = user1_create_response.json()

        # Verify that the quota usage for the organization's mailbox quota
        mailbox_quota_summary_per_organization = (
            await quota_service.get_quota_summary_per_resource(
                organization_id=admin_organization_id,
                resource=QuotaConsumingResource.MAILBOX,
                period=QuotaPeriod.ANNUAL,
            )
        )

        assert mailbox_quota_summary_per_organization.total_remaining == 4
        assert mailbox_quota_summary_per_organization.total_used == 1
        assert mailbox_quota_summary_per_organization.total_limit == 5

        # Verify that the quota usage for the user's mailbox quota
        mailbox_quota_summary_per_user = (
            await quota_service.get_quota_summary_per_resource(
                organization_id=admin_organization_id,
                resource=QuotaConsumingResource.MAILBOX,
                period=QuotaPeriod.ANNUAL,
                user_id=user1_id,
            )
        )
        assert mailbox_quota_summary_per_user.total_remaining == 1
        assert mailbox_quota_summary_per_user.total_used == 1
        assert mailbox_quota_summary_per_user.total_limit == 2

        # Verify that the quota usage for the plan included mailbox for the organization was not increased by 1
        included_mailbox_quota_summary = (
            await quota_service.get_quota_summary_per_resource(
                organization_id=admin_organization_id,
                resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
                period=QuotaPeriod.ANNUAL,
            )
        )
        assert (
            included_mailbox_quota_summary.total_remaining == 2
        )  # This value is 2 because the mailbox is a mock record, so it's not included in the plan-included-cost-mailbox quota
        assert included_mailbox_quota_summary.total_used == 0
        assert included_mailbox_quota_summary.total_limit == 2

        user1_email_account_id = UUID(user1_created_data["id"])

        # Create an email account owned by user2
        user2_create_request = CreateEmailAccountRequest(
            owner_user_id=user2_id,
            email=f"user2@{email_domain}",
            first_name="User2",
            last_name="Test",
            reply_to_email=f"reply2@{email_domain}",
            warmup_limit=10,
            daily_quota=40,
            email_account_pool_ids=None,
            is_in_default_pool=False,
            mock_config_params=MockEmailAccountConfigParams(mock_purchase=True),
            outbound_domain_id=domain_id,
        )

        # Call the creation endpoint as admin to create user2's email account
        user2_create_response = await admin_api_client.api_test_client.client.post(
            "/api/v2/emails/accounts",
            json=user2_create_request.model_dump(mode="json"),
        )

        # Verify the creation response
        assert user2_create_response.status_code == 200
        user2_created_data = user2_create_response.json()

        # Verify that the quota usage for the organization's mailbox quota
        mailbox_quota_summary_per_organization = (
            await quota_service.get_quota_summary_per_resource(
                organization_id=admin_organization_id,
                resource=QuotaConsumingResource.MAILBOX,
                period=QuotaPeriod.ANNUAL,
            )
        )

        assert mailbox_quota_summary_per_organization.total_remaining == 3
        assert mailbox_quota_summary_per_organization.total_used == 2
        assert mailbox_quota_summary_per_organization.total_limit == 5

        # Verify that the quota usage for the user's mailbox quota
        mailbox_quota_summary_per_user = (
            await quota_service.get_quota_summary_per_resource(
                organization_id=admin_organization_id,
                resource=QuotaConsumingResource.MAILBOX,
                period=QuotaPeriod.ANNUAL,
                user_id=user2_id,
            )
        )
        assert mailbox_quota_summary_per_user.total_remaining == 1
        assert mailbox_quota_summary_per_user.total_used == 1
        assert mailbox_quota_summary_per_user.total_limit == 2

        # Verify that the quota usage for the plan included mailbox for the organization was not increased by 1
        included_mailbox_quota_summary = (
            await quota_service.get_quota_summary_per_resource(
                organization_id=admin_organization_id,
                resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
                period=QuotaPeriod.ANNUAL,
            )
        )
        assert (
            included_mailbox_quota_summary.total_remaining == 2
        )  # This value is 2 because the mailbox is a mock record, so it's not included in the plan-included-cost-mailbox quota
        assert included_mailbox_quota_summary.total_used == 0
        assert included_mailbox_quota_summary.total_limit == 2

        user2_email_account_id = UUID(user2_created_data["id"])

        # Test 1: User1 can access their own email account
        user1_get_response = await user1_api_client.api_test_client.client.get(
            f"/api/v2/emails/accounts/{user1_email_account_id}",
        )
        assert user1_get_response.status_code == 200

        # Test 2: User1 can update their own email account
        user1_update_payload = {
            "first_name": "Updated",
            "last_name": "Name",
        }
        user1_update_response = await user1_api_client.api_test_client.client.patch(
            f"/api/v2/emails/accounts/{user1_email_account_id}",
            json=user1_update_payload,
        )
        assert user1_update_response.status_code == 200
        assert user1_update_response.json()["first_name"] == "Updated"

        # Test 3: Admin can access any email account (even if not the owner)
        admin_get_response = await admin_api_client.api_test_client.client.get(
            f"/api/v2/emails/accounts/{user1_email_account_id}",
        )
        assert admin_get_response.status_code == 200

        # Test 4: Admin can update any email account (even if not the owner)
        admin_update_payload = {
            "first_name": "Admin",
            "last_name": "Updated",
        }
        admin_update_response = await admin_api_client.api_test_client.client.patch(
            f"/api/v2/emails/accounts/{user1_email_account_id}",
            json=admin_update_payload,
        )
        assert admin_update_response.status_code == 200
        assert admin_update_response.json()["first_name"] == "Admin"

        # Test 5: User2 can access their own email account
        user2_get_response = await user2_api_client.api_test_client.client.get(
            f"/api/v2/emails/accounts/{user2_email_account_id}",
        )
        assert user2_get_response.status_code == 200

        # Test 5b: User1 should NOT be able to read User2's email account (read restrictions)
        user1_get_user2_response = await user1_api_client.api_test_client.client.get(
            f"/api/v2/emails/accounts/{user2_email_account_id}",
        )
        # We now restrict reading to owner or admin, so this should not be allowed
        assert user1_get_user2_response.status_code in [403, 404]

        # Test 6a: User2 should NOT be able to read User1's email account (read restrictions)
        user2_get_user1_response = await user2_api_client.api_test_client.client.get(
            f"/api/v2/emails/accounts/{user1_email_account_id}",
        )
        # We now restrict reading to owner or admin, so this should not be allowed
        assert user2_get_user1_response.status_code in [403, 404]

        # Test 6b: User1 cannot update User2's email account
        user1_update_user2_payload = {
            "first_name": "Unauthorized",
            "last_name": "Update",
        }
        user1_update_user2_response = (
            await user1_api_client.api_test_client.client.patch(
                f"/api/v2/emails/accounts/{user2_email_account_id}",
                json=user1_update_user2_payload,
            )
        )
        # This should fail with a 403 Forbidden error
        assert user1_update_user2_response.status_code == 403

        # Test 7: User2 cannot update User1's email account
        user2_update_user1_payload = {
            "first_name": "Unauthorized",
            "last_name": "Update",
        }
        user2_update_user1_response = (
            await user2_api_client.api_test_client.client.patch(
                f"/api/v2/emails/accounts/{user1_email_account_id}",
                json=user2_update_user1_payload,
            )
        )
        # This should fail with a 403 Forbidden error
        assert user2_update_user1_response.status_code == 403

        # Create a first email account for the admin owned by the admin
        admin_create_request = CreateEmailAccountRequest(
            owner_user_id=admin_user_id,
            email=f"adminowned@{email_domain}",
            first_name="Admin",
            last_name="Owned",
            reply_to_email=f"adminreply@{email_domain}",
            warmup_limit=10,
            daily_quota=40,
            email_account_pool_ids=None,
            is_in_default_pool=False,
            mock_config_params=MockEmailAccountConfigParams(mock_purchase=True),
            outbound_domain_id=domain_id,
        )

        # Call the creation endpoint as admin
        admin_create_response = await admin_api_client.api_test_client.client.post(
            "/api/v2/emails/accounts",
            json=admin_create_request.model_dump(mode="json"),
        )

        # Verify the creation response
        assert admin_create_response.status_code == 200
        admin_created_data = admin_create_response.json()

        # Verify that the quota usage for the organization's mailbox quota
        mailbox_quota_summary_per_organization = (
            await quota_service.get_quota_summary_per_resource(
                organization_id=admin_organization_id,
                resource=QuotaConsumingResource.MAILBOX,
                period=QuotaPeriod.ANNUAL,
            )
        )

        assert mailbox_quota_summary_per_organization.total_remaining == 2
        assert mailbox_quota_summary_per_organization.total_used == 3
        assert mailbox_quota_summary_per_organization.total_limit == 5

        # Verify that the quota usage for the user's mailbox quota
        mailbox_quota_summary_per_user = (
            await quota_service.get_quota_summary_per_resource(
                organization_id=admin_organization_id,
                resource=QuotaConsumingResource.MAILBOX,
                period=QuotaPeriod.ANNUAL,
                user_id=user2_id,
            )
        )
        assert mailbox_quota_summary_per_user.total_remaining == 1
        assert mailbox_quota_summary_per_user.total_used == 1
        assert mailbox_quota_summary_per_user.total_limit == 2

        # Verify that the quota usage for the plan included mailbox for the organization was not increased by 1
        included_mailbox_quota_summary = (
            await quota_service.get_quota_summary_per_resource(
                organization_id=admin_organization_id,
                resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
                period=QuotaPeriod.ANNUAL,
            )
        )
        assert (
            included_mailbox_quota_summary.total_remaining == 2
        )  # This value is 2 because the mailbox is a mock record, so it's not included in the plan-included-cost-mailbox quota
        assert included_mailbox_quota_summary.total_used == 0
        assert included_mailbox_quota_summary.total_limit == 2

        admin_email_account_id = UUID(admin_created_data["id"])

        # Test 8: User1 should NOT be able to read an admin's email account (read restrictions)
        # (Only owners and admins can read accounts)
        user1_get_admin_response = await user1_api_client.api_test_client.client.get(
            f"/api/v2/emails/accounts/{admin_email_account_id}",
        )
        # We now restrict reading to owner or admin, so this should not be allowed
        assert user1_get_admin_response.status_code in [403, 404]

        # Test 9: User1 cannot update an admin's email account
        user1_update_admin_payload = {
            "first_name": "Unauthorized",
            "last_name": "Update",
        }
        user1_update_admin_response = (
            await user1_api_client.api_test_client.client.patch(
                f"/api/v2/emails/accounts/{admin_email_account_id}",
                json=user1_update_admin_payload,
            )
        )
        # This should fail with a 403 Forbidden error
        assert user1_update_admin_response.status_code == 403

        # Test 10: User2 should NOT be able to read an admin's email account (read restrictions)
        # (Only owners and admins can read accounts)
        user2_get_admin_response = await user2_api_client.api_test_client.client.get(
            f"/api/v2/emails/accounts/{admin_email_account_id}",
        )
        # We now restrict reading to owner or admin, so this should not be allowed
        assert user2_get_admin_response.status_code in [403, 404]

        # Test 11: User2 cannot update an admin's email account
        user2_update_admin_payload = {
            "first_name": "Unauthorized",
            "last_name": "Update",
        }
        user2_update_admin_response = (
            await user2_api_client.api_test_client.client.patch(
                f"/api/v2/emails/accounts/{admin_email_account_id}",
                json=user2_update_admin_payload,
            )
        )
        # This should fail with a 403 Forbidden error
        assert user2_update_admin_response.status_code == 403

        # Test 12: Test direct service layer permission checks
        # Create user auth contexts for testing
        admin_auth_context = UserAuthContext(
            user_id=admin_user_id,
            organization_id=admin_organization_id,
            groups=["admin"],
        )

        user1_auth_context = UserAuthContext(
            user_id=user1_id,
            organization_id=admin_organization_id,
        )

        user2_auth_context = UserAuthContext(
            user_id=user2_id,
            organization_id=admin_organization_id,
        )

        # Test that the service layer correctly enforces permissions
        # Admin can patch any entity
        admin_patch_result = await email_account_service_v2.authed_patch_entity(
            user_auth_context=admin_auth_context,
            entity_id=user1_email_account_id,
            request=PatchEmailAccountRequestV2(first_name="Service Test"),
        )
        assert admin_patch_result.first_name == "Service Test"

        # User1 can patch their own entity
        user1_patch_result = await email_account_service_v2.authed_patch_entity(
            user_auth_context=user1_auth_context,
            entity_id=user1_email_account_id,
            request=PatchEmailAccountRequestV2(first_name="Owner Update"),
        )
        assert user1_patch_result.first_name == "Owner Update"

        # User1 cannot patch an entity they don't own (admin's account)
        with pytest.raises(ForbiddenError):
            await email_account_service_v2.authed_patch_entity(
                user_auth_context=user1_auth_context,
                entity_id=admin_email_account_id,
                request=PatchEmailAccountRequestV2(first_name="Unauthorized"),
            )

        # User1 cannot patch User2's entity
        with pytest.raises(ForbiddenError):
            await email_account_service_v2.authed_patch_entity(
                user_auth_context=user1_auth_context,
                entity_id=user2_email_account_id,
                request=PatchEmailAccountRequestV2(first_name="Unauthorized"),
            )

        # User2 cannot patch User1's entity
        with pytest.raises(ForbiddenError):
            await email_account_service_v2.authed_patch_entity(
                user_auth_context=user2_auth_context,
                entity_id=user1_email_account_id,
                request=PatchEmailAccountRequestV2(first_name="Unauthorized"),
            )

        # Test 13: Test list email accounts endpoint
        # User1 should only see their own account
        user1_list_response = await user1_api_client.api_test_client.client.post(
            "/api/v2/emails/accounts/_list",
            json={
                "filter": None,
                "sort": None,
                "page_size": 10,
                "page_number": 1,
            },
        )
        assert user1_list_response.status_code == 200
        user1_response_data = user1_list_response.json()
        user1_records = user1_response_data["list_data"]
        assert len(user1_records) == 1
        assert user1_records[0]["data"]["id"] == str(user1_email_account_id)

        # Admin should see all 3 accounts (1 owned by user1, 1 owned by user2, 1 owned by admin)
        admin_list_response = await admin_api_client.api_test_client.client.post(
            "/api/v2/emails/accounts/_list",
            json={
                "filter": None,
                "sort": None,
                "page_size": 10,
                "page_number": 1,
            },
        )
        assert admin_list_response.status_code == 200
        admin_response_data = admin_list_response.json()
        admin_records = admin_response_data["list_data"]
        assert len(admin_records) == 3

        # Test 14: Test archiving permissions
        archive_user1_request = ArchiveEmailAccountRequest(
            sequence_handling=EmailAccountArchiveSequenceHandling.STOP
        )
        mock_temporal_client = AsyncMock()
        mock_temporal_client.start_workflow = AsyncMock(return_value=None)
        mock_temporal_client.create_schedule = AsyncMock(return_value=None)
        mock_schedule_handle = AsyncMock()
        mock_schedule_handle.delete = AsyncMock(return_value=None)
        mock_schedule_handle.describe = AsyncMock(return_value=None)
        mock_temporal_client.get_schedule_handle = MagicMock(
            return_value=mock_schedule_handle
        )
        with (
            patch(
                "salestech_be.web.api.email.common.imap_sync_schedule_service.get_temporal_client",
                return_value=mock_temporal_client,
            ),
            patch(
                "salestech_be.core.email.account.service_v2.get_temporal_client",
                return_value=mock_temporal_client,
            ),
        ):
            user1_archive_response = await user1_api_client.api_test_client.client.post(
                f"/api/v2/emails/accounts/{user1_email_account_id}/_archive",
                json=archive_user1_request.model_dump(mode="json"),
            )
        assert user1_archive_response.status_code == 200

        # User1 should now only see their archived account
        user1_list_response = await user1_api_client.api_test_client.client.post(
            "/api/v2/emails/accounts/_list",
            json={
                "filter": None,
                "sort": None,
                "page_size": 10,
                "page_number": 1,
            },
        )
        assert user1_list_response.status_code == 200
        user1_archived_response_data = user1_list_response.json()
        user1_archived_records = user1_archived_response_data["list_data"]
        assert len(user1_archived_records) == 1
        assert user1_archived_records[0]["data"]["id"] == str(user1_email_account_id)
        assert user1_archived_records[0]["data"]["archived_at"] is not None

        # Test 15: Test unarchiving permissions
        user1_unarchive_response = await user1_api_client.api_test_client.client.post(
            f"/api/v2/emails/accounts/{user1_email_account_id}/_unarchive",
        )
        assert user1_unarchive_response.status_code == 200
        # User1 should now see their unarchived account
        user1_unarchived_list_response = (
            await user1_api_client.api_test_client.client.post(
                "/api/v2/emails/accounts/_list",
                json={
                    "filter": None,
                    "sort": None,
                    "page_size": 10,
                    "page_number": 1,
                },
            )
        )
        assert user1_unarchived_list_response.status_code == 200
        user1_unarchived_response_data = user1_unarchived_list_response.json()
        user1_unarchived_records = user1_unarchived_response_data["list_data"]
        assert len(user1_unarchived_records) == 1
        assert user1_unarchived_records[0]["data"]["id"] == str(user1_email_account_id)
        assert user1_unarchived_records[0]["data"]["archived_at"] is None

        # Test 16: Test unallowed archiving permissions
        # User1 should not be able to archive another user's account
        user1_archive_user2_response = (
            await user1_api_client.api_test_client.client.post(
                f"/api/v2/emails/accounts/{user2_email_account_id}/_archive",
                json=archive_user1_request.model_dump(mode="json"),
            )
        )
        assert user1_archive_user2_response.status_code == 403

        # Test 17: Test allowed archiving permissions for admins
        mock_temporal_client = AsyncMock()
        mock_temporal_client.start_workflow = AsyncMock(return_value=None)
        mock_temporal_client.create_schedule = AsyncMock(return_value=None)
        mock_schedule_handle = AsyncMock()
        mock_schedule_handle.delete = AsyncMock(return_value=None)
        mock_schedule_handle.describe = AsyncMock(return_value=None)
        mock_temporal_client.get_schedule_handle = MagicMock(
            return_value=mock_schedule_handle
        )
        with (
            patch(
                "salestech_be.web.api.email.common.imap_sync_schedule_service.get_temporal_client",
                return_value=mock_temporal_client,
            ),
            patch(
                "salestech_be.core.email.account.service_v2.get_temporal_client",
                return_value=mock_temporal_client,
            ),
        ):
            admin_archive_user1_response = (
                await admin_api_client.api_test_client.client.post(
                    f"/api/v2/emails/accounts/{user1_email_account_id}/_archive",
                    json=archive_user1_request.model_dump(mode="json"),
                )
            )
        assert admin_archive_user1_response.status_code == 200

        # Test 18: Test unallowed unarchiving permissions for user2
        user2_unarchive_user1_response = (
            await user2_api_client.api_test_client.client.post(
                f"/api/v2/emails/accounts/{user1_email_account_id}/_unarchive",
            )
        )
        assert user2_unarchive_user1_response.status_code == 403

        # Test 19: Test allowed unarchiving permissions for admins
        admin_unarchive_user1_response = (
            await admin_api_client.api_test_client.client.post(
                f"/api/v2/emails/accounts/{user1_email_account_id}/_unarchive",
            )
        )
        assert admin_unarchive_user1_response.status_code == 200


async def test_send_invoice_on_plan_included_mailbox_limit(
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    outbound_workspace_repository: OutboundWorkspaceRepository,
    quota_policy_service: QuotaPolicyService,
    quota_service: QuotaService,
    outbound_domain_repository: OutboundDomainRepository,
) -> None:
    """Test that slack invoice messages are sent when exceeding the plan-included mailbox limit."""
    # Setup
    admin_api_client = multi_api_client["admin"]
    organization_id = admin_api_client.default_organization_id
    user_id = admin_api_client.default_user_id

    with (
        patch("salestech_be.settings.settings.enable_slack_invoice", True),
        patch(
            "salestech_be.integrations.slack.slack_client.SlackClient.send_message"
        ) as mock_send_message,
    ):
        # Create a workspace
        await create_outbound_workspace(
            outbound_workspace_repository=outbound_workspace_repository,
            organization_id=organization_id,
        )

        # Create a mailbox purchase quota policy
        await create_mailbox_purchase_quota_policy(
            quota_policy_service=quota_policy_service,
            organization_id=organization_id,
            user_id=user_id,
        )

        # Create a user mailbox quota policy
        await create_user_mailbox_quota_policy(
            quota_policy_service=quota_policy_service,
            organization_id=organization_id,
            user_id=user_id,
            quota_limit=5,  # Enough for all our test accounts
        )

        # Create mock domain
        email_domain = str(uuid.uuid4()) + ".com"
        buy_domain_request = {
            "domains": [email_domain],
            "forward_to_domain": None,
            "is_mock_record": True,
        }
        buy_domains_response = await admin_api_client.api_test_client.client.post(
            "/api/v1/emails/outbound/domains/_buy",
            json=buy_domain_request,
        )
        assert buy_domains_response.status_code == 200
        domain_id = UUID(buy_domains_response.json()[0]["id"])

        # Force domain active status
        await outbound_domain_repository.update_outbound_domain(
            domain_id, {"status": OutboundDomainStatus.ACTIVE}
        )

        # Create the first mailbox (1 of 2 included in plan)
        first_account_request = CreateEmailAccountRequest(
            owner_user_id=user_id,
            email=f"first@{email_domain}",
            first_name="First",
            last_name="Account",
            reply_to_email=f"reply1@{email_domain}",
            warmup_limit=10,
            daily_quota=40,
            email_account_pool_ids=None,
            is_in_default_pool=False,
            mock_config_params=MockEmailAccountConfigParams(mock_purchase=True),
            outbound_domain_id=domain_id,
        )
        first_response = await admin_api_client.api_test_client.client.post(
            "/api/v2/emails/accounts",
            json=first_account_request.model_dump(mode="json"),
        )
        assert first_response.status_code == 200
        response_data = first_response.json()
        assert response_data["reply_to_email"] == f"reply1@{email_domain}"

        # Verify slack wasn't called for the first account
        mock_send_message.assert_not_called()

        # Verify quota usage for plan included mailboxes
        quota_after_first = await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )
        assert (
            quota_after_first.total_remaining == 2
        )  # This value is 2 because the mailbox is a mock record, so it's not included in the plan-included-cost-mailbox quota
        assert quota_after_first.total_used == 0
        assert quota_after_first.total_limit == 2

        # Create the second mailbox (2 of 2 included in plan)
        second_account_request = CreateEmailAccountRequest(
            owner_user_id=user_id,
            email=f"second@{email_domain}",
            first_name="Second",
            last_name="Account",
            reply_to_email=f"reply2@{email_domain}",
            warmup_limit=10,
            daily_quota=40,
            email_account_pool_ids=None,
            is_in_default_pool=False,
            mock_config_params=MockEmailAccountConfigParams(mock_purchase=True),
            outbound_domain_id=domain_id,
        )
        second_response = await admin_api_client.api_test_client.client.post(
            "/api/v2/emails/accounts",
            json=second_account_request.model_dump(mode="json"),
        )
        assert second_response.status_code == 200
        response_data = second_response.json()
        assert response_data["reply_to_email"] == f"reply2@{email_domain}"

        # Verify slack wasn't called for the second account either
        mock_send_message.assert_not_called()

        # Verify quota is now at limit
        quota_after_second = await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )
        assert (
            quota_after_second.total_remaining == 2
        )  # This value is 2 because the mailbox is a mock record, so it's not included in the plan-included-cost-mailbox quota
        assert quota_after_second.total_used == 0
        assert quota_after_second.total_limit == 2

        # Create the third mailbox (exceeds plan limit)
        third_account_request = CreateEmailAccountRequest(
            owner_user_id=user_id,
            email=f"third@{email_domain}",
            first_name="Third",
            last_name="Account",
            reply_to_email=f"reply3@{email_domain}",
            warmup_limit=10,
            daily_quota=40,
            email_account_pool_ids=None,
            is_in_default_pool=False,
            mock_config_params=MockEmailAccountConfigParams(mock_purchase=True),
            outbound_domain_id=domain_id,
        )

        await quota_service.increase_usage(
            organization_id=organization_id,
            entity_id=organization_id,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
            usage=2,  # Set it to the limit
            timestamp=zoned_utc_now(),
        )
        third_response = await admin_api_client.api_test_client.client.post(
            "/api/v2/emails/accounts",
            json=third_account_request.model_dump(mode="json"),
        )
        assert third_response.status_code == 200
        response_data = third_response.json()
        assert response_data["reply_to_email"] == f"reply3@{email_domain}"

        # Verify slack was called for the third account
        mock_send_message.assert_called_once()

        # Check the slack message contains expected information
        call_args = mock_send_message.call_args[1]
        assert "third@" in call_args["text"]  # Should contain mailbox name
        assert "Invoice" in call_args["text"]  # Should mention invoice
        assert "$4.00" in call_args["text"]  # Should contain the price ($4.00)
