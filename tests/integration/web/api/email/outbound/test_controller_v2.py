import uuid
from datetime import UTC, datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

import dns.asyncresolver
import pytest
import tldextract
from _pytest.fixtures import fixture
from httpx import AsyncClient

from salestech_be.common.exception.exception import (
    InvalidArgumentError,
    PaymentError,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    OutboundDomainRelationship,
)
from salestech_be.core.common.accounts_receivable import AccountsReceivable
from salestech_be.core.email.outbound_domain.dns_check_service import (
    check_dkim,
    check_dmarc,
    check_mx,
    check_txt,
    is_nxdomain_from_a_record,
    is_nxdomain_from_mx,
    is_nxdomain_from_ns_record,
)
from salestech_be.core.email.outbound_domain.schema import (
    ArchiveDomainRequest,
    BuyDomainRequest,
    DomainBillingCycle,
    DomainPaymentMethod,
    EmailAccountArchiveSequenceHandling,
    FindDomainRequest,
    FindDomainResponse,
)
from salestech_be.core.email.outbound_domain.service import (
    OutboundDomainService,
    contains_special_or_whitespace,
    is_domain_available,
)
from salestech_be.core.quota.service.quota_policy_service import QuotaPolicyService
from salestech_be.core.quota.service.quota_service import QuotaService
from salestech_be.db.dao.email_account import (
    EmailAccountRepository,
)
from salestech_be.db.dao.organization_repository import OrganizationRepository
from salestech_be.db.dao.outbound_repository import (
    DomainHealthRepository,
    OutboundDomainRepository,
    OutboundWorkspaceRepository,
)
from salestech_be.db.dao.quota_repository import QuotaPolicyRepository
from salestech_be.db.dao.sequence_enrollment_repo import SequenceEnrollmentRepository
from salestech_be.db.dao.sequence_repository import SequenceRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.core.types import EntityDnsRecord
from salestech_be.db.models.email_account import (
    EmailAccount,
    EmailAccountType,
    EmailProvider,
)
from salestech_be.db.models.outbound import (
    DomainHealth,
    OutboundDomain,
    OutboundDomainStatus,
    OutboundVendor,
    OutboundWorkspace,
)
from salestech_be.db.models.quota import (
    QuotaConsumerEntityType,
    QuotaConsumingResource,
    QuotaPeriod,
)
from salestech_be.db.models.sequence import (
    SequenceEnrollment,
    SequenceEnrollmentStatus,
    SequenceStatus,
    SequenceV2,
    SequenceV2Schedule,
    SequenceVisibility,
)
from salestech_be.integrations.infraforge.type import (
    ExternalBuyDomainsResponse,
    ExternalCheckDomainAvailabilityResponse,
    ExternalGetDomainsResponse,
    InfraforgeDomainStatus,
    OneTimeDomainPurchaseInvoice,
    SetupResponse,
    SetupStatus,
)
from salestech_be.settings import settings
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from tests.integration.web.api.util.common_api_client import CommonAPIClient


@fixture
async def outbound_domain_service(_engine: DatabaseEngine) -> OutboundDomainService:
    return OutboundDomainService(engine=_engine)


"""
Any test that directly inserts a domain into the database instead of purchasing doesn't affect the quota.
"""


async def create_domain_purchase_quota_policy_of_1_domain(
    quota_policy_service: QuotaPolicyService,
    organization_id: uuid.UUID,
    user_id: uuid.UUID,
) -> None:
    await quota_policy_service.upsert_quota_policy(
        organization_id=organization_id,
        entity_id=organization_id,
        entity_type=QuotaConsumerEntityType.ORGANIZATION,
        resource=QuotaConsumingResource.DOMAIN,
        quota_limit=1,
        period=QuotaPeriod.ANNUAL,
        user_id=user_id,
        applied_sub_entity_types=None,
    )


async def test_find_domain_health_by_domain_id_and_update(
    outbound_workspace_repository: OutboundWorkspaceRepository,
    outbound_domain_repository: OutboundDomainRepository,
    domain_health_repository: DomainHealthRepository,
    organization_repository: OrganizationRepository,
) -> None:
    """
    The upcoming few tests are just relevant for correcting formatting for the domain health tests
    so they don't actually create any domains or anything, which means that quota policies are not
    being tested.
    """
    test_user = uuid.uuid4()
    test_domain = str(uuid.uuid4()) + ".com"
    placeholder_org = (
        await organization_repository.get_or_create_placeholder_organization(
            created_by_user_id=test_user
        )
    )
    # Create workspace based on organization and insert workspace into the repository
    workspace = await outbound_workspace_repository.insert_outbound_workspace(
        name="test workspace",
        vendor=OutboundVendor.INFRAFORGE,
        organization_id=placeholder_org.id,
        external_id="123456",
    )
    assert workspace is not None

    domain = await outbound_domain_repository.insert_outbound_domain(
        organization_id=placeholder_org.id,
        workspace_id=workspace.id,
        created_by_user_id=test_user,
        domain=test_domain,
        external_id="654321",
        status=OutboundDomainStatus.ACTIVE,
        forward_to_domain=None,
        invoice=OneTimeDomainPurchaseInvoice(
            amountPaid=1400,
            creditsApplied=0,
            id="123456789",
            paidAt="2024-03-19T00:00:00Z",
            subTotal=1400,
            total=1400,
        ),
    )
    assert domain is not None

    domain_health = await domain_health_repository.insert_domain_health(
        outbound_domain_id=domain.id,
        mx_records=not_none(
            EntityDnsRecord.list_from_request_field(
                ["10 mx1.reevo-internal.mocked", "20 mx2.reevo-internal.mocked"]
            )
        ),
        spf_records=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=spf1 include:_spf.reevo-internal.mocked ~all"]
            )
        ),  # using .list_from_request_field since they're all lists for consistency
        dkim_record=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=DKIM1; k=rsa; p=ThisIsATotallyMockedDkimPublicKey..."]
            )
        ),
        dmarc_record=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=DMARC1; p=reject; rua=mailto:<EMAIL>"]
            )
        ),
    )
    assert domain_health is not None

    fetched_domain_health = (
        await domain_health_repository.get_domain_health_by_outbound_domain_id(
            outbound_domain_id=domain.id
        )
    )
    assert fetched_domain_health is not None

    assert fetched_domain_health.domain_id == domain_health.domain_id
    assert fetched_domain_health.spf_records == domain_health.spf_records
    assert len(fetched_domain_health.mx_records) == len(domain_health.mx_records)
    assert fetched_domain_health.mx_records[0] in domain_health.mx_records
    assert fetched_domain_health.mx_records[1] in domain_health.mx_records
    updated_domain_health = await domain_health_repository.update_domain_health(
        outbound_domain_id=domain.id,
        column_to_update={
            "mx_records": EntityDnsRecord.list_from_request_field(
                ["20 mx1.reevo.ai", "10 mx2.reevo.ai"]
            ),
        },
    )
    assert updated_domain_health is not None
    assert updated_domain_health.domain_id == domain_health.domain_id
    assert EntityDnsRecord(record="10 mx2.reevo.ai") in updated_domain_health.mx_records

    assert EntityDnsRecord(record="20 mx1.reevo.ai") in updated_domain_health.mx_records
    assert (
        EntityDnsRecord(record="10 mx1.reevo.ai")
        not in updated_domain_health.mx_records
    )  # non updated records should not be present


async def test_spf_record_checks() -> None:
    test_domain = "tryreevo.com."
    mock_answer = MagicMock()
    mock_answer.to_text.return_value = "v=spf1 ip4:************* -all"
    with (
        patch(
            "salestech_be.core.email.outbound_domain.dns_check_service.resolver.resolve",
            return_value=[mock_answer],
        ),
    ):
        spf_record = await check_txt(test_domain, "v=spf1")
        assert len(spf_record) == 1, "SPF record should be present"
        assert "v=spf1 ip4:************* -all" in spf_record, (
            "Expected SPF version not found"
        )


async def test_dmarc_record_checks() -> None:
    test_domain = "tryreevo.com."
    mock_answer = MagicMock()
    mock_answer.to_text.return_value = "v=DMARC1;p=quarantine;sp=quarantine;pct=100;ri=86400;aspf=s;adkim=s;fo=1;rua=mailto:<EMAIL>;ruf=mailto:<EMAIL>"
    with (
        patch(
            "salestech_be.core.email.outbound_domain.dns_check_service.resolver.resolve",
            return_value=[mock_answer],
        ),
    ):
        dmarc_record = await check_dmarc(test_domain)
        assert len(dmarc_record) == 1, "DMARC record should be present"
        assert (
            "v=DMARC1;p=quarantine;sp=quarantine;pct=100;ri=86400;aspf=s;adkim=s;fo=1;rua=mailto:<EMAIL>;ruf=mailto:<EMAIL>"
            in dmarc_record
        ), "Expected DMARC version not found"


async def test_mx_record_checks() -> None:
    test_domain = "tryreevo.com."
    mock_answer = MagicMock()
    mock_answer.to_text.return_value = "1 iris.secured-shield.com."
    with (
        patch(
            "salestech_be.core.email.outbound_domain.dns_check_service.resolver.resolve",
            return_value=[mock_answer],
        ),
    ):
        mx_record = await check_mx(test_domain)
        assert len(mx_record) > 0, "MX record should be present"
        assert "1 iris.secured-shield.com." in mx_record, (
            "Expected MX version not found"
        )


async def test_dkim_record_checks() -> None:
    test_domain = "tryreevo.com."
    mock_answer = MagicMock()
    mock_answer.to_text.return_value = "v=DKIM1; h=sha256; k=rsa;p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvXfLNyJK44lnt0SeU7iIJoYGhB7gXUiBTj7eaugrQ+mZK1heqIRwhLpeae1JRdU3HUREPS9Mnuzsc7F4q793XR84rgYIKdNyvBhlXbt4vZFOL2qC3NPrSxQv2rPN2ooEGw/vIDxv2anqr6jpKPaDc+7rn9YoEF8bPCrZKIWeAXZvfUj9n8MvinhGCUXlBUoHAXXakp7cICO7jj9b8rO2pooWV2MZK03GtmiRBF9/HMBVz/PMdN9gfJW9pCT9JULxgGdQaKnQvxAVCxfDwUNZWKDrr11OmKsRhaf/1xMXhObp+EIvYUrn3hc/yvsHDgrIWXcHwuR8ZezGVxNPS1iYrwIDAQAB"
    with (
        patch(
            "salestech_be.core.email.outbound_domain.dns_check_service.resolver.resolve",
            return_value=[mock_answer],
        ),
    ):
        dkim_record = await check_dkim(test_domain, "default")
        assert len(dkim_record) == 1, "DKIM record should be present"
        assert (
            "v=DKIM1; h=sha256; k=rsa;p=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvXfLNyJK44lnt0SeU7iIJoYGhB7gXUiBTj7eaugrQ+mZK1heqIRwhLpeae1JRdU3HUREPS9Mnuzsc7F4q793XR84rgYIKdNyvBhlXbt4vZFOL2qC3NPrSxQv2rPN2ooEGw/vIDxv2anqr6jpKPaDc+7rn9YoEF8bPCrZKIWeAXZvfUj9n8MvinhGCUXlBUoHAXXakp7cICO7jj9b8rO2pooWV2MZK03GtmiRBF9/HMBVz/PMdN9gfJW9pCT9JULxgGdQaKnQvxAVCxfDwUNZWKDrr11OmKsRhaf/1xMXhObp+EIvYUrn3hc/yvsHDgrIWXcHwuR8ZezGVxNPS1iYrwIDAQAB"
            in dkim_record
        ), "Expected DKIM record not found"


async def test_insert_domain_health_spf_only_with_dmarc_update(
    outbound_workspace_repository: OutboundWorkspaceRepository,
    outbound_domain_repository: OutboundDomainRepository,
    domain_health_repository: DomainHealthRepository,
    organization_repository: OrganizationRepository,
) -> None:
    # Testing domain health creation with only SPF to make sure that we handle unhealthy domains that aren't completely configured correctly
    test_user = uuid.uuid4()
    test_domain = str(uuid.uuid4()) + ".com"
    placeholder_org = (
        await organization_repository.get_or_create_placeholder_organization(
            created_by_user_id=test_user
        )
    )

    workspace = await outbound_workspace_repository.insert_outbound_workspace(
        name="test workspace",
        vendor=OutboundVendor.INFRAFORGE,
        organization_id=placeholder_org.id,
        external_id="123456",
    )
    assert workspace is not None

    domain = await outbound_domain_repository.insert_outbound_domain(
        organization_id=placeholder_org.id,
        workspace_id=workspace.id,
        created_by_user_id=test_user,
        domain=test_domain,
        external_id="654321",
        status=OutboundDomainStatus.ACTIVE,
        forward_to_domain=None,
        invoice=OneTimeDomainPurchaseInvoice(
            amountPaid=1400,
            creditsApplied=0,
            id="654321",
            paidAt="2024-03-19T00:00:00Z",
            subTotal=1400,
            total=1400,
        ),
    )
    assert domain is not None

    domain_health = await domain_health_repository.insert_domain_health(
        outbound_domain_id=domain.id,
        mx_records=[],
        spf_records=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=spf1 include:_spf.reevo.ai ~all"]
            )
        ),
        dkim_record=[],
        dmarc_record=[],
    )
    assert domain_health is not None

    fetched_domain_health = (
        await domain_health_repository.get_domain_health_by_outbound_domain_id(
            outbound_domain_id=domain.id
        )
    )
    assert fetched_domain_health is not None

    assert fetched_domain_health.domain_id == domain_health.domain_id
    assert fetched_domain_health.spf_records == domain_health.spf_records
    # Making sure that we don't have any other DNS records
    assert len(fetched_domain_health.mx_records) == 0
    assert len(fetched_domain_health.dkim_record) == 0
    assert len(fetched_domain_health.dmarc_record) == 0

    # Change SPF and make sure that we can update a formerly empty field (UNHEALTHY can turn HEALTHY)
    updated_domain_health = await domain_health_repository.update_domain_health(
        outbound_domain_id=domain.id,
        column_to_update={
            "spf_records": EntityDnsRecord.list_from_request_field(
                ["v=spf1 include:_spf.google.com ~all"]
            ),
            "dmarc_record": EntityDnsRecord.list_from_request_field(
                [
                    "v=DMARC1;p=quarantine;sp=quarantine;pct=100;ri=86400;aspf=s;adkim=s;fo=1;rua=mailto:<EMAIL>;ruf=mailto:<EMAIL>"
                ]
            ),
        },
    )
    assert updated_domain_health is not None
    assert updated_domain_health.domain_id == domain_health.domain_id
    assert updated_domain_health.spf_records == [
        EntityDnsRecord(record="v=spf1 include:_spf.google.com ~all")
    ]
    assert updated_domain_health.dmarc_record == [
        EntityDnsRecord(
            record="v=DMARC1;p=quarantine;sp=quarantine;pct=100;ri=86400;aspf=s;adkim=s;fo=1;rua=mailto:<EMAIL>;ruf=mailto:<EMAIL>"
        )
    ]


async def test_buy_domain_v2(
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    outbound_domain_repository: OutboundDomainRepository,
    outbound_workspace_repository: OutboundWorkspaceRepository,
    domain_health_repository: DomainHealthRepository,
    quota_policy_service: QuotaPolicyService,
    quota_service: QuotaService,
    quota_policy_repository: QuotaPolicyRepository,
    outbound_domain_service: OutboundDomainService,
) -> None:
    admin_api_client = multi_api_client["admin"]
    organization_id = admin_api_client.default_organization_id
    test_domain = str(uuid.uuid4())
    test_tld = "com"

    # Create and insert a workspace into the database
    workspace = await outbound_workspace_repository.insert_outbound_workspace(
        organization_id=organization_id,
        name="Test Workspace",
        vendor=OutboundVendor.INFRAFORGE,
        external_id="ws_123456789",
    )
    assert workspace is not None

    # Create a domain purchase quota policy of 1 domain since we are testing the buy domain endpoint with limit testing
    # Understand that the actual quota limit is 3, but we are testing the limit of 1 domain since it may change and I want to keep the test consistent
    await create_domain_purchase_quota_policy_of_1_domain(
        quota_policy_service=quota_policy_service,
        organization_id=organization_id,
        user_id=admin_api_client.default_user_id,
    )

    # Don't need quota policy upsert since we now create a policy if there isn't one existing on a purchase

    # Create request data
    buy_domain_request = BuyDomainRequest(
        domains=[test_domain + "." + test_tld], forward_to_domain="https://reevo.ai"
    )

    with (
        # patch(
        #     "salestech_be.core.email.outbound_domain.service.is_prod_env",
        #     return_value=True,
        # ),
        patch(
            "salestech_be.core.email.outbound_domain.service.OutboundDomainService.get_or_create_workspace",
            new_callable=AsyncMock,
        ) as mock_check_workspace,
        patch(
            "salestech_be.integrations.infraforge.async_infraforge_client.AsyncInfraForgeClient.check_domain_availability",
            new_callable=AsyncMock,
        ) as mock_check_availability,
        patch(
            "salestech_be.integrations.infraforge.async_infraforge_client.AsyncInfraForgeClient.buy_domains",
            new_callable=AsyncMock,
        ) as mock_buy_domains,
        patch(
            "salestech_be.core.email.outbound_domain.dns_check_service.check_mx",
            new_callable=AsyncMock,
        ) as mock_check_mx,
        patch(
            "salestech_be.core.email.outbound_domain.dns_check_service.check_txt",
            new_callable=AsyncMock,
        ) as mock_check_txt,
        patch(
            "salestech_be.core.email.outbound_domain.dns_check_service.check_dmarc",
            new_callable=AsyncMock,
        ) as mock_check_dmarc,
        patch(
            "salestech_be.core.email.outbound_domain.dns_check_service.check_dkim",
            new_callable=AsyncMock,
        ) as mock_check_dkim,
    ):
        # Configure the mocks
        mock_check_workspace.return_value = workspace
        mock_check_availability.return_value = ExternalCheckDomainAvailabilityResponse(
            available=True,
            domain=test_domain + "." + test_tld,
            price=10.0,
        )

        # Create mock response data
        invoice = OneTimeDomainPurchaseInvoice(
            id="inv_123456789",
            amountPaid=1000,
            creditsApplied=0,
            paidAt="2024-03-19T00:00:00Z",
            subTotal=1000,
            total=1000,
        )

        domain = ExternalGetDomainsResponse(
            id="dom_123456789",
            createdAt="2024-03-19T00:00:00Z",
            sld=test_domain,
            status=InfraforgeDomainStatus.ACTIVE,
            tld=test_tld,
            updatedAt="2024-03-19T00:00:00Z",
            workspaceId="ws_123456789",
            setupId="testing_setup_id",
        )

        setup = SetupResponse(
            accountId="acc_123456789",
            address1="2445 Augustine Dr",
            address2="Suite 500",
            city="Santa Clara",
            country="US",
            createdAt="2024-03-19T00:00:00Z",
            email="<EMAIL>",
            firstName="Curtis",
            forwardToDomain="https://reevo.ai",
            id="setup_123456789",
            lastName="Tan",
            organization="Reevo",
            phone="*************",
            postalCode="95054",
            province="CA",
            status=SetupStatus.COMPLETED,
            updatedAt="2024-03-19T00:00:00Z",
            dmarcEmail="<EMAIL>",
            jobTitle=None,
            workspaceId="ws_123456789",
        )

        mock_buy_domains.return_value = ExternalBuyDomainsResponse(
            invoice=invoice, setup=setup, domains=[domain]
        )

        mock_check_mx.return_value = ["10 mx1.reevo.ai", "20 mx2.reevo.ai"]
        mock_check_txt.return_value = ["v=spf1 include:_spf.reevo.ai ~all"]
        mock_check_dmarc.return_value = [
            "v=DMARC1; p=reject; rua=mailto:<EMAIL>"
        ]
        mock_check_dkim.return_value = [
            "v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4..."
        ]

        purchasable_domains = await outbound_domain_service.validate_domain_request_and_get_purchasable_domains(
            request=buy_domain_request,
            user_id=admin_api_client.default_user_id,
            organization_id=organization_id,
            workspace=workspace,
        )

        response = await outbound_domain_service.purchase_and_process_domains(
            request=buy_domain_request,
            user_id=admin_api_client.default_user_id,
            organization_id=organization_id,
            purchasable_domains=purchasable_domains,
        )
        assert response is not None
        await outbound_domain_service.perform_domain_health_checks(response)

        assert response is not None
        assert response.domain == test_domain + "." + test_tld
        assert response.status == OutboundDomainStatus.ACTIVE
        assert response.transaction_type == AccountsReceivable.INCLUDED_IN_PLAN

        # Insert another domain into test database
        test_domain_1 = str(uuid.uuid4()) + ".com"
        domain1 = await outbound_domain_repository.insert_outbound_domain(
            organization_id=organization_id,
            workspace_id=workspace.id,
            domain=test_domain_1,
            external_id="dom_123456789",
            status=OutboundDomainStatus.ACTIVE,
            created_by_user_id=admin_api_client.default_user_id,
            forward_to_domain="https://reevo.ai",
            invoice=invoice,
        )
        assert domain1 is not None

        # Verify quota policy was created since we made a mock workspace
        quota_policy = await quota_policy_repository.get_entity_relevant_policies(
            organization_id=organization_id,
            entity_id=organization_id,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            resource=QuotaConsumingResource.DOMAIN,
            period=QuotaPeriod.ANNUAL,
        )
        assert (
            len(quota_policy) == 1
        )  # There should be a created quota_policy since we are upserting in both buy and create workspace

        quota_policy_summary = await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_DOMAIN,
            period=QuotaPeriod.ANNUAL,
        )
        assert quota_policy_summary.total_remaining == 0
        assert quota_policy_summary.total_used == 1

        # Assure that the Reevo-Plan included domain quota policy incremented by 1
        quota_policy_summary = await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_DOMAIN,
            period=QuotaPeriod.ANNUAL,
        )
        assert quota_policy_summary.total_used == 1
        assert quota_policy_summary.total_limit == 1

        # Verify domain was created in database
        domain_id = response.id
        saved_domain = (
            await outbound_domain_repository.get_domain_by_id_and_workspace_id(
                outbound_domain_id=domain_id, workspace_id=workspace.id
            )
        )
        assert saved_domain is not None
        assert saved_domain.domain == test_domain + "." + test_tld
        assert saved_domain.status == OutboundDomainStatus.ACTIVE
        assert saved_domain.external_id == "dom_123456789"
        assert saved_domain.forward_to_domain == "https://reevo.ai"

        # Verify domain health record was created
        domain_health = (
            await domain_health_repository.get_domain_health_by_outbound_domain_id(
                outbound_domain_id=domain_id
            )
        )
        assert domain_health is not None
        assert EntityDnsRecord(record="10 mx1.reevo.ai") in domain_health.mx_records
        assert EntityDnsRecord(record="20 mx2.reevo.ai") in domain_health.mx_records
        assert domain_health.spf_records == [
            EntityDnsRecord(record="v=spf1 include:_spf.reevo.ai ~all")
        ]
        assert domain_health.dkim_record == [
            EntityDnsRecord(record="v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4...")
        ]
        assert domain_health.dmarc_record == [
            EntityDnsRecord(record="v=DMARC1; p=reject; rua=mailto:<EMAIL>")
        ]

        buy_domain_request = BuyDomainRequest(
            domains=[str(uuid.uuid4()) + ".com"], forward_to_domain="https://reevo.ai"
        )
        with pytest.raises(PaymentError):
            await outbound_domain_service.validate_domain_request_and_get_purchasable_domains(
                request=buy_domain_request,
                user_id=admin_api_client.default_user_id,
                organization_id=organization_id,
                workspace=workspace,
            )
            await outbound_domain_service.purchase_and_process_domains(
                request=buy_domain_request,
                user_id=admin_api_client.default_user_id,
                organization_id=organization_id,
                purchasable_domains=purchasable_domains,
            )

        # Test to see that our next find on purchased domain returns that the domain we just bought is purchased
        mock_new_tlds = [
            FindDomainResponse(
                domain="example.com",
                billing_cycle=DomainBillingCycle.ANNUALLY,
                available=True,
                price_cents=1099,
            ),
            FindDomainResponse(
                domain="example.gov",
                billing_cycle=DomainBillingCycle.ANNUALLY,
                available=True,
                price_cents=1099,
            ),
        ]
        mock_alt_domains = [
            FindDomainResponse(
                domain=f"alternative{i}.com",
                billing_cycle=DomainBillingCycle.ANNUALLY,
                available=True,
                price_cents=1099,
            )
            for i in range(4)
        ]
        with (
            patch.object(
                outbound_domain_service,
                "find_new_tlds",
                AsyncMock(return_value=mock_new_tlds),
            ),
            patch.object(
                outbound_domain_service,
                "generate_alternative_domains_with_prefixes",
                AsyncMock(return_value=mock_alt_domains),
            ),
            patch("dns.asyncresolver.resolve", side_effect=dns.asyncresolver.NXDOMAIN),
        ):
            request = FindDomainRequest(
                query=test_domain + "." + test_tld,
                suggest_alternative=10,
            )

        results = await outbound_domain_service.find_domains(
            request=request,
            user_id=uuid.uuid4(),
            organization_id=uuid.uuid4(),
        )

        # Verify results
        assert results is not None
        assert (
            FindDomainResponse(
                domain=test_domain + "." + test_tld,
                available=False,
                price_cents=0,
                billing_cycle=DomainBillingCycle.ANNUALLY,
            )
            in results
        )


async def test_tld_extract() -> None:
    extracted = tldextract.extract("example")
    sld = extracted.domain
    tld = extracted.suffix
    assert sld == "example"
    assert tld == ""

    extracted = tldextract.extract("example.com")
    sld = extracted.domain
    tld = extracted.suffix
    assert sld == "example"
    assert tld == "com"

    extracted = tldextract.extract("google")
    sld = extracted.domain
    tld = extracted.suffix
    assert sld == ""
    assert tld == "google"

    extracted = tldextract.extract("abc.zra")
    sld = extracted.domain
    tld = extracted.suffix
    subdomain = extracted.subdomain
    assert sld == "zra"
    assert tld == ""
    assert subdomain == "abc"


async def test_find_domains(
    outbound_domain_service: OutboundDomainService,
    multi_api_client: dict[str, CommonAPIClient],
) -> None:
    # Mock find_new_tlds to return some test domains
    admin_api_client = multi_api_client["admin"]
    organization_id = admin_api_client.default_organization_id
    user_id = admin_api_client.default_user_id
    mock_new_tlds = [
        FindDomainResponse(
            domain="example.com",
            billing_cycle=DomainBillingCycle.ANNUALLY,
            available=True,
            price_cents=1099,
        ),
        FindDomainResponse(
            domain="example.gov",
            billing_cycle=DomainBillingCycle.ANNUALLY,
            available=True,
            price_cents=1099,
        ),
        FindDomainResponse(
            domain="example.edu",
            billing_cycle=DomainBillingCycle.ANNUALLY,
            available=True,
            price_cents=1099,
        ),
        FindDomainResponse(
            domain="example.org",
            billing_cycle=DomainBillingCycle.ANNUALLY,
            available=True,
            price_cents=1099,
        ),
    ]

    # Mock find_alt_domains to return alternative domains
    mock_alt_domains = [
        FindDomainResponse(
            domain=f"alternative{i}.com",
            billing_cycle=DomainBillingCycle.ANNUALLY,
            available=True,
            price_cents=1099,
        )
        for i in range(10)
    ]
    # Create a fake domain to put into get_all_domains and test our parsing of the domain
    fake_domain = OutboundDomain(
        id=uuid.uuid4(),
        organization_id=organization_id,
        workspace_id=uuid.uuid4(),
        domain="fakedomain.com",
        status=OutboundDomainStatus.ACTIVE,
        created_by_user_id=user_id,
        external_id="fakedomain_id_**********",
        forward_to_domain="https://reevo.ai",
        invoice=OneTimeDomainPurchaseInvoice(
            amountPaid=1400,
            creditsApplied=0,
            id="fakedomain_id_**********",
            paidAt="2024-03-19T00:00:00Z",
            subTotal=1400,
            total=1400,
        ),
    )
    with (
        patch.object(
            outbound_domain_service,
            "find_new_tlds",
            AsyncMock(return_value=mock_new_tlds),
        ) as mock_find_new_tlds,
        patch.object(
            outbound_domain_service,
            "generate_alternative_domains_with_prefixes",
            AsyncMock(return_value=mock_alt_domains),
        ) as mock_generate_alternative_domains_with_prefixes,
        patch("dns.asyncresolver.resolve", side_effect=dns.asyncresolver.NXDOMAIN),
        patch(
            "salestech_be.db.dao.outbound_repository.OutboundDomainRepository.get_all_domains",
            AsyncMock(return_value=[fake_domain]),
        ),
    ):
        request = FindDomainRequest(
            query="ExaMpLe",
            suggest_alternative=10,
        )

        results = await outbound_domain_service.find_domains(
            request=request,
            user_id=user_id,
            organization_id=organization_id,
        )

        # Verify results
        assert results is not None
        assert len(results) == request.suggest_alternative

        # Following 2 asserts are to verify that the correct methods were called with the correct arguments, lower case for Example, "com" is defaulted, and we use a fakedomain to parse all the actual domain name
        mock_find_new_tlds.assert_called_once_with(
            sld="example",
            initial_request="",
            all_domains_names={"fakedomain.com"},
            total_included_domains_remaining=1,
            organization_id=organization_id,
        )  # nothing is purchased, so we should have 1 reevo-plan included domain remaining
        mock_generate_alternative_domains_with_prefixes.assert_called_once_with(
            sld="example",
            initial_tld="com",
            initial_request="",
            all_domains_names={"fakedomain.com"},
            total_included_domains_remaining=1,
            organization_id=organization_id,
        )
        # Verify the methods were called with correct arguments
        assert (
            FindDomainResponse(
                domain="example.com",
                available=True,
                price_cents=1099,
                billing_cycle=DomainBillingCycle.ANNUALLY,
            )
            in results
        )
        assert (
            FindDomainResponse(
                domain="example.gov",
                available=True,
                price_cents=1099,
                billing_cycle=DomainBillingCycle.ANNUALLY,
            )
            in results
        )
        assert (
            FindDomainResponse(
                domain="alternative1.com",
                available=True,
                price_cents=1099,
                billing_cycle=DomainBillingCycle.ANNUALLY,
            )
            in results
        )
        assert (
            FindDomainResponse(
                domain="alternative2.com",
                available=True,
                price_cents=1099,
                billing_cycle=DomainBillingCycle.ANNUALLY,
            )
            in results
        )

        # Test with a very long SLD
        request = FindDomainRequest(
            query="thisisaverylongsldthatshouldfailthetestsoimjusttypingalongstringofcharactersseeifthisworksandcausesissueshopefullynot.com",
            suggest_alternative=5,
        )

        try:
            results = await outbound_domain_service.find_domains(
                request=request,
                user_id=user_id,
                organization_id=organization_id,
            )
            pytest.fail("Expected InvalidArgumentError was not raised")
        except InvalidArgumentError as error:
            # Verify that the error details mention length requirements
            assert error.additional_error_details
            assert "length requirements" in str(error.additional_error_details.details)

        # Test with a short SLD
        request = FindDomainRequest(
            query="ex",
            suggest_alternative=5,
        )

        try:
            results = await outbound_domain_service.find_domains(
                request=request,
                user_id=user_id,
                organization_id=organization_id,
            )
            pytest.fail("Expected InvalidArgumentError was not raised")
        except InvalidArgumentError as error:
            assert error.additional_error_details
            # Verify that the error details mention length requirements
            assert "length requirements" in str(error.additional_error_details.details)

        # Test with a punycode domain
        request = FindDomainRequest(
            query="xn--example-qsa.com",
            suggest_alternative=5,
        )

        try:
            results = await outbound_domain_service.find_domains(
                request=request,
                user_id=user_id,
                organization_id=organization_id,
            )
            pytest.fail("Expected InvalidArgumentError was not raised")
        except InvalidArgumentError as error:
            assert error.additional_error_details
            # Verify that the error details mention length requirements
            assert "contains special characters or punycode" in str(
                error.additional_error_details.details
            )

        # Test with google to make sure it doesn't raise an error and adjusts the sld "google" instead of ""
        request = FindDomainRequest(
            query="google",
            suggest_alternative=5,
        )

        # Additional mock for the "google" special case
        with patch.object(
            outbound_domain_service.infraforge_client,
            "check_domain_availability",
            AsyncMock(
                return_value=ExternalCheckDomainAvailabilityResponse(
                    available=True,
                    domain="google.com",
                    price=10.0,
                )
            ),
        ):
            results = await outbound_domain_service.find_domains(
                request=request,
                user_id=user_id,
                organization_id=organization_id,
            )
            assert results is not None
            assert len(results) == request.suggest_alternative

        request = FindDomainRequest(
            query="abc.zra",
            suggest_alternative=10,
        )

        """
        This test is just to check that the find_domains endpoint is working as expected,
        and that the correct methods are being called with the correct arguments.
        We are verifying that the subdomains are being consolidated into the main domain,
        and (i.e. abc.zra.com becomes abczra.com), following the behavior of domain registrars
        like GoDaddy and Squarespace.
        """
        with (
            patch.object(
                outbound_domain_service, "find_new_tlds", AsyncMock(return_value=[])
            ),
            patch.object(
                outbound_domain_service,
                "generate_alternative_domains_with_prefixes",
                AsyncMock(return_value=[]),
            ),
            patch.object(
                outbound_domain_service.infraforge_client,
                "check_domain_availability",
                AsyncMock(return_value=None),
            ),
            patch(
                "salestech_be.core.email.outbound_domain.service.check_find_request",
                new_callable=MagicMock,
            ) as mock_check_find_request,
        ):
            results = await outbound_domain_service.find_domains(
                request=request,
                user_id=uuid.uuid4(),
                organization_id=uuid.uuid4(),
            )
            mock_check_find_request.assert_called_once_with(
                sld="abczra", tld="", query="abc.zra"
            )
            assert results == []


async def test_list_outbound_domains(
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    outbound_domain_repository: OutboundDomainRepository,
    outbound_workspace_repository: OutboundWorkspaceRepository,
    domain_health_repository: DomainHealthRepository,
    email_account_repository: EmailAccountRepository,
) -> None:
    admin_api_client = multi_api_client["admin"]
    organization_id = admin_api_client.default_organization_id
    user_id = admin_api_client.default_user_id
    test_domain_name = str(uuid.uuid4())

    # Create a workspace first
    outbound_workspace = OutboundWorkspace(
        id=uuid.uuid4(),
        organization_id=organization_id,
        name="Test Workspace",
        vendor=OutboundVendor.INFRAFORGE,
        external_id="**********",
    )
    outbound_workspace = await outbound_workspace_repository.insert(outbound_workspace)

    # Create test domains
    test_domains = []
    test_email_accounts = []
    for i in range(3):
        domain = await outbound_domain_repository.insert_outbound_domain(
            organization_id=organization_id,
            workspace_id=outbound_workspace.id,
            created_by_user_id=user_id,
            domain=f"{test_domain_name}{i}.com",
            external_id=f"ext_id_{i}",
            status=OutboundDomainStatus.ACTIVE,
            forward_to_domain=None,
            invoice=OneTimeDomainPurchaseInvoice(
                amountPaid=1400,
                creditsApplied=0,
                id=f"ext_id_{i}",
                paidAt="2024-03-19T00:00:00Z",
                subTotal=1400,
                total=1400,
            ),
        )
        assert domain is not None
        await domain_health_repository.insert_domain_health(
            outbound_domain_id=domain.id,
            mx_records=not_none(
                EntityDnsRecord.list_from_request_field(
                    ["10 mx1.reevo.ai", "20 mx2.reevo.ai"]
                )
            ),
            spf_records=not_none(
                EntityDnsRecord.list_from_request_field(
                    ["v=spf1 include:_spf.reevo.ai ~all"]
                )
            ),
            dkim_record=not_none(
                EntityDnsRecord.list_from_request_field(
                    ["v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4..."]
                )
            ),
            dmarc_record=not_none(
                EntityDnsRecord.list_from_request_field(
                    ["v=DMARC1; p=reject; rua=mailto:<EMAIL>"]
                )
            ),
        )
        test_domains.append(domain)
        for j in range(3):
            email_account = await email_account_repository.create_email_account(
                EmailAccount(
                    id=uuid.uuid4(),
                    organization_id=organization_id,
                    owner_user_id=user_id,
                    email=f"test{j}@test{i}.com",
                    type=EmailAccountType.REGULAR,
                    outbound_domain_id=domain.id,
                    active=True,
                    external_id=f"ext_id_{j}",
                    vendor=EmailProvider.INFRAFORGE,
                    first_name=f"Test{j}",
                    last_name="User",
                    is_default=False,
                    seconds_delay_between_emails=600,
                    created_at=zoned_utc_now(),
                    created_by_user_id=user_id,
                )
            )
            test_email_accounts.append(email_account)

    # Create a domain with no mailboxes
    empty_domain = str(uuid.uuid4()) + ".com"
    domain_no_mailboxes = await outbound_domain_repository.insert_outbound_domain(
        organization_id=organization_id,
        workspace_id=outbound_workspace.id,
        created_by_user_id=user_id,
        domain=empty_domain,
        external_id="ext_id_empty",
        status=OutboundDomainStatus.ACTIVE,
        forward_to_domain=None,
        invoice=OneTimeDomainPurchaseInvoice(
            amountPaid=1400,
            creditsApplied=0,
            id="654321",
            paidAt="2024-03-19T00:00:00Z",
            subTotal=1400,
            total=1400,
        ),
    )
    assert domain_no_mailboxes is not None
    await domain_health_repository.insert_domain_health(
        outbound_domain_id=domain_no_mailboxes.id,
        mx_records=not_none(
            EntityDnsRecord.list_from_request_field(
                ["10 mx1.reevo.ai", "20 mx2.reevo.ai"]
            )
        ),
        spf_records=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=spf1 include:_spf.reevo.ai ~all"]
            )
        ),
        dkim_record=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4..."]
            )
        ),
        dmarc_record=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=DMARC1; p=reject; rua=mailto:<EMAIL>"]
            )
        ),
    )
    test_domains.append(domain_no_mailboxes)

    # Call the list endpoint
    response = await admin_api_client.api_test_client.client.post(
        "/api/v1/emails/outbound/domains/_list",
        json={
            "filter_spec": None,
            "sorting_spec": None,
            "ordered_object_fields": None,
            "user_id": str(user_id),
            "cursor": None,
        },
    )

    # Verify response
    assert response.status_code == 200
    response_data = response.json()

    # Check records
    records = response_data["list_data"]
    assert len(records) >= 3  # At least our 3 test domains

    # Verify our test domains are in the response
    test_domain_names = [domain.domain for domain in test_domains]
    found_domain_names = []

    for record in records:
        domain_name = record["data"]["domain"]
        if domain_name in test_domain_names:
            found_domain_names.append(domain_name)

            # Verify the record structure and data
            assert "data" in record
            assert "id" in record["data"]
            assert record["data"]["domain"] == domain_name
            assert record["data"]["organization_id"] == str(organization_id)
            assert record["data"]["status"] == OutboundDomainStatus.ACTIVE

            # Find the matching test domain
            test_domain = next(d for d in test_domains if d.domain == domain_name)
            assert record["data"]["id"] == str(test_domain.id)

            # Verify mailbox counts
            if domain_name == empty_domain:
                assert record["data"]["total_mailbox_count"] == 0
                assert record["data"]["active_mailbox_count"] == 0
            else:
                assert record["data"]["total_mailbox_count"] == 3
                assert record["data"]["active_mailbox_count"] == 3

    # Ensure all our test domains were found
    assert sorted(found_domain_names) == sorted(test_domain_names)

    # Test filtering by record_id
    specific_domain = test_domains[0]
    filter_response = await admin_api_client.api_test_client.client.post(
        "/api/v1/emails/outbound/domains/_list",
        json={
            "record_id": str(specific_domain.id),
            "cursor": None,
            "filter_spec": None,
            "sorting_spec": None,
            "ordered_object_fields": None,
        },
    )

    assert filter_response.status_code == 200
    filter_data = filter_response.json()

    # Should only return one record
    assert len(filter_data["list_data"]) == 1
    assert filter_data["list_data"][0]["data"]["domain"] == specific_domain.domain
    assert filter_data["list_data"][0]["data"]["id"] == str(specific_domain.id)

    get_response = await admin_api_client.api_test_client.client.get(
        f"/api/v1/emails/outbound/domains/{specific_domain.id}",
    )
    assert get_response.status_code == 200
    get_data = get_response.json()
    assert get_data["domain"] == specific_domain.domain
    assert get_data["id"] == str(specific_domain.id)
    assert get_data["organization_id"] == str(organization_id)
    assert get_data["purchased_at"] is not None
    assert get_data["created_by_user_id"] == str(user_id)


async def test_buy_domain_v2_mock_record(
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    outbound_domain_repository: OutboundDomainRepository,
    outbound_workspace_repository: OutboundWorkspaceRepository,
    domain_health_repository: DomainHealthRepository,
    email_account_repository: EmailAccountRepository,
    quota_policy_repository: QuotaPolicyRepository,
    quota_service: QuotaService,
) -> None:
    admin_api_client = multi_api_client["admin"]
    organization_id = admin_api_client.default_organization_id
    user_id = admin_api_client.default_user_id
    domain_name = str(uuid.uuid4()) + ".com"

    # Create a workspace first
    buy_domain_request = BuyDomainRequest(
        domains=[domain_name],
        is_mock_record=True,
    )
    response = await admin_api_client.api_test_client.client.post(
        "/api/v1/emails/outbound/domains/_buy",
        json=buy_domain_request.model_dump(),
    )
    assert response.status_code == 200
    response_data = response.json()
    assert len(response_data) == 1
    assert response_data[0]["domain"] == domain_name

    # Verify the domain quota summary
    domain_quota_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.DOMAIN,
        period=QuotaPeriod.ANNUAL,
    )

    assert domain_quota_summary is not None
    assert domain_quota_summary.total_remaining == 1
    assert domain_quota_summary.total_used == 1
    assert domain_quota_summary.total_limit == 2

    # Verify the Reevo plan included domain quota policy
    included_domain_quota_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.PLAN_INCLUDED_COST_DOMAIN,
        period=QuotaPeriod.ANNUAL,
    )
    assert included_domain_quota_summary is not None
    assert (
        included_domain_quota_summary.total_remaining == 1
    )  # This value is 1 because the domain is a mock record, so it's not included in the plan-included-cost-domain quota
    assert included_domain_quota_summary.total_used == 0
    assert included_domain_quota_summary.total_limit == 1

    # Verify workspace was created with mock_record=True
    workspace = (
        await outbound_workspace_repository.get_outbound_workspace_by_organization_id(
            organization_id=organization_id
        )
    )
    assert workspace is not None
    assert workspace.is_mock_record is True

    # Verify quota policy was created since creating workspace creates a quota policy for the org
    quota_policy = await quota_policy_repository.get_entity_relevant_policies(
        organization_id=organization_id,
        entity_id=organization_id,
        entity_type=QuotaConsumerEntityType.ORGANIZATION,
        resource=QuotaConsumingResource.DOMAIN,
        period=QuotaPeriod.ANNUAL,
    )
    assert len(quota_policy) == 1
    assert quota_policy[0].quota_limit == 2
    # Up to 2 domains for now
    assert quota_policy[0].period == QuotaPeriod.ANNUAL

    # Verify domain was created with mock_record=True
    domains = await outbound_domain_repository.get_outbound_domains_by_org_id(
        organization_id=organization_id
    )
    assert len(domains) == 1
    domain = domains[0]
    assert domain.is_mock_record is True
    assert domain.domain == domain_name
    assert domain.organization_id == organization_id
    assert domain.workspace_id == workspace.id

    # verify an email account can be created for a mock domain
    email_account = await email_account_repository.create_email_account(  # noqa: F841
        EmailAccount(
            id=uuid.uuid4(),
            organization_id=organization_id,
            owner_user_id=user_id,
            email="<EMAIL>",
            type=EmailAccountType.REGULAR,
            outbound_domain_id=domain.id,
            active=True,
            external_id="ext_id_1",
            vendor=EmailProvider.INFRAFORGE,
            first_name="Test",
            last_name="User",
            is_default=False,
            seconds_delay_between_emails=600,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
        )
    )


async def test_list_outbound_domain_with_relationships(
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    outbound_domain_repository: OutboundDomainRepository,
    outbound_workspace_repository: OutboundWorkspaceRepository,
    domain_health_repository: DomainHealthRepository,
    email_account_repository: EmailAccountRepository,
    sequence_repository: SequenceRepository,
    sequence_enrollment_repository: SequenceEnrollmentRepository,
) -> None:
    admin_api_client = multi_api_client["admin"]
    organization_id = admin_api_client.default_organization_id
    user_id = admin_api_client.default_user_id
    test_domain = str(uuid.uuid4()) + ".com"
    test_domain_2 = str(uuid.uuid4()) + ".com"

    # Create a workspace
    outbound_workspace = OutboundWorkspace(
        id=uuid.uuid4(),
        organization_id=organization_id,
        name="Test Workspace",
        vendor=OutboundVendor.INFRAFORGE,
        external_id="**********",
    )
    outbound_workspace = await outbound_workspace_repository.insert(outbound_workspace)

    # Create a test domain
    domain = await outbound_domain_repository.insert_outbound_domain(
        organization_id=organization_id,
        workspace_id=outbound_workspace.id,
        created_by_user_id=user_id,
        domain=test_domain,
        external_id="ext_id_single",
        status=OutboundDomainStatus.ACTIVE,
        forward_to_domain=None,
        invoice=OneTimeDomainPurchaseInvoice(
            amountPaid=1400,
            creditsApplied=0,
            id="654321",
            paidAt="2024-03-19T00:00:00Z",
            subTotal=1400,
            total=1400,
        ),
    )
    assert domain is not None

    # Create domain health records
    await domain_health_repository.insert_domain_health(
        outbound_domain_id=domain.id,
        mx_records=not_none(
            EntityDnsRecord.list_from_request_field(
                ["10 mx1.reevo.ai", "20 mx2.reevo.ai"]
            )
        ),
        spf_records=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=spf1 include:_spf.reevo.ai ~all"]
            )
        ),
        dkim_record=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4..."]
            )
        ),
        dmarc_record=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=DMARC1; p=reject; rua=mailto:<EMAIL>"]
            )
        ),
    )

    # create a second test domain
    domain2 = await outbound_domain_repository.insert_outbound_domain(
        organization_id=organization_id,
        workspace_id=outbound_workspace.id,
        created_by_user_id=user_id,
        domain=test_domain_2,
        external_id="ext_id_second",
        status=OutboundDomainStatus.ACTIVE,
        forward_to_domain=None,
        invoice=OneTimeDomainPurchaseInvoice(
            amountPaid=1400,
            creditsApplied=0,
            id="654321",
            paidAt="2024-03-19T00:00:00Z",
            subTotal=1400,
            total=1400,
        ),
    )
    assert domain2 is not None

    # create domain health records for the second domain
    await domain_health_repository.insert_domain_health(
        outbound_domain_id=domain2.id,
        mx_records=not_none(
            EntityDnsRecord.list_from_request_field(
                ["10 mx1.reevo.ai", "20 mx2.reevo.ai"]
            )
        ),
        spf_records=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=spf1 include:_spf.reevo.ai ~all"]
            )
        ),
        dkim_record=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4..."]
            )
        ),
        dmarc_record=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=DMARC1; p=reject; rua=mailto:<EMAIL>"]
            )
        ),
    )

    # Create a sequence
    sequence = SequenceV2(
        id=uuid.uuid4(),
        organization_id=organization_id,
        owner_user_id=user_id,
        status=SequenceStatus.ACTIVE,
        name="Single Test Sequence",
        visibility=SequenceVisibility.TEAM_EDITABLE,
        schedule=SequenceV2Schedule(
            timezone="America/New_York",
            skip_holidays=False,
            schedule_times=[],
        ),
        created_at=zoned_utc_now(),
        created_by_user_id=user_id,
    )
    sequence = await sequence_repository.insert(sequence)

    # Create two email accounts
    email_account1 = await email_account_repository.create_email_account(
        EmailAccount(
            id=uuid.uuid4(),
            organization_id=organization_id,
            owner_user_id=user_id,
            email="<EMAIL>",
            type=EmailAccountType.REGULAR,
            outbound_domain_id=domain.id,
            active=True,
            external_id="ext_id_single_email1",
            vendor=EmailProvider.INFRAFORGE,
            first_name="Single",
            last_name="Test1",
            is_default=False,
            seconds_delay_between_emails=600,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
        )
    )

    email_account2 = await email_account_repository.create_email_account(  # noqa: F841
        EmailAccount(
            id=uuid.uuid4(),
            organization_id=organization_id,
            owner_user_id=user_id,
            email="<EMAIL>",
            type=EmailAccountType.REGULAR,
            outbound_domain_id=domain2.id,
            active=True,
            external_id="ext_id_second_email2",
            vendor=EmailProvider.INFRAFORGE,
            first_name="Second",
            last_name="Test2",
            is_default=False,
            seconds_delay_between_emails=600,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
        )
    )

    # Create sequence enrollment for only the first email account
    sequence_enrollment = SequenceEnrollment(
        id=uuid.uuid4(),
        organization_id=organization_id,
        email_account_id=email_account1.id,
        sequence_id=sequence.id,
        contact_id=uuid.uuid4(),
        status=SequenceEnrollmentStatus.ACTIVE,
        enrolled_at=zoned_utc_now(),
        enrolled_by_user_id=user_id,
        updated_at=zoned_utc_now(),
        updated_by_user_id=user_id,
    )
    await sequence_enrollment_repository.insert(sequence_enrollment)

    # Test 1: List all domains without filters
    all_domains = await admin_api_client.api_test_client.client.post(
        "/api/v1/emails/outbound/domains/_list",
        json={
            "cursor": None,
            "filter_spec": None,
            "sorting_spec": None,
            "ordered_object_fields": None,
        },
    )
    assert all_domains.status_code == 200
    all_domains_data = all_domains.json()
    assert len(all_domains_data["list_data"]) >= 1

    # Test 2: Filter for our specific domain with sequence relationship
    filtered_domains = await admin_api_client.api_test_client.client.post(
        "/api/v1/emails/outbound/domains/_list",
        json={
            "filter_spec": {
                "primary_object_identifier": {
                    "object_kind": "STANDARD",
                    "object_name": "outbound_domain",
                },
                "filter": {
                    "filter_type": "VALUE",
                    "field": {
                        "relationship_id": OutboundDomainRelationship.outbound_domain__to__sequence.value,
                        "field": {"path": ["id"]},
                    },
                    "value": str(sequence.id),
                    "operator": "EQ",
                },
            },
            "sorting_spec": None,
            "cursor": None,
            "ordered_object_fields": None,
        },
    )

    # Verify filtered response
    assert filtered_domains.status_code == 200
    filtered_data = filtered_domains.json()
    assert len(filtered_data["list_data"]) == 1
    record = filtered_data["list_data"][0]
    assert (
        record["related_records"][
            OutboundDomainRelationship.outbound_domain__to__sequence.value
        ]
        is not None
    )
    sequence_record = record["related_records"][
        OutboundDomainRelationship.outbound_domain__to__sequence.value
    ][0]
    assert sequence_record["data"]["id"] == str(sequence.id)
    assert sequence_record["data"]["name"] == sequence.name
    assert sequence_record["data"]["status"] == sequence.status
    assert sequence_record["data"]["organization_id"] == str(organization_id)
    assert sequence_record["data"]["created_at"] is not None

    domain_data = filtered_data["list_data"][0]["data"]
    # Verify domain data
    assert domain_data["id"] == str(domain.id)
    assert domain_data["domain"] == test_domain
    assert domain_data["organization_id"] == str(organization_id)
    assert domain_data["status"] == OutboundDomainStatus.ACTIVE
    assert domain_data["total_mailbox_count"] == 1  # Only one mailbox
    assert domain_data["active_mailbox_count"] == 1  # Only one active mailbox
    assert domain_data["sequence_count"] == 1  # Only one sequence
    # Test 3: Get specific domain details
    get_response = await admin_api_client.api_test_client.client.get(
        f"/api/v1/emails/outbound/domains/{domain.id}",
    )
    assert get_response.status_code == 200
    get_data = get_response.json()
    assert get_data["domain"] == test_domain
    assert get_data["id"] == str(domain.id)
    assert get_data["organization_id"] == str(organization_id)
    assert get_data["created_by_user_id"] == str(user_id)


async def test_list_unhealthy_and_pending_domains(
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    outbound_domain_repository: OutboundDomainRepository,
    outbound_workspace_repository: OutboundWorkspaceRepository,
    domain_health_repository: DomainHealthRepository,
    email_account_repository: EmailAccountRepository,
) -> None:
    admin_api_client = multi_api_client["admin"]
    organization_id = admin_api_client.default_organization_id
    user_id = admin_api_client.default_user_id
    unhealthy_domain_name = str(uuid.uuid4()) + ".com"
    pending_domain_name = str(uuid.uuid4()) + ".com"

    outbound_workspace = OutboundWorkspace(
        id=uuid.uuid4(),
        organization_id=organization_id,
        name="Test Workspace",
        vendor=OutboundVendor.INFRAFORGE,
        external_id="**********",
    )
    outbound_workspace = await outbound_workspace_repository.insert(outbound_workspace)

    # Create a test domain
    unhealthy_domain = await outbound_domain_repository.insert_outbound_domain(
        organization_id=organization_id,
        workspace_id=outbound_workspace.id,
        created_by_user_id=user_id,
        domain=unhealthy_domain_name,
        external_id="ext_id_single",
        status=OutboundDomainStatus.ACTIVE,
        forward_to_domain=None,
        invoice=OneTimeDomainPurchaseInvoice(
            amountPaid=1400,
            creditsApplied=0,
            id="654321",
            paidAt="2024-03-19T00:00:00Z",
            subTotal=1400,
            total=1400,
        ),
    )
    assert unhealthy_domain is not None

    await outbound_domain_repository.update_outbound_domain(
        unhealthy_domain.id,
        column_to_update={"created_at": datetime.now(tz=UTC) - timedelta(days=5)},
    )
    # create pending domain
    pending_domain = await outbound_domain_repository.insert_outbound_domain(
        organization_id=organization_id,
        workspace_id=outbound_workspace.id,
        created_by_user_id=user_id,
        domain=pending_domain_name,
        external_id="ext_id_pending",
        status=OutboundDomainStatus.ACTIVE,
        forward_to_domain=None,
        invoice=OneTimeDomainPurchaseInvoice(
            amountPaid=1400,
            creditsApplied=0,
            id="6543210",
            paidAt="2024-03-19T00:00:00Z",
            subTotal=1400,
            total=1400,
        ),
    )
    assert pending_domain is not None

    # Create domain health records
    await domain_health_repository.insert_domain_health(
        outbound_domain_id=unhealthy_domain.id,
        mx_records=not_none(
            EntityDnsRecord.list_from_request_field(
                ["10 mx1.unhealthy.reevo.ai", "20 mx2.unhealthy.reevo.ai"]
            )
        ),
        spf_records=not_none(EntityDnsRecord.list_from_request_field([])),
        dkim_record=not_none(EntityDnsRecord.list_from_request_field([])),
        dmarc_record=not_none(EntityDnsRecord.list_from_request_field([])),
    )

    await domain_health_repository.insert_domain_health(
        outbound_domain_id=pending_domain.id,
        mx_records=not_none(
            EntityDnsRecord.list_from_request_field(
                ["10 mx1.pending.reevo.ai", "20 mx2.pending.reevo.ai"]
            )
        ),
        spf_records=not_none(EntityDnsRecord.list_from_request_field([])),
        dkim_record=not_none(EntityDnsRecord.list_from_request_field([])),
        dmarc_record=not_none(EntityDnsRecord.list_from_request_field([])),
    )

    await outbound_domain_repository.update_outbound_domain(
        pending_domain.id,
        column_to_update={"created_at": datetime.now(tz=UTC) - timedelta(minutes=5)},
    )

    await domain_health_repository.update_domain_health(
        outbound_domain_id=unhealthy_domain.id,
        column_to_update={"created_at": datetime.now(tz=UTC) - timedelta(days=5)},
    )

    # Create two email accounts
    email_account1 = await email_account_repository.create_email_account(
        EmailAccount(
            id=uuid.uuid4(),
            organization_id=organization_id,
            owner_user_id=user_id,
            email="<EMAIL>",
            type=EmailAccountType.REGULAR,
            outbound_domain_id=unhealthy_domain.id,
            active=True,
            external_id="ext_id_single_email1",
            vendor=EmailProvider.INFRAFORGE,
            first_name="Single",
            last_name="Test1",
            is_default=False,
            seconds_delay_between_emails=600,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
        )
    )
    assert email_account1 is not None

    # Test 1: List all domains without filters
    list_response = await admin_api_client.api_test_client.client.post(
        "/api/v1/emails/outbound/domains/_list",
        json={
            "filter_spec": None,
            "sorting_spec": None,
            "ordered_object_fields": None,
            "user_id": str(user_id),
            "cursor": None,
        },
    )

    assert list_response.status_code == 200
    list_data = list_response.json()["list_data"]

    # Find domains by their names instead of relying on index
    domain_data_by_name = {}
    for item in list_data:
        domain_name = item["data"]["domain"]
        domain_data_by_name[domain_name] = item["data"]

    # Get the specific domains by name
    unhealthy_domain_data = domain_data_by_name[unhealthy_domain_name]
    pending_domain_data = domain_data_by_name[pending_domain_name]

    # Verify unhealthy domain has expected structure
    assert "domain_configuration" in unhealthy_domain_data
    assert unhealthy_domain_data["domain_configuration"]["contains_mx_records"] is True
    assert (
        unhealthy_domain_data["domain_configuration"]["contains_spf_records"] is False
    )
    assert (
        unhealthy_domain_data["domain_configuration"]["contains_dkim_record"] is False
    )
    assert (
        unhealthy_domain_data["domain_configuration"]["contains_dmarc_record"] is False
    )
    assert (
        unhealthy_domain_data["domain_health"] == "UNHEALTHY"
    )  # cannot be pending since created_at is 5 days before
    assert unhealthy_domain_data["status"] == "ACTIVE"
    assert unhealthy_domain_data["total_mailbox_count"] == 1
    assert unhealthy_domain_data["active_mailbox_count"] == 1
    assert unhealthy_domain_data["sequence_count"] == 0

    # Verify pending domain has expected structure
    assert "domain_configuration" in pending_domain_data
    assert pending_domain_data["domain_configuration"]["contains_mx_records"] is True
    assert pending_domain_data["domain_configuration"]["contains_spf_records"] is False
    assert pending_domain_data["domain_configuration"]["contains_dkim_record"] is False
    assert pending_domain_data["domain_configuration"]["contains_dmarc_record"] is False
    assert (
        pending_domain_data["domain_health"] == "PENDING"
    )  # should be pending since pending domain is made 5 minutes prior
    assert pending_domain_data["status"] == "ACTIVE"
    assert pending_domain_data["total_mailbox_count"] == 0
    assert pending_domain_data["active_mailbox_count"] == 0
    assert pending_domain_data["sequence_count"] == 0


async def test_check_dns_resolvers() -> None:
    mock_resolver = AsyncMock()
    mock_resolver.resolve.side_effect = dns.asyncresolver.NXDOMAIN
    with patch(
        "salestech_be.core.email.outbound_domain.dns_check_service.resolver.resolve",
        side_effect=dns.asyncresolver.NXDOMAIN,
    ):  # Raises NXDOMAIN exception, which we can check available
        assert (await is_nxdomain_from_ns_record("example.com")) is True
        assert (await is_nxdomain_from_ns_record("nonexistent.com")) is True
        assert (await is_nxdomain_from_ns_record("invalid.com")) is True
        assert (await is_nxdomain_from_a_record("example.com")) is True
        assert (await is_nxdomain_from_a_record("nonexistent.com")) is True
        assert (await is_nxdomain_from_a_record("invalid.com")) is True
        assert (await is_nxdomain_from_mx("example.com")) is True
        assert (await is_nxdomain_from_mx("nonexistent.com")) is True
        assert (await is_nxdomain_from_mx("invalid.com")) is True
        assert (await is_domain_available("example.com")) is True

    mock_resolver_exists = AsyncMock()
    mock_resolver_exists.resolve.return_value = ["some result"]
    with patch(
        "salestech_be.core.email.outbound_domain.dns_check_service.resolver.resolve",
        return_value=mock_resolver_exists,
        side_effect=None,
    ):  # Returns a value, so it is not available
        assert (await is_nxdomain_from_ns_record("newexample.com")) is False
        assert (await is_nxdomain_from_ns_record("existent.com")) is False
        assert (await is_nxdomain_from_ns_record("valid.com")) is False
        assert (await is_nxdomain_from_a_record("newexample.com")) is False
        assert (await is_nxdomain_from_a_record("existent.com")) is False
        assert (await is_nxdomain_from_a_record("valid.com")) is False
        assert (await is_nxdomain_from_mx("newexample.com")) is False
        assert (await is_nxdomain_from_mx("existent.com")) is False
        assert (await is_nxdomain_from_mx("valid.com")) is False
        assert (await is_domain_available("newexample.com")) is False


async def test_find_domains_expensive_tld(
    outbound_domain_service: OutboundDomainService,
) -> None:
    # Mock check_domain_availability to return an expensive domain first
    expensive_domain_response = ExternalCheckDomainAvailabilityResponse(
        available=True,
        domain="example.ai",
        price=100.0,  # This will be 10000 cents, above MAXIMUM_PRICE_CENTS (2000)
    )

    # Mock find_new_tlds to return some cheaper test domains
    mock_new_tlds = [
        FindDomainResponse(
            domain="example.com",
            billing_cycle=DomainBillingCycle.ANNUALLY,
            available=True,
            price_cents=1099,
        ),
        FindDomainResponse(
            domain="example.org",
            billing_cycle=DomainBillingCycle.ANNUALLY,
            available=True,
            price_cents=1099,
        ),
    ]

    # Mock find_alt_domains to return alternative domains
    mock_alt_domains = [
        FindDomainResponse(
            domain=f"alternative{i}.com",
            billing_cycle=DomainBillingCycle.ANNUALLY,
            available=True,
            price_cents=1099,
        )
        for i in range(3)
    ]

    with (
        patch.object(
            outbound_domain_service.infraforge_client,
            "check_domain_availability",
            AsyncMock(return_value=expensive_domain_response),
        ),
        patch.object(
            outbound_domain_service,
            "find_new_tlds",
            AsyncMock(return_value=mock_new_tlds),
        ),
        patch.object(
            outbound_domain_service,
            "generate_alternative_domains_with_prefixes",
            AsyncMock(return_value=mock_alt_domains),
        ),
        patch("dns.asyncresolver.resolve", side_effect=dns.asyncresolver.NXDOMAIN),
    ):
        request = FindDomainRequest(
            query="example.ai",  # Request an .ai domain which will be expensive
            suggest_alternative=6,
        )

        results = await outbound_domain_service.find_domains(
            request=request,
            user_id=uuid.uuid4(),
            organization_id=uuid.uuid4(),
        )

        # Verify results
        assert results is not None
        assert len(results) == 6  # Should still return 6 results

        # The expensive .ai domain should still be in the results since we now show the primary domain as unavailable if it's too expensive or doesn't returns as false
        expensive_domain = FindDomainResponse(
            domain="example.ai",
            price_cents=10000,
            available=False,
            billing_cycle=DomainBillingCycle.ANNUALLY,
        )
        assert expensive_domain in results

        # Verify we got cheaper alternatives instead
        assert any(r.domain == "example.com" for r in results)
        assert any(r.domain == "example.org" for r in results)
        assert any(r.domain == "alternative0.com" for r in results)

        # Verify all returned domains are within price limit except the unavailable domain
        for result in results:
            if result.domain == "example.ai":
                assert result.price_cents == 10000
            else:
                assert result.price_cents <= settings.max_cost_per_plan_included_domain


async def test_find_domains_with_unavailable_primary_domain(
    outbound_domain_service: OutboundDomainService,
) -> None:
    # Mock check_domain_availability to return an unavailable domain
    unavailable_domain_response = ExternalCheckDomainAvailabilityResponse(
        available=False,
        domain="example.com",
        price=10.0,
    )
    # Mock find_new_tlds to return some test domains
    mock_new_tlds = [
        FindDomainResponse(
            domain="example.net",
            billing_cycle=DomainBillingCycle.ANNUALLY,
            available=True,
            price_cents=1099,
        ),
        FindDomainResponse(
            domain="example.org",
            billing_cycle=DomainBillingCycle.ANNUALLY,
            available=True,
            price_cents=1099,
        ),
    ]

    # Mock find_alt_domains to return alternative domains
    mock_alt_domains = [
        FindDomainResponse(
            domain=f"alternative{i}.com",
            billing_cycle=DomainBillingCycle.ANNUALLY,
            available=True,
            price_cents=1099,
        )
        for i in range(3)
    ]

    with (
        patch.object(
            outbound_domain_service.infraforge_client,
            "check_domain_availability",
            AsyncMock(return_value=unavailable_domain_response),
        ),
        patch.object(
            outbound_domain_service,
            "find_new_tlds",
            AsyncMock(return_value=mock_new_tlds),
        ),
        patch.object(
            outbound_domain_service,
            "generate_alternative_domains_with_prefixes",
            AsyncMock(return_value=mock_alt_domains),
        ),
        patch("dns.asyncresolver.resolve", side_effect=dns.asyncresolver.NXDOMAIN),
    ):
        request = FindDomainRequest(
            query="example.com",
            suggest_alternative=5,
        )

        results = await outbound_domain_service.find_domains(
            request=request,
            user_id=uuid.uuid4(),
            organization_id=uuid.uuid4(),
        )

        assert results is not None
        assert len(results) == 5  # Should still return 5 results

        # Verify the unavailable domain is in the results since it's the primary domain
        unavailable_domain = FindDomainResponse(
            domain="example.com",
            billing_cycle=DomainBillingCycle.ANNUALLY,
            available=False,
            price_cents=1000,
        )
        assert unavailable_domain in results

        # Verify the alternative domains are returned
        assert any(r.domain == "example.net" for r in results)
        assert any(r.domain == "alternative0.com" for r in results)

        # Verify all returned domains are within price limit
        for result in results:
            if result.domain == "example.com":
                assert result.price_cents == 1000
            else:
                assert result.price_cents <= settings.max_cost_per_plan_included_domain


async def test_archive_domain(
    outbound_domain_repository: OutboundDomainRepository,
    outbound_workspace_repository: OutboundWorkspaceRepository,
    organization_repository: OrganizationRepository,
    sequence_repository: SequenceRepository,
    email_account_repository: EmailAccountRepository,
    sequence_enrollment_repository: SequenceEnrollmentRepository,
    domain_health_repository: DomainHealthRepository,
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
) -> None:
    admin_api_client = multi_api_client["admin"]
    organization_id = admin_api_client.default_organization_id
    user_id = admin_api_client.default_user_id
    archive_domain_name = str(uuid.uuid4()) + ".com"

    # Create and insert a workspace into the database
    workspace = await outbound_workspace_repository.insert_outbound_workspace(
        organization_id=organization_id,
        name="Test Workspace",
        vendor=OutboundVendor.INFRAFORGE,
        external_id="ws_123456789",
    )
    assert workspace is not None

    # Create a domain
    domain = await outbound_domain_repository.insert_outbound_domain(
        organization_id=organization_id,
        workspace_id=workspace.id,
        created_by_user_id=user_id,
        domain=archive_domain_name,
        status=OutboundDomainStatus.ACTIVE,
        external_id="ext_id_test_domain",
        forward_to_domain=None,
        invoice=OneTimeDomainPurchaseInvoice(
            amountPaid=1400,
            creditsApplied=0,
            id="654321",
            paidAt="2024-03-19T00:00:00Z",
            subTotal=1400,
            total=1400,
        ),
    )
    assert domain is not None
    email_account = await email_account_repository.create_email_account(
        EmailAccount(
            id=uuid.uuid4(),
            organization_id=organization_id,
            owner_user_id=user_id,
            email=f"archive_test@{archive_domain_name}",
            type=EmailAccountType.REGULAR,
            outbound_domain_id=domain.id,
            active=True,
            external_id="ext_id_archive_test",
            vendor=EmailProvider.INFRAFORGE,
            first_name="Archive",
            last_name="Test",
            is_default=False,
            seconds_delay_between_emails=600,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
        )
    )

    await domain_health_repository.insert_domain_health(
        outbound_domain_id=domain.id,
        mx_records=not_none(
            EntityDnsRecord.list_from_request_field(
                ["10 mx1.reevo.ai", "20 mx2.reevo.ai"]
            )
        ),
        spf_records=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=spf1 include:_spf.reevo.ai ~all"]
            )
        ),
        dkim_record=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=DKIM1; k=rsa; p=MIGfMA0GCSqGSIb3DQEBAQUAA4..."]
            )
        ),
        dmarc_record=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=DMARC1; p=reject; rua=mailto:<EMAIL>"]
            )
        ),
    )

    archive_request = ArchiveDomainRequest(
        sequence_handling=EmailAccountArchiveSequenceHandling.STOP,
    )

    # Mock the domain health check schedule service methods
    with (
        patch(
            "salestech_be.settings.settings.enable_domain_health_check_per_domain", True
        ),
        patch(
            "salestech_be.core.email.outbound_domain.domain_health_check_schedule_service.DomainHealthCheckScheduleService.delete_domain_health_check_per_domain_schedule",
            new_callable=AsyncMock,
        ) as mock_delete_schedule,
        patch(
            "salestech_be.core.email.outbound_domain.domain_health_check_schedule_service.DomainHealthCheckScheduleService.create_domain_health_check_per_domain_schedule",
            new_callable=AsyncMock,
        ) as mock_create_schedule,
    ):
        # Use the API endpoint instead of calling the service directly
        response = await admin_api_client.api_test_client.client.post(
            f"/api/v1/emails/outbound/domains/{domain.id}/_archive",
            json=archive_request.model_dump(mode="json"),
        )
        assert response.status_code == 200

        # Verify the response data
        response_data = response.json()
        assert response_data["id"] == str(domain.id)
        assert response_data["status"] == OutboundDomainStatus.INACTIVE
        assert response_data["archived_at"] is not None
        first_archived_at = response_data["archived_at"]

        # Verify delete_domain_health_check_per_domain_schedule was called with the correct parameters
        mock_delete_schedule.assert_called_once_with(
            domain_id=domain.id,
            organization_id=organization_id,
            domain_name=domain.domain,
        )
        mock_create_schedule.assert_not_called()

        # Verify the domain was actually archived in the database
        archived_domain = await outbound_domain_repository.get_domain_by_id_and_org_id(
            domain.id, organization_id=organization_id, exclude_archived=False
        )
        assert archived_domain is not None
        assert archived_domain.status == OutboundDomainStatus.INACTIVE
        assert archived_domain.archived_at is not None

        # Verify the email account was archived in the database
        archived_email_account = (
            await email_account_repository.find_account_by_id_including_archived(
                email_account_id=email_account.id
            )
        )
        assert archived_email_account is not None
        assert archived_email_account.active is not True
        assert archived_email_account.archived_at is not None

        list_response = await admin_api_client.api_test_client.client.post(
            "/api/v1/emails/outbound/domains/_list",
            json={
                "filter_spec": None,
                "sorting_spec": None,
                "ordered_object_fields": None,
                "user_id": str(user_id),
                "cursor": None,
            },
        )

        assert list_response.status_code == 200
        domain_data = list_response.json()["list_data"][0]["data"]

        # Verify domain has expected structure
        assert "domain_configuration" in domain_data
        assert domain_data["domain_configuration"]["contains_mx_records"] is True
        assert domain_data["domain_configuration"]["contains_spf_records"] is True
        assert domain_data["domain_configuration"]["contains_dkim_record"] is True
        assert domain_data["domain_configuration"]["contains_dmarc_record"] is True
        assert domain_data["domain_health"] is None
        assert domain_data["status"] == "INACTIVE"
        assert domain_data["total_mailbox_count"] == 1
        assert domain_data["active_mailbox_count"] == 0
        assert domain_data["sequence_count"] == 0

        # Try to archive the domain again, shouldn't change anything
        response = await admin_api_client.api_test_client.client.post(
            f"/api/v1/emails/outbound/domains/{domain.id}/_archive",
            json=archive_request.model_dump(mode="json"),
        )
        assert response.status_code == 200
        response_data = response.json()
        assert (
            response_data["archived_at"] == first_archived_at
        )  # archiving already archived domain should return the same archived_at

        # Now test unarchiving
        mock_delete_schedule.reset_mock()
        mock_create_schedule.reset_mock()

        response = await admin_api_client.api_test_client.client.post(
            f"/api/v1/emails/outbound/domains/{domain.id}/_unarchive",
        )
        assert response.status_code == 200

        # Verify create_domain_health_check_per_domain_schedule was called with the correct parameters
        mock_create_schedule.assert_called_once_with(
            domain_id=domain.id,
            organization_id=organization_id,
            domain_name=domain.domain,
        )
        mock_delete_schedule.assert_not_called()

        # Verify the domain was unarchived in the database
        unarchived_domain = (
            await outbound_domain_repository.get_domain_by_id_and_org_id(
                domain.id, organization_id=organization_id, exclude_archived=False
            )
        )
        assert unarchived_domain is not None
        assert unarchived_domain.status == OutboundDomainStatus.ACTIVE
        assert unarchived_domain.archived_at is None

        # Verify the email account is still archived in the database
        archived_email_account = (
            await email_account_repository.find_account_by_id_including_archived(
                email_account.id
            )
        )
        assert archived_email_account is not None
        assert archived_email_account.active is not True
        assert archived_email_account.archived_at is not None


async def test_regex_for_domain_name_validation() -> None:
    assert contains_special_or_whitespace("test.com") is True
    assert contains_special_or_whitespace("test123") is False
    assert contains_special_or_whitespace("test@") is True
    assert contains_special_or_whitespace("    ") is True
    assert contains_special_or_whitespace("test-test") is False
    assert contains_special_or_whitespace("") is False


async def test_find_domains_handles_external_errors(
    outbound_domain_service: OutboundDomainService,
) -> None:
    """Test that find_domains handles external API errors gracefully."""

    # Mock the infraforge client to raise an ExternalServiceError
    with patch.object(
        outbound_domain_service.infraforge_client,
        "check_domain_availability",
        AsyncMock(return_value=None),
    ):
        request = FindDomainRequest(
            query="example.com",
            suggest_alternative=5,
        )

        # This should not raise an exception despite the external API error
        results = await outbound_domain_service.find_domains(
            request=request,
            user_id=uuid.uuid4(),
            organization_id=uuid.uuid4(),
        )

        # Should return an empty list due to the errors
        assert results == []


async def test_find_correct_payment_method(
    outbound_domain_service: OutboundDomainService,
) -> None:
    """Test that find_correct_payment_method returns the correct payment method."""
    payment_method = outbound_domain_service.find_correct_payment_method(
        domain_price_cents=1000,
        total_included_domains_remaining=1,
    )
    assert payment_method == DomainPaymentMethod.INCLUDED_IN_PLAN

    payment_method = outbound_domain_service.find_correct_payment_method(
        domain_price_cents=1000,
        total_included_domains_remaining=0,
    )
    assert payment_method == DomainPaymentMethod.ACCOUNTS_RECEIVABLE

    payment_method = outbound_domain_service.find_correct_payment_method(
        domain_price_cents=2200,
        total_included_domains_remaining=-1,
    )
    assert payment_method == DomainPaymentMethod.ACCOUNTS_RECEIVABLE

    payment_method = outbound_domain_service.find_correct_payment_method(
        domain_price_cents=10001,
        total_included_domains_remaining=1,
    )
    assert payment_method == DomainPaymentMethod.CONTACT_SUPPORT

    payment_method = outbound_domain_service.find_correct_payment_method(
        domain_price_cents=100001,
        total_included_domains_remaining=0,
    )
    assert payment_method == DomainPaymentMethod.CONTACT_SUPPORT


async def test_find_domains_with_new_domain_payments(
    outbound_domain_service: OutboundDomainService,
    multi_api_client: dict[str, CommonAPIClient],
) -> None:
    """Test that find_domains handles new domain payments correctly."""
    # Mock the infraforge client to return a test domain
    # This might look strange, but it's because check availability returns the domains in dollars, but the invoice is in cents, not sure why Infraforge does this
    admin_api_client = multi_api_client["admin"]
    user_id = admin_api_client.default_user_id
    organization_id = admin_api_client.default_organization_id
    mocked_check_domain_availability = [
        ExternalCheckDomainAvailabilityResponse(
            available=True,
            domain="example.ai",
            price=100.20,
        ),
        ExternalCheckDomainAvailabilityResponse(
            available=True,
            domain="example.com",
            price=58.00,
        ),
        ExternalCheckDomainAvailabilityResponse(
            available=True,
            domain="example.net",
            price=31.31,
        ),
        ExternalCheckDomainAvailabilityResponse(
            available=True,
            domain="example.org",
            price=11.19,
        ),
    ]
    for i in range(10):
        new_check_domain_availability = ExternalCheckDomainAvailabilityResponse(
            available=True,
            domain=f"example{i}.com",
            price=11.99,
        )
        mocked_check_domain_availability.append(new_check_domain_availability)

    mock_infraforge_client = AsyncMock()
    mock_infraforge_client.check_domain_availability.side_effect = (
        mocked_check_domain_availability
    )

    with (
        patch("salestech_be.settings.settings.enable_new_domain_payments", True),
        patch(
            "salestech_be.settings.settings.enable_new_domain_payments_org_ids",
            [organization_id],
        ),
        patch(
            "salestech_be.core.email.outbound_domain.service.check_find_request",
            return_value=None,
        ),
        patch(
            "salestech_be.core.email.outbound_domain.service.is_domain_available",
            return_value=True,
        ),
        patch.object(
            outbound_domain_service, "infraforge_client", mock_infraforge_client
        ),
    ):
        request = FindDomainRequest(
            query="example.ai",
            suggest_alternative=10,
        )

        results = await outbound_domain_service.find_domains(
            request=request,
            user_id=user_id,
            organization_id=organization_id,
        )

        domain_payment_methods = {r.domain: r for r in results}

        example_ai = domain_payment_methods["example.ai"]
        assert example_ai.payment_method == DomainPaymentMethod.CONTACT_SUPPORT
        assert example_ai.price_cents == 10020
        assert example_ai.domain == "example.ai"

        example_com = domain_payment_methods["example.com"]
        assert example_com.payment_method == DomainPaymentMethod.ACCOUNTS_RECEIVABLE
        assert example_com.price_cents == 5800
        assert example_com.domain == "example.com"

        example_net = domain_payment_methods["example.net"]
        assert example_net.payment_method == DomainPaymentMethod.ACCOUNTS_RECEIVABLE
        assert example_net.price_cents == 3131
        assert example_net.domain == "example.net"

        example_org = domain_payment_methods["example.org"]
        assert example_org.payment_method == DomainPaymentMethod.INCLUDED_IN_PLAN
        assert example_org.price_cents == 1119
        assert example_org.domain == "example.org"

        example1_com = domain_payment_methods["example1.com"]
        assert example1_com.payment_method == DomainPaymentMethod.INCLUDED_IN_PLAN
        assert example1_com.price_cents == 1199
        assert example1_com.domain == "example1.com"


async def test_get_or_create_org_level_num_mailbox_quota_with_backfill(
    outbound_domain_service: OutboundDomainService,
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    quota_service: QuotaService,
) -> None:
    admin_api_client = multi_api_client["admin"]
    user_id = admin_api_client.default_user_id
    organization_id = admin_api_client.default_organization_id

    quota_policy_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )

    # non-existent mailbox quota policy right now for this organization so it should all be 0's
    assert quota_policy_summary.total_remaining == 0
    assert quota_policy_summary.total_used == 0
    assert quota_policy_summary.total_remaining == 0

    # get_or_create_org_level_num_mailbox_quota_with_backfill creates the backfill, but the delta and usage is still 0
    new_quota_usage_with_actual_summary = await outbound_domain_service.get_or_create_org_level_num_mailbox_quota_with_backfill(
        organization_id=organization_id,
        user_id=user_id,
    )

    assert new_quota_usage_with_actual_summary.delta == 0
    assert new_quota_usage_with_actual_summary.quota_summary.total_used == 0
    assert new_quota_usage_with_actual_summary.quota_summary.total_remaining == 5
    assert new_quota_usage_with_actual_summary.quota_summary.total_limit == 5

    # when we fetch the summary it should be updated
    new_quota_policy_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )

    assert new_quota_policy_summary.total_used == 0
    assert new_quota_policy_summary.total_remaining == 5
    assert new_quota_policy_summary.total_limit == 5


async def test_get_or_create_org_level_num_domain_quota_with_backfill(
    outbound_domain_service: OutboundDomainService,
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    quota_service: QuotaService,
) -> None:
    """Test that get_or_create_org_level_num_domain_quota_with_backfill works correctly."""
    admin_api_client = multi_api_client["admin"]
    user_id = admin_api_client.default_user_id
    organization_id = admin_api_client.default_organization_id

    quota_policy_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.DOMAIN,
        period=QuotaPeriod.ANNUAL,
    )

    # non-existent right now so it should all be 0's
    assert quota_policy_summary.total_remaining == 0
    assert quota_policy_summary.total_used == 0
    assert quota_policy_summary.total_remaining == 0

    # get_or_create_org_level_num_domain_quota_with_backfill creates the backfill, but the delta and usage is still 0 (not remaining or limit)
    new_quota_usage_with_actual_summary = await outbound_domain_service.get_or_create_org_level_num_domain_quota_with_backfill(
        organization_id=organization_id,
        user_id=user_id,
    )

    assert new_quota_usage_with_actual_summary.delta == 0
    assert new_quota_usage_with_actual_summary.quota_summary.total_used == 0
    assert new_quota_usage_with_actual_summary.quota_summary.total_remaining == 2
    assert new_quota_usage_with_actual_summary.quota_summary.total_limit == 2

    # when we fetch the summary it should be updated
    new_quota_policy_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.DOMAIN,
        period=QuotaPeriod.ANNUAL,
    )

    assert new_quota_policy_summary.total_used == 0
    assert new_quota_policy_summary.total_remaining == 2
    assert new_quota_policy_summary.total_limit == 2


async def test_get_or_create_org_level_plan_included_domain_quota_with_backfill(
    outbound_domain_service: OutboundDomainService,
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    quota_service: QuotaService,
) -> None:
    """Test that get_or_create_org_level_plan_included_domain_quota_with_backfill works correctly."""
    admin_api_client = multi_api_client["admin"]
    user_id = admin_api_client.default_user_id
    organization_id = admin_api_client.default_organization_id

    quota_policy_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.PLAN_INCLUDED_COST_DOMAIN,
        period=QuotaPeriod.ANNUAL,
    )

    # non-existent right now so it should all be 0's
    assert quota_policy_summary.total_used == 0
    assert quota_policy_summary.total_remaining == 0
    assert quota_policy_summary.total_limit == 0

    # get_or_create_org_level_plan_included_domain_quota_with_backfill creates the backfill, but the delta and usage is still 0 (not remaining or limit)
    new_quota_usage_with_actual_summary = await outbound_domain_service.get_or_create_org_level_plan_included_domain_quota_with_backfill(
        organization_id=organization_id,
        user_id=user_id,
    )

    assert new_quota_usage_with_actual_summary.delta == 0
    assert new_quota_usage_with_actual_summary.quota_summary.total_used == 0
    assert new_quota_usage_with_actual_summary.quota_summary.total_remaining == 1
    assert new_quota_usage_with_actual_summary.quota_summary.total_limit == 1

    # when we fetch the summary it should be updated
    new_quota_policy_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.PLAN_INCLUDED_COST_DOMAIN,
        period=QuotaPeriod.ANNUAL,
    )

    assert new_quota_policy_summary.total_used == 0
    assert new_quota_policy_summary.total_remaining == 1
    assert new_quota_policy_summary.total_limit == 1


async def test_get_or_create_org_level_plan_included_mailbox_quota_with_backfill(
    outbound_domain_service: OutboundDomainService,
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    quota_service: QuotaService,
) -> None:
    """Test that get_or_create_org_level_plan_included_mailbox_quota_with_backfill works correctly."""
    admin_api_client = multi_api_client["admin"]
    user_id = admin_api_client.default_user_id
    organization_id = admin_api_client.default_organization_id

    quota_policy_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )

    # non-existent right now so it should all be 0's
    assert quota_policy_summary.total_used == 0
    assert quota_policy_summary.total_remaining == 0
    assert quota_policy_summary.total_limit == 0

    # get_or_create_org_level_plan_included_mailbox_quota_with_backfill creates the backfill, but the delta and usage is still 0 (not remaining or limit)
    new_quota_usage_with_actual_summary = await outbound_domain_service.get_or_create_org_level_plan_included_mailbox_quota_with_backfill(
        organization_id=organization_id,
        user_id=user_id,
    )

    assert new_quota_usage_with_actual_summary.delta == 0
    assert new_quota_usage_with_actual_summary.quota_summary.total_used == 0
    assert new_quota_usage_with_actual_summary.quota_summary.total_remaining == 2
    assert new_quota_usage_with_actual_summary.quota_summary.total_limit == 2

    # when we fetch the summary it should be updated
    new_quota_policy_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )

    assert new_quota_policy_summary.total_used == 0
    assert new_quota_policy_summary.total_remaining == 2
    assert new_quota_policy_summary.total_limit == 2


async def test_get_or_create_all_org_level_domain_and_mailbox_quotas_with_backfill(
    outbound_domain_service: OutboundDomainService,
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
    quota_service: QuotaService,
    outbound_workspace_repository: OutboundWorkspaceRepository,
    outbound_domain_repository: OutboundDomainRepository,
    email_account_repository: EmailAccountRepository,
) -> None:
    """Test that get_or_create_domain_and_mailbox_quotas works correctly."""
    admin_api_client = multi_api_client["admin"]
    user_id = admin_api_client.default_user_id
    organization_id = admin_api_client.default_organization_id
    domain_name = str(uuid.uuid4()) + ".com"

    # Create and insert a workspace into the database
    workspace = await outbound_workspace_repository.insert_outbound_workspace(
        organization_id=organization_id,
        name="Test Workspace",
        vendor=OutboundVendor.INFRAFORGE,
        external_id="ws_123456789",
    )
    assert workspace is not None

    # Create and insert a domain into the database for backfilling purposes
    domain = await outbound_domain_repository.insert_outbound_domain(
        organization_id=organization_id,
        workspace_id=workspace.id,
        created_by_user_id=user_id,
        domain=domain_name,
        status=OutboundDomainStatus.ACTIVE,
        external_id="ext_id_test_domain",
        forward_to_domain=None,
        invoice=OneTimeDomainPurchaseInvoice(
            amountPaid=1400,
            creditsApplied=0,
            id="654321",
            paidAt="2024-03-19T00:00:00Z",
            subTotal=1400,
            total=1400,
        ),
        transaction_type=AccountsReceivable.INCLUDED_IN_PLAN,
    )
    assert domain is not None

    # Create and insert a mailbox into the database for backfilling purposes
    await email_account_repository.create_email_account(
        EmailAccount(
            id=uuid.uuid4(),
            organization_id=organization_id,
            owner_user_id=user_id,
            email=f"test_mailbox@{domain_name}",
            type=EmailAccountType.OUTBOUND,
            outbound_domain_id=domain.id,
            active=True,
            external_id="ext_id_test_mailbox",
            vendor=EmailProvider.INFRAFORGE,
            first_name="Test",
            last_name="Mailbox",
            is_default=False,
            seconds_delay_between_emails=600,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
            transaction_type=AccountsReceivable.INCLUDED_IN_PLAN,
        )
    )

    # all of these upcoming quotas should be 0's since they don't exist yet and haven't been defensively created/gotten
    domain_quota_policy_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.DOMAIN,
        period=QuotaPeriod.ANNUAL,
    )
    assert domain_quota_policy_summary.total_used == 0
    assert domain_quota_policy_summary.total_remaining == 0
    assert domain_quota_policy_summary.total_limit == 0

    mailbox_quota_policy_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )
    assert mailbox_quota_policy_summary.total_used == 0
    assert mailbox_quota_policy_summary.total_remaining == 0
    assert mailbox_quota_policy_summary.total_limit == 0

    plan_included_domain_quota_policy_summary = (
        await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_DOMAIN,
            period=QuotaPeriod.ANNUAL,
        )
    )
    assert plan_included_domain_quota_policy_summary.total_used == 0
    assert plan_included_domain_quota_policy_summary.total_remaining == 0
    assert plan_included_domain_quota_policy_summary.total_limit == 0

    plan_included_mailbox_quota_policy_summary = (
        await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )
    )
    assert plan_included_mailbox_quota_policy_summary.total_used == 0
    assert plan_included_mailbox_quota_policy_summary.total_remaining == 0
    assert plan_included_mailbox_quota_policy_summary.total_limit == 0

    # now we call the function
    new_quota_usage_with_actual_summary_dict = (
        await outbound_domain_service.get_or_create_domain_and_mailbox_quotas(
            organization_id=organization_id,
            user_id=user_id,
        )
    )

    # Verify that the object was created with right key and values
    assert organization_id in new_quota_usage_with_actual_summary_dict
    assert (
        new_quota_usage_with_actual_summary_dict[
            organization_id
        ].org_level_num_domain_quota_object
        is not None
    )
    assert (
        new_quota_usage_with_actual_summary_dict[
            organization_id
        ].org_level_num_mailbox_quota_object
        is not None
    )
    assert (
        new_quota_usage_with_actual_summary_dict[
            organization_id
        ].org_level_plan_included_domain_quota_object
        is not None
    )
    assert (
        new_quota_usage_with_actual_summary_dict[
            organization_id
        ].org_level_plan_included_mailbox_quota_object
        is not None
    )

    # Verify that each summary is as we expect with backfilled values for usage
    domain_quota_policy_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.DOMAIN,
        period=QuotaPeriod.ANNUAL,
    )
    assert domain_quota_policy_summary.total_used == 1
    assert domain_quota_policy_summary.total_remaining == 1
    assert domain_quota_policy_summary.total_limit == 2

    mailbox_quota_policy_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )
    assert mailbox_quota_policy_summary.total_used == 1
    assert mailbox_quota_policy_summary.total_remaining == 4
    assert mailbox_quota_policy_summary.total_limit == 5

    plan_included_domain_quota_policy_summary = (
        await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_DOMAIN,
            period=QuotaPeriod.ANNUAL,
        )
    )
    assert plan_included_domain_quota_policy_summary.total_used == 1
    assert plan_included_domain_quota_policy_summary.total_remaining == 0
    assert plan_included_domain_quota_policy_summary.total_limit == 1

    plan_included_mailbox_quota_policy_summary = (
        await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )
    )
    assert plan_included_mailbox_quota_policy_summary.total_used == 1
    assert plan_included_mailbox_quota_policy_summary.total_remaining == 1
    assert plan_included_mailbox_quota_policy_summary.total_limit == 2

    # verify that if usage is incorrectly increased, it will be corrected by the backfill
    await quota_service.increase_usage(
        organization_id=organization_id,
        entity_id=organization_id,
        entity_type=QuotaConsumerEntityType.ORGANIZATION,
        resource=QuotaConsumingResource.MAILBOX,
        usage=1,
        timestamp=zoned_utc_now(),
    )

    mailbox_quota_policy_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )
    assert mailbox_quota_policy_summary.total_used == 2
    assert mailbox_quota_policy_summary.total_remaining == 3
    assert mailbox_quota_policy_summary.total_limit == 5

    await (
        outbound_domain_service.get_or_create_org_level_num_mailbox_quota_with_backfill(
            organization_id=organization_id, user_id=user_id
        )
    )

    # verify that the backfill corrected the usage
    mailbox_quota_policy_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.MAILBOX,
        period=QuotaPeriod.ANNUAL,
    )

    assert mailbox_quota_policy_summary.total_used == 1
    assert mailbox_quota_policy_summary.total_remaining == 4
    assert mailbox_quota_policy_summary.total_limit == 5


async def test_domain_purchase_slack_alert(
    outbound_domain_service: OutboundDomainService,
    multi_api_client: dict[str, CommonAPIClient],
    api_client: AsyncClient,
) -> None:
    """Test that the domain purchase slack alert is sent correctly."""
    admin_api_client = multi_api_client["admin"]
    organization_id = admin_api_client.default_organization_id
    user_id = admin_api_client.default_user_id
    test_domain = "testdomain.com"

    # Create mock purchase response
    mock_purchase_response = ExternalBuyDomainsResponse(
        domains=[
            ExternalGetDomainsResponse(
                id="dom_123456789",
                createdAt="2024-03-19T00:00:00Z",
                sld="testdomain",
                status=InfraforgeDomainStatus.ACTIVE,
                tld="com",
                updatedAt="2024-03-19T00:00:00Z",
                workspaceId="ws_123456789",
                setupId="setup_123456789",
            )
        ],
        setup=SetupResponse(
            accountId="acc_123456789",
            address1="2445 Augustine Dr",
            address2="Suite 500",
            city="Santa Clara",
            country="US",
            createdAt="2024-03-19T00:00:00Z",
            email="<EMAIL>",
            firstName="Curtis",
            forwardToDomain="https://reevo.ai",
            id="setup_123456789",
            lastName="Tan",
            organization="Reevo",
            phone="*************",
            postalCode="95054",
            province="CA",
            status=SetupStatus.COMPLETED,
            updatedAt="2024-03-19T00:00:00Z",
            dmarcEmail="<EMAIL>",
            jobTitle=None,
            workspaceId="ws_123456789",
        ),
        invoice=OneTimeDomainPurchaseInvoice(
            id="inv_123456789",
            amountPaid=1000,
            creditsApplied=0,
            paidAt="2024-03-19T00:00:00Z",
            subTotal=1000,
            total=1000,
        ),
    )

    # Create request
    request = BuyDomainRequest(
        domains=[test_domain],
        forward_to_domain="https://reevo.ai",
        is_mock_record=False,
    )

    # TODO: @benson remove FF once approved
    with (
        patch(
            "salestech_be.settings.settings.enable_slack_domain_purchase_alert", True
        ),
        patch.object(
            outbound_domain_service.infraforge_client,
            "buy_domains",
            AsyncMock(return_value=mock_purchase_response),
        ),
        patch.object(
            outbound_domain_service,
            "get_or_create_workspace",
            AsyncMock(
                return_value=OutboundWorkspace(
                    id=uuid.uuid4(),
                    organization_id=organization_id,
                    name="Test Workspace",
                    vendor=OutboundVendor.INFRAFORGE,
                    external_id="ws_123456789",
                )
            ),
        ),
        patch.object(
            outbound_domain_service,
            "_send_domain_purchase_slack_alert",
            new_callable=AsyncMock,
        ) as mock_send_slack_alert,
    ):
        # Call _purchase_domains
        result = await outbound_domain_service._purchase_domains(
            request=request,
            user_id=user_id,
            organization_id=organization_id,
            purchasable_domains=[test_domain],
        )

        # Verify the response
        assert result == mock_purchase_response

        # Verify _send_domain_purchase_slack_alert was called with correct arguments
        mock_send_slack_alert.assert_called_once_with(
            infraforge_response=mock_purchase_response,
            full_domain_name=test_domain,
            organization_id=organization_id,
        )


async def test_fix_domain_health_records(
    outbound_domain_service: OutboundDomainService,
    outbound_domain_repository: OutboundDomainRepository,
    domain_health_repository: DomainHealthRepository,
    outbound_workspace_repository: OutboundWorkspaceRepository,
    api_client: AsyncClient,
    multi_api_client: dict[str, CommonAPIClient],
) -> None:
    """Test that fix_domain_health_records works correctly."""
    admin_api_client = multi_api_client["admin"]
    organization_id = admin_api_client.default_organization_id
    user_id = admin_api_client.default_user_id
    domain_name = str(uuid.uuid4()) + ".com"

    workspace = await outbound_workspace_repository.insert_outbound_workspace(
        organization_id=organization_id,
        name="Test Workspace",
        vendor=OutboundVendor.INFRAFORGE,
        external_id="ws_123456789",
    )
    assert workspace is not None

    invoice = OneTimeDomainPurchaseInvoice(
        amountPaid=1400,
        creditsApplied=0,
        id="654321",
        paidAt="2024-03-19T00:00:00Z",
        subTotal=1400,
        total=1400,
    )

    outbound_domain = await outbound_domain_repository.insert_outbound_domain(
        organization_id=organization_id,
        workspace_id=workspace.id,
        created_by_user_id=user_id,
        domain=domain_name,
        status=OutboundDomainStatus.ACTIVE,
        external_id="ext_id_test_domain",
        forward_to_domain=None,
        invoice=invoice,
        transaction_type=AccountsReceivable.INCLUDED_IN_PLAN,
    )

    assert outbound_domain is not None

    domain_health_object = DomainHealth(
        domain_id=outbound_domain.id,
        mx_records=[],
        spf_records=[],
        dkim_record=[],
        dmarc_record=[],
    )
    await domain_health_repository.upsert_domain_health(
        domain_health=domain_health_object,
    )

    new_domain_health_object = DomainHealth(
        domain_id=outbound_domain.id,
        mx_records=not_none(
            EntityDnsRecord.list_from_request_field(
                ["10 mx1.reevo-internal.mocked", "20 mx2.reevo-internal.mocked"]
            )
        ),
        spf_records=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=spf1 include:_spf.reevo-internal.mocked ~all"]
            )
        ),
        dkim_record=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=DKIM1; k=rsa; p=ThisIsATotallyMockedDkimPublicKey..."]
            )
        ),
        dmarc_record=not_none(
            EntityDnsRecord.list_from_request_field(
                ["v=DMARC1; p=reject; rua=mailto:<EMAIL>"]
            )
        ),
    )

    domain_health = await domain_health_repository.upsert_domain_health(
        domain_health=new_domain_health_object,
    )

    assert domain_health is not None
    assert domain_health.mx_records == EntityDnsRecord.list_from_request_field(
        [
            "10 mx1.reevo-internal.mocked",
            "20 mx2.reevo-internal.mocked",
        ]
    )
    assert domain_health.spf_records == EntityDnsRecord.list_from_request_field(
        [
            "v=spf1 include:_spf.reevo-internal.mocked ~all",
        ]
    )
    assert domain_health.dkim_record == EntityDnsRecord.list_from_request_field(
        [
            "v=DKIM1; k=rsa; p=ThisIsATotallyMockedDkimPublicKey...",
        ]
    )
    assert domain_health.dmarc_record == EntityDnsRecord.list_from_request_field(
        [
            "v=DMARC1; p=reject; rua=mailto:<EMAIL>",
        ]
    )
