from collections.abc import Awaitable, Callable
from uuid import UUID, uuid4

from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.db.dao.company_repository import CompanyRepository
from salestech_be.db.dto.company_dto import CompanyDto
from salestech_be.db.models.company import Company
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.prospecting.common.prospecting_common_service import (
    ProspectingCommonService,
)


async def test_convert_to_accounts_success(
    prospecting_common_service: ProspectingCommonService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    account_service: AccountService,
    company_repository: CompanyRepository,
) -> None:
    user_id, organization_id = await make_user_org()
    company_id = uuid4()
    company = Company(
        id=company_id,
        name="Test Company",
        primary_domain="testcompany.com",
        website_url="https://testcompany.com",
        linkedin_url="https://linkedin.com/company/testcompany",
        organization_id=organization_id,
        user_id=user_id,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await company_repository.insert(company)
    account = await account_service.get_or_create_account(
        create_account_request=CreateAccountRequest(
            company_id=company_id,
            display_name="Test Company",
            owner_user_id=user_id,
            domain_name="testcompany.com",
            official_website="https://testcompany.com",
        ),
        organization_id=organization_id,
        user_id=user_id,
    )
    assert account is not None
    assert account.id is not None
    assert account.company_id == company_id
    assert account.domain_name == "testcompany.com"
    assert account.official_website == "https://testcompany.com"
    assert account.owner_user_id == user_id
    assert account.organization_id == organization_id
    assert account.created_at is not None
    assert account.updated_at is not None

    company_dto = CompanyDto(db_company=company)
    account_simple_list = await prospecting_common_service.convert_company_to_accounts(
        company_dto_list=[company_dto],
        organization_id=organization_id,
        user_id=user_id,
    )
    assert len(account_simple_list) == 1
    assert account_simple_list[0].id == account.id
    assert account_simple_list[0].company_id == company_id


async def test_convert_to_accounts_success_with_linkedin_url_and_website_url(
    prospecting_common_service: ProspectingCommonService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    account_service: AccountService,
    company_repository: CompanyRepository,
) -> None:
    user_id, organization_id = await make_user_org()
    company_id = uuid4()
    company = Company(
        id=company_id,
        name="Test Company",
        primary_domain="testcompany.com",
        website_url="https://testcompany.com",
        linkedin_url="https://linkedin.com/company/testcompany",
        organization_id=organization_id,
        user_id=user_id,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await company_repository.insert(company)
    account = await account_service.get_or_create_account(
        create_account_request=CreateAccountRequest(
            company_id=company_id,
            display_name="Test Company",
            owner_user_id=user_id,
            domain_name="testcompany.com",
            official_website="https://testcompany.com",
        ),
        organization_id=organization_id,
        user_id=user_id,
    )
    assert account is not None
    assert account.id is not None
    assert account.company_id == company_id
    assert account.domain_name == "testcompany.com"
    assert account.official_website == "https://testcompany.com"
    assert account.owner_user_id == user_id
    assert account.organization_id == organization_id
    assert account.created_at is not None
    assert account.updated_at is not None

    company1 = Company(
        id=uuid4(),
        name="Test Company1",
        primary_domain="linkedin.com/company/testcompany1",
        website_url="https://linkedin.com/company/testcompany1",
        linkedin_url="https://linkedin.com/company/testcompany1",
        organization_id=organization_id,
        user_id=user_id,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await company_repository.insert(company1)

    company_dto_list = [CompanyDto(db_company=company), CompanyDto(db_company=company1)]
    account_simple_list = await prospecting_common_service.convert_company_to_accounts(
        company_dto_list=company_dto_list,
        organization_id=organization_id,
        user_id=user_id,
    )
    assert len(account_simple_list) == 2
    company_ids = [account_simple_list[0].company_id, account_simple_list[1].company_id]
    accounts = await account_service.list_account_by_company_ids(
        organization_id=organization_id,
        company_ids=company_ids,
    )
    account_map = {account.company_id: account for account in accounts}
    # check the second account
    account1 = account_map[company1.id]
    assert account1 is not None
    assert account1.id is not None
    assert account1.company_id == company1.id
    assert account1.domain_name is None
    assert account1.official_website is None
    assert account1.owner_user_id == user_id
    assert account1.organization_id == organization_id

    # convert again should get the same result
    account_simple_list = await prospecting_common_service.convert_company_to_accounts(
        company_dto_list=company_dto_list,
        organization_id=organization_id,
        user_id=user_id,
    )
    assert len(account_simple_list) == 2


async def test_convert_to_accounts_success_with_primary_domain_is_none(
    prospecting_common_service: ProspectingCommonService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    account_service: AccountService,
    company_repository: CompanyRepository,
) -> None:
    user_id, organization_id = await make_user_org()
    company_id = uuid4()
    company = Company(
        id=company_id,
        name="Test Company",
        primary_domain="testcompany.com",
        website_url="https://testcompany.com",
        linkedin_url="https://linkedin.com/company/testcompany",
        organization_id=organization_id,
        user_id=user_id,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await company_repository.insert(company)
    account = await account_service.get_or_create_account(
        create_account_request=CreateAccountRequest(
            company_id=company_id,
            display_name="Test Company",
            owner_user_id=user_id,
            domain_name="testcompany.com",
            official_website="https://testcompany.com",
        ),
        organization_id=organization_id,
        user_id=user_id,
    )
    assert account is not None
    assert account.id is not None
    assert account.company_id == company_id
    assert account.domain_name == "testcompany.com"
    assert account.official_website == "https://testcompany.com"
    assert account.owner_user_id == user_id
    assert account.organization_id == organization_id
    assert account.created_at is not None
    assert account.updated_at is not None

    company1 = Company(
        id=uuid4(),
        name="Test Company1",
        primary_domain=None,
        website_url="https://linkedin.com/company/testcompany1",
        linkedin_url="https://linkedin.com/company/testcompany1",
        organization_id=organization_id,
        user_id=user_id,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )
    await company_repository.insert(company1)

    company_dto_list = [CompanyDto(db_company=company), CompanyDto(db_company=company1)]
    account_simple_list = await prospecting_common_service.convert_company_to_accounts(
        company_dto_list=company_dto_list,
        organization_id=organization_id,
        user_id=user_id,
    )
    assert len(account_simple_list) == 2
    company_ids = [account_simple_list[0].company_id, account_simple_list[1].company_id]
    accounts = await account_service.list_account_by_company_ids(
        organization_id=organization_id,
        company_ids=company_ids,
    )
    account_map = {account.company_id: account for account in accounts}
    # check the second account
    account1 = account_map[company1.id]
    assert account1 is not None
    assert account1.id is not None
    assert account1.company_id == company1.id
    assert account1.domain_name is None
    assert account1.official_website is None
    assert account1.owner_user_id == user_id
    assert account1.organization_id == organization_id

    # convert again should get the same result
    account_simple_list = await prospecting_common_service.convert_company_to_accounts(
        company_dto_list=company_dto_list,
        organization_id=organization_id,
        user_id=user_id,
    )
    assert len(account_simple_list) == 2
