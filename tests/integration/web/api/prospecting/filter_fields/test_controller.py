import uuid
from unittest.mock import AsyncMock, patch

import pytest

from salestech_be.common.exception.exception import InvalidArgumentError
from salestech_be.common.query_util.filter_schema import (
    CompositeFilter,
    FilterSpec,
    ValueFilter,
)
from salestech_be.common.query_util.operator import MatchOperator
from salestech_be.common.schema_manager.std_object_field_identifier import (
    ProspectingFilterFieldOptionsFacetField,
)
from salestech_be.common.type.metadata.schema import QualifiedField
from salestech_be.core.data.types import StandardRecord
from salestech_be.core.prospecting.type.filter_field import (
    FilterFieldOptionsFacetV2,
    FilterFieldOptionsV2,
    ProspectingFilterFieldType,
)
from salestech_be.db.dao.prospecting_tag_repository import ProspectingTagRepository
from salestech_be.db.models.prospecting_tag import ProspectingTag
from salestech_be.db.models.search_query import ProspectingSearchQueryType
from salestech_be.integrations.pdl.model import (
    PdlAutocompleteResponse,
    PeopleDataLabsAutocompleteItem,
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.common.container import (
    ListEntityRequestV2,
    ListResponse,
)
from salestech_be.web.api.prospecting.filter_fields.schema import (
    SearchFilterFieldOptionsRequest,
)
from tests.integration.web.api.util.api_client import APITestClient


async def create_prospecting_tag(
    prospecting_tag_repository: ProspectingTagRepository,
    ext_id: str | None = None,
    kind: ProspectingFilterFieldType = ProspectingFilterFieldType.location,
    name: str = "name",
) -> ProspectingTag:
    """Helper function to create a prospecting tag for testing.

    Args:
        prospecting_tag_repository: Repository to insert tag
        ext_id: External ID for the tag
        kind: Kind of tag
        name: Name of the tag

    Returns:
        The created prospecting tag
    """
    return await prospecting_tag_repository.insert(
        ProspectingTag(
            id=uuid.uuid4(),
            ext_id=ext_id if ext_id else str(uuid.uuid4()),
            kind=kind,
            name=name,
            value=name,
            category="category",
            data={"k": "v"},
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
    )


async def test_list_filter_fields_success(
    authed_api_test_client: APITestClient,
) -> None:
    # Test basic listing of all filter fields
    all_fields_response = await authed_api_test_client.post(
        endpoint_name="list_prospecting_filter_fields",
        response_type=ListResponse[StandardRecord[FilterFieldOptionsFacetV2]],
        request_body=ListEntityRequestV2(),
    )
    assert all_fields_response is not None

    # Get total count of filter fields from all query types
    filter_fields_dict = ProspectingFilterFieldType.get_filter_field_list_by_pdl()
    total_filter_field_count = sum(
        len(fields) for fields in filter_fields_dict.values()
    )
    assert len(all_fields_response.list_data) == total_filter_field_count

    # Test listing with filter to show only searchable fields for PEOPLE query type
    searchable_fields_response = await authed_api_test_client.post(
        endpoint_name="list_prospecting_filter_fields",
        response_type=ListResponse[StandardRecord[FilterFieldOptionsFacetV2]],
        request_body=ListEntityRequestV2(
            filter_spec=FilterSpec(
                primary_object_identifier=FilterFieldOptionsFacetV2.object_id,
                filter=CompositeFilter(
                    all_of=[
                        ValueFilter(
                            field=QualifiedField(
                                path=(
                                    ProspectingFilterFieldOptionsFacetField.prospecting_search_query_type,
                                ),
                            ),
                            value=ProspectingSearchQueryType.PEOPLE,
                            operator=MatchOperator.EQ,
                        ),
                        ValueFilter(
                            field=QualifiedField(
                                path=(
                                    ProspectingFilterFieldOptionsFacetField.is_searchable,
                                ),
                            ),
                            value=True,
                            operator=MatchOperator.EQ,
                        ),
                    ],
                ),
            )
        ),
    )
    assert searchable_fields_response is not None
    assert len(searchable_fields_response.list_data) == 7

    # Test filtering by PEOPLE query type
    people_fields_response = await authed_api_test_client.post(
        endpoint_name="list_prospecting_filter_fields",
        response_type=ListResponse[StandardRecord[FilterFieldOptionsFacetV2]],
        request_body=ListEntityRequestV2(
            filter_spec=FilterSpec(
                primary_object_identifier=FilterFieldOptionsFacetV2.object_id,
                filter=CompositeFilter(
                    all_of=[
                        ValueFilter(
                            field=QualifiedField(
                                path=(
                                    ProspectingFilterFieldOptionsFacetField.prospecting_search_query_type,
                                ),
                            ),
                            value=ProspectingSearchQueryType.PEOPLE,
                            operator=MatchOperator.EQ,
                        )
                    ],
                ),
            )
        ),
    )
    assert people_fields_response is not None
    assert len(people_fields_response.list_data) == len(
        filter_fields_dict[ProspectingSearchQueryType.PEOPLE]
    )

    # Test filtering by COMPANY query type
    company_fields_response = await authed_api_test_client.post(
        endpoint_name="list_prospecting_filter_fields",
        response_type=ListResponse[StandardRecord[FilterFieldOptionsFacetV2]],
        request_body=ListEntityRequestV2(
            filter_spec=FilterSpec(
                primary_object_identifier=FilterFieldOptionsFacetV2.object_id,
                filter=CompositeFilter(
                    all_of=[
                        ValueFilter(
                            field=QualifiedField(
                                path=(
                                    ProspectingFilterFieldOptionsFacetField.prospecting_search_query_type,
                                ),
                            ),
                            value=ProspectingSearchQueryType.COMPANY,
                            operator=MatchOperator.EQ,
                        )
                    ],
                ),
            )
        ),
    )
    assert company_fields_response is not None
    assert len(company_fields_response.list_data) == len(
        filter_fields_dict[ProspectingSearchQueryType.COMPANY]
    )


async def test_search_filter_fields_success(
    prospecting_tag_repository: ProspectingTagRepository,
    authed_api_test_client: APITestClient,
) -> None:
    # Mock PDL client response for PDL-enabled scenario
    mock_pdl_response = PdlAutocompleteResponse(
        data=[
            PeopleDataLabsAutocompleteItem(name="Software Development Lead", count=100),
            PeopleDataLabsAutocompleteItem(name="Software Testing Engineer", count=50),
        ],
        fields=["job_title"],
        status=200,
    )

    with patch(
        "salestech_be.integrations.pdl.pdl_client.PdlClient.autocomplete",
        new_callable=AsyncMock,
        return_value=mock_pdl_response,
    ):
        # Test PDL search
        search_response = await authed_api_test_client.post(
            endpoint_name="search_filter_field_options",
            response_type=ListResponse[FilterFieldOptionsV2],
            request_body=SearchFilterFieldOptionsRequest(
                filter_field_type=ProspectingFilterFieldType.person_title,
                query="Software",
            ),
        )

        # Verify PDL search results
        assert search_response is not None
        assert len(search_response.list_data) == 2
        assert search_response.list_data[0].display_name == "Software Development Lead"
        assert search_response.list_data[1].display_name == "Software Testing Engineer"
        assert search_response.list_data[0].count == 100
        assert search_response.list_data[1].count == 50


async def test_search_filter_fields_invalid_type(
    authed_api_test_client: APITestClient,
) -> None:
    """Test search filter fields with invalid input parameters.

    Tests various error cases:
    - Query too short
    - Missing query or filter field type
    """

    # Test with query string that's too short
    with pytest.raises(InvalidArgumentError):
        await authed_api_test_client.post(
            endpoint_name="search_filter_field_options",
            response_type=ListResponse[FilterFieldOptionsV2],
            request_body=SearchFilterFieldOptionsRequest(
                filter_field_type=ProspectingFilterFieldType.person_title,
                query="",
            ),
        )

    # Test with null query
    with pytest.raises(InvalidArgumentError):
        await authed_api_test_client.post(
            endpoint_name="search_filter_field_options",
            response_type=ListResponse[FilterFieldOptionsV2],
            request_body=SearchFilterFieldOptionsRequest(
                filter_field_type=ProspectingFilterFieldType.person_title,
                query=None,
            ),
        )

    # Test with null filter field type and query
    with pytest.raises(InvalidArgumentError):
        await authed_api_test_client.post(
            endpoint_name="search_filter_field_options",
            response_type=ListResponse[FilterFieldOptionsV2],
            request_body=SearchFilterFieldOptionsRequest(
                filter_field_type=None,
                query=None,
            ),
        )
