from collections.abc import Awaitable, Callable
from datetime import datetime
from unittest.mock import AsyncMock, patch
from uuid import UUID, uuid4

import jwt
import pytest
import pytz
from fastapi import FastAPI
from httpx import AsyncClient

from salestech_be.common.exception.exception import PaymentError
from salestech_be.core.auth.service import UserAuthService
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.quota.service.quota_service import QuotaService
from salestech_be.core.user.service.permission_service import (
    PermissionService,
)
from salestech_be.core.user.types import PermissionSpecialOperations, UserRole
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.models.quota import QuotaConsumingResource, QuotaPeriod
from salestech_be.db.models.user_organization_association import (
    UserOrganizationAssociationStatus,
)
from salestech_be.integrations.knock.model import KnockTenant, KnockUser
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.validation import not_none
from tests.integration.web.api.util.common_api_client import CommonAPIClient

logger = get_logger(__name__)


async def test_microsoft_personal_account(
    fastapi_app: FastAPI,
    api_client: AsyncClient,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
) -> None:
    user_id, organization_id = await make_user_org()

    with (
        patch(
            "salestech_be.services.auth.clients.auth0_oauth2_client.authorize_access_token",
            new_callable=AsyncMock,
            return_value={"userinfo": {"email": "<EMAIL>"}},
        ),
        patch(
            "salestech_be.integrations.knock.client.KnockClient.identify_user",
            new_callable=AsyncMock,
            return_value=KnockUser(id="test-knock-id"),
        ),
        patch(
            "salestech_be.integrations.knock.client.KnockClient.set_tenant",
            new_callable=AsyncMock,
            return_value=KnockTenant(id="test-knock-id", properties={}),
        ),
    ):
        post_response = await api_client.get(
            "/api/v1/auth/callback",
            headers={
                "x-reevo-user-id": str(user_id),
                "x-reevo-org-id": str(organization_id),
                "accept": "application/json",
                "Content-Type": "application/json",
            },
        )
        logger.info(post_response.json())
        assert (
            post_response.json()["detail"]
            == "do not support Microsoft personal account"
        )
        assert post_response.status_code == 403


async def test_non_microsoft_personal_account(
    fastapi_app: FastAPI,
    api_client: AsyncClient,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
) -> None:
    user_id, organization_id = await make_user_org()

    with (
        patch(
            "salestech_be.services.auth.clients.auth0_oauth2_client.authorize_access_token",
            new_callable=AsyncMock,
            return_value={
                "userinfo": {
                    "email": "<EMAIL>",
                    "iss": str(uuid4()),
                    "aud": str(uuid4()),
                    "sub": str(uuid4()),
                }
            },
        ),
        patch(
            "salestech_be.integrations.knock.client.KnockClient.identify_user",
            new_callable=AsyncMock,
            return_value=KnockUser(id="test-knock-id"),
        ),
        patch(
            "salestech_be.integrations.knock.client.KnockClient.set_tenant",
            new_callable=AsyncMock,
            return_value=KnockTenant(id="test-knock-id", properties={}),
        ),
    ):
        post_response = await api_client.get(
            "/api/v1/auth/callback",
            headers={
                "x-reevo-user-id": str(user_id),
                "x-reevo-org-id": str(organization_id),
                "accept": "application/json",
                "Content-Type": "application/json",
            },
        )
        logger.info(post_response.json())
        assert post_response.status_code == 200


async def test_default_permission_sets_and_groups_are_created_upon_non_microsoft_personal_account(
    fastapi_app: FastAPI,
    api_client: AsyncClient,
    make_user: Callable[[], Awaitable[UUID]],
    permission_service: PermissionService,
) -> None:
    user_id = await make_user()

    with (
        patch(
            "salestech_be.services.auth.clients.auth0_oauth2_client.authorize_access_token",
            new_callable=AsyncMock,
            return_value={
                "userinfo": {
                    "email": f"xxxx-{uuid4()!s}@admin.com",
                    "iss": str(uuid4()),
                    "aud": str(uuid4()),
                    "sub": str(uuid4()),
                }
            },
        ),
        patch(
            "salestech_be.integrations.knock.client.KnockClient.identify_user",
            new_callable=AsyncMock,
            return_value=KnockUser(id="test-knock-id"),
        ),
        patch(
            "salestech_be.integrations.knock.client.KnockClient.set_tenant",
            new_callable=AsyncMock,
            return_value=KnockTenant(id="test-knock-id", properties={}),
        ),
    ):
        post_response = await api_client.get(
            "/api/v1/auth/callback",
            headers={
                # Organization ID is not provided since we are expecting the first user to have their organization created for them.
                "x-reevo-user-id": str(user_id),
                "accept": "application/json",
                "Content-Type": "application/json",
            },
        )
        assert post_response.status_code == 200
        response = post_response.json()

        # WARNING: we are using verify_signature=False here because we do not care to validate the token during tests.
        # Do NOT do this in production!
        perm = jwt.decode(
            jwt=response["reevo_access_token"], options={"verify_signature": False}
        )["perm"]
        assert PermissionSpecialOperations.ADMIN_ALL.value in perm

        # We need an explicit cast to UUID since we are unwrapping the API response.
        organization_id: UUID = UUID(str(response["organization_id"]))

        # check if the default permission sets (super user, admin, user) are created

        default_super_user_permission_set = (
            await permission_service.get_permission_set_by_name(
                name=PermissionService.DEFAULT_SUPER_USER_PERMISSION_SET_NAME,
                organization_id=organization_id,
            )
        )
        assert default_super_user_permission_set is not None

        default_admin_permission_set = (
            await permission_service.get_permission_set_by_name(
                name=PermissionService.DEFAULT_ADMIN_PERMISSION_SET_NAME,
                organization_id=organization_id,
            )
        )
        assert default_admin_permission_set is not None

        default_user_permission_set = (
            await permission_service.get_permission_set_by_name(
                name=PermissionService.DEFAULT_USER_PERMISSION_SET_NAME,
                organization_id=organization_id,
            )
        )
        assert default_user_permission_set is not None

        # check if the permission set groups (admin, user) is created

        default_admin_permission_set_group = (
            await permission_service.get_permission_set_group_by_name(
                name=PermissionService.DEFAULT_ADMIN_PERMISSION_SET_GROUP_NAME,
                organization_id=organization_id,
            )
        )
        assert default_admin_permission_set_group is not None

        default_user_permission_set_group = (
            await permission_service.get_permission_set_group_by_name(
                name=PermissionService.DEFAULT_USER_PERMISSION_SET_GROUP_NAME,
                organization_id=organization_id,
            )
        )
        assert default_user_permission_set_group is not None


async def test_user_invite_default_permission_setup(
    permission_service: PermissionService,
    user_auth_service: UserAuthService,
    common_api_client: CommonAPIClient,
) -> None:
    """Test that when a new user is invited, they get the correct default permissions."""
    organization_id = common_api_client.default_organization_id
    test_email = f"test.user-{uuid4()!s}@gmail.com"

    with (
        patch.object(
            UserAuthService,
            "setup_auth_db_user",
            new_callable=AsyncMock,
        ),
        patch.object(
            UserAuthService,
            "get_reset_password_link",
            new_callable=AsyncMock,
            return_value="mock-link",
        ),
        patch(
            "salestech_be.integrations.auth0.client.get_auth0_database_authentication_api_client",
            new_callable=AsyncMock,
        ),
        patch(
            "salestech_be.util.asyncio_util.adapter.run_in_pool",
            new_callable=AsyncMock,
        ),
    ):
        await common_api_client.backfill_permissions(organization_id)

        # Invite the new user
        new_user_org_association, _ = await user_auth_service.invite_new_user(
            user_auth_context=UserAuthContext(
                organization_id=organization_id,
                user_id=common_api_client.default_user_id,
            ),
            email=test_email,
            organization_id=organization_id,
            roles=[UserRole.USER],
        )
        assert new_user_org_association is not None
        new_user_id = new_user_org_association.user_id

        # Get the default user permission set group
        default_user_permission_set_group = (
            await permission_service.get_permission_set_group_by_name(
                name=PermissionService.DEFAULT_USER_PERMISSION_SET_GROUP_NAME,
                organization_id=organization_id,
            )
        )
        assert default_user_permission_set_group is not None

        # Get the user's permission set group associations
        permission_claims = (
            await permission_service.get_user_permission_set_group_associations(
                user_id=new_user_id,
                organization_id=organization_id,
            )
        )
        assert len(permission_claims) > 0
        assert next(iter(permission_claims.items())) == (
            default_user_permission_set_group.name,
            default_user_permission_set_group.id,
        )

        # Get all user permissions to verify they have the correct permissions
        user_permissions = await permission_service.get_user_permission_claims(
            user_id=new_user_id,
            organization_id=organization_id,
        )

        # Verify the user has all permissions from the default user permission set group
        permission_sets = default_user_permission_set_group.permission_sets
        if permission_sets and permission_sets[0].allowed_operations:
            # If there are permissions defined, verify they match exactly
            assert all(
                permission in user_permissions
                for permission in permission_sets[0].allowed_operations
            )
        else:
            # If no permissions are defined, verify user has no permissions
            assert not user_permissions


@patch.object(settings, "enable_core_plan_quota_policy", True)
async def test_user_invite_default_permission_setup_with_quota_policy(
    user_auth_service: UserAuthService,
    user_repository: UserRepository,
    quota_service: QuotaService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
) -> None:
    # Boilerplate to create a user, and associate them with an organization.
    user_id, organization_id = await make_user_org()
    await user_repository.associate_user_to_an_organization(
        user_id=user_id,
        organization_id=organization_id,
        roles=[UserRole.ADMIN],
    )

    # Test that the quota is created, and backfilled.
    await user_auth_service.get_or_create_active_users_quota_with_backfill(
        organization_id=organization_id,
        user_id=user_id,
    )

    # Pull the quota summary to verify the quota is backfilled.
    quota_summary = await quota_service.get_quota_summary_per_resource(
        organization_id=organization_id,
        resource=QuotaConsumingResource.USERS,
        period=QuotaPeriod.LIFETIME,
    )

    assert quota_summary.total_used == 1
    assert quota_summary.total_remaining == 4
    assert quota_summary.total_limit == 5


@patch.object(settings, "enable_core_plan_quota_policy", True)
async def test_user_invite_without_an_org_id(
    user_auth_service: UserAuthService,
    user_repository: UserRepository,
    quota_service: QuotaService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
) -> None:
    with (
        # Mock the auth0 client.
        patch.object(
            UserAuthService,
            "setup_auth_db_user",
            new_callable=AsyncMock,
        ),
        patch.object(
            UserAuthService,
            "get_reset_password_link",
            new_callable=AsyncMock,
            return_value="mock-link",
        ),
        patch(
            "salestech_be.integrations.auth0.client.get_auth0_database_authentication_api_client",
            new_callable=AsyncMock,
        ),
        patch(
            "salestech_be.util.asyncio_util.adapter.run_in_pool",
            new_callable=AsyncMock,
        ),
    ):
        user_id, organization_id = await make_user_org()
        uoa, _ = await user_auth_service.invite_new_user(
            user_auth_context=UserAuthContext(
                organization_id=organization_id,
                user_id=user_id,  # this represents the super_admin that is inviting the initial user.
            ),
            email=f"test.user-{uuid4()}@reevo.local",
            roles=[UserRole.ADMIN],
            organization_id=None,  # purposely not providing an org id to simulate the first user to be invited.
        )
        assert uoa is not None
        assert (
            uoa.organization_id is not None
        )  # the first user to be invited should have their org created for them.
        assert uoa.user_id is not None
        assert uoa.roles == [UserRole.ADMIN]

        num_active_users = (
            await user_repository.get_num_active_users_by_organization_id(
                organization_id=uoa.organization_id,
            )
        )
        assert num_active_users == 1  # the number of active users should be 1.

        # Pull the quota summary to verify the quota is backfilled.
        quota_summary = await quota_service.get_quota_summary_per_resource(
            organization_id=uoa.organization_id,
            resource=QuotaConsumingResource.USERS,
            period=QuotaPeriod.LIFETIME,
        )

        assert quota_summary.total_used == 1
        assert quota_summary.total_remaining == 4
        assert quota_summary.total_limit == 5


@patch.object(settings, "enable_core_plan_quota_policy", True)
async def test_user_invite_into_an_existing_org(
    user_auth_service: UserAuthService,
    user_repository: UserRepository,
    quota_service: QuotaService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
) -> None:
    # Boilerplate to create a user, and associate them with an organization.
    user_id, organization_id = await make_user_org()
    await user_repository.associate_user_to_an_organization(
        user_id=user_id,
        organization_id=organization_id,
        roles=[UserRole.ADMIN],
    )

    num_active_users = await user_repository.get_num_active_users_by_organization_id(
        organization_id=organization_id,
    )
    assert num_active_users == 1

    with (
        # Mock the auth0 client.
        patch.object(
            UserAuthService,
            "setup_auth_db_user",
            new_callable=AsyncMock,
        ),
        patch.object(
            UserAuthService,
            "get_reset_password_link",
            new_callable=AsyncMock,
            return_value="mock-link",
        ),
        patch(
            "salestech_be.integrations.auth0.client.get_auth0_database_authentication_api_client",
            new_callable=AsyncMock,
        ),
        patch(
            "salestech_be.util.asyncio_util.adapter.run_in_pool",
            new_callable=AsyncMock,
        ),
    ):
        uoa, _ = await user_auth_service.invite_new_user(
            user_auth_context=UserAuthContext(
                organization_id=organization_id,
                user_id=user_id,
            ),
            email=f"test.user-{uuid4()}@reevo.local",
            roles=[UserRole.USER],
            organization_id=organization_id,  # inviting a user into an existing org.
        )
        assert uoa is not None
        assert uoa.organization_id == organization_id
        assert uoa.roles == [UserRole.USER]

        # the user id should be different from the one that was created in the boilerplate.
        assert uoa.user_id != user_id

        num_active_users = (
            await user_repository.get_num_active_users_by_organization_id(
                organization_id=organization_id,
            )
        )

        # the number of active users should have increased by 1.
        assert num_active_users == 2

        # Pull the quota summary to verify the quota is backfilled.
        quota_summary = await quota_service.get_quota_summary_per_resource(
            organization_id=uoa.organization_id,
            resource=QuotaConsumingResource.USERS,
            period=QuotaPeriod.LIFETIME,
        )

        assert (
            quota_summary.total_used == 2
        )  # this should match the number of active users.
        assert quota_summary.total_remaining == 3
        assert quota_summary.total_limit == 5


@patch.object(settings, "enable_core_plan_quota_policy", True)
async def test_existing_active_user_invite_into_an_existing_org(
    user_auth_service: UserAuthService,
    user_repository: UserRepository,
    quota_service: QuotaService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    make_user: Callable[[], Awaitable[UUID]],
) -> None:
    # Boilerplate to create a user, and associate them with an organization.
    admin_user_id, organization_id = await make_user_org()
    await user_repository.associate_user_to_an_organization(
        user_id=admin_user_id,
        organization_id=organization_id,
        roles=[UserRole.ADMIN],
    )

    regular_user_id = await make_user()
    await user_repository.associate_user_to_an_organization(
        user_id=regular_user_id,
        organization_id=organization_id,
        roles=[UserRole.USER],
    )
    regular_user_email = not_none(
        await user_repository.get_by_id(regular_user_id)
    ).email

    num_active_users = await user_repository.get_num_active_users_by_organization_id(
        organization_id=organization_id,
    )
    assert num_active_users == 2

    with (
        # Mock the auth0 client.
        patch.object(
            UserAuthService,
            "setup_auth_db_user",
            new_callable=AsyncMock,
        ),
        patch.object(
            UserAuthService,
            "get_reset_password_link",
            new_callable=AsyncMock,
            return_value="mock-link",
        ),
        patch(
            "salestech_be.integrations.auth0.client.get_auth0_database_authentication_api_client",
            new_callable=AsyncMock,
        ),
        patch(
            "salestech_be.util.asyncio_util.adapter.run_in_pool",
            new_callable=AsyncMock,
        ),
    ):
        uoa, _ = await user_auth_service.invite_new_user(
            user_auth_context=UserAuthContext(
                organization_id=organization_id,
                user_id=admin_user_id,
            ),
            email=regular_user_email,
            roles=[UserRole.USER],
            organization_id=organization_id,  # inviting a user into an existing org.
        )
        assert uoa is not None
        assert uoa.organization_id == organization_id
        assert uoa.user_id == regular_user_id
        assert uoa.roles == [UserRole.USER]

        # Verify the number of active users is still the same.
        num_active_users = (
            await user_repository.get_num_active_users_by_organization_id(
                organization_id=organization_id,
            )
        )

        # the number of active users should not change.
        assert num_active_users == 2

        # Pull the quota summary to verify the quota is backfilled.
        quota_summary = await quota_service.get_quota_summary_per_resource(
            organization_id=uoa.organization_id,
            resource=QuotaConsumingResource.USERS,
            period=QuotaPeriod.LIFETIME,
        )

        # this should match the number of active users.
        assert quota_summary.total_used == 2
        assert quota_summary.total_remaining == 3
        assert quota_summary.total_limit == 5


@patch.object(settings, "enable_core_plan_quota_policy", True)
async def test_existing_inactive_user_invite_into_an_existing_org(
    user_auth_service: UserAuthService,
    user_repository: UserRepository,
    quota_service: QuotaService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    make_user: Callable[[], Awaitable[UUID]],
) -> None:
    # Boilerplate to create a user, and associate them with an organization.
    admin_user_id, organization_id = await make_user_org()
    await user_repository.associate_user_to_an_organization(
        user_id=admin_user_id,
        organization_id=organization_id,
        roles=[UserRole.ADMIN],
    )

    regular_user_id = await make_user()
    await user_repository.associate_user_to_an_organization(
        user_id=regular_user_id,
        organization_id=organization_id,
        roles=[UserRole.USER],
        status=UserOrganizationAssociationStatus.INACTIVE,
    )
    regular_user_email = not_none(
        await user_repository.get_by_id(regular_user_id)
    ).email

    num_active_users = await user_repository.get_num_active_users_by_organization_id(
        organization_id=organization_id,
    )

    # the number of active users should be 1, since one is inactive.
    assert num_active_users == 1

    with (
        # Mock the auth0 client.
        patch.object(
            UserAuthService,
            "setup_auth_db_user",
            new_callable=AsyncMock,
        ),
        patch.object(
            UserAuthService,
            "get_reset_password_link",
            new_callable=AsyncMock,
            return_value="mock-link",
        ),
        patch(
            "salestech_be.integrations.auth0.client.get_auth0_database_authentication_api_client",
            new_callable=AsyncMock,
        ),
        patch(
            "salestech_be.util.asyncio_util.adapter.run_in_pool",
            new_callable=AsyncMock,
        ),
    ):
        uoa, _ = await user_auth_service.invite_new_user(
            user_auth_context=UserAuthContext(
                organization_id=organization_id,
                user_id=admin_user_id,
            ),
            email=regular_user_email,
            roles=[UserRole.USER],
            organization_id=organization_id,  # inviting a user into an existing org.
        )
        assert uoa is not None
        assert uoa.organization_id == organization_id
        assert uoa.user_id == regular_user_id
        assert uoa.roles == [UserRole.USER]

        # Verify the number of active users is still the same.
        num_active_users = (
            await user_repository.get_num_active_users_by_organization_id(
                organization_id=organization_id,
            )
        )

        # the number of active users should increase by 1.
        assert num_active_users == 2

        # Pull the quota summary to verify the quota is backfilled.
        quota_summary = await quota_service.get_quota_summary_per_resource(
            organization_id=uoa.organization_id,
            resource=QuotaConsumingResource.USERS,
            period=QuotaPeriod.LIFETIME,
        )

        # this should match the number of active users.
        assert quota_summary.total_used == 2
        assert quota_summary.total_remaining == 3
        assert quota_summary.total_limit == 5


@patch.object(settings, "enable_core_plan_quota_policy", True)
@patch.object(settings, "quota_policy_core_plan_max_active_users", 2)
async def test_user_invite_into_an_existing_org_exceeding_quota_policy(
    user_auth_service: UserAuthService,
    user_repository: UserRepository,
    quota_service: QuotaService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    make_user: Callable[[], Awaitable[UUID]],
) -> None:
    # Boilerplate to create a user, and associate them with an organization.
    admin_user_id, organization_id = await make_user_org()
    await user_repository.associate_user_to_an_organization(
        user_id=admin_user_id,
        organization_id=organization_id,
        roles=[UserRole.ADMIN],
    )

    regular_active_user_id = await make_user()
    await user_repository.associate_user_to_an_organization(
        user_id=regular_active_user_id,
        organization_id=organization_id,
        roles=[UserRole.USER],
    )

    # this is an inactive that that should not be counted towards the quota.
    # if we attempt to invite this user, it should be rejected by the quota policy.
    regular_inactive_user_id = await make_user()
    await user_repository.associate_user_to_an_organization(
        user_id=regular_inactive_user_id,
        organization_id=organization_id,
        roles=[UserRole.USER],
        status=UserOrganizationAssociationStatus.INACTIVE,
    )
    regular_inactive_user_email = not_none(
        await user_repository.get_by_id(regular_inactive_user_id)
    ).email

    # this is a new user that should be rejected by the quota policy.
    user_should_be_rejected_by_quota_policy = await make_user()
    user_should_be_rejected_email = not_none(
        await user_repository.get_by_id(user_should_be_rejected_by_quota_policy)
    ).email

    num_active_users = await user_repository.get_num_active_users_by_organization_id(
        organization_id=organization_id,
    )

    assert num_active_users == 2

    with (
        # Mock the auth0 client.
        patch.object(
            UserAuthService,
            "setup_auth_db_user",
            new_callable=AsyncMock,
        ),
        patch.object(
            UserAuthService,
            "get_reset_password_link",
            new_callable=AsyncMock,
            return_value="mock-link",
        ),
        patch(
            "salestech_be.integrations.auth0.client.get_auth0_database_authentication_api_client",
            new_callable=AsyncMock,
        ),
        patch(
            "salestech_be.util.asyncio_util.adapter.run_in_pool",
            new_callable=AsyncMock,
        ),
    ):
        # inviting an inactive user, that would exceed the quota policy, should be rejected.
        with pytest.raises(PaymentError):
            await user_auth_service.invite_new_user(
                user_auth_context=UserAuthContext(
                    organization_id=organization_id,
                    user_id=admin_user_id,
                ),
                email=regular_inactive_user_email,
                roles=[UserRole.USER],
                organization_id=organization_id,
            )

        # inviting a new user, that would exceed the quota policy, should be rejected.
        with pytest.raises(PaymentError):
            await user_auth_service.invite_new_user(
                user_auth_context=UserAuthContext(
                    organization_id=organization_id,
                    user_id=admin_user_id,
                ),
                email=user_should_be_rejected_email,
                roles=[UserRole.USER],
                organization_id=organization_id,
            )

        # Verify the number of active users is still the same.
        num_active_users = (
            await user_repository.get_num_active_users_by_organization_id(
                organization_id=organization_id,
            )
        )

        # the number of active users should increase by 1.
        assert num_active_users == 2

        # Pull the quota summary to verify the quota is backfilled.
        quota_summary = await quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.USERS,
            period=QuotaPeriod.LIFETIME,
        )

        # this should match the number of active users.
        assert quota_summary.total_used == 2
        assert quota_summary.total_remaining == 0
        assert quota_summary.total_limit == 2


@patch.object(settings, "enable_core_plan_quota_policy", True)
@patch.object(settings, "quota_policy_core_plan_max_active_users", 2)
async def test_active_users_quota_policy_period_alignment(
    user_auth_service: UserAuthService,
    user_repository: UserRepository,
    quota_service: QuotaService,
    make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]],
    make_user: Callable[[], Awaitable[UUID]],
) -> None:
    """
    This tests the behavior of the Active Users quota policy when migrated to the LIFETIME period.
    """

    # Boilerplate to create a user, and associate them with an organization.
    admin_user_id, organization_id = await make_user_org()
    await user_repository.associate_user_to_an_organization(
        user_id=admin_user_id,
        organization_id=organization_id,
        roles=[UserRole.ADMIN],
    )

    # Patching zoned_utc_now to the day before the year 2025.
    zoned_utc_now_value = datetime(2024, 12, 31, 0, 0, 0, tzinfo=pytz.UTC)
    with (
        # for get_or_create_active_users_quota_with_backfill().
        patch(
            "salestech_be.core.auth.service.zoned_utc_now",
            return_value=zoned_utc_now_value,
        ),
        # for upsert_quota_policy().
        patch(
            "salestech_be.core.quota.service.quota_policy_service.zoned_utc_now",
            return_value=zoned_utc_now_value,
        ),
        # for increase_usage(), and get_quota_summary_per_resource().
        patch(
            "salestech_be.core.quota.service.quota_service.zoned_utc_now",
            return_value=zoned_utc_now_value,
        ),
    ):
        active_users_quota = (
            await user_auth_service.get_or_create_active_users_quota_with_backfill(
                organization_id=organization_id, user_id=admin_user_id
            )
        )

        assert active_users_quota.total_used == 1
        assert active_users_quota.total_limit == 2
        assert active_users_quota.total_remaining == 1

    # Patching zoned_utc_now to the day after the year 2024.
    zoned_utc_now_value = datetime(2025, 1, 1, 0, 0, 0, tzinfo=pytz.UTC)
    with (
        # for get_or_create_active_users_quota_with_backfill().
        patch(
            "salestech_be.core.auth.service.zoned_utc_now",
            return_value=zoned_utc_now_value,
        ),
        # for upsert_quota_policy().
        patch(
            "salestech_be.core.quota.service.quota_policy_service.zoned_utc_now",
            return_value=zoned_utc_now_value,
        ),
        # for increase_usage(), and get_quota_summary_per_resource().
        patch(
            "salestech_be.core.quota.service.quota_service.zoned_utc_now",
            return_value=zoned_utc_now_value,
        ),
    ):
        active_users_quota = (
            await user_auth_service.get_or_create_active_users_quota_with_backfill(
                organization_id=organization_id, user_id=admin_user_id
            )
        )
        assert active_users_quota.total_used == 1
        assert active_users_quota.total_limit == 2
        assert active_users_quota.total_remaining == 1
