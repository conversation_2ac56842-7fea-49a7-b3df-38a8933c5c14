"""Tests for custom object association and association record APIs.

To run all tests in this file:
    pytest salestech_be/tests/integration/web/api/metadata/test_object_associations.py

To run a specific test:
    pytest salestech_be/tests/integration/web/api/metadata/test_object_associations.py -k <test_name>

For example:
    pytest salestech_be/tests/integration/web/api/metadata/test_object_associations.py -k test_create_and_get_association_record

To run with debug output:
    pytest -s salestech_be/tests/integration/web/api/metadata/test_object_associations.py

Available tests:
- test_create_and_get_association: Tests creating and retrieving custom object associations
- test_get_associations_for_object: Tests listing associations for a custom object
- test_create_and_get_association_record: Tests creating and retrieving association records
"""

import time
from collections.abc import Awaitable, Callable
from decimal import Decimal
from typing import TypedDict
from uuid import UUID, uuid4
from venv import logger

import pytest

from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.common.type.metadata.common import (
    CustomObjectIdentifier,
    FieldKind,
    ObjectKind,
    RelationshipType,
    StandardObjectIdentifier,
)
from salestech_be.common.type.metadata.field.field_indexable_config import (
    CaseAwareUniqueIndexableConfig,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    NumericFieldProperty,
    TextFieldProperty,
)
from salestech_be.common.type.metadata.field.field_type_property_update import (
    TextFieldPropertyUpdate,
)
from salestech_be.common.type.metadata.field.field_value import (
    NumericFieldValue,
    TextFieldValue,
)
from salestech_be.common.type.metadata.schema import (
    CustomFieldDescriptor,
    CustomObjectDescriptor,
    InboundRelationship,
    OutboundRelationship,
    StandardFieldDescriptor,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.custom_object.type.object_status import CustomObjectStatus
from salestech_be.web.api.common.custom_object.schema import (
    CreateCustomFieldDataRequestV2,
    CreateCustomObjectDataRequestV2,
)
from salestech_be.web.api.metadata.schema import (
    CreateAssociationRecordRequest,
    CreateAssociationRequest,
    CreateCustomFieldRequestV2,
    CustomField,
    CustomObject,
    CustomObjectAssociation,
    UpdateAssociationRecordRequest,
    UpdateAssociationRequest,
)
from tests.integration.web.api.metadata.util import MetadataClient
from tests.integration.web.api.util.api_client import (
    APITestClient,
    APITestError,
)


@pytest.fixture
def metadata_client(authed_api_test_client: APITestClient) -> MetadataClient:
    return MetadataClient(test_api_client=authed_api_test_client)


@pytest.fixture
async def create_custom_objects(
    metadata_client: MetadataClient,
) -> Callable[[], Awaitable[tuple[CustomObject, CustomObject]]]:
    """Factory fixture to create unique custom objects for each test"""

    async def _create_objects() -> tuple[CustomObject, CustomObject]:
        # Create two standalone custom objects with unique names
        timestamp = int(time.time() * 1000)
        object1 = await metadata_client.create_custom_object(
            object_display_name=f"Test Object 1 - {timestamp}"
        )
        object2 = await metadata_client.create_custom_object(
            object_display_name=f"Test Object 2 - {timestamp}"
        )
        return object1, object2

    return _create_objects


class TestAssociation:
    async def test_create_and_get_association(
        self,
        metadata_client: MetadataClient,
        create_custom_objects: Callable[
            [], Awaitable[tuple[CustomObject, CustomObject]]
        ],
    ) -> None:
        # Create unique objects for this test
        source_object, target_object = await create_custom_objects()

        source_object_identifier = CustomObjectIdentifier(
            object_kind=ObjectKind.CUSTOM,
            organization_id=source_object.organization_id,
            object_id=source_object.id,
        )
        target_object_identifier = CustomObjectIdentifier(
            object_kind=ObjectKind.CUSTOM,
            organization_id=target_object.organization_id,
            object_id=target_object.id,
        )

        # Create association
        create_request = CreateAssociationRequest(
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            relationship_type=RelationshipType.LOOKUP,
            association_name="Test Custom to Custom Association",
            inverse_name="Test Inverse Custom to Custom Association",
        )

        association = await metadata_client.create_association(create_request)

        # Verify creation
        if isinstance(association.source_object_identifier, CustomObjectIdentifier):
            assert association.source_object_identifier.object_id == source_object.id

        if isinstance(association.target_object_identifier, CustomObjectIdentifier):
            assert association.target_object_identifier.object_id == target_object.id

        # Get and verify association
        retrieved = await metadata_client.get_association(
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            association_id=association.id,
        )

        assert retrieved.id == association.id
        assert retrieved.relationship_type == RelationshipType.LOOKUP

    async def test_get_associations_for_object_no_associations(
        self,
        metadata_client: MetadataClient,
        create_custom_objects: Callable[
            [], Awaitable[tuple[CustomObject, CustomObject]]
        ],
    ) -> None:
        # Create unique objects for this test
        source_object, target_object = await create_custom_objects()

        source_object_identifier = CustomObjectIdentifier(
            object_kind=ObjectKind.CUSTOM,
            organization_id=source_object.organization_id,
            object_id=source_object.id,
        )
        target_object_identifier = CustomObjectIdentifier(
            object_kind=ObjectKind.CUSTOM,
            organization_id=target_object.organization_id,
            object_id=target_object.id,
        )

        # Get associations for source object - should be empty
        source_associations = await metadata_client.get_associations_for_object(
            object_identifier=source_object_identifier,
        )
        logger.info(f"Source associations: {source_associations}")
        assert len(source_associations.list_data) == 0

        # Get associations for target object - should be empty
        target_associations = await metadata_client.get_associations_for_object(
            object_identifier=target_object_identifier,
        )
        logger.info(f"Target associations: {target_associations}")
        assert len(target_associations.list_data) == 0

    async def test_get_associations_for_object(
        self,
        metadata_client: MetadataClient,
        create_custom_objects: Callable[
            [], Awaitable[tuple[CustomObject, CustomObject]]
        ],
    ) -> None:
        # Create unique objects for this test
        source_object, target_object = await create_custom_objects()

        source_object_identifier = CustomObjectIdentifier(
            object_kind=ObjectKind.CUSTOM,
            organization_id=source_object.organization_id,
            object_id=source_object.id,
        )
        target_object_identifier = CustomObjectIdentifier(
            object_kind=ObjectKind.CUSTOM,
            organization_id=target_object.organization_id,
            object_id=target_object.id,
        )

        # Create association
        create_request = CreateAssociationRequest(
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            relationship_type=RelationshipType.LOOKUP,
            association_name="Test Custom to Custom Association",
            inverse_name="Test Inverse Custom to Custom Association",
        )

        association = await metadata_client.create_association(create_request)

        # Get associations for source object
        associations = await metadata_client.get_associations_for_object(
            object_identifier=source_object_identifier,
        )

        assert len(associations.list_data) == 1
        assert associations.list_data[0].id == association.id

    async def test_update_association(
        self,
        metadata_client: MetadataClient,
        create_custom_objects: Callable[
            [], Awaitable[tuple[CustomObject, CustomObject]]
        ],
    ) -> None:
        # Create unique objects for this test
        source_object, target_object = await create_custom_objects()

        source_object_identifier = CustomObjectIdentifier(
            object_kind=ObjectKind.CUSTOM,
            organization_id=source_object.organization_id,
            object_id=source_object.id,
        )
        target_object_identifier = CustomObjectIdentifier(
            object_kind=ObjectKind.CUSTOM,
            organization_id=target_object.organization_id,
            object_id=target_object.id,
        )

        # Create association
        create_request = CreateAssociationRequest(
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            relationship_type=RelationshipType.LOOKUP,
            association_name="Test Custom to Custom Association",
            inverse_name="Test Inverse Custom to Custom Association",
        )

        association = await metadata_client.create_association(create_request)

        # Update association
        update_request = UpdateAssociationRequest(
            association_name="Updated Association",
            id=association.id,
        )

        updated = await metadata_client.update_association(
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            association_id=association.id,
            request=update_request,
        )

        assert updated.association_name == "Updated Association"
        assert updated.id == association.id

    async def test_delete_association(
        self,
        metadata_client: MetadataClient,
        create_custom_objects: Callable[
            [], Awaitable[tuple[CustomObject, CustomObject]]
        ],
    ) -> None:
        # Create unique objects for this test
        source_object, target_object = await create_custom_objects()

        source_object_identifier = CustomObjectIdentifier(
            object_kind=ObjectKind.CUSTOM,
            organization_id=source_object.organization_id,
            object_id=source_object.id,
        )
        target_object_identifier = CustomObjectIdentifier(
            object_kind=ObjectKind.CUSTOM,
            organization_id=target_object.organization_id,
            object_id=target_object.id,
        )

        # Create association
        create_request = CreateAssociationRequest(
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            relationship_type=RelationshipType.LOOKUP,
            association_name="Test To Be Deleted Custom to Custom Association",
            inverse_name="Test Inverse To Be Deleted Custom to Custom Association",
        )

        association = await metadata_client.create_association(create_request)

        # Delete association
        deleted_association = await metadata_client.delete_association(
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            association_id=association.id,
        )

        # Attempt to get the deleted association and log the response
        try:
            await metadata_client.get_association(
                source_object_identifier=source_object_identifier,
                target_object_identifier=target_object_identifier,
                association_id=deleted_association.id,
            )
        except (ResourceNotFoundError, APITestError):
            logger.info("Got expected error after deletion!!")


class TestAssociationRecord:
    async def _setup_test_association(
        self,
        metadata_client: MetadataClient,
        create_custom_objects: Callable[
            [], Awaitable[tuple[CustomObject, CustomObject]]
        ],
    ) -> tuple[
        CustomObjectAssociation,
        UUID,  # source_record_id
        UUID,  # target_record_id
        UUID,  # organization_id
    ]:
        """Set up test objects, fields, and create an association between them."""
        # Create custom objects
        timestamp = int(time.time() * 1000)
        source_object, target_object = await create_custom_objects()
        organization_id = source_object.organization_id

        source_object_identifier = CustomObjectIdentifier(
            object_kind=ObjectKind.CUSTOM,
            organization_id=source_object.organization_id,
            object_id=source_object.id,
        )
        target_object_identifier = CustomObjectIdentifier(
            object_kind=ObjectKind.CUSTOM,
            organization_id=target_object.organization_id,
            object_id=target_object.id,
        )

        # Create association between objects
        create_request = CreateAssociationRequest(
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            relationship_type=RelationshipType.LOOKUP,
            association_name="Test Custom to Custom Association",
            inverse_name="Test Inverse Custom to Custom Association",
        )
        association = await metadata_client.create_association(request=create_request)

        # Create fields for source object
        source_field = await metadata_client.create_custom_field_v2(
            custom_object_id=source_object.id,
            custom_field_request=CreateCustomFieldRequestV2(
                custom_field_property_create=TextFieldProperty(
                    field_display_name="Source Name",
                    is_required=False,
                    index_config=CaseAwareUniqueIndexableConfig(
                        is_indexed=True, is_case_sensitive=False
                    ),
                    max_length=200,
                ),
            ),
        )

        # Create fields for target object
        target_field = await metadata_client.create_custom_field_v2(
            custom_object_id=target_object.id,
            custom_field_request=CreateCustomFieldRequestV2(
                custom_field_property_create=TextFieldProperty(
                    field_display_name="Target Name",
                    is_required=False,
                    index_config=CaseAwareUniqueIndexableConfig(
                        is_indexed=True, is_case_sensitive=False
                    ),
                    max_length=200,
                ),
            ),
        )

        # Create source record
        source_record = await metadata_client.create_custom_object_data_v2(
            custom_object_id=source_object.id,
            create_request=CreateCustomObjectDataRequestV2(
                custom_field_data_requests=[
                    CreateCustomFieldDataRequestV2(
                        custom_field_id=source_field.id,
                        value=TextFieldValue(text=f"Source {timestamp}"),
                    ),
                ],
                display_name=f"Source {timestamp}",
            ),
        )

        # Create target record
        target_record = await metadata_client.create_custom_object_data_v2(
            custom_object_id=target_object.id,
            create_request=CreateCustomObjectDataRequestV2(
                custom_field_data_requests=[
                    CreateCustomFieldDataRequestV2(
                        custom_field_id=target_field.id,
                        value=TextFieldValue(text=f"Target {timestamp}"),
                    ),
                ],
                display_name=f"Target {timestamp}",
            ),
        )

        return association, source_record.id, target_record.id, organization_id

    async def test_create_and_get_association_record(
        self,
        metadata_client: MetadataClient,
        create_custom_objects: Callable[
            [], Awaitable[tuple[CustomObject, CustomObject]]
        ],
    ) -> None:
        """Test creating and retrieving an association record."""
        # Set up test objects and association
        (
            association,
            source_record_id,
            target_record_id,
            organization_id,
        ) = await self._setup_test_association(metadata_client, create_custom_objects)

        # Create the association record
        create_record_request = CreateAssociationRecordRequest(
            organization_id=organization_id,
            association=association,
            source_record_id=source_record_id,
            target_record_id=target_record_id,
        )
        association_record = await metadata_client.create_association_record(
            request=create_record_request
        )

        # Verify the association record was created correctly
        assert association_record.organization_id == organization_id
        assert association_record.association_id == association.id
        assert association_record.source_record_id == source_record_id
        assert association_record.target_record_id == target_record_id

        # Get and verify the association record
        get_association_record = (
            await metadata_client.get_association_record_by_record_ids(
                organization_id=organization_id,
                association_id=association.id,
                source_record_id=source_record_id,
                target_record_id=target_record_id,
            )
        )

        # Verify the retrieved record matches what we created
        assert get_association_record.organization_id == organization_id
        assert get_association_record.association_id == association.id
        assert get_association_record.source_record_id == source_record_id
        assert get_association_record.target_record_id == target_record_id

    async def test_update_association_record(
        self,
        metadata_client: MetadataClient,
        create_custom_objects: Callable[
            [], Awaitable[tuple[CustomObject, CustomObject]]
        ],
    ) -> None:
        """Test updating an association record."""
        # Set up test objects and create initial association record
        (
            association,
            source_record_id,
            target_record_id,
            organization_id,
        ) = await self._setup_test_association(metadata_client, create_custom_objects)

        # Create initial association record
        create_record_request = CreateAssociationRecordRequest(
            organization_id=organization_id,
            association=association,
            source_record_id=source_record_id,
            target_record_id=target_record_id,
        )
        initial_record = await metadata_client.create_association_record(
            request=create_record_request
        )

        # Create a new target record ID for the update
        new_target_id = uuid4()

        # Update the association record
        update_request = UpdateAssociationRecordRequest(
            organization_id=organization_id,
            association_id=association.id,
            source_record_id=source_record_id,
            target_record_id=target_record_id,
            association_name=association.association_name,
            update_data={"target_record_id": str(new_target_id)},
        )
        updated_record = await metadata_client.update_association_record(
            organization_id=update_request.organization_id,
            association_id=update_request.association_id,
            source_record_id=update_request.source_record_id,
            target_record_id=update_request.target_record_id,
            association_name=update_request.association_name,
            update_data=update_request.update_data,
        )

        # Verify the update
        assert updated_record.organization_id == organization_id
        assert updated_record.association_id == association.id
        assert updated_record.source_record_id == source_record_id
        assert updated_record.target_record_id == new_target_id  # Verify new target ID
        assert updated_record.updated_at is not None
        assert updated_record.updated_at > initial_record.created_at

        # Verify persistence by retrieving the record
        retrieved_record = await metadata_client.get_association_record_by_record_ids(
            organization_id=organization_id,
            association_id=association.id,
            source_record_id=source_record_id,
            target_record_id=new_target_id,  # Use new target ID
        )

        # Verify retrieved record matches the update
        assert retrieved_record.target_record_id == new_target_id
        assert retrieved_record.updated_at == updated_record.updated_at

    async def test_remap_association_record(
        self,
        metadata_client: MetadataClient,
        create_custom_objects: Callable[
            [], Awaitable[tuple[CustomObject, CustomObject]]
        ],
    ) -> None:
        """Test remapping an association record."""
        # Set up test objects and create initial association record
        (
            association,
            source_record_id,
            target_record_id,
            organization_id,
        ) = await self._setup_test_association(metadata_client, create_custom_objects)

        # Create initial association record
        create_record_request = CreateAssociationRecordRequest(
            organization_id=organization_id,
            association=association,
            source_record_id=source_record_id,
            target_record_id=target_record_id,
        )
        initial_record = await metadata_client.create_association_record(
            request=create_record_request
        )

        # Create two new target record IDs for the remap
        new_target_id_1 = uuid4()
        new_target_id_2 = uuid4()

        # Remap the association record
        remapped_records = await metadata_client.remap_association_record(
            organization_id=organization_id,
            association_id=association.id,
            source_record_id=source_record_id,
            target_record_ids=[new_target_id_1, new_target_id_2],
        )

        # Verify the remap
        assert len(remapped_records) == 2
        assert remapped_records[0].id != initial_record.id
        assert remapped_records[1].id != initial_record.id
        assert remapped_records[0].target_record_id == new_target_id_1
        assert remapped_records[1].target_record_id == new_target_id_2

    async def test_delete_association_record(
        self,
        metadata_client: MetadataClient,
        create_custom_objects: Callable[
            [], Awaitable[tuple[CustomObject, CustomObject]]
        ],
    ) -> None:
        """Test deleting an association record."""
        # Set up test objects and create initial association record
        (
            association,
            source_record_id,
            target_record_id,
            organization_id,
        ) = await self._setup_test_association(metadata_client, create_custom_objects)

        # Create initial association record
        create_record_request = CreateAssociationRecordRequest(
            organization_id=organization_id,
            association=association,
            source_record_id=source_record_id,
            target_record_id=target_record_id,
        )
        initial_record = await metadata_client.create_association_record(
            request=create_record_request
        )

        # Verify initial record was created correctly
        assert initial_record.organization_id == organization_id
        assert initial_record.association_id == association.id
        assert initial_record.source_record_id == source_record_id
        assert initial_record.target_record_id == target_record_id
        assert initial_record.created_at is not None
        assert initial_record.updated_at is None  # Should be None for new record

        # Delete the association record
        deleted_record = await metadata_client.delete_association_record(
            organization_id=organization_id,
            association_id=association.id,
            source_record_id=source_record_id,
            target_record_id=target_record_id,
            association_name=association.association_name,
        )

        # Verify the deleted record matches the initial record
        assert deleted_record.id == initial_record.id
        assert deleted_record.organization_id == organization_id
        assert deleted_record.association_id == association.id
        assert deleted_record.source_record_id == source_record_id
        assert deleted_record.target_record_id == target_record_id

    async def test_create_and_get_association_record_for_standard_and_custom_objects(
        self,
        metadata_client: MetadataClient,
    ) -> None:
        """Test creating an association between a custom object and a standard Account object."""
        # Create custom source object (City)
        timestamp = int(time.time() * 1000)
        source_object = await metadata_client.create_custom_object(
            object_display_name=f"Test Object 1 - {timestamp}"
        )

        # Use organization ID from the created source object
        organization_id = source_object.organization_id

        source_object_identifier = CustomObjectIdentifier(
            object_kind=ObjectKind.CUSTOM,
            organization_id=source_object.organization_id,
            object_id=source_object.id,
        )

        target_object_identifier = StandardObjectIdentifier(
            object_kind=ObjectKind.STANDARD,
            object_name=ExtendableStandardObject.account,
        )

        # Create association between custom object and standard Account object
        create_request = CreateAssociationRequest(
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            relationship_type=RelationshipType.LOOKUP,
            association_name="Test Custom to Standard Association",
            inverse_name="Test Inverse Custom to Standard Association",
        )

        # Create the association
        association = await metadata_client.create_association(request=create_request)

        # Create source object fields
        source_object_text_field = await metadata_client.create_custom_field_v2(
            custom_object_id=source_object.id,
            custom_field_request=CreateCustomFieldRequestV2(
                custom_field_property_create=TextFieldProperty(
                    field_display_name="City Name",
                    is_required=False,
                    index_config=CaseAwareUniqueIndexableConfig(
                        is_indexed=True, is_case_sensitive=False
                    ),
                    max_length=200,
                ),
            ),
        )

        source_object_number_field = await metadata_client.create_custom_field_v2(
            custom_object_id=source_object.id,
            custom_field_request=CreateCustomFieldRequestV2(
                custom_field_property_create=NumericFieldProperty(
                    field_display_name="Population",
                    is_required=False,
                ),
            ),
        )

        # Create source object record with numeric field using Decimal
        los_angeles = await metadata_client.create_custom_object_data_v2(
            custom_object_id=source_object.id,
            create_request=CreateCustomObjectDataRequestV2(
                custom_field_data_requests=[
                    CreateCustomFieldDataRequestV2(
                        custom_field_id=source_object_text_field.id,
                        value=TextFieldValue(text=f"Los Angeles {timestamp}"),
                    ),
                    CreateCustomFieldDataRequestV2(
                        custom_field_id=source_object_number_field.id,
                        value=NumericFieldValue(number=Decimal("123")),
                    ),
                ],
                display_name=f"Los Angeles {timestamp}",
            ),
        )

        # Use an existing Account ID
        account_id = uuid4()

        # Create the association record between custom object and Account
        create_record_request = CreateAssociationRecordRequest(
            organization_id=organization_id,
            association=association,
            source_record_id=los_angeles.id,
            target_record_id=account_id,
        )

        # Create the association record
        association_record = await metadata_client.create_association_record(
            request=create_record_request
        )

        # Verify the association record was created correctly
        assert association_record.organization_id == organization_id
        assert association_record.association_id == association.id
        assert association_record.source_record_id == los_angeles.id
        assert association_record.target_record_id == account_id

        # Get and verify the association record
        get_association_record = (
            await metadata_client.get_association_record_by_record_ids(
                organization_id=organization_id,
                association_id=association.id,
                source_record_id=los_angeles.id,
                target_record_id=account_id,
            )
        )

        # Verify the retrieved record matches what we created
        assert get_association_record.organization_id == organization_id
        assert get_association_record.association_id == association.id
        assert get_association_record.source_record_id == los_angeles.id
        assert get_association_record.target_record_id == account_id

    async def test_create_and_get_association_record_for_custom_objects_parent_child(
        self,
        metadata_client: MetadataClient,
        create_custom_objects: Callable[
            [], Awaitable[tuple[CustomObject, CustomObject]]
        ],
    ) -> None:
        """Test creating an association between two custom objects that have a parent-child relationship."""
        # Create parent and child custom objects (e.g., Company and Department)
        timestamp = int(time.time() * 1000)
        source_object, target_object = await create_custom_objects()

        # Use organization ID from the created source object
        organization_id = source_object.organization_id

        source_object_identifier = CustomObjectIdentifier(
            object_kind=ObjectKind.CUSTOM,
            organization_id=source_object.organization_id,
            object_id=source_object.id,
        )

        target_object_identifier = CustomObjectIdentifier(
            object_kind=ObjectKind.CUSTOM,
            organization_id=target_object.organization_id,
            object_id=target_object.id,
        )

        # Create association between the custom objects (Company -> Department)
        create_request = CreateAssociationRequest(
            source_object_identifier=source_object_identifier,
            target_object_identifier=target_object_identifier,
            relationship_type=RelationshipType.PARENT_CHILD,
            association_name="Test Parent to Child Association",
            inverse_name="Test Inverse Parent to Child Association",
        )

        # Create the association
        association = await metadata_client.create_association(request=create_request)

        # Create fields for parent object (Company)
        company_name_field = await metadata_client.create_custom_field_v2(
            custom_object_id=source_object.id,
            custom_field_request=CreateCustomFieldRequestV2(
                custom_field_property_create=TextFieldProperty(
                    field_display_name="Company Name",
                    is_required=False,
                    index_config=CaseAwareUniqueIndexableConfig(
                        is_indexed=True, is_case_sensitive=False
                    ),
                    max_length=200,
                ),
            ),
        )

        # Create fields for child object (Department)
        department_name_field = await metadata_client.create_custom_field_v2(
            custom_object_id=target_object.id,
            custom_field_request=CreateCustomFieldRequestV2(
                custom_field_property_create=TextFieldProperty(
                    field_display_name="Department Name",
                    is_required=False,
                    index_config=CaseAwareUniqueIndexableConfig(
                        is_indexed=True, is_case_sensitive=False
                    ),
                    max_length=200,
                ),
            ),
        )

        # Create parent record (Company)
        company = await metadata_client.create_custom_object_data_v2(
            custom_object_id=source_object.id,
            create_request=CreateCustomObjectDataRequestV2(
                custom_field_data_requests=[
                    CreateCustomFieldDataRequestV2(
                        custom_field_id=company_name_field.id,
                        value=TextFieldValue(text=f"Acme Corp {timestamp}"),
                    ),
                ],
                display_name=f"Acme Corp {timestamp}",
            ),
        )

        # Create child record (Department)
        department = await metadata_client.create_custom_object_data_v2(
            custom_object_id=target_object.id,
            create_request=CreateCustomObjectDataRequestV2(
                custom_field_data_requests=[
                    CreateCustomFieldDataRequestV2(
                        custom_field_id=department_name_field.id,
                        value=TextFieldValue(text=f"Engineering {timestamp}"),
                    ),
                ],
                display_name=f"Engineering {timestamp}",
            ),
        )

        # Create the association record between Company and Department
        create_record_request = CreateAssociationRecordRequest(
            organization_id=organization_id,
            association=association,
            source_record_id=company.id,
            target_record_id=department.id,
        )

        # Create the association record
        association_record = await metadata_client.create_association_record(
            request=create_record_request
        )

        # Verify the association record was created correctly
        assert association_record.organization_id == organization_id
        assert association_record.association_id == association.id
        assert association_record.source_record_id == company.id
        assert association_record.target_record_id == department.id

        # Get and verify the association record
        get_association_record = (
            await metadata_client.get_association_record_by_record_ids(
                organization_id=organization_id,
                association_id=association.id,
                source_record_id=company.id,
                target_record_id=department.id,
            )
        )

        # Verify the retrieved record matches what we created
        assert get_association_record.organization_id == organization_id
        assert get_association_record.association_id == association.id
        assert get_association_record.source_record_id == company.id
        assert get_association_record.target_record_id == department.id


class TestAssociationsInOrganizationSchema:
    async def test_associations_in_organization_schema(
        self,
        metadata_client: MetadataClient,
        create_custom_objects: Callable[
            [], Awaitable[tuple[CustomObject, CustomObject]]
        ],
    ) -> None:
        """Test that associations are correctly reflected in organization schema."""
        # Create two custom objects
        custom_object1, custom_object2 = await create_custom_objects()

        # Create custom to custom association
        custom_to_custom_request = CreateAssociationRequest(
            source_object_identifier=CustomObjectIdentifier(
                object_kind=ObjectKind.CUSTOM,
                organization_id=custom_object1.organization_id,
                object_id=custom_object1.id,
            ),
            target_object_identifier=CustomObjectIdentifier(
                object_kind=ObjectKind.CUSTOM,
                organization_id=custom_object2.organization_id,
                object_id=custom_object2.id,
            ),
            relationship_type=RelationshipType.LOOKUP,
            association_name="Custom1 to Custom2",
            inverse_name="Custom2 to Custom1",
            max_source_records=1,
            max_target_records=-1,
        )
        await metadata_client.create_association(custom_to_custom_request)

        # Create standard to custom association
        standard_to_custom_request = CreateAssociationRequest(
            source_object_identifier=StandardObjectIdentifier(
                object_kind=ObjectKind.STANDARD,
                object_name=ExtendableStandardObject.account,
            ),
            target_object_identifier=CustomObjectIdentifier(
                object_kind=ObjectKind.CUSTOM,
                organization_id=custom_object1.organization_id,
                object_id=custom_object1.id,
            ),
            relationship_type=RelationshipType.LOOKUP,
            association_name="Account to Custom1",
            inverse_name="Custom1 to Account",
            max_source_records=-1,
            max_target_records=1,
        )
        await metadata_client.create_association(standard_to_custom_request)

        # Get organization schema
        org_schema = await metadata_client.get_organization_schema_details()

        # Debug logging for all objects
        logger.debug("\nDEBUG: Organization Schema Objects:")
        for obj in org_schema.objects:
            logger.debug("\nObject Details:")
            logger.debug(f"  Kind: {obj.object_kind}")
            logger.debug(f"  Inbound Relationships: {len(obj.inbound_relationships)}")
            for rel in obj.inbound_relationships:
                logger.debug(
                    f"    - {rel.relationship_name} from {rel.related_object_identifier}"
                )
            logger.debug(f"  Outbound Relationships: {len(obj.outbound_relationships)}")
            for rel2 in obj.outbound_relationships:
                logger.debug(
                    f"    - {rel2.relationship_name} to {rel2.related_object_identifier}"
                )

        # Find custom objects first with better error handling
        try:
            custom1_descriptor = next(
                obj
                for obj in org_schema.objects
                if obj.object_kind == ObjectKind.CUSTOM
                and obj.object_identifier.object_id == custom_object1.id
            )
            logger.debug("\nDEBUG: Found Custom Object 1:")
            logger.debug(f"  ID: {custom_object1.id}")
            logger.debug(
                f"  Inbound Relationships: {len(custom1_descriptor.inbound_relationships)}"
            )
            logger.debug(
                f"  Outbound Relationships: {len(custom1_descriptor.outbound_relationships)}"
            )
        except StopIteration:
            raise ValueError(
                f"Custom object 1 not found in schema. ID: {custom_object1.id}\n"
                f"Available custom objects: {[obj.object_identifier.object_id for obj in org_schema.objects if obj.object_kind == ObjectKind.CUSTOM]}"
            )

        # Find custom objects first
        custom2_descriptor = next(
            obj
            for obj in org_schema.objects
            if obj.object_kind == ObjectKind.CUSTOM
            and obj.object_identifier.object_id == custom_object2.id
        )

        # Find account object with better error handling
        standard_objects = [
            obj
            for obj in org_schema.objects
            if obj.object_kind == ObjectKind.STANDARD
            and hasattr(obj.object_identifier, "object_name")
        ]

        logger.debug("\nDEBUG: Standard Objects Found:")
        for obj in standard_objects:
            logger.debug(f"Standard Object Name: {obj.object_identifier.object_name}")

        account_descriptor = next(
            (
                obj
                for obj in standard_objects
                if obj.object_identifier.object_name.upper()
                == ExtendableStandardObject.account.value.upper()
            ),
            None,
        )

        if account_descriptor is None:
            raise ValueError(
                f"Account object not found in schema. Available standard objects: "
                f"{[obj.object_identifier.object_name for obj in standard_objects]}"
            )

        # Verify custom1's relationships
        assert len(custom1_descriptor.inbound_relationships) == 1
        assert len(custom1_descriptor.outbound_relationships) == 1 + 3

        # Check custom1's inbound relationship from account
        account_rel = custom1_descriptor.inbound_relationships[0]
        assert account_rel.relationship_name == "Custom1 to Account"

        # Case-insensitive comparison for object identifiers
        assert (
            account_rel.related_object_identifier.object_kind
            == account_descriptor.object_identifier.object_kind
        )
        assert (
            account_rel.related_object_identifier.object_name.upper()
            == account_descriptor.object_identifier.object_name.upper()
        )

        # Check custom1's outbound relationship to custom2
        rel_names = [
            rel.relationship_name for rel in custom1_descriptor.outbound_relationships
        ]
        custom2_rel = next(
            rel
            for rel in custom1_descriptor.outbound_relationships
            if rel.relationship_name == "Custom1 to Custom2"
        )
        assert "Custom1 to Custom2" in rel_names
        assert (
            custom2_rel.related_object_identifier
            == custom2_descriptor.object_identifier
        )
        assert custom2_rel.self_cardinality == OutboundRelationship.Cardinality.ONE
        assert (
            custom2_rel.related_object_cardinality
            == OutboundRelationship.Cardinality.MANY
        )

        # Verify custom2's relationships
        assert len(custom2_descriptor.inbound_relationships) == 1
        assert len(custom2_descriptor.outbound_relationships) == 3

        # Check custom2's inbound relationship from custom1
        custom1_rel = custom2_descriptor.inbound_relationships[0]
        assert custom1_rel.relationship_name == "Custom2 to Custom1"
        assert (
            custom1_rel.related_object_identifier
            == custom1_descriptor.object_identifier
        )
        assert custom1_rel.self_cardinality == InboundRelationship.Cardinality.MANY
        assert (
            custom1_rel.related_object_cardinality
            == InboundRelationship.Cardinality.ONE
        )

        # Verify account's relationships
        assert (
            len(account_descriptor.inbound_relationships) == 3
        )  # 3 because account has 3 inbound standard relationships.
        assert (
            len(account_descriptor.outbound_relationships) == 6
        )  # 6 because 5 standard relationships and 1 custom relationship.

        # Debug all account relationships
        logger.debug("\nDEBUG: Account Relationships:")
        logger.debug(f"Inbound ({len(account_descriptor.inbound_relationships)}):")
        for rel in account_descriptor.inbound_relationships:
            logger.debug(f"  - {rel.relationship_name}")
        logger.debug(f"Outbound ({len(account_descriptor.outbound_relationships)}):")
        for relx in account_descriptor.outbound_relationships:
            logger.debug(f"  - {relx.relationship_name}")

        # Find the custom1 relationship among all outbound relationships
        custom1_rel2 = next(
            (
                rel3
                for rel3 in account_descriptor.outbound_relationships
                if rel3.relationship_name == "Account to Custom1"
            ),
            None,
        )

        assert custom1_rel2 is not None, (
            f"Expected to find 'Account to Custom1' relationship in account outbound relationships. "
            f"Available relationships: {[rel.relationship_name for rel in account_descriptor.outbound_relationships]}"
        )


class TestIconOnOrganizationSchema:
    async def test_icon_on_organization_schema(
        self,
        metadata_client: MetadataClient,
        create_custom_objects: Callable[
            [], Awaitable[tuple[CustomObject, CustomObject]]
        ],
    ) -> None:
        """Test that icon is correctly reflected in organization schema."""
        # Create two custom objects
        custom_object1, custom_object2 = await create_custom_objects()

        # Get organization schema
        org_schema = await metadata_client.get_organization_schema_details()

        # Find our custom objects in the schema
        custom_objects = [
            obj
            for obj in org_schema.objects
            if obj.object_identifier.object_kind == ObjectKind.CUSTOM
        ]

        # Find descriptors for our test objects
        obj1_descriptor = next(
            (
                obj
                for obj in custom_objects
                if obj.object_identifier.object_id == custom_object1.id
            ),
            None,
        )
        obj2_descriptor = next(
            (
                obj
                for obj in custom_objects
                if obj.object_identifier.object_id == custom_object2.id
            ),
            None,
        )

        assert obj1_descriptor is not None, "First custom object not found in schema"
        assert obj2_descriptor is not None, "Second custom object not found in schema"

        # By default both should have icon as None
        assert obj1_descriptor.icon is None
        assert obj2_descriptor.icon is None


class TestPinningCustomFields:
    async def test_pinning_custom_fields(
        self,
        metadata_client: MetadataClient,
        create_custom_objects: Callable[
            [], Awaitable[tuple[CustomObject, CustomObject]]
        ],
    ) -> None:
        """Test that icon is correctly reflected in organization schema."""
        # Create two custom objects but use just the first one
        custom_object1, _ = await create_custom_objects()

        # Get organization schema
        await metadata_client.get_organization_schema_details()

        # Create a custom field
        custom_field = await metadata_client.create_custom_field_v2(
            custom_object_id=custom_object1.id,
            custom_field_request=CreateCustomFieldRequestV2(
                custom_field_property_create=TextFieldProperty(
                    field_display_name="Custom Field to be Pinned",
                    is_required=False,
                    index_config=CaseAwareUniqueIndexableConfig(
                        is_indexed=True, is_case_sensitive=False
                    ),
                    max_length=200,
                ),
            ),
        )

        # Pin the custom field
        updated_custom_field = await metadata_client.updated_custom_field_v2(
            custom_object_id=custom_object1.id,
            custom_field_id=custom_field.id,
            custom_field_property_update=TextFieldPropertyUpdate(
                is_pinned=True,
            ),
        )

        # Check that the custom field is pinned
        assert updated_custom_field.properties is not None, (
            "Custom field properties should not be None"
        )
        assert updated_custom_field.properties.is_pinned is True, (
            "Custom field should be pinned"
        )


class TestVisibleOnNavigationOnOrganizationSchema:
    async def test_visible_on_navigation_on_organization_schema(
        self,
        metadata_client: MetadataClient,
        create_custom_objects: Callable[
            [], Awaitable[tuple[CustomObject, CustomObject]]
        ],
    ) -> None:
        """Test that visible_on_navigation is correctly reflected in organization schema."""
        # Create two custom objects - one visible, one hidden
        custom_object1, custom_object2 = await create_custom_objects()

        # Get organization schema
        org_schema = await metadata_client.get_organization_schema_details()

        # Find our custom objects in the schema
        custom_objects = [
            obj
            for obj in org_schema.objects
            if obj.object_identifier.object_kind == ObjectKind.CUSTOM
        ]

        # Find descriptors for our test objects
        obj1_descriptor = next(
            (
                obj
                for obj in custom_objects
                if obj.object_identifier.object_id == custom_object1.id
            ),
            None,
        )
        obj2_descriptor = next(
            (
                obj
                for obj in custom_objects
                if obj.object_identifier.object_id == custom_object2.id
            ),
            None,
        )

        assert obj1_descriptor is not None, "First custom object not found in schema"
        assert obj2_descriptor is not None, "Second custom object not found in schema"

        # By default both should be visible
        assert obj1_descriptor.visible_on_navigation is True
        assert obj2_descriptor.visible_on_navigation is True


class TestStandardFieldsOnOrganizationSchema:
    async def test_standard_fields_on_organization_schema(
        self,
        metadata_client: MetadataClient,
        create_custom_objects: Callable[
            [], Awaitable[tuple[CustomObject, CustomObject]]
        ],
    ) -> None:
        """
        Test that standard fields are correctly reflected in organization schema.

        - display_name
        - created_at
        - updated_at
        - created_by_user_id
        - updated_by_user_id
        """

        # Create two custom objects - one visible, one hidden
        custom_object1, custom_object2 = await create_custom_objects()

        # Get organization schema
        org_schema = await metadata_client.get_organization_schema_details()

        # Find our custom objects in the schema
        custom_objects = [
            obj
            for obj in org_schema.objects
            if obj.object_identifier.object_kind == ObjectKind.CUSTOM
        ]

        # Find descriptors for our test objects
        obj1_descriptor = next(
            (
                obj
                for obj in custom_objects
                if obj.object_identifier.object_id == custom_object1.id
                and isinstance(obj, CustomObjectDescriptor)
            ),
            None,
        )
        obj2_descriptor = next(
            (
                obj
                for obj in custom_objects
                if obj.object_identifier.object_id == custom_object2.id
            ),
            None,
        )

        assert obj1_descriptor is not None, "First custom object not found in schema"
        assert obj2_descriptor is not None, "Second custom object not found in schema"

        # Test display_name
        assert obj1_descriptor.display_name == custom_object1.object_display_name
        assert obj2_descriptor.display_name == custom_object2.object_display_name

        # Test created_at
        assert obj1_descriptor.created_at == custom_object1.created_at
        assert obj2_descriptor.created_at == custom_object2.created_at

        # Test updated_at
        assert obj1_descriptor.updated_at == custom_object1.updated_at
        assert obj2_descriptor.updated_at == custom_object2.updated_at

        # Test created_by_user_id
        assert obj1_descriptor.created_by_user_id == custom_object1.created_by_user_id
        assert obj2_descriptor.created_by_user_id == custom_object2.created_by_user_id

        # Test updated_by_user_id
        assert obj1_descriptor.updated_by_user_id == custom_object1.updated_by_user_id
        assert obj2_descriptor.updated_by_user_id == custom_object2.updated_by_user_id

    async def test_display_name_is_nullable_is_false(
        self,
        metadata_client: MetadataClient,
    ) -> None:
        object_display_name = "Testing display_name is_nullable"

        # Create a custom object
        await metadata_client.create_custom_object(
            object_display_name=object_display_name,
        )

        # Get schema and find our specific custom object
        organization_schema = await metadata_client.get_organization_schema_details()
        custom_object = next(
            obj
            for obj in organization_schema.objects
            if obj.object_kind == ObjectKind.CUSTOM
            and obj.object_display_name == object_display_name
        )

        # Find the display_name field
        display_name_field = next(
            field
            for field in custom_object.fields
            if isinstance(field, StandardFieldDescriptor)
            and field.field_identifier.field_name == "display_name"
        )
        assert display_name_field is not None, "Display name field not found"

        # Verify is_nullable is False
        assert isinstance(display_name_field.field_type_property, TextFieldProperty)
        assert display_name_field.field_type_property.is_nullable is False


class TestUpdatingStatus:
    async def test_update_status_to_frozen_via_patch(
        self,
        metadata_client: MetadataClient,
    ) -> None:
        """Test updating a custom object with status to FROZEN."""
        # Create a custom object
        custom_object = await metadata_client.create_custom_object(
            object_display_name="Update Status to Frozen API Test",
        )

        # Get organization schema
        org_schema = await metadata_client.get_organization_schema_details()

        # Find our custom object in the schema
        custom_objects = [
            obj
            for obj in org_schema.objects
            if obj.object_kind == ObjectKind.CUSTOM
            and isinstance(obj, CustomObjectDescriptor)
            and obj.object_identifier.object_id == custom_object.id
        ]

        assert len(custom_objects) == 1, "Custom object not found in schema"
        custom_object_descriptor = custom_objects[0]

        # Verify the custom object is active
        assert custom_object_descriptor.status == CustomObjectStatus.ACTIVE
        assert (
            custom_object_descriptor.display_name == "Update Status to Frozen API Test"
        )

        # Update custom object status to FROZEN
        await metadata_client.update_custom_object(
            custom_object_id=custom_object.id,
            new_status=CustomObjectStatus.FROZEN,
        )

        # Get updated schema to verify changes
        updated_schema = await metadata_client.get_organization_schema_details()

        # Find our updated custom object
        updated_objects = [
            obj
            for obj in updated_schema.objects
            if obj.object_kind == ObjectKind.CUSTOM
            and isinstance(obj, CustomObjectDescriptor)
            and obj.object_identifier.object_id == custom_object.id
        ]

        assert (
            len(updated_objects) == 0
        )  # Object should now be removed since it is soft-deleted


class TestIcons:
    async def test_create_icon_via_post(
        self,
        metadata_client: MetadataClient,
    ) -> None:
        # Create custom object with icon
        custom_object = await metadata_client.create_custom_object(
            object_display_name="Create Icon API Test", icon="matty_test_icon.png"
        )

        # Get organization schema
        org_schema = await metadata_client.get_organization_schema_details()

        # Find our custom object in the schema
        custom_objects = [
            obj
            for obj in org_schema.objects
            if obj.object_kind == ObjectKind.CUSTOM
            and isinstance(obj, CustomObjectDescriptor)
            and obj.object_identifier.object_id == custom_object.id
        ]

        assert len(custom_objects) == 1, "Custom object not found in schema"
        custom_object_descriptor = custom_objects[0]

        # Verify the icon is present
        assert custom_object_descriptor.icon == "matty_test_icon.png"
        assert custom_object_descriptor.display_name == "Create Icon API Test"

    async def test_update_icon_via_patch(
        self,
        metadata_client: MetadataClient,
    ) -> None:
        # Create custom object with icon
        custom_object = await metadata_client.create_custom_object(
            object_display_name="Update Icon API Test", icon="raj_test_icon.png"
        )

        # Get organization schema
        org_schema = await metadata_client.get_organization_schema_details()

        # Find our custom object in the schema
        custom_objects = [
            obj
            for obj in org_schema.objects
            if obj.object_kind == ObjectKind.CUSTOM
            and isinstance(obj, CustomObjectDescriptor)
            and obj.object_identifier.object_id == custom_object.id
        ]

        assert len(custom_objects) == 1, "Custom object not found in schema"
        custom_object_descriptor = custom_objects[0]

        # Verify the icon is present
        assert custom_object_descriptor.icon == "raj_test_icon.png"
        assert custom_object_descriptor.display_name == "Update Icon API Test"

        # Update the icon to "Francis Test Object with Icon"
        await metadata_client.update_custom_object(
            custom_object_id=custom_object.id,
            new_icon="francis_test_icon.png",
        )

        # Get updated schema to verify changes
        updated_schema = await metadata_client.get_organization_schema_details()

        # Find our updated custom object
        updated_objects = [
            obj
            for obj in updated_schema.objects
            if obj.object_kind == ObjectKind.CUSTOM
            and isinstance(obj, CustomObjectDescriptor)
            and obj.object_identifier.object_id == custom_object.id
        ]

        assert len(updated_objects) == 1
        updated_object = updated_objects[0]

        # Verify the updates
        assert updated_object.icon == "francis_test_icon.png"
        assert updated_object.display_name == "Update Icon API Test"


class TestVisibleOnNavigation:
    async def test_create_visible_on_navigation_false_via_post(
        self,
        metadata_client: MetadataClient,
    ) -> None:
        """Test creating a custom object with visible_on_navigation flag."""
        # Create a custom object with visible_on_navigation set to False
        custom_object = await metadata_client.create_custom_object(
            object_display_name="Create Visible On Navigation False API Test",
            visible_on_navigation=False,
        )

        # Get organization schema
        org_schema = await metadata_client.get_organization_schema_details()

        # Find our custom object in the schema
        custom_objects = [
            obj
            for obj in org_schema.objects
            if obj.object_kind == ObjectKind.CUSTOM
            and isinstance(obj, CustomObjectDescriptor)
            and obj.object_identifier.object_id == custom_object.id
        ]

        assert len(custom_objects) == 1, "Custom object not found in schema"
        custom_object_descriptor = custom_objects[0]

        # Verify the visible_on_navigation is False
        assert custom_object_descriptor.visible_on_navigation is False
        assert (
            custom_object_descriptor.display_name
            == "Create Visible On Navigation False API Test"
        )

    async def test_update_visible_on_navigation_to_true_via_patch(
        self,
        metadata_client: MetadataClient,
    ) -> None:
        """Test creating a custom object with visible_on_navigation flag."""
        # Create a custom object with visible_on_navigation set to False
        custom_object = await metadata_client.create_custom_object(
            object_display_name="Update Visible On Navigation to True API Test",
            visible_on_navigation=False,
        )

        # Get organization schema
        org_schema = await metadata_client.get_organization_schema_details()

        # Find our custom object in the schema
        custom_objects = [
            obj
            for obj in org_schema.objects
            if obj.object_kind == ObjectKind.CUSTOM
            and isinstance(obj, CustomObjectDescriptor)
            and obj.object_identifier.object_id == custom_object.id
        ]

        assert len(custom_objects) == 1, "Custom object not found in schema"
        custom_object_descriptor = custom_objects[0]

        # Verify the visible_on_navigation is False
        assert custom_object_descriptor.visible_on_navigation is False
        assert (
            custom_object_descriptor.display_name
            == "Update Visible On Navigation to True API Test"
        )

        # Update visible_on_navigation to True
        await metadata_client.update_custom_object(
            custom_object_id=custom_object.id, new_visible_on_navigation=True
        )

        # Get updated schema to verify changes
        updated_schema = await metadata_client.get_organization_schema_details()

        # Find our updated custom object
        updated_objects = [
            obj
            for obj in updated_schema.objects
            if obj.object_kind == ObjectKind.CUSTOM
            and isinstance(obj, CustomObjectDescriptor)
            and obj.object_identifier.object_id == custom_object.id
        ]

        assert len(updated_objects) == 1
        updated_object = updated_objects[0]

        # Verify the updates
        assert updated_object.visible_on_navigation is True
        assert (
            updated_object.display_name
            == "Update Visible On Navigation to True API Test"
        )

    async def test_update_visible_on_navigation_to_false_via_patch(
        self,
        metadata_client: MetadataClient,
    ) -> None:
        """Test setting visible_on_navigation to False via patch."""
        # Create a custom object with visible_on_navigation set to True
        custom_object = await metadata_client.create_custom_object(
            object_display_name="Update Visible On Navigation to False API Test",
            visible_on_navigation=True,
        )

        # Get organization schema
        org_schema = await metadata_client.get_organization_schema_details()

        # Find our custom object in the schema
        custom_objects = [
            obj
            for obj in org_schema.objects
            if obj.object_kind == ObjectKind.CUSTOM
            and isinstance(obj, CustomObjectDescriptor)
            and obj.object_identifier.object_id == custom_object.id
        ]

        assert len(custom_objects) == 1, "Custom object not found in schema"
        custom_object_descriptor = custom_objects[0]

        # Verify the visible_on_navigation is True
        assert custom_object_descriptor.visible_on_navigation is True
        assert (
            custom_object_descriptor.display_name
            == "Update Visible On Navigation to False API Test"
        )

        # Update visible_on_navigation to True
        await metadata_client.update_custom_object(
            custom_object_id=custom_object.id, new_visible_on_navigation=False
        )

        # Get updated schema to verify changes
        updated_schema = await metadata_client.get_organization_schema_details()

        # Find our updated custom object
        updated_objects = [
            obj
            for obj in updated_schema.objects
            if obj.object_kind == ObjectKind.CUSTOM
            and isinstance(obj, CustomObjectDescriptor)
            and obj.object_identifier.object_id == custom_object.id
        ]

        assert len(updated_objects) == 1
        updated_object = updated_objects[0]

        # Verify the updates
        assert updated_object.visible_on_navigation is False
        assert (
            updated_object.display_name
            == "Update Visible On Navigation to False API Test"
        )


class CustomFieldCreate(TypedDict):
    display_name: str
    description: str
    field_type: str
    required: bool


class TestCreateCustomField:
    async def test_create_custom_field(
        self,
        metadata_client: MetadataClient,
    ) -> None:
        # Create custom source object (City)
        timestamp = int(time.time() * 1000)
        source_object = await metadata_client.create_custom_object(
            object_display_name=f"Test Object 1 - {timestamp}"
        )

        # Create text field and verify it's editable
        source_object_text_field = await metadata_client.create_custom_field_v2(
            custom_object_id=source_object.id,
            custom_field_request=CreateCustomFieldRequestV2(
                custom_field_property_create=TextFieldProperty(
                    field_display_name="City Name",
                    is_required=False,
                    index_config=CaseAwareUniqueIndexableConfig(
                        is_indexed=True, is_case_sensitive=False
                    ),
                    max_length=200,
                ),
            ),
        )
        assert source_object_text_field.properties is not None
        assert isinstance(source_object_text_field.properties, TextFieldProperty)
        assert source_object_text_field.properties.is_ui_editable is True

    async def test_create_custom_object_with_fields_in_schema(
        self,
        metadata_client: MetadataClient,
    ) -> None:
        # Create custom object (Product)
        timestamp = int(time.time() * 1000)
        product_display_name = f"Product {timestamp}"

        custom_object = await metadata_client.create_custom_object(
            object_display_name=product_display_name,
        )

        # Create custom fields for the Product object
        fields_to_create: list[CustomFieldCreate] = [
            {
                "display_name": "Price",
                "description": "Product price",
                "field_type": "NUMBER",
                "required": True,
            },
            {
                "display_name": "Category",
                "description": "Product category",
                "field_type": "STRING",
                "required": True,
            },
            {
                "display_name": "Launch Date",
                "description": "Product launch date",
                "field_type": "DATE",
                "required": False,
            },
        ]

        created_fields: list[CustomField] = []
        for field in fields_to_create:
            created_field = await metadata_client.create_custom_field_v2(
                custom_object_id=custom_object.id,
                custom_field_request=CreateCustomFieldRequestV2(
                    custom_field_property_create=TextFieldProperty(
                        field_display_name=field["display_name"],
                        is_required=field["required"],
                        index_config=CaseAwareUniqueIndexableConfig(
                            is_indexed=True, is_case_sensitive=False
                        ),
                        max_length=200,
                    ),
                ),
            )
            created_fields.append(created_field)

        # Get organization schema and verify the custom object and fields
        org_schema = await metadata_client.get_organization_schema_details()

        # Find our custom object in the schema
        custom_objects = [
            obj for obj in org_schema.objects if obj.object_kind == ObjectKind.CUSTOM
        ]

        assert len(custom_objects) == 1, "Custom object not found in schema"
        custom_object_descriptor = custom_objects[0]

        # Verify it's a CustomObjectDescriptor
        assert isinstance(custom_object_descriptor, CustomObjectDescriptor)

        # # Verify object properties
        # assert custom_object_descriptor.object_identifier == product_identifier

        standard_fields = [
            field
            for field in custom_object_descriptor.fields
            if isinstance(field, StandardFieldDescriptor)
        ]

        assert len(standard_fields) == 11, "Expected 11 standard fields"

        # # Verify all created fields are present
        schema_fields = {
            field.field_identifier.field_id: field
            for field in custom_object_descriptor.fields
            if isinstance(field, CustomFieldDescriptor)
        }

        for expected_field in created_fields:
            field_id = expected_field.id
            logger.debug(f"Checking field {field_id}")
            logger.debug(f"Available field IDs in schema: {list(schema_fields.keys())}")

            assert field_id in schema_fields, f"Field {field_id} not found in schema"

            schema_field = schema_fields[field_id]
            logger.debug(f"Schema field: {schema_field}")
            logger.debug(f"Expected field: {expected_field}")
            assert isinstance(schema_field, CustomFieldDescriptor)
            assert schema_field.field_identifier.field_id == field_id, (
                f"Field ID mismatch. Expected: {field_id}, Got: {schema_field.field_identifier.field_id}"
            )
            assert schema_field.field_identifier.field_kind == FieldKind.CUSTOM, (
                f"Field kind mismatch. Expected: {FieldKind.CUSTOM}, Got: {schema_field.field_identifier.field_kind}"
            )
