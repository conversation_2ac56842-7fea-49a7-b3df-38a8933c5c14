from datetime import datetime

import pytest
import pytz
from pydantic_extra_types.timezone_name import TimeZoneName

from salestech_be.util.validation import (
    cast_datetime_or_none,
    count_not_none,
    get_rhs_of_range,
    strip_the_plus_suffix,
)


async def test_count_non_none() -> None:
    assert count_not_none() == 0
    assert count_not_none(1) == 1
    assert count_not_none(1, 2) == 2
    assert count_not_none(None, 1, None, 2) == 2
    assert count_not_none(None, None) == 0
    assert count_not_none(None) == 0


@pytest.mark.parametrize(
    "input_value,expected",
    [
        (None, None),
        ("", None),
    ],
)
def test_cast_datetime_or_none_empty_values(
    input_value: str | None, expected: datetime | None
) -> None:
    """Test that empty values return None."""
    assert cast_datetime_or_none(input_value, None) == expected


@pytest.mark.parametrize(
    "input_value,expected_year,expected_month,expected_day,expected_hour,expected_minute,expected_second",
    [
        # Standard ISO format
        ("2023-01-15 14:30", 2023, 1, 15, 14, 30, 0),
        ("2023-01-15 14:30:45", 2023, 1, 15, 14, 30, 45),
        ("2023-01-15T14:30", 2023, 1, 15, 14, 30, 0),
        ("2023-01-15T14:30:45", 2023, 1, 15, 14, 30, 45),
        ("2023-01-15T14:30:45.123456", 2023, 1, 15, 14, 30, 45),
        # Date only formats (should set time to 00:00:00)
        ("2023-01-15", 2023, 1, 15, 0, 0, 0),
        ("2023/01/15", 2023, 1, 15, 0, 0, 0),
        # US date format
        ("01/15/2023", 2023, 1, 15, 0, 0, 0),
        ("01/15/2023 14:30", 2023, 1, 15, 14, 30, 0),
        ("01/15/2023 14:30:45", 2023, 1, 15, 14, 30, 45),
        # European date format
        ("15/01/2023", 2023, 1, 15, 0, 0, 0),
        ("15/01/2023 14:30", 2023, 1, 15, 14, 30, 0),
        # Month name formats
        ("Jan 15 2023", 2023, 1, 15, 0, 0, 0),
        ("Jan 15, 2023", 2023, 1, 15, 0, 0, 0),
        ("January 15 2023", 2023, 1, 15, 0, 0, 0),
        ("January 15, 2023", 2023, 1, 15, 0, 0, 0),
        # Day first with month name
        ("15 Jan 2023", 2023, 1, 15, 0, 0, 0),
        ("15 January 2023", 2023, 1, 15, 0, 0, 0),
    ],
)
def test_cast_datetime_or_none_formats(
    input_value: str,
    expected_year: int,
    expected_month: int,
    expected_day: int,
    expected_hour: int,
    expected_minute: int,
    expected_second: int,
) -> None:
    """Test that various datetime formats are correctly parsed with default UTC timezone."""
    result = cast_datetime_or_none(input_value, None)

    assert result is not None
    assert result.year == expected_year
    assert result.month == expected_month
    assert result.day == expected_day
    assert result.hour == expected_hour
    assert result.minute == expected_minute
    assert result.second == expected_second
    assert result.tzinfo is not None
    assert result.tzinfo.tzname(None) == "UTC"


def test_cast_datetime_or_none_different_timezones() -> None:
    """Test parsing with different timezones."""
    test_date = "2023-01-15 14:30"

    # Test with UTC
    utc_result = cast_datetime_or_none(test_date, TimeZoneName("UTC"))
    assert utc_result is not None
    assert utc_result.tzinfo is not None
    assert utc_result.tzinfo.tzname(None) == "UTC"
    assert utc_result.hour == 14

    # Test with America/Los_Angeles
    pst_result = cast_datetime_or_none(test_date, TimeZoneName("America/Los_Angeles"))
    assert pst_result is not None
    assert pst_result.tzinfo is not None
    tz_name = pst_result.tzinfo.tzname(None)
    assert tz_name in (
        "PST",
        "PDT",
        "-0800",
        "-0700",
        "America/Los_Angeles",
    )
    assert (
        pst_result.hour == 14
    )  # Hour should be same since we're localizing, not converting

    # Test with Asia/Tokyo
    jst_result = cast_datetime_or_none(test_date, TimeZoneName("Asia/Tokyo"))
    assert jst_result is not None
    assert jst_result.tzinfo is not None
    tz_name = jst_result.tzinfo.tzname(None)
    assert tz_name in ("JST", "+0900", "Asia/Tokyo")
    assert (
        jst_result.hour == 14
    )  # Hour should be same since we're localizing, not converting


def test_get_rhs_of_range() -> None:
    assert get_rhs_of_range(None) is None
    # Test the various dashes and minus signs
    assert get_rhs_of_range("10-70") == "70"
    assert get_rhs_of_range("11\u201371") == "71"
    assert get_rhs_of_range("12\u201472") == "72"
    assert get_rhs_of_range("13\u221274") == "74"
    # Test number of ranges not 2
    assert get_rhs_of_range("10-70-90") == "10-70-90"
    # Test bogus
    assert get_rhs_of_range("bogus") == "bogus"


def test_strip_the_plus_suffix() -> None:
    assert strip_the_plus_suffix(None) is None
    assert strip_the_plus_suffix("12+") == "12"
    assert strip_the_plus_suffix("  22+   ") == "22"
    assert strip_the_plus_suffix("32") == "32"
    assert strip_the_plus_suffix("42+++++") == "42"
    assert strip_the_plus_suffix("++52+++++") == "++52"


def test_cast_datetime_or_none_with_timezone_in_string() -> None:
    """Test parsing strings that already contain timezone information."""
    # With UTC timezone specified in string
    utc_string = "2023-01-15T14:30:00Z"
    utc_result = cast_datetime_or_none(utc_string, None)
    assert utc_result is not None
    assert utc_result.tzinfo is not None
    assert utc_result.hour == 14

    # With explicit timezone offset
    offset_string = "2023-01-15T14:30:00+09:00"  # JST
    offset_result = cast_datetime_or_none(offset_string, None)
    assert offset_result is not None
    assert offset_result.tzinfo is not None
    assert offset_result.hour == 14  # Hour preserved from string

    # When converting to UTC, the hour should change
    assert offset_result.astimezone(pytz.UTC).hour == 5  # 14:00 JST = 05:00 UTC


def test_cast_datetime_or_none_with_datetime_object() -> None:
    """Test passing a datetime object instead of a string."""
    # Naive datetime
    naive_dt = datetime(2023, 1, 15, 14, 30)  # noqa: DTZ001
    naive_result = cast_datetime_or_none(naive_dt, None)
    assert naive_result is not None
    assert naive_result.tzinfo is not None
    assert naive_result.tzinfo.tzname(None) == "UTC"

    # Already timezone-aware datetime
    aware_dt = datetime(2023, 1, 15, 14, 30, tzinfo=pytz.timezone("Europe/Paris"))
    aware_result = cast_datetime_or_none(aware_dt, None)
    assert aware_result is not None
    assert aware_result.tzinfo is not None
    assert aware_result.tzinfo.tzname(None) in (
        "CET",
        "CEST",
        "+0100",
        "+0200",
        "Europe/Paris",
    )

    # Should preserve the original timezone
    assert aware_result == aware_dt


def test_cast_datetime_or_none_unusual_formats() -> None:
    """Test some unusual but valid datetime formats."""
    # RFC 2822 format (email date format)
    rfc2822 = "Mon, 15 Jan 2023 14:30:00 +0000"
    rfc2822_result = cast_datetime_or_none(rfc2822, None)
    assert rfc2822_result is not None
    assert rfc2822_result.year == 2023
    assert rfc2822_result.month == 1
    assert rfc2822_result.day == 15
    assert rfc2822_result.hour == 14

    # Natural language format
    natural = "15 January 2023 at 2:30 PM"
    natural_result = cast_datetime_or_none(natural, None)
    assert natural_result is not None
    assert natural_result.year == 2023
    assert natural_result.month == 1
    assert natural_result.day == 15
    assert natural_result.hour == 14

    # ISO 8601 format with Z suffix (Zulu time/UTC)
    iso_string = "2023-01-15T14:30:00Z"
    iso_result = cast_datetime_or_none(iso_string, None)
    assert iso_result is not None
    assert iso_result.year == 2023
    assert iso_result.month == 1
    assert iso_result.day == 15


def test_cast_datetime_or_none_invalid_formats() -> None:
    """Test invalid datetime formats."""
    # Completely invalid format
    assert cast_datetime_or_none("not a date", None) is None

    # Invalid date components
    assert cast_datetime_or_none("2023-13-15", None) is None  # Invalid month
    assert cast_datetime_or_none("2023-01-32", None) is None  # Invalid day

    # Almost valid but missing parts
    assert (
        cast_datetime_or_none("2023-01", None) is not None
    )  # Should work with dateutil parser

    # Valid date with invalid time
    assert cast_datetime_or_none("2023-01-15 25:70", None) is None
