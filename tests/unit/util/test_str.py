import pytest
from pydantic import BaseModel, ValidationError

from salestech_be.util.pydantic_types.str import (
    SchemeOptionalHttpUrlStr,
    is_linkedin_domain,
)


class _MyHttpUrlModel(BaseModel):
    url: SchemeOptionalHttpUrlStr


@pytest.mark.parametrize(
    "url",
    [
        "http://example.co.uk",
        "https://example.com",
        "example.com",
        "reevo.ai:443/api",
        "reevo.ai:8080/api/v1/contacts/_dosomething?a=345",
        # following should work, since we don't require tld / registered domain in this type for now
        "localhost:8080",
        "example",
    ],
)
def test_scheme_optional_http_url_str_success(url: str) -> None:
    model = _MyHttpUrlModel(url=url)
    assert isinstance(model.url, str)
    assert model.url == url


@pytest.mark.parametrize(
    "url",
    ["tel:+*********0", "*******.1.1"],
)
def test_scheme_optional_http_url_str_failure(url: str) -> None:
    with pytest.raises(ValidationError):
        _MyHttpUrlModel(url=url)


class TestIsLinkedInUrl:
    """Test cases for the is_linkedin_url function."""

    @pytest.mark.parametrize(
        "url",
        [
            # Standard LinkedIn URLs
            "https://www.linkedin.com",
            "http://www.linkedin.com",
            "https://linkedin.com",
            "http://linkedin.com",
            "www.linkedin.com",
            "linkedin.com",
            # LinkedIn URLs with paths
            "https://www.linkedin.com/company/example",
            "https://linkedin.com/in/john-doe",
            "linkedin.com/company/test-company",
            "www.linkedin.com/in/jane-smith",
            # LinkedIn URLs with query parameters
            "https://linkedin.com/company/example?tab=about",
            "linkedin.com/in/user?trk=profile",
            # LinkedIn URLs with fragments
            "https://linkedin.com/company/example#about",
            # Mixed case
            "HTTPS://WWW.LINKEDIN.COM",
            "LinkedIn.com",
            "WWW.LinkedIn.COM",
        ],
    )
    def test_is_linkedin_url_valid_linkedin_urls(self, url: str) -> None:
        """Test that valid LinkedIn URLs are correctly identified."""
        assert is_linkedin_domain(url) is True

    @pytest.mark.parametrize(
        "url",
        [
            # Non-LinkedIn domains
            "https://www.facebook.com",
            "https://twitter.com",
            "https://github.com",
            "https://google.com",
            "example.com",
            "test.org",
            # LinkedIn-like but different domains
            "https://www.linkedin.net",
            "https://linkedin.org",
            "https://mylinkedin.com",
            "https://linkedin-clone.com",
            # Subdomains that are not LinkedIn
            "https://fake.linkedin.com.evil.com",
            "https://notlinkedin.com",
            # URLs with linkedin in path but different domain
            "https://example.com/linkedin",
            "https://social.com/linkedin/profile",
            # Empty and None cases handled by validate_domain_name
            "",
            # Invalid URLs
            "not-a-url",
        ],
    )
    def test_is_linkedin_url_invalid_linkedin_urls(self, url: str) -> None:
        """Test that non-LinkedIn URLs are correctly rejected."""
        assert is_linkedin_domain(url) is False

    @pytest.mark.parametrize(
        "url",
        [
            # Edge cases with subdomains
            "https://api.linkedin.com",  # Should be False - subdomain
            "https://mobile.linkedin.com",  # Should be False - subdomain
            "https://help.linkedin.com",  # Should be False - subdomain
            "https://business.linkedin.com",  # Should be False - subdomain
        ],
    )
    def test_is_linkedin_url_subdomain_cases(self, url: str) -> None:
        """Test that LinkedIn subdomains are correctly handled."""
        # Based on validate_domain_name logic, subdomains should return False
        # because validate_domain_name("api.linkedin.com") returns "api.linkedin.com", not "linkedin.com"
        assert is_linkedin_domain(url) is False

    def test_is_linkedin_url_with_www_subdomain(self) -> None:
        """Test that www subdomain is correctly handled."""
        # The validate_domain_name function filters out "www" subdomain
        # so "www.linkedin.com" should return "linkedin.com"
        assert is_linkedin_domain("www.linkedin.com") is True
        assert is_linkedin_domain("https://www.linkedin.com") is True

    @pytest.mark.parametrize(
        "url",
        [
            # URLs with ports
            "https://linkedin.com:443",
            "http://linkedin.com:80",
            "linkedin.com:8080",
            # URLs with complex paths and parameters
            "https://linkedin.com/company/example/about/?tab=overview&section=main",
            "linkedin.com/in/john-doe-*********/detail/contact-info/",
        ],
    )
    def test_is_linkedin_url_complex_valid_cases(self, url: str) -> None:
        """Test complex but valid LinkedIn URLs."""
        assert is_linkedin_domain(url) is True

    def test_is_linkedin_url_case_insensitive(self) -> None:
        """Test that the function is case insensitive."""
        test_cases = [
            "LINKEDIN.COM",
            "LinkedIn.Com",
            "linkedin.COM",
            "LiNkEdIn.cOm",
        ]
        for url in test_cases:
            assert is_linkedin_domain(url) is True

    def test_is_linkedin_url_different_protocols(self) -> None:
        """Test that the function correctly identifies LinkedIn URLs with different protocols."""
        # The validate_domain_name function extracts domain regardless of protocol
        test_cases = [
            "ftp://linkedin.com",
            "mailto:<EMAIL>",
        ]
        for url in test_cases:
            assert is_linkedin_domain(url) is True

    @pytest.mark.parametrize(
        "url",
        [
            # Company profile URLs
            "https://www.linkedin.com/company/microsoft",
            "https://linkedin.com/company/google",
            "linkedin.com/company/apple-inc",
            "www.linkedin.com/company/meta",
            "https://linkedin.com/company/salesforce/about/",
            "linkedin.com/company/amazon/jobs/",
            "https://www.linkedin.com/company/tesla-motors/people/",
            # Personal profile URLs
            "https://www.linkedin.com/in/satya-nadella",
            "https://linkedin.com/in/sundarpichai",
            "linkedin.com/in/tim-cook-12345678",
            "www.linkedin.com/in/mark-zuckerberg/",
            "https://linkedin.com/in/marc-benioff/detail/contact-info/",
            "linkedin.com/in/jeff-bezos/recent-activity/",
            # School/University URLs
            "https://www.linkedin.com/school/stanford-university",
            "https://linkedin.com/school/harvard-university/",
            "linkedin.com/school/mit/alumni/",
            # Group URLs
            "https://www.linkedin.com/groups/12345678",
            "https://linkedin.com/groups/software-engineers/",
            # Job posting URLs
            "https://www.linkedin.com/jobs/view/*********0",
            "https://linkedin.com/jobs/search/?keywords=engineer",
            # Event URLs
            "https://www.linkedin.com/events/*********0",
            "https://linkedin.com/events/tech-conference-2024/",
            # Learning URLs
            "https://www.linkedin.com/learning/python-essential-training",
            "https://linkedin.com/learning/courses/data-science",
            # Sales Navigator URLs
            "https://www.linkedin.com/sales/search/people",
            "https://linkedin.com/sales/lead/*********",
            # Showcase pages
            "https://www.linkedin.com/showcase/microsoft-azure",
            "https://linkedin.com/showcase/google-cloud/",
            # Feed and activity URLs
            "https://www.linkedin.com/feed/",
            "https://linkedin.com/feed/update/urn:li:activity:*********0",
            # Messaging URLs
            "https://www.linkedin.com/messaging/",
            "https://linkedin.com/messaging/thread/*********0",
            # Network URLs
            "https://www.linkedin.com/mynetwork/",
            "https://linkedin.com/mynetwork/invite-connect/connections/",
            # Search URLs with parameters
            "https://www.linkedin.com/search/results/people/?keywords=engineer&origin=GLOBAL_SEARCH_HEADER",
            "https://linkedin.com/search/results/companies/?keywords=tech&geoUrn=%5B%22103644278%22%5D",
            # URLs with tracking parameters
            "https://www.linkedin.com/company/microsoft?trk=company_logo",
            "https://linkedin.com/in/satya-nadella?trk=profile-badge",
            # URLs with fragments
            "https://www.linkedin.com/company/google#life",
            "https://linkedin.com/in/tim-cook#experience",
            # Complex nested paths
            "https://www.linkedin.com/company/salesforce/life/engineering-team/",
            "https://linkedin.com/school/stanford-university/programs/computer-science/",
            # URLs with multiple query parameters
            "https://www.linkedin.com/jobs/search/?keywords=python&location=San%20Francisco&f_TPR=r86400",
            "https://linkedin.com/search/results/people/?keywords=data%20scientist&network=%5B%22F%22%5D&origin=FACETED_SEARCH",
        ],
    )
    def test_is_linkedin_url_with_company_and_profile_paths(self, url: str) -> None:
        """Test that LinkedIn URLs with company names, profiles, and various paths are correctly identified."""
        assert is_linkedin_domain(url) is True

    @pytest.mark.parametrize(
        "url",
        [
            # International LinkedIn domains (these should return False as they're different domains)
            "https://www.linkedin.cn/company/microsoft",
            "https://linkedin.de/in/user",
            "https://linkedin.fr/company/google",
            # LinkedIn-like URLs on different domains
            "https://www.facebook.com/linkedin",
            "https://twitter.com/linkedin",
            "https://github.com/linkedin",
            # URLs with linkedin in the path but different domains
            "https://careers.microsoft.com/linkedin-integration",
            "https://blog.google.com/linkedin-partnership",
        ],
    )
    def test_is_linkedin_url_similar_but_different_domains(self, url: str) -> None:
        """Test that URLs with linkedin in the path but different domains are correctly rejected."""
        assert is_linkedin_domain(url) is False
