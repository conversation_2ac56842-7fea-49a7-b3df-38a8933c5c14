import uuid
from decimal import Decimal
from typing import Any
from uuid import UUID

import pytest
from sqlalchemy.sql.elements import TextClause

from salestech_be.db.dao.generic_repository import GenericRepository, SelectionSpec
from salestech_be.db.models.core.base import Column, JsonColumn, TableModel
from salestech_be.db.models.core.constants import TableName
from salestech_be.db.models.core.utils import (
    SqlComparator,
    SqlSelection,
    SqlSort,
    SqlSortRankMapping,
)
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class DummyTable(TableModel):
    """Test table model for testing SQL generation."""

    table_name = TableName.account  # reuse existing table name for simplicity
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    name: Column[str]
    age: Column[int | None] = None
    type: Column[str | None] = None
    score: Column[Decimal | None] = None
    created_at: Column[ZoneRequiredDateTime]
    tags: Column[list[str] | None] = None  # array field
    metadata: Column[dict[str, Any] | None] = None  # json field
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    archived_at: Column[ZoneRequiredDateTime | None] = None
    uuid_list: JsonColumn[list[UUID] | None] = None


class WrapperGenericRepository(GenericRepository):
    """Test class to access protected methods."""

    def __init__(self) -> None:
        pass  # No need for actual DB engine in tests

    def get_list_statement(
        self,
        table_model: type[TableModel],
        *,
        selections: tuple[SqlSelection, ...] = (),
        sortings: tuple[SqlSort, ...] = (),
        limit: int | None = None,
        exclude_deleted_or_archived: bool = True,
        any_selections: tuple[SqlSelection, ...] = (),
        must_not_selections: tuple[SqlSelection, ...] = (),
    ) -> tuple[TextClause, str]:
        return self._get_list_by_selection_spec_statement(
            table_model=table_model,
            selection_spec=SelectionSpec(
                must=selections,
                any=any_selections,
                must_not=must_not_selections,
                sorting=sortings,
                limit=limit,
                exclude_invisible=exclude_deleted_or_archived,
            ),
        )


def test_json_sql_selection() -> None:
    selection = SqlSelection(
        column_name="uuid_list",
        sql_comparator=SqlComparator.JSON_CONTAINS,
        comparable_value=str(uuid.uuid4()),
    )

    repo = WrapperGenericRepository()
    stmt, stmt_str = repo.get_list_statement(
        table_model=DummyTable,
        selections=(selection,),
        sortings=(),
        exclude_deleted_or_archived=True,
    )

    assert stmt.text is not None
    assert (
        stmt_str
        == "select * from public.account where (uuid_list @> '[:p_0_uuid_list]'::jsonb and deleted_at is null and archived_at is null) and (TRUE) and (not (FALSE))"
    )


def test_get_list_by_filter_and_sort_specs_statement_single_selection() -> None:
    """Test _get_list_by_filter_and_sort_specs_statement with a single selection filter."""
    # Create a SqlSelection for name = 'test'
    selection = SqlSelection(
        column_name="name",
        sql_comparator=SqlComparator.EQ,
        comparable_value="test",
    )

    # Call the function through our test class
    repo = WrapperGenericRepository()
    stmt, stmt_str = repo.get_list_statement(
        table_model=DummyTable,
        selections=(selection,),
        sortings=(),
        exclude_deleted_or_archived=True,
    )

    # Verify the SQL statement
    assert stmt.text is not None
    expected_sql = "select * from public.account where (name = :p_0_name and deleted_at is null and archived_at is null) and (TRUE) and (not (FALSE))"
    assert stmt_str == expected_sql

    # Verify the parameters
    params = stmt.compile().params
    assert params["p_0_name"] == "test"


def test_get_list_by_filter_and_sort_specs_statement_multiple_selections() -> None:
    """Test _get_list_by_filter_and_sort_specs_statement with multiple selection filters."""
    # Create SqlSelections for name = 'test' AND age > 25
    selections = (
        SqlSelection(
            column_name="name",
            sql_comparator=SqlComparator.EQ,
            comparable_value="test",
        ),
        SqlSelection(
            column_name="age",
            sql_comparator=SqlComparator.GT,
            comparable_value=25,
        ),
    )

    # Call the function through our test class
    repo = WrapperGenericRepository()
    stmt, stmt_str = repo.get_list_statement(
        table_model=DummyTable,
        selections=selections,
        sortings=(),
        exclude_deleted_or_archived=True,
    )

    # Verify the SQL statement
    assert stmt.text is not None
    expected_sql = "select * from public.account where (name = :p_0_name and age > :p_1_age and deleted_at is null and archived_at is null) and (TRUE) and (not (FALSE))"
    assert stmt_str == expected_sql

    # Verify the parameters
    params = stmt.compile().params
    assert params["p_0_name"] == "test"
    assert params["p_1_age"] == 25


def test_get_list_by_filter_and_sort_specs_statement_single_sort() -> None:
    """Test _get_list_by_filter_and_sort_specs_statement with a single sort."""
    # Create a SqlSort for name ASC NULLS LAST
    sort = SqlSort(
        column_name="name",
        order_str="ASC",
        nulls_order_str="NULLS LAST",
    )

    # Call the function through our test class
    repo = WrapperGenericRepository()
    stmt, stmt_str = repo.get_list_statement(
        table_model=DummyTable,
        selections=(),
        sortings=(sort,),
        exclude_deleted_or_archived=True,
    )

    # Verify the SQL statement
    assert stmt.text is not None
    expected_sql = "select * from public.account where (deleted_at is null and archived_at is null) and (TRUE) and (not (FALSE)) order by name ASC NULLS LAST"
    assert stmt_str == expected_sql


def test_get_list_by_filter_and_sort_specs_statement_multiple_sorts() -> None:
    """Test _get_list_by_filter_and_sort_specs_statement with multiple sorts."""
    # Create SqlSorts for name ASC NULLS LAST, age DESC NULLS FIRST
    sorts = (
        SqlSort(
            column_name="name",
            order_str="ASC",
            nulls_order_str="NULLS LAST",
        ),
        SqlSort(
            column_name="age",
            order_str="DESC",
            nulls_order_str="NULLS FIRST",
        ),
    )

    # Call the function through our test class
    repo = WrapperGenericRepository()
    stmt, stmt_str = repo.get_list_statement(
        table_model=DummyTable,
        selections=(),
        sortings=sorts,
        exclude_deleted_or_archived=True,
    )

    # Verify the SQL statement
    assert stmt.text is not None
    expected_sql = "select * from public.account where (deleted_at is null and archived_at is null) and (TRUE) and (not (FALSE)) order by name ASC NULLS LAST, age DESC NULLS FIRST"
    assert stmt_str == expected_sql


def test_get_list_by_filter_and_sort_specs_statement_selections_and_sorts() -> None:
    """Test _get_list_by_filter_and_sort_specs_statement with both selections and sorts."""
    # Create SqlSelections for name = 'test' AND age > 25
    selections = (
        SqlSelection(
            column_name="name",
            sql_comparator=SqlComparator.EQ,
            comparable_value="test",
        ),
        SqlSelection(
            column_name="age",
            sql_comparator=SqlComparator.GT,
            comparable_value=25,
        ),
    )

    # Create SqlSorts for name ASC NULLS LAST, age DESC NULLS FIRST
    sorts = (
        SqlSort(
            column_name="name",
            order_str="ASC",
            nulls_order_str="NULLS LAST",
        ),
        SqlSort(
            column_name="age",
            order_str="DESC",
            nulls_order_str="NULLS FIRST",
        ),
    )

    # Call the function through our test class
    repo = WrapperGenericRepository()
    stmt, stmt_str = repo.get_list_statement(
        table_model=DummyTable,
        selections=selections,
        sortings=sorts,
        exclude_deleted_or_archived=True,
    )

    # Verify the SQL statement
    assert stmt.text is not None
    expected_sql = "select * from public.account where (name = :p_0_name and age > :p_1_age and deleted_at is null and archived_at is null) and (TRUE) and (not (FALSE)) order by name ASC NULLS LAST, age DESC NULLS FIRST"
    assert stmt_str == expected_sql

    # Verify the parameters
    params = stmt.compile().params
    assert params["p_0_name"] == "test"
    assert params["p_1_age"] == 25


def test_get_list_by_filter_and_sort_specs_statement_empty_selections_and_sorts() -> (
    None
):
    """Test _get_list_by_filter_and_sort_specs_statement with empty selections and sorts."""
    repo = WrapperGenericRepository()
    stmt, stmt_str = repo.get_list_statement(
        table_model=DummyTable,
        selections=(),
        sortings=(),
        exclude_deleted_or_archived=True,
    )

    # Verify the SQL statement
    assert stmt.text is not None
    expected_sql = "select * from public.account where (deleted_at is null and archived_at is null) and (TRUE) and (not (FALSE))"
    assert stmt_str == expected_sql


def test_get_list_by_filter_and_sort_specs_statement_with_limit() -> None:
    """Test _get_list_by_filter_and_sort_specs_statement with limit."""
    repo = WrapperGenericRepository()
    stmt, stmt_str = repo.get_list_statement(
        table_model=DummyTable,
        selections=(),
        sortings=(),
        limit=10,
        exclude_deleted_or_archived=True,
    )

    # Verify the SQL statement
    assert stmt.text is not None
    expected_sql = "select * from public.account where (deleted_at is null and archived_at is null) and (TRUE) and (not (FALSE)) limit 10"
    assert stmt_str == expected_sql


def test_get_list_by_filter_and_sort_specs_statement_include_deleted() -> None:
    """Test _get_list_by_filter_and_sort_specs_statement with exclude_deleted_or_archived=False."""
    repo = WrapperGenericRepository()
    stmt, stmt_str = repo.get_list_statement(
        table_model=DummyTable,
        selections=(),
        sortings=(),
        exclude_deleted_or_archived=False,
    )

    # Verify the SQL statement
    assert stmt.text is not None
    expected_sql = (
        "select * from public.account where (TRUE) and (TRUE) and (not (FALSE))"
    )
    assert stmt_str == expected_sql


def test_get_list_by_filter_and_sort_specs_statement_with_rank_mapping() -> None:
    """Test _get_list_by_filter_and_sort_specs_statement with rank mapping in sort."""
    # Create a SqlSort with rank mapping for type field
    sort = SqlSort(
        column_name="type",
        order_str="ASC",
        nulls_order_str="NULLS LAST",
        rank_mapping=SqlSortRankMapping(
            rank_map={"A": 1, "B": 2, "C": 3},
            rank_default=4,
        ),
    )

    # Call the function through our test class
    repo = WrapperGenericRepository()
    stmt, stmt_str = repo.get_list_statement(
        table_model=DummyTable,
        selections=(),
        sortings=(sort,),
        exclude_deleted_or_archived=True,
    )

    # Verify the SQL statement
    assert stmt.text is not None
    expected_sql = "select * from public.account where (deleted_at is null and archived_at is null) and (TRUE) and (not (FALSE)) order by CASE type WHEN :s_0_c_0_type THEN :s_0_r_0_type WHEN :s_0_c_1_type THEN :s_0_r_1_type WHEN :s_0_c_2_type THEN :s_0_r_2_type ELSE :s_0_r_3_type END ASC NULLS LAST"
    assert stmt_str == expected_sql

    # Verify the parameters
    params = stmt.compile().params
    assert params["s_0_c_0_type"] == "A"
    assert params["s_0_r_0_type"] == 1
    assert params["s_0_c_1_type"] == "B"
    assert params["s_0_r_1_type"] == 2
    assert params["s_0_c_2_type"] == "C"
    assert params["s_0_r_2_type"] == 3
    assert params["s_0_r_3_type"] == 4  # default rank


def test_get_list_by_filter_and_sort_specs_statement_multiple_rank_mappings() -> None:
    """Test _get_list_by_filter_and_sort_specs_statement with multiple rank mappings."""
    # Create multiple SqlSorts with rank mappings
    sorts = (
        SqlSort(
            column_name="type",
            order_str="ASC",
            nulls_order_str="NULLS LAST",
            rank_mapping=SqlSortRankMapping(
                rank_map={"A": 1, "B": 2, "C": 3},
                rank_default=4,
            ),
        ),
        SqlSort(
            column_name="name",
            order_str="DESC",
            nulls_order_str="NULLS FIRST",
            rank_mapping=SqlSortRankMapping(
                rank_map={"HIGH": 3, "MEDIUM": 2, "LOW": 1},
                rank_default=0,
            ),
        ),
    )

    # Call the function through our test class
    repo = WrapperGenericRepository()
    stmt, stmt_str = repo.get_list_statement(
        table_model=DummyTable,
        selections=(),
        sortings=sorts,
        exclude_deleted_or_archived=True,
    )

    # Verify the SQL statement
    assert stmt.text is not None
    expected_sql = "select * from public.account where (deleted_at is null and archived_at is null) and (TRUE) and (not (FALSE)) order by CASE type WHEN :s_0_c_0_type THEN :s_0_r_0_type WHEN :s_0_c_1_type THEN :s_0_r_1_type WHEN :s_0_c_2_type THEN :s_0_r_2_type ELSE :s_0_r_3_type END ASC NULLS LAST, CASE name WHEN :s_1_c_0_name THEN :s_1_r_0_name WHEN :s_1_c_1_name THEN :s_1_r_1_name WHEN :s_1_c_2_name THEN :s_1_r_2_name ELSE :s_1_r_3_name END DESC NULLS FIRST"
    assert stmt_str == expected_sql

    # Verify the parameters
    params = stmt.compile().params
    # Type sort parameters
    assert params["s_0_c_0_type"] == "A"
    assert params["s_0_r_0_type"] == 1
    assert params["s_0_c_1_type"] == "B"
    assert params["s_0_r_1_type"] == 2
    assert params["s_0_c_2_type"] == "C"
    assert params["s_0_r_2_type"] == 3
    assert params["s_0_r_3_type"] == 4

    # Name sort parameters
    assert params["s_1_c_0_name"] == "HIGH"
    assert params["s_1_r_0_name"] == 3
    assert params["s_1_c_1_name"] == "MEDIUM"
    assert params["s_1_r_1_name"] == 2
    assert params["s_1_c_2_name"] == "LOW"
    assert params["s_1_r_2_name"] == 1
    assert params["s_1_r_3_name"] == 0


def test_get_list_by_filter_and_sort_specs_statement_null_value() -> None:
    """Test _get_list_by_filter_and_sort_specs_statement with null value in selection."""
    # Create a SqlSelection for age IS NULL
    selection = SqlSelection(
        column_name="age",
        sql_comparator=SqlComparator.IS_NULL,
        comparable_value=None,
    )

    # Call the function through our test class
    repo = WrapperGenericRepository()
    stmt, stmt_str = repo.get_list_statement(
        table_model=DummyTable,
        selections=(selection,),
        sortings=(),
        exclude_deleted_or_archived=True,
    )

    # Verify the SQL statement
    assert stmt.text is not None
    expected_sql = "select * from public.account where (age IS NULL and deleted_at is null and archived_at is null) and (TRUE) and (not (FALSE))"
    assert stmt_str == expected_sql

    # Verify no parameters for NULL comparison
    params = stmt.compile().params
    assert not any(param.startswith("p_0_") for param in params)


def test_get_list_by_filter_and_sort_specs_statement_invalid_column() -> None:
    """Test _get_list_by_filter_and_sort_specs_statement with invalid column names."""
    # Create a SqlSelection with invalid column
    selection = SqlSelection(
        column_name="invalid_column",
        sql_comparator=SqlComparator.EQ,
        comparable_value="test",
    )

    # Create a SqlSort with invalid column
    sort = SqlSort(
        column_name="another_invalid_column",
        order_str="ASC",
        nulls_order_str="NULLS LAST",
    )

    # Call the function through our test class
    repo = WrapperGenericRepository()

    # The function should raise a ValueError
    with pytest.raises(ValueError) as e:
        repo.get_list_statement(
            table_model=DummyTable,
            selections=(selection,),
            sortings=(sort,),
            exclude_deleted_or_archived=True,
        )
    assert "invalid_column" in str(e)


def test_get_list_by_filter_and_sort_specs_statement_any_selections() -> None:
    """Test _get_list_by_filter_and_sort_specs_statement with any selections (OR conditions)."""
    # Create SqlSelections for name = 'test' OR age > 25
    selections = (
        SqlSelection(
            column_name="name",
            sql_comparator=SqlComparator.EQ,
            comparable_value="test",
        ),
        SqlSelection(
            column_name="age",
            sql_comparator=SqlComparator.GT,
            comparable_value=25,
        ),
    )

    # Call the function through our test class
    repo = WrapperGenericRepository()
    stmt, stmt_str = repo.get_list_statement(
        table_model=DummyTable,
        any_selections=selections,
        exclude_deleted_or_archived=True,
    )

    # Verify the SQL statement
    assert stmt.text is not None
    expected_sql = "select * from public.account where (deleted_at is null and archived_at is null) and (name = :a_0_name or age > :a_1_age) and (not (FALSE))"
    assert stmt_str == expected_sql

    # Verify the parameters
    params = stmt.compile().params
    assert params["a_0_name"] == "test"
    assert params["a_1_age"] == 25


def test_get_list_by_filter_and_sort_specs_statement_must_not_selections() -> None:
    """Test _get_list_by_filter_and_sort_specs_statement with must_not selections (NOT conditions)."""
    # Create SqlSelections for NOT (name = 'test' OR age > 25)
    selections = (
        SqlSelection(
            column_name="name",
            sql_comparator=SqlComparator.EQ,
            comparable_value="test",
        ),
        SqlSelection(
            column_name="age",
            sql_comparator=SqlComparator.GT,
            comparable_value=25,
        ),
    )

    # Call the function through our test class
    repo = WrapperGenericRepository()
    stmt, stmt_str = repo.get_list_statement(
        table_model=DummyTable,
        must_not_selections=selections,
        exclude_deleted_or_archived=True,
    )

    # Verify the SQL statement
    assert stmt.text is not None
    expected_sql = "select * from public.account where (deleted_at is null and archived_at is null) and (TRUE) and (not (name = :n_0_name or age > :n_1_age))"
    assert stmt_str == expected_sql

    # Verify the parameters
    params = stmt.compile().params
    assert params["n_0_name"] == "test"
    assert params["n_1_age"] == 25


def test_get_list_by_filter_and_sort_specs_statement_combined_selections() -> None:
    """Test _get_list_by_filter_and_sort_specs_statement with must, any, and must_not selections."""
    # Create selections for:
    # MUST (type = 'A')
    # AND (name = 'test' OR age > 25)
    # AND NOT (score = 100)
    # AND NOT (score IS NULL)
    must_selections = (
        SqlSelection(
            column_name="type",
            sql_comparator=SqlComparator.EQ,
            comparable_value="A",
        ),
    )
    any_selections = (
        SqlSelection(
            column_name="name",
            sql_comparator=SqlComparator.EQ,
            comparable_value="test",
        ),
        SqlSelection(
            column_name="age",
            sql_comparator=SqlComparator.GT,
            comparable_value=25,
        ),
    )
    must_not_selections = (
        SqlSelection(
            column_name="score",
            sql_comparator=SqlComparator.EQ,
            comparable_value=Decimal("100"),
        ),
        SqlSelection(
            column_name="score",
            sql_comparator=SqlComparator.IS_NOT_NULL,
            comparable_value=None,
        ),
    )

    # Call the function through our test class
    repo = WrapperGenericRepository()
    stmt, stmt_str = repo.get_list_statement(
        table_model=DummyTable,
        selections=must_selections,
        any_selections=any_selections,
        must_not_selections=must_not_selections,
        sortings=(),
        exclude_deleted_or_archived=True,
    )

    # Verify the SQL statement
    assert stmt.text is not None
    expected_sql = "select * from public.account where (type = :p_0_type and deleted_at is null and archived_at is null) and (name = :a_0_name or age > :a_1_age) and (not (score = :n_0_score or score IS NOT NULL))"
    assert stmt_str == expected_sql

    # Verify the parameters
    params = stmt.compile().params
    # Must parameters
    assert params["p_0_type"] == "A"
    # Any parameters
    assert params["a_0_name"] == "test"
    assert params["a_1_age"] == 25
    # Must not parameters
    assert params["n_0_score"] == Decimal("100")


def test_get_find_by_column_values_statement_with_none_in_lists() -> None:
    """Test _get_find_by_column_values_statement with None values in lists and ColumnIsArray."""
    from salestech_be.db.dao.array import ColumnIsArray

    repo = WrapperGenericRepository()

    # Test 1: List containing None values
    stmt = repo._get_find_by_column_values_statement(
        DummyTable,
        name=["test1", "test2", None],
        type="A",
    )

    # Should generate SQL with OR condition for null handling
    expected_sql_parts = [
        "(name = any(:name) or name is null)",
        "type = :type"
    ]

    stmt_str = str(stmt.compile(compile_kwargs={"literal_binds": False}))
    for part in expected_sql_parts:
        assert part in stmt_str

    # Test 2: List containing only None
    stmt = repo._get_find_by_column_values_statement(
        DummyTable,
        name=[None],
        type="A",
    )

    stmt_str = str(stmt.compile(compile_kwargs={"literal_binds": False}))
    assert "name is null" in stmt_str
    assert "type = :type" in stmt_str

    # Test 3: ColumnIsArray containing None values
    stmt = repo._get_find_by_column_values_statement(
        DummyTable,
        tags=ColumnIsArray(value=["tag1", "tag2", None]),
    )

    stmt_str = str(stmt.compile(compile_kwargs={"literal_binds": False}))
    assert "(tags = any(:tags) or tags is null)" in stmt_str

    # Test 4: Empty list
    stmt = repo._get_find_by_column_values_statement(
        DummyTable,
        name=[],
        type="A",
    )

    stmt_str = str(stmt.compile(compile_kwargs={"literal_binds": False}))
    assert "false" in stmt_str.lower()
    assert "type = :type" in stmt_str
