from datetime import UTC, datetime

import pytest

from salestech_be.common.util import (
    format_cents_to_usd,
    validate_datetime_to_epoch,
    validate_datetime_to_epoch_or_none,
)


def test_validate_datetime_to_epoch_int() -> None:
    # Arrange
    epoch = 1234567890

    # Act
    result = validate_datetime_to_epoch(epoch)

    # Assert
    assert result == epoch


def test_validate_datetime_to_epoch_iso_string() -> None:
    # Arrange
    dt_str = "2024-03-15T10:30:00Z"
    expected_epoch = int(datetime(2024, 3, 15, 10, 30, tzinfo=UTC).timestamp())

    # Act
    result = validate_datetime_to_epoch(dt_str)

    # Assert
    assert result == expected_epoch


def test_validate_datetime_to_epoch_datetime_object() -> None:
    # Arrange
    dt = datetime(2024, 3, 15, 10, 30, tzinfo=UTC)
    expected_epoch = int(dt.timestamp())

    # Act
    result = validate_datetime_to_epoch(dt)

    # Assert
    assert result == expected_epoch


def test_validate_datetime_to_epoch_naive_datetime() -> None:
    # Arrange
    dt = datetime(2024, 3, 15, 10, 30)  # noqa:DTZ001
    expected_epoch = int(dt.replace(tzinfo=UTC).timestamp())

    # Act
    result = validate_datetime_to_epoch(dt)

    # Assert
    assert result == expected_epoch


@pytest.mark.parametrize(
    "date_str,date_format",
    [
        ("2024-03-15", "%Y-%m-%d"),
        ("03/15/2024", "%m/%d/%Y"),
        ("15/03/2024", "%d/%m/%Y"),
        ("Mar 15 2024", "%b %d %Y"),
        ("Mar 15, 2024", "%b %d, %Y"),
        ("March 15 2024", "%B %d %Y"),
        ("March 15, 2024", "%B %d, %Y"),
    ],
)
def test_validate_datetime_to_epoch_various_date_formats(
    date_str: str, date_format: str
) -> None:
    # Arrange
    expected_dt = datetime.strptime(date_str, date_format).replace(tzinfo=UTC)
    expected_epoch = int(expected_dt.timestamp())

    # Act
    result = validate_datetime_to_epoch(date_str)

    # Assert
    assert result == expected_epoch


def test_validate_datetime_to_epoch_invalid_string() -> None:
    # Arrange
    invalid_dt = "not a datetime"

    # Act
    with pytest.raises(ValueError):
        validate_datetime_to_epoch(invalid_dt)


def test_validate_datetime_to_epoch_invalid_type() -> None:
    # Arrange
    invalid_input = ["not a datetime"]

    # Act
    with pytest.raises(ValueError):
        validate_datetime_to_epoch(invalid_input)


def test_validate_datetime_to_epoch_or_none_none_input() -> None:
    # Arrange
    input_value = None

    # Act
    result = validate_datetime_to_epoch_or_none(input_value)

    # Assert
    assert result is None


@pytest.mark.parametrize(
    "input_value",
    [
        "    ",
        "",
    ],
)
def test_validate_datetime_to_epoch_or_none_empty_string(input_value: str) -> None:
    # Arrange & Act
    result = validate_datetime_to_epoch_or_none(input_value)

    # Assert
    assert result is None


@pytest.mark.parametrize(
    "input_value",
    [
        "null",
        "NULL",
        "Null",
        "none",
        "NONE",
        "None",
    ],
)
def test_validate_datetime_to_epoch_or_none_null_strings(input_value: str) -> None:
    # Arrange & Act
    result = validate_datetime_to_epoch_or_none(input_value)

    # Assert
    assert result is None


def test_validate_datetime_to_epoch_or_none_valid_input() -> None:
    # Arrange
    dt = datetime(2024, 3, 15, 10, 30, tzinfo=UTC)
    expected_epoch = int(dt.timestamp())

    # Act
    result = validate_datetime_to_epoch_or_none(dt)

    # Assert
    assert result == expected_epoch


@pytest.mark.parametrize(
    "input_value,expected",
    [
        # Regular seconds timestamp (within ±2e10)
        (1234567890, 1234567890),
        (0, 0),
        (-1234567890, -1234567890),
        # Milliseconds timestamp (outside ±2e10)
        (1234567890000, 1234567890),
        (-1234567890000, -1234567890),
        (int(2e11), int(2e11 / 1000)),
        (int(-2e11), int(-2e11 / 1000)),
    ],
)
def test_validate_datetime_to_epoch_int_cases(input_value: int, expected: int) -> None:
    # Act
    result = validate_datetime_to_epoch(input_value)

    # Assert
    assert result == expected


@pytest.mark.parametrize(
    "input_value,expected",
    [
        # Regular seconds timestamp (within ±2e10)
        (1234567890.123, 1234567890),
        (0.0, 0),
        (-1234567890.456, -1234567890),
        # Milliseconds timestamp (outside ±2e10)
        (1234567890000.0, 1234567890),
        (-1234567890000.0, -1234567890),
        (2e11, int(2e11 / 1000)),
        (-2e11, int(-2e11 / 1000)),
    ],
)
def test_validate_datetime_to_epoch_float_cases(
    input_value: float, expected: int
) -> None:
    # Act
    result = validate_datetime_to_epoch(input_value)

    # Assert
    assert result == expected


@pytest.mark.parametrize(
    "input_value,expected",
    [
        # Regular seconds timestamp (within ±2e10)
        ("1234567890", 1234567890),
        ("0", 0),
        ("-1234567890", -1234567890),
        # Milliseconds timestamp (outside ±2e10)
        ("1234567890000", 1234567890),
        ("-1234567890000", -1234567890),
        (str(int(2e11)), int(2e11 / 1000)),
        (str(int(-2e11)), int(-2e11 / 1000)),
        # Float strings in seconds
        ("1234567890.123", 1234567890),
        ("0.0", 0),
        ("-1234567890.456", -1234567890),
        # Float strings in milliseconds
        ("1234567890000.0", 1234567890),
        ("-1234567890000.0", -1234567890),
        # Scientific notation
        ("2e11", int(2e11 / 1000)),
        ("-2e11", int(-2e11 / 1000)),
        ("1.234e10", int(1.234e10)),
        ("-1.234e10", int(-1.234e10)),
    ],
)
def test_validate_datetime_to_epoch_numeric_strings(
    input_value: str, expected: int
) -> None:
    # Act
    result = validate_datetime_to_epoch(input_value)

    # Assert
    assert result == expected


def test_format_cents_to_usd() -> None:
    assert format_cents_to_usd(14000) == "$140.00 USD"
    assert format_cents_to_usd(1400) == "$14.00 USD"
    assert format_cents_to_usd(1000) == "$10.00 USD"
    assert format_cents_to_usd(0) == "$0.00 USD"
    assert format_cents_to_usd(100) == "$1.00 USD"
    assert format_cents_to_usd(10) == "$0.10 USD"
