from collections.abc import Iterable
from datetime import date, datetime
from types import NoneType
from typing import Any, cast
from uuid import UUID, uuid4

import orjson
import pytest
from pydantic import BaseModel, ValidationError
from pytest import param as pparam

from salestech_be.common.query_util.filter_schema import (
    CompositeFilter,
    FilterSpec,
    FilterValue,
    RelativeTime,
    ValueFilter,
    filter_depth,
)
from salestech_be.common.query_util.operator import MatchOperator
from salestech_be.common.type.metadata.common import (
    StandardObjectIdentifier,
)
from salestech_be.common.type.metadata.schema import (
    ArrayElement,
    FieldReference,
    QualifiedField,
)


@pytest.mark.parametrize(
    "value, expected_type_or_error, element_type",
    [
        pparam(1, int, None, id="int_value"),
        pparam("1", str, None, id="str_value"),
        pparam(1.1, float, None, id="float_value"),
        pparam("1.1", str, None, id="str_float_value"),
        pparam(0, int, None, id="zero_int_value"),
        pparam("0", str, None, id="zero_str_value"),
        pparam(0.0, float, None, id="zero_float_value"),
        pparam("0.0", str, None, id="zero_str_float_value"),
        pparam("test", str, None, id="str_test"),
        pparam(True, bool, None, id="bool_true"),
        pparam("True", str, None, id="str_true"),
        pparam(False, bool, None, id="bool_false"),
        pparam("False", str, None, id="str_false"),
        pparam(str(uuid4()).lower(), UUID, None, id="uuid_lower"),
        pparam(str(uuid4()).upper(), UUID, None, id="uuid_upper"),
        pparam(datetime.now().isoformat(), datetime, None, id="datetime_iso"),
        pparam(datetime.now().date().isoformat(), date, None, id="date_iso"),
        pparam(["a", "b", "c"], set, str, id="set_str"),
        pparam([1, 2, 3], set, int, id="set_int"),
        pparam(["1", "2", "3"], set, str, id="set_str_numbers"),
        pparam([1.1, 2.2, 3.3], set, float, id="set_float"),
        pparam(["1.1", "2.2", "3.3"], set, str, id="set_str_floats"),
        pparam(
            [str(uuid4()).lower(), str(uuid4()).lower()], set, UUID, id="set_uuid_lower"
        ),
        pparam(
            [str(uuid4()).upper(), str(uuid4()).upper()], set, UUID, id="set_uuid_upper"
        ),
        pparam(
            [datetime.now().isoformat(), datetime.now().isoformat()],
            set,
            datetime,
            id="set_datetime_iso",
        ),
        pparam(
            [datetime.now(), datetime.now().isoformat()],
            set,
            datetime,
            id="set_mixed_datetime",
        ),
        pparam(
            [datetime.now().date().isoformat(), datetime.now().date().isoformat()],
            set,
            date,
            id="set_date_iso",
        ),
        pparam(
            RelativeTime(
                filter_value_type="relative_time",
                days=1,
                hours=2,
                minutes=3,
                seconds=4,
                negative=True,
            ),
            RelativeTime,
            None,
            id="relative_time",
        ),
        pparam(None, NoneType, None, id="none_value"),
        pparam([1, "abc"], ValidationError, None, id="mixed_list"),
        pparam({"a": "b"}, ValidationError, None, id="dict_value"),
    ],
)
def test_value_filter_deserialization(
    value: FilterValue, expected_type_or_error: type, element_type: type | None
) -> None:
    """
    Test cases to ensure the ValueFilter class can be deserialized from JSON
    with all possible types of "value" fields.
    """
    value_filter_dict = {
        "filter_type": "VALUE",
        "field": {
            "path": ["a_field"],
        },
        "value": value.model_dump(mode="json")
        if isinstance(value, BaseModel)
        else value,
        "operator": "EQ",
    }
    value_filter_json = orjson.dumps(value_filter_dict).decode()

    if issubclass(expected_type_or_error, Exception):
        with pytest.raises(expected_type_or_error):
            ValueFilter.model_validate_json(value_filter_json)
        return
    else:
        filter_instance = ValueFilter.model_validate_json(value_filter_json)

    assert isinstance(filter_instance.value, expected_type_or_error)
    if element_type:
        for v in cast(Iterable[Any], filter_instance.value):
            assert isinstance(v, element_type)

    assert (
        ValueFilter.model_validate(filter_instance.model_dump(mode="python"))
        == filter_instance
    )
    assert (
        ValueFilter.model_validate(filter_instance.model_dump(mode="json"))
        == filter_instance
    )
    assert ValueFilter.model_validate_json(value_filter_json) == filter_instance


@pytest.mark.parametrize("operator", MatchOperator)
def test_none_value_is_only_allowed_for_nullable_operators(
    operator: MatchOperator,
) -> None:
    if operator in ValueFilter._nullable_value_operators:
        assert (
            ValueFilter(
                operator=operator, value=None, field=QualifiedField(path=("name",))
            )
            is not None
        )
    else:
        with pytest.raises(ValidationError, match="Value cannot be None"):
            ValueFilter(
                operator=operator, value=None, field=QualifiedField(path=("name",))
            )


def test_filter_depth() -> None:
    # in the following filter, test data,
    # you see some of the "all_of", "any_of", "none_of" are omitted,
    # this is intentional to test the field model deserialization
    # can default absent fields to empty list without error out
    _3_layer_filter_data = """
    {
      "filter_type": "COMPOSITE",
      "any_of": [
        {
          "filter_type": "VALUE",
          "field": {
            "path": ["field_name", "*"]
          },
          "value": "value",
          "operator": "EQ"
        },
        {
          "filter_type": "COMPOSITE",
          "all_of": [
            {
              "filter_type": "VALUE",
              "field": {
                "relationship_id": "some_relationship",
                "field": {
                  "path": ["0c353b84-b0db-4448-a77d-2741da7b74ac"]
                }
              },
              "value": [
                  "b",
                  "c",
                  "a"
              ],
              "operator": "IN"
            },
            {
              "filter_type": "VALUE",
              "field": {
                "path": ["field_name"]
              },
              "value": "something",
              "operator": "EQ"
            }
          ]
        }
      ],
      "none_of": []
    }
    """
    c_filter = CompositeFilter.model_validate_json(_3_layer_filter_data)
    assert filter_depth(c_filter) == 3
    fs = FilterSpec(
        primary_object_identifier=StandardObjectIdentifier(object_name="object_name"),
        filter=c_filter,
    )
    assert fs.filter == c_filter
    assert c_filter.all_of == []
    assert c_filter.none_of == []

    # validate individual value filter
    assert (first_any_filter := c_filter.any_of[0])
    assert isinstance(first_any_filter, ValueFilter)
    assert isinstance(first_any_filter.field, QualifiedField)
    assert first_any_filter.field.path == ("field_name", ArrayElement.ALL)
    assert first_any_filter.value == "value"
    assert first_any_filter.operator == MatchOperator.EQ

    # validate the nested composite filter
    assert (nested_composite_filter := c_filter.any_of[1])
    assert isinstance(nested_composite_filter, CompositeFilter)
    assert nested_composite_filter.any_of == []
    assert nested_composite_filter.none_of == []
    assert (nested_all_filter := nested_composite_filter.all_of[0])
    assert isinstance(nested_all_filter, ValueFilter)
    assert isinstance(nested_all_filter.field, FieldReference)
    assert nested_all_filter.field.relationship_id == "some_relationship"
    assert nested_all_filter.field.field == QualifiedField(
        path=(UUID("0c353b84-b0db-4448-a77d-2741da7b74ac"),)
    )


def test_no_more_than_4_layers_in_filter_spec() -> None:
    _5_layer_filter_data = """
    {
      "filter_type": "COMPOSITE",
      "all_of": [],
      "any_of": [
        {
          "filter_type": "VALUE",
          "field": {
            "path": ["a"]
          },
          "value": "something",
          "operator": "EQ"
        },
        {
          "filter_type": "COMPOSITE",
          "all_of": [
            {
              "filter_type": "COMPOSITE",
              "any_of": [
                {
                  "filter_type": "VALUE",
                  "operator": "IN",
                  "field": {
                    "path": ["a"]
                  },
                  "value": [
                      "b",
                      "c",
                      "a"
                  ]
                },
                {
                  "filter_type": "COMPOSITE",
                  "any_of": [
                    {
                      "filter_type": "VALUE",
                      "operator": "IN",
                      "field": {
                        "path": ["a"]
                      },
                      "value": [
                          "b",
                          "c",
                          "a"
                      ]
                    },
                    {
                      "filter_type": "VALUE",
                      "operator": "EQ",
                      "field": {
                        "path": ["a"]
                      },
                      "value": "something"
                    }
                  ],
                  "none_of": []
                }
              ],
              "none_of": []
            }
          ],
          "any_of": []
        }
      ],
      "none_of": []
    }
    """
    c_filter = CompositeFilter.model_validate_json(_5_layer_filter_data)
    assert filter_depth(c_filter) == 5
    with pytest.raises(ValidationError) as e:
        FilterSpec(
            primary_object_identifier=StandardObjectIdentifier(
                object_name="object_name"
            ),
            filter=c_filter,
        )
    e.match("Filter depth cannot exceed 4, but found 5")
