#!/usr/bin/env python3

"""
CLI wrapper for salestech_be API operations.

Running locally:

You can either invoke `reevo` directly within the virtual env, or use `uv run reevo`.

Running in Dev:

```shell
/usr/bin/chamber exec "reevo-be-dev" -- uv run reevo
```

Usages:
    cURL an API endpoint, with the Authorization header set (using JWT generation), and template-rendering:
        reevo curl <URL> -- <curl options>

        e.g.
        reevo curl "http://localhost:8000/api/v1/organization/{organization_id}/backfill_permissions" -- --request POST

    Generate a JWT for a user:
        reevo token generate

Setup if you do not want to specify the --user-id and --org-id each time:

    `.env.cli` contents:
    LOCAL_REEVO_USER_ID="3b2f46a7-e4b3-42f1-a7f4-a232eb5dcfaa"
    LOCAL_REEVO_ORG_ID="cce7b290-8a08-4904-a6c7-2b6613877cf5"
"""

import argparse
import asyncio
import json
import os
import sys
import textwrap
import time
import uuid
from shlex import quote

import aiofiles
import argcomplete
import dns
import dns.asyncresolver
import whoisit
from dotenv import dotenv_values

from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings

logger = get_logger(__name__)
logger.remove()  # Disable logging for this script.

from salestech_be.core.user.service.permission_service import (  # noqa: E402
    get_permission_service_by_db_engine,
)
from salestech_be.services.auth.tokens import create_access_token  # noqa: E402
from salestech_be.services.auth.types import ReevoJWTClaims  # noqa: E402
from salestech_be.temporal.database import get_or_init_db_engine  # noqa: E402

FOUR_HOURS_FROM_NOW = time.time() + (4 * 60 * 60)
ONE_MINUTE_FROM_NOW = time.time() + 60  # just in case the application is hot-reloading.

WHOISIT_RDAP_BOOTSTRAP_FILE = ".whoisit_rdap_bootstrap_info.json"
WHOISIT_RDAP_BOOTSTRAP_MAX_AGE_DAYS = 7

config = dotenv_values(".env.cli")


async def generate_jwt(
    args: argparse.Namespace, token_expiry: float = FOUR_HOURS_FROM_NOW
) -> str:
    user_id = uuid.UUID(quote(args.user_id))
    organization_id = uuid.UUID(quote(args.org_id))

    db_engine = await get_or_init_db_engine()
    perm_service = get_permission_service_by_db_engine(db_engine)
    user_permission_claims = await perm_service.get_user_permission_claims(
        user_id=user_id,
        organization_id=organization_id,
    )

    return create_access_token(
        claims=ReevoJWTClaims(
            iss=args.jwt_issuer,
            aud=args.jwt_audience,
            sub=user_id,
            org=organization_id,
            exp=token_expiry,
            iat=time.time(),
            perm=user_permission_claims,
        )
    )


async def curl_call(args: argparse.Namespace) -> str:
    """
    Wrap an API endpoint for use in a curl command, injecting the Authorization header for convenience.

    Will attempt to recognize certain placeholders in the endpoint URL, and replace them with the correct values.
    """
    token = await generate_jwt(args, token_expiry=ONE_MINUTE_FROM_NOW)
    api_endpoint = args.endpoint.format(
        organization_id=args.org_id,
    )

    curl_cmd = f"""
curl --verbose \
-H "x-reevo-user-id: {quote(args.user_id)}" \
-H "x-reevo-org-id: {quote(args.org_id)}" \
-H "Authorization: Bearer {token}" \
{quote(api_endpoint)}
    """

    # Make it easier to copy-paste the command for debugging.
    curl_cmd = (
        textwrap.dedent(curl_cmd.strip())
        + " "
        + " ".join(quote(arg) for arg in args.curlopts)
    )
    sys.stderr.write(curl_cmd + os.linesep)

    proc = await asyncio.create_subprocess_shell(curl_cmd)
    sys.exit(proc.returncode)


async def domain_check(args: argparse.Namespace) -> None:  # noqa: C901, PLR0912, PLR0915
    """
    Check if a domain is valid.
    """
    domain = args.domain

    bootstrap_info = None
    if os.path.exists(WHOISIT_RDAP_BOOTSTRAP_FILE):
        sys.stderr.write("whoisit bootstrap data archive found" + os.linesep)
        async with aiofiles.open(WHOISIT_RDAP_BOOTSTRAP_FILE) as f:
            sys.stderr.write("loading whoisit bootstrap data" + os.linesep)
            bootstrap_info = json.loads(await f.read())
            whoisit.load_bootstrap_data(bootstrap_info)
    else:
        sys.stderr.write("no whoisit bootstrap data archive found" + os.linesep)

    is_bootstrap_old = False
    if whoisit.is_bootstrapped() and whoisit.bootstrap_is_older_than(
        days=WHOISIT_RDAP_BOOTSTRAP_MAX_AGE_DAYS
    ):
        is_bootstrap_old = True
        sys.stderr.write(
            f"bootstrapping whoisit data is older than {WHOISIT_RDAP_BOOTSTRAP_MAX_AGE_DAYS} days"
            + os.linesep
        )

    if not bootstrap_info or is_bootstrap_old:
        sys.stderr.write("bootstrapping whoisit" + os.linesep)
        await whoisit.bootstrap_async()
        sys.stderr.write("saving whoisit bootstrap data" + os.linesep)
        bootstrap_info = whoisit.save_bootstrap_data()
        async with aiofiles.open(WHOISIT_RDAP_BOOTSTRAP_FILE, "w") as f:
            await f.write(json.dumps(bootstrap_info))

    async def resolve_record(
        domain: str, record_type: dns.rdatatype.RdataType
    ) -> list[str] | None:
        try:
            result = await dns.asyncresolver.resolve(domain, record_type)
            return [str(r) for r in result]
        except (
            dns.resolver.NXDOMAIN,
            dns.resolver.NoAnswer,
            dns.resolver.NoNameservers,
        ):
            return None
        except Exception as e:
            sys.stderr.write(
                f"Error resolving {record_type} record for {domain}: {e!s}"
            )
            return None

    async def get_rdap_info(domain: str) -> list[str] | None:
        fields_we_care_about = [
            "handle",
            "name",
            "registration_date",
            "nameservers",
            "status",
        ]
        try:
            result = await whoisit.domain_async(domain, follow_related=False)
            sys.stderr.write(f"result: {result}")
            return [f"{r}: {result[r]}" for r in result if r in fields_we_care_about]
        except whoisit.errors.RateLimitedError as e:
            return [f"unavailable, we have been rate limited: {e}"]
        except whoisit.errors.ResourceDoesNotExist:
            return None

    async with asyncio.TaskGroup() as group:
        dns_a = group.create_task(resolve_record(domain, dns.rdatatype.A))
        dns_mx = group.create_task(resolve_record(domain, dns.rdatatype.MX))
        dns_ns = group.create_task(resolve_record(domain, dns.rdatatype.NS))
        dns_txt = group.create_task(resolve_record(domain, dns.rdatatype.TXT))

        dns_dmarc = group.create_task(
            resolve_record("_dmarc." + domain, dns.rdatatype.TXT)
        )

        dns_dkim_infraforge = group.create_task(
            resolve_record("default._domainkey." + domain, dns.rdatatype.TXT)
        )
        dns_dkim_google = group.create_task(
            resolve_record("google._domainkey." + domain, dns.rdatatype.TXT)
        )
        rdap_info = group.create_task(get_rdap_info(domain))

    has_dkim = dns_dkim_infraforge.result() or dns_dkim_google.result()

    recommendations = []
    if dmarc_attributes := dns_dmarc.result():
        for attribute in dmarc_attributes[0].split(";"):
            key, value = attribute.split("=", 1)
            if key == "aspf" and value == "s":
                recommendations.append("set DMARC aspf=r (relaxed)")
            elif key == "adkim" and value == "s":
                recommendations.append("set DMARC adkim=r (relaxed)")
            elif key == "p" and value == "reject" and not has_dkim:
                recommendations.append("set DMARC p=none until you have DKIM in place")
            elif key == "sp" and value == "reject" and not has_dkim:
                recommendations.append("set DMARC sp=none until you have DKIM in place")

    results = {
        "A": dns_a.result(),
        "MX": dns_mx.result(),
        "NS": dns_ns.result(),
        "TXT": dns_txt.result(),
        "_dmarc (TXT)": dns_dmarc.result(),
        "default._domainkey (TXT) (Infraforge)": dns_dkim_infraforge.result(),
        "google._domainkey (TXT) (Google)": dns_dkim_google.result(),
        "recommendations": recommendations,
        "RDAP": rdap_info.result(),
    }

    for record_type, records in results.items():
        if records:
            sys.stdout.write(f"{record_type}:" + os.linesep)
            sys.stdout.write(
                os.linesep.join(f"    {record}" for record in records) + os.linesep
            )
        else:
            sys.stdout.write(f"{record_type}: none" + os.linesep)


async def async_main() -> None:
    # ==============================
    # Setup argparse.

    # command: reev
    parser = argparse.ArgumentParser(
        prog="reevo", description="CLI wrapper for Reevo operations"
    )
    parser.add_argument(
        "--user-id", type=str, default=config.get("LOCAL_REEVO_USER_ID")
    )
    parser.add_argument("--org-id", type=str, default=config.get("LOCAL_REEVO_ORG_ID"))
    parser.add_argument(
        "--jwt-issuer",
        type=str,
        default=config.get("SALESTECH_BE_JWT_ISSUER")
        or os.environ.get("SALESTECH_BE_JWT_ISSUER")
        or settings.jwt_issuer,
    )
    parser.add_argument(
        "--jwt-audience",
        type=str,
        default=config.get("SALESTECH_BE_JWT_AUDIENCE")
        or os.environ.get("SALESTECH_BE_JWT_AUDIENCE")
        or settings.jwt_audience,
    )
    parser.add_argument(
        "--jwt-secret",
        type=str,
        default=config.get("SALESTECH_BE_JWT_SECRET")
        or os.environ.get("SALESTECH_BE_JWT_SECRET")
        or settings.jwt_secret,
    )

    subparsers = parser.add_subparsers()

    # subcommand: reevo curl
    curl_parser = subparsers.add_parser("curl", help="CURL operations")
    curl_parser.add_argument("endpoint", type=str)
    curl_parser.add_argument("curlopts", nargs=argparse.REMAINDER)
    curl_parser.set_defaults(func=curl_call)

    # subcommand: reevo decrypt
    decrypt_parser = subparsers.add_parser("decrypt", help="Decrypt operations")
    decrypt_parser.add_argument("encrypted_string", type=str)

    # subcommand: reevo domain
    domain_parser = subparsers.add_parser("domain", help="Domain operations")

    # subcommand: reevo domain check
    domain_subparsers = domain_parser.add_subparsers()
    domain_check_parser = domain_subparsers.add_parser(
        "check", help="Domain check operations"
    )
    domain_check_parser.add_argument("domain", type=str)
    domain_check_parser.set_defaults(func=domain_check)

    # subcommand: reevo token
    token_parser = subparsers.add_parser("token", help="JWT operations")

    # subcommand: reevo token generate
    token_subparsers = token_parser.add_subparsers()
    token_generate_parser = token_subparsers.add_parser(
        "generate", help="Generate a JWT for a user"
    )
    token_generate_parser.set_defaults(func=generate_jwt)

    argcomplete.autocomplete(parser)

    # ==============================
    # Parse, and run the target command.

    args = parser.parse_args()

    # Get the `func` handler, or show the USAGE.
    func = getattr(args, "func", None)
    if not func:
        parser.print_help()
        sys.exit(1)

    if args.func in (curl_call, generate_jwt) and (not args.user_id or not args.org_id):
        parser.error("Both --user-id and --org-id are required")

    output = await func(args)
    if output:
        sys.stdout.write(output)


def main() -> None:
    asyncio.run(async_main())
