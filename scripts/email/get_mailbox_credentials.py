#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to retrieve and update SMTP/IMAP credentials for an email account from Infraforge API.

Usage:
    python scripts/email/get_mailbox_credentials.py <email_account_id> <organization_id>

Example:
    python scripts/email/get_mailbox_credentials.py 123e4567-e89b-12d3-a456-************ 456e7890-e89b-12d3-a456-************
"""

import asyncio
import sys
from uuid import UUID

from salestech_be.core.email.account.service_v2 import (
    get_email_account_service_v2_by_db_engine,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings

logger = get_logger(__name__)


async def get_and_update_mailbox_credentials(
    email_account_id: UUID,
    organization_id: UUID,
) -> None:
    """
    Retrieve SMTP/IMAP credentials from Infraforge API and update the email account.

    Args:
        email_account_id: UUID of the email account
        organization_id: UUID of the organization
    """
    try:
        # Initialize database engine
        db_engine = DatabaseEngine(url=str(settings.db_url))

        # Get email account service
        email_account_service = get_email_account_service_v2_by_db_engine(db_engine)

        logger.info(f"Retrieving credentials for email account {email_account_id}")

        # Call the service method to get and persist credentials
        updated_account = (
            await email_account_service.get_and_persist_mailbox_credentials(
                email_account_id=email_account_id,
                organization_id=organization_id,
            )
        )

        logger.info(
            f"Successfully updated credentials for email account: {updated_account.email}"
        )

    except Exception as e:
        logger.error(f"Failed to retrieve credentials: {e}")
        raise


def main() -> None:
    """Main function to parse arguments and run the credential retrieval."""
    if len(sys.argv) != 3:  # noqa: PLR2004
        logger.error(
            "Usage: python scripts/email/get_mailbox_credentials.py <email_account_id> <organization_id>"
        )
        logger.error(
            "Example: python scripts/email/get_mailbox_credentials.py 123e4567-e89b-12d3-a456-************ 456e7890-e89b-12d3-a456-************"
        )
        sys.exit(1)

    try:
        email_account_id = UUID(sys.argv[1])
        organization_id = UUID(sys.argv[2])
    except ValueError as e:
        logger.error(f"Invalid UUID format: {e}")
        sys.exit(1)

    # Run the async function
    asyncio.run(get_and_update_mailbox_credentials(email_account_id, organization_id))


if __name__ == "__main__":
    main()
