import asyncio

from deepeval import evaluate
from deepeval.metrics import AnswerRelevancyMetric, GEval
from deepeval.test_case import LLMTestCase, LLMTestCaseParams
from langfuse import Lang<PERSON>

from salestech_be.core.ai.insights.llm_calls.generate_objection_insights_from_email_llm_call import (
    generate_objection_insights_from_email_llm_call,
)
from salestech_be.settings import settings

langfuse = Langfuse(
    public_key=settings.langfuse_public_key.get_secret_value(),
    secret_key=settings.langfuse_secret_key.get_secret_value(),
    host=settings.langfuse_host,
)

# Define custom evaluation metrics for objection insights
correctness_metric = GEval(
    name="Correctness",
    evaluation_steps=[
        "Check whether the objection insights in 'actual output' accurately identify objections in the email thread",
        "Verify that the insights correctly identify whether the email is a sales email",
        "Check if the source locations correctly reference parts of the email containing the objection",
        "Verify that keyword tags are relevant to the identified objection",
        "Heavily penalize omission of important objections present in the 'expected output'",
    ],
    evaluation_params=[
        LLMTestCaseParams.INPUT,
        LLMTestCaseParams.ACTUAL_OUTPUT,
        LLMTestCaseParams.EXPECTED_OUTPUT,
    ],
)

relevancy_metric = GEval(
    name="Relevancy",
    evaluation_steps=[
        "Evaluate whether the identified objections are genuinely relevant to sales conversations",
        "Check if the insights focus on actual objections rather than general statements",
        "Verify that the insights would be actionable for a sales representative",
        "Assess whether the objections are properly contextualized within the email thread",
    ],
    evaluation_params=[
        LLMTestCaseParams.INPUT,
        LLMTestCaseParams.ACTUAL_OUTPUT,
    ],
)

metrics = [correctness_metric, relevancy_metric, AnswerRelevancyMetric()]


async def run_evals() -> None:
    """Run evaluations on the objection insights dataset."""
    eval_metrics = metrics

    # Get the dataset for objection insights
    dataset = langfuse.get_dataset(
        "insights.generate_objections_from_email_thread-v0.1-golden"
    )

    test_cases = []

    # Process each item in the dataset
    for item in dataset.items:
        # Extract input parameters from the dataset item
        input_data = item.input

        # Call the function to generate objection insights
        actual_output = await generate_objection_insights_from_email_llm_call(
            messages=input_data.get("messages", ""),
            seller_contacts=input_data.get("seller_contacts", ""),
            seller_company_name=input_data.get("seller_company_name", ""),
            customer_contacts=input_data.get("customer_contacts", ""),
            customer_company_name=input_data.get("customer_company_name", ""),
            formatted_existing_insights=input_data.get(
                "formatted_existing_insights", ""
            ),
            organization_id=input_data.get(
                "organization_id", "00000000-0000-0000-0000-000000000000"
            ),
            langfuse_session_id=input_data.get("langfuse_session_id", "eval_session"),
            min_num_insights=input_data.get("min_num_insights", 0),
            max_num_insights=input_data.get("max_num_insights", 10),
        )

        # Convert the actual output to match expected format
        actual_output_dict = {
            "objections": [insight.model_dump() for insight in actual_output]
        }
        actual_output_str = str(actual_output_dict)

        # Create a test case comparing actual output with expected output
        test_case = LLMTestCase(
            input=str(input_data),
            actual_output=actual_output_str,
            expected_output=str(item.expected_output),
        )
        test_cases.append(test_case)

    # Run the evaluation
    evaluate(
        test_cases=test_cases,
        metrics=eval_metrics,
        hyperparameters={
            "feature": "objection_insights",
            "model": "claude-3.7",
            "agent_type": "zeroshot",
            "version": 1,
            "prompt template": "",
        },
    )


if __name__ == "__main__":
    asyncio.run(run_evals())
